{"time":"2023-09-26 10:58:42.123","thread":"main","level":"INFO ","class_line":"org.springframework.test.context.support.DefaultTestContextBootstrapper:260","msg":"Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]"}
{"time":"2023-09-26 10:58:42.136","thread":"main","level":"INFO ","class_line":"org.springframework.test.context.support.DefaultTestContextBootstrapper:209","msg":"Could not instantiate TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener]. Specify custom listener classes or make the default listener classes (and their required dependencies) available. Offending class: [org/springframework/web/context/request/RequestAttributes]"}
{"time":"2023-09-26 10:58:42.137","thread":"main","level":"INFO ","class_line":"org.springframework.test.context.support.DefaultTestContextBootstrapper:187","msg":"Using TestExecutionListeners: [org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@2ef1b7f4, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@29ac632a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3e45ca81, org.springframework.test.context.transaction.TransactionalTestExecutionListener@3cc9957d, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@1fa4db07]"}
{"time":"2023-09-26 10:58:42.430","thread":"main","level":"INFO ","class_line":"org.apache.dubbo.common.logger.LoggerFactory:","msg":"using logger: org.apache.dubbo.common.logger.log4j.Log4jLoggerAdapter"}

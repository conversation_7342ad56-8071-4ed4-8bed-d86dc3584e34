## 测试的类
> com.howbuy.crm.nt.remind.service.RemindXgTaskProduceServiceImpl
## 测试的方法
> remindXgTaskProduce(String arg)

## 分支伪代码
``` java
新规任务生成提醒通知开始
查询需要发送的消息提醒
if(存在消息){
    查询售后负责人,售后作业岗角色的所有投顾
    if(存在对应投顾){
        调用发送消息接口
        if(发送成功){
            记录成功日志
        }else{
            记录失败日志
        }
    }
}

## 测试案例
1.不存在新任务
### 1、no_cmremindmsg
2.存在新任务，但不存在投顾
### 2、no_conscode
3.存在新任务，存在投顾，发送失败
### 3、no_pushmsg
4.存在新任务，存在投顾，发送成功
### 4、sucess_pushmsg
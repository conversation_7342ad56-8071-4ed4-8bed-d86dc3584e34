## 测试的类
> com.howbuy.crm.nt.remind.service.RemindPushMsgServiceImpl
## 测试的方法 
> pushMsgData(String arg)

## 分支伪代码
``` java
抓出PC端待发送的消息   非企业微信,未推送,切到了期望推送时间
if(抓取到PC待发送){
    500一批    先置为发送中
    for循环挨个插入CM_REMIND_MSG_DAY表   
    并更新CM_PUSH_MSG 表的 PushFlag
}
抓出企业微信待发送的消息   企业微信,未推送,切到了期望推送时间,人工的
if(抓取到企业微信待发送){
    500一批    先置为发送中
    for循环挨个调用企业微信接口推送   并更新pushflag{
        根据pushid查询附件表是否有附件
        if(有附件){
            调用企业微信接口,推送卡片类型信息
        }else{无附件  
            调用企业微信接口,推送文本类型信息
        }
        更新CM_PUSH_MSG 表的 PushFlag
    }
}

## 测试案例
抓取到PC待发送
### 1、testNotEmptyPCExpectPushMsg
未抓取到PC待发送
### 2、testEmptyPCExpectPushMsg
抓取到企业微信待发送
### 3、testNotEmptyWechatExpectPushMsg
抓取到企业微信待发送 ,且有附件
### 4、testNotEmptyWechatExpectPushMsgAndExsitAnnex
抓取到企业微信待发送 ,且没有附件
### 5、testNotEmptyWechatExpectPushMsgAndNotExsitAnnex
未抓取到企业微信待发送
### 6、testEmptyWechatExpectPushMsg

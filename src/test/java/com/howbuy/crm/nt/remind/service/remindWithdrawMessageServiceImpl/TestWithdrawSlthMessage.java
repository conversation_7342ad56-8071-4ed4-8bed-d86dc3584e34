package com.howbuy.crm.nt.remind.service.remindWithdrawMessageServiceImpl;

import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.RemindWithdrawMessageDaoBuss;
import com.howbuy.crm.nt.remind.dao.RemindWithdrawMessageDao;
import com.howbuy.crm.nt.remind.dto.CmDoubleTrade;
import com.howbuy.crm.nt.remind.service.RemindWithdrawMessageServiceImpl;
import lombok.SneakyThrows;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * RemindWithdrawMessageServiceImpl.withdrawSlthMessage 资料退回发消息
 * <AUTHOR>
 * 2022年6月14日16:32:31
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestWithdrawSlthMessage extends PowerMockTestCase {
    @InjectMocks
    private RemindWithdrawMessageServiceImpl serviceMock;
    @Mock
    private RemindWithdrawMessageDao remindWithdrawMessageDaoMock;
    @Mock
    private RemindWithdrawMessageDaoBuss remindWithdrawMessageDaoBussMock;
    @Mock
    private CmPushMsgService cmPushMsgServiceMock;

    /**
     * 1 获取到的双录单信息不为空 且  投顾不为空
     */
    @Test
    @SneakyThrows
    public void testNotEmptyOrderNotEmptyConsCode() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "","1");
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(1)).pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 2 获取到的双录单信息为空  或  投顾为空
     */
    @Test
    @SneakyThrows
    public void testEmptyOrderOrEmptyConsCode() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "",null);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(0))
                .invoke("pushSlthMsg",Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());

    }


    /**
     * 3 获取到的销助信息不为空
     */
    @Test
    @SneakyThrows
    public void testNotEmptySL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "","1");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }


    /**
     * 4 获取到的销助信息为空
     */
    @Test
    @SneakyThrows
    public void testEmptySL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "","1");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,0);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(1))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 5 获取的销助里包含双录单投顾
     */
    @Test
    @SneakyThrows
    public void testConsCodeInSL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "","1");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }
    /**
     * 6 获取的销助里包含双录单创建者
     */
    @Test
    @SneakyThrows
    public void testCreatorInSL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "1","2");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 7 分部销助中不包含此投顾 且 双录单投顾  等于  双录单创建者
     */
    @Test
    @SneakyThrows
    public void testConsCodeNotInSLAndConsCodeIsCreator() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "2","2");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(0))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(2))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }
    /**
     * 8 获取的销助里包含双录单投顾  且  分部销助中不包含创建者 且 创建者不为空
     */
    @Test
    @SneakyThrows
    public void testConsCodeInSLAndCreatorNotInSL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "2","1");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(0))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(2))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 9 分部销助中不包含此投顾 且 双录单投顾  不等于  双录单创建者
     */
    @Test
    @SneakyThrows
    public void testConsCodeNotInSLAndConsCodeNotIsCreator() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "1","2");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 10 分部销助中不包含此投顾 且 双录单投顾  不等于  双录单创建者  且   分部销助中不包含创建者 且 创建者不为空
     */
    @Test
    @SneakyThrows
    public void testConsCodeNotInSLAndConsCodeNotIsCreatorAndCreatorNotInSL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", "3","2");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(2))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * 11 获取的销助里包含双录单投顾  且  分部销助中不包含创建者 且 创建者为空
     */
    @Test
    @SneakyThrows
    public void testConsCodeInSLAndNullCreatorNotInSL() {
        RemindWithdrawMessageServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock根据orderId获取cm_double_trade双录单信息
        mockGetCmDoubleTrade(spy,"1", null,"2");
        //mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
        mockGetSalesAssistantWithAuth(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindWithdrawMessageServiceImpl.class,
                        "withdrawSlthMessage")[0], spy, "1");
        //断言验证
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("1"),anyString(),any());
        PowerMockito.verifyPrivate(spy, Mockito.times(1))
                .invoke("pushSlthMsg",ArgumentMatchers.eq("2"),anyString(),any());
        Mockito.verify(remindWithdrawMessageDaoBussMock,new Times(0))
                .pushMsgByTemplateStr(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }

    /**
     * mock根据投顾号获取该投顾所在部门下拥有ROLE_SIC_ASSISTANT角色的投顾
     * @param spy 测试目标类
     * @param size 投顾编号
     */
    @SneakyThrows
    private void mockGetSalesAssistantWithAuth(RemindWithdrawMessageServiceImpl spy, int size) {
        MemberModifier.field(RemindWithdrawMessageServiceImpl.class, "remindWithdrawMessageDaoBuss").set(spy, remindWithdrawMessageDaoBussMock);
        ArrayList<String> salesAssistantList = new ArrayList<>();
        if(size>0){
            salesAssistantList.add("1");
        }
        PowerMockito.when(cmPushMsgServiceMock.getSalesAssistantWithAuth(Mockito.any())) .thenReturn(salesAssistantList);
    }

    /**
     * mock根据Id获取cm_double_trade双录单信息
     * @param spy 测试目标类
     * @param id 双录单id
     * @param creator 双录单创建人
     * @param conscode 双录单对应投顾
     */
    @SneakyThrows
    private void mockGetCmDoubleTrade(RemindWithdrawMessageServiceImpl spy, String id,
                                      String creator, String conscode) {
        MemberModifier.field(RemindWithdrawMessageServiceImpl.class, "remindWithdrawMessageDao").set(spy, remindWithdrawMessageDaoMock);
        CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
        cmDoubleTrade.setId(id);
        cmDoubleTrade.setCreator(creator);
        cmDoubleTrade.setConscode(conscode);
        PowerMockito.when(remindWithdrawMessageDaoMock.getCmDoubleTrade(Mockito.any())) .thenReturn(cmDoubleTrade);
    }
}

package com.howbuy.crm.nt.remind.service.remindPushMsgServiceImpl;

import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.nt.remind.service.RemindPushMsgServiceImpl;
import com.howbuy.crm.util.CrmNtConstant;
import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.annotations.Test;

import java.util.ArrayList;

/**
 * RemindPushMsgServiceImpl.pushMsgData 调度魔方  一分钟一次  处理待推送表消息  进每日消息提醒表  /  企业微信
 * <AUTHOR>
 * 2022年3月16日14:06:11
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestPushMsgData extends PowerMockTestCase {
    @InjectMocks
    private RemindPushMsgServiceImpl serviceMock;
    @Mock
    private CmRemindMsgDaoBuss cmRemindMsgDaoBussMock;
    @Mock
    private CmPushMsgService cmPushMsgServiceMock;
    /**
     * 1抓取到PC待发送
     */
    @Test
    @SneakyThrows
    public void testNotEmptyPCExpectPushMsg() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 1);
        //mock抓出企业微信端待发送的消息
        mockGetWechatExpectPushMsg(spy, 0);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                 "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(1)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(1)).insertCmRemindMsgDayAndUpdatePushFlag(Mockito.any());
    }

    /**
     * 2未抓取到PC待发送
     */
    @Test
    @SneakyThrows
    public void testEmptyPCExpectPushMsg() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 0);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                        "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(0)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(0)).insertCmRemindMsgDayAndUpdatePushFlag(Mockito.any());
    }

    /**
     * 3抓取到企业微信待发送
     */
    @Test
    @SneakyThrows
    public void testNotEmptyWechatExpectPushMsg() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 0);
        //mock抓出企业微信端待发送的消息
        mockGetWechatExpectPushMsg(spy, 1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                        "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(2)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(1)).getCmPushMsgAnnexTitle(Mockito.any());
    }

    /**
     * 4抓取到企业微信待发送 ,且有附件
     */
    @Test
    @SneakyThrows
    public void testNotEmptyWechatExpectPushMsgAndExsitAnnex() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 0);
        //mock抓出企业微信端待发送的消息
        mockGetWechatExpectPushMsg(spy, 1);
        //mock附件
        mockGetCmPushMsgAnnexTitle(spy,1);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                        "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(2)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(1)).getCmPushMsgAnnexTitle(Mockito.any());
        Mockito.verify(cmPushMsgServiceMock,new Times(1)).postWechatMsg(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(cmPushMsgServiceMock).postWechatMsg(null,null,null, CrmNtConstant.TEMP_TEXT_CARD,"nullnull");


    }
    /**
     * 5抓取到企业微信待发送 ,且没有附件
     */
    @Test
    @SneakyThrows
    public void testNotEmptyWechatExpectPushMsgAndNotExsitAnnex() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 0);
        //mock抓出企业微信端待发送的消息
        mockGetWechatExpectPushMsg(spy, 1);
        //mock附件
        mockGetCmPushMsgAnnexTitle(spy,0);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                        "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(2)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(1)).getCmPushMsgAnnexTitle(Mockito.any());
        Mockito.verify(cmPushMsgServiceMock,new Times(1)).postWechatMsg(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
        Mockito.verify(cmPushMsgServiceMock).postWechatMsg(null,null,null, CrmNtConstant.TEMP_TEXT_TEXT,"#");
    }

    /**
     * 6未抓取到企业微信待发送
     */
    @Test
    @SneakyThrows
    public void testEmptyWechatExpectPushMsg() {
        RemindPushMsgServiceImpl spy = PowerMockito.spy(serviceMock);
        //mock抓出PC端待发送的消息
        mockGetPCExpectPushMsg(spy, 0);
        //mock抓出企业微信端待发送的消息
        mockGetWechatExpectPushMsg(spy, 0);
        //执行待test方法
        ReflectionUtils.invokeMethod(
                MemberModifier.methods(RemindPushMsgServiceImpl.class,
                        "pushMsgData")[0], spy, "");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(0)).batchUpdatePushFlag(Mockito.any(), Mockito.any());
        Mockito.verify(cmRemindMsgDaoBussMock,new Times(0)).getCmPushMsgAnnexTitle(Mockito.any());
        Mockito.verify(cmPushMsgServiceMock,new Times(0)).postWechatMsg(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any());
    }


    /**
     *mock附件
     * @param spy 测试目标类
     * @param size 获取到的附件listsize
     */
    @SneakyThrows
    private void mockGetCmPushMsgAnnexTitle(RemindPushMsgServiceImpl spy, int size) {
        MemberModifier.field(RemindPushMsgServiceImpl.class, "cmRemindMsgDaoBuss").set(spy, cmRemindMsgDaoBussMock);
        if(size>0){
            CmPushMsgAnnex info = new CmPushMsgAnnex();
            PowerMockito.when(cmRemindMsgDaoBussMock.getCmPushMsgAnnexTitle(Mockito.any())) .thenReturn(info);
        }else{
            PowerMockito.when(cmRemindMsgDaoBussMock.getCmPushMsgAnnexTitle(Mockito.any())) .thenReturn(null);
        }
    }
    /**
     * mock抓出企业微信端待发送的消息
     * @param spy 测试目标类
     * @param size 获取到的企业微信待推送listsize
     */
    @SneakyThrows
    private void mockGetWechatExpectPushMsg(RemindPushMsgServiceImpl spy, int size) {
        MemberModifier.field(RemindPushMsgServiceImpl.class, "cmRemindMsgDaoBuss").set(spy, cmRemindMsgDaoBussMock);
        ArrayList<CmPushMsg> cmPushMsgs = new ArrayList<>();
        if(size>0){
            for (int i = 0; i <size ; i++) {
                CmPushMsg info = new CmPushMsg();
                cmPushMsgs.add(info);
            }
        }
        PowerMockito.when(cmRemindMsgDaoBussMock.getWechatExpectPushMsg()) .thenReturn(cmPushMsgs);
    }
    /**
     * mock抓出PC端待发送的消息
     * @param spy 测试目标类
     * @param size 获取到的PC待推送listsize
     */
    @SneakyThrows
    private void mockGetPCExpectPushMsg(RemindPushMsgServiceImpl spy, int size) {
        MemberModifier.field(RemindPushMsgServiceImpl.class, "cmRemindMsgDaoBuss").set(spy, cmRemindMsgDaoBussMock);
        ArrayList<CmPushMsg> cmPushMsgs = new ArrayList<>();
        if(size>0){
            for (int i = 0; i <size ; i++) {
                CmPushMsg info = new CmPushMsg();
                cmPushMsgs.add(info);
            }
        }
        PowerMockito.when(cmRemindMsgDaoBussMock.getPCExpectPushMsg()) .thenReturn(cmPushMsgs);
    }

}

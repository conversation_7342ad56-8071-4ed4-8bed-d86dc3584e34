## 测试的类
> com.howbuy.crm.nt.remind.service.RemindMsgCollectServiceImpl
## 测试的方法
> syncPushMsgData(String arg)  同步提醒消息数据方法

## 分支伪代码
```java
回收当天表数据
if (参数中包含分红提醒类别) {
       同步分红提醒数据
}else{
       打印日志“产品分红任务已停用！”
} 
if (参数中包含到期提醒类别) {
       同步到期提醒数据
}else{
       打印日志“产品到期任务已停用！”
}  
if (参数中包含客户生日类别) {
       同步客户生日数据
}else{
       打印日志“客户生日任务已停用！”
} 
if (参数中包含交易类型 = 强赎 到期通知类别) {
       同步强赎 到期通知数据
}else{
       打印日志“交易类型:强赎通知任务已停用！”
} 
if (参数中包含交易类型 = 红利发放 分红通知类别) {
       同步交易类型:红利发放通知 数据
}else{
       打印日志“交易类型:红利发放通知任务已停用！”
} 
if (参数中包含券商产品缴费提醒任务 缴款通知类别) {
       同步券商产品缴费提醒任务 缴款通知数据
}else{
       打印日志“券商产品缴费提醒已停用！”
} 
```

## 测试案例
##### 1、testSyncFhtxData：同步分红提醒
##### 2、testSyncDqtxData：同步到期提醒
##### 3、testSyncKhsrData：同步客户生日提醒
##### 4、testSyncQsdqtzData：同步强赎 到期通知
##### 5、testSyncFhffData：同步红利发放提醒
##### 6、testSyncQscpjfData：同步券商产品缴费提醒
##### 7、testNotMatchTypeData：没有匹配到类型
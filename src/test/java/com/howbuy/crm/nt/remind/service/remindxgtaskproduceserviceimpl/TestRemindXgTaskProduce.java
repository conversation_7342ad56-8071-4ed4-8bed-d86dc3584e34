package com.howbuy.crm.nt.remind.service.remindxgtaskproduceserviceimpl;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.nt.remind.service.RemindXgTaskProduceServiceImpl;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemindXgTaskProduceTest.java
 * @Description TODO
 * @createTime 2023年01月09日 09:14:00
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({RemindXgTaskProduceServiceImpl.class})
public class TestRemindXgTaskProduce extends PowerMockTestCase {

    @InjectMocks
    private RemindXgTaskProduceServiceImpl serviceMock;

    @Mock
    private CmRemindMsgDaoBuss cmRemindMsgDaoBussMock;

    @Mock
    private CmPushMsgService cmPushMsgServiceMock;

    /**
     * @param
     * @return
     * @Description: 1.不存在新任务
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_cmremindmsg() {

        RemindXgTaskProduceServiceImpl spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn(null).when(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
        //执行待test方法
        spy.remindXgTaskProduce("1");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
    }

    /**
     * @param
     * @return
     * @Description: 2.存在新任务，但不存在投顾
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_conscode() {

        RemindXgTaskProduceServiceImpl spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn(1).when(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();

        //mock verifyAndFillLoginPwd 返回false
        PowerMockito.doReturn(null).when(spy, "getConscode");
        //执行待test方法
        spy.remindXgTaskProduce("1");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
    }

    /**
     * @param
     * @return
     * @Description: 3.存在新任务，存在投顾，发送失败
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_pushmsg() {

        RemindXgTaskProduceServiceImpl spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn(1).when(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
        //mock verifyAndFillLoginPwd 返回false
        PowerMockito.doReturn(initConsCodes()).when(spy, "getConscode");
        PowerMockito.doReturn(null).when(cmPushMsgServiceMock).pushMsg(Mockito.any());
        //执行待test方法
        spy.remindXgTaskProduce("1");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
        Mockito.verify(cmPushMsgServiceMock).pushMsg(Mockito.any());
    }

    /**
     * @param
     * @return
     * @Description: 4.存在新任务，存在投顾，发送成功
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void sucess_pushmsg() {

        RemindXgTaskProduceServiceImpl spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn(1).when(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
        //mock verifyAndFillLoginPwd 返回false
        PowerMockito.doReturn(initConsCodes()).when(spy, "getConscode");
        PowerMockito.doReturn(initBaseResponse()).when(cmPushMsgServiceMock).pushMsg(Mockito.any());
        //执行待test方法
        spy.remindXgTaskProduce("1");
        //断言验证
        Mockito.verify(cmRemindMsgDaoBussMock).countEcDoubleRecordMessage();
        Mockito.verify(cmPushMsgServiceMock).pushMsg(Mockito.any());
    }

    private List<String> initConsCodes() {
        List<String> consCodes = Lists.newArrayList();
        consCodes.add("11111111");
        return consCodes;
    }

    private BaseResponse initBaseResponse() {
        BaseResponse response = new BaseResponse();
        response.setReturnCode(BaseConstantEnum.SUCCESS.getCode());
        return response;
    }
}

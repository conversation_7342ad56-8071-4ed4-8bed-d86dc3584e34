## 测试的类
> com.howbuy.crm.nt.remind.service.RemindWithdrawMessageServiceImpl
## 测试的方法 
> withdrawSlthMessage(String id)

## 分支伪代码
``` java
根据orderId获取CM_COUNTER_ORDER双录单信息
if(获取到的双录单信息不为空 且 双录单投顾不为空){
    根据双录单取分部销助
    if(获取到销助){
        循环给每个人推消息 type2
        if(获取的销助里包含双录单投顾){
            msgcheck = true
        }
        if(获取的销助里包含双录单创建者){
            crecheck = true
        }
    }else{
        存空白错误消息存档
    }
    if(分部销助中不包含此投顾){
        if(双录单投顾  等于  双录单创建者){
            给投顾推type2消息
        }else{
            给投顾推type1消息
            if(分部销助中不包含创建者 且 创建者不为空){
                给创建者推type2消息
            }
        }
    }else{
        if(分部销助中不包含创建者 且 创建者不为空){
            给创建者推type2消息
        }
    }
}

## 测试案例
获取到的双录单信息不为空 且  投顾不为空
### 1、testNotEmptyOrderNotEmptyConsCode
获取到的双录单信息为空  或  投顾为空
### 2、testEmptyOrderOrEmptyConsCode
获取到的销助信息不为空
### 3、testNotEmptySL
获取到的销助信息为空
### 4、testEmptySL
获取的销助里包含双录单投顾
### 5、testConsCodeInSL
获取的销助里包含双录单创建者
### 6、testCreatorInSL
分部销助中不包含此投顾 且 双录单投顾  等于  双录单创建者
### 7、testConsCodeNotInSLAndConsCodeIsCreator
获取的销助里包含双录单投顾  且  分部销助中不包含创建者 且 创建者不为空
### 8、testConsCodeInSLAndCreatorNotInSL
分部销助中不包含此投顾 且 双录单投顾  不等于  双录单创建者
### 9、testConsCodeNotInSLAndConsCodeNotIsCreator
分部销助中不包含此投顾 且 双录单投顾  不等于  双录单创建者  且   分部销助中不包含创建者 且 创建者不为空
### 10、testConsCodeNotInSLAndConsCodeNotIsCreatorAndCreatorNotInSL
获取的销助里包含双录单投顾  且  分部销助中不包含创建者 且 创建者为空
 ### 11、testConsCodeInSLAndNullCreatorNotInSL
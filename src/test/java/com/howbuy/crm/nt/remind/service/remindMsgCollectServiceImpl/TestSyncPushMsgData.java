package com.howbuy.crm.nt.remind.service.remindMsgCollectServiceImpl;

import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.slf4j.LoggerFactory;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.slf4j.Logger;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.nt.remind.service.RemindMsgCollectServiceImpl;
import org.springframework.util.ReflectionUtils;
import static org.mockito.Mockito.*;

/**
 * 单元测试：新增扫码签到客户信息
 * <AUTHOR>
 * @date 2022/7/12 15:06
 */
@PrepareForTest({RemindMsgCollectServiceImpl.class, LoggerFactory.class})
@PowerMockIgnore("javax.management.*")
@Test
public class TestSyncPushMsgData extends PowerMockTestCase {

	@InjectMocks
    private RemindMsgCollectServiceImpl serviceMock;
    
	@Mock
    private Logger log;
	@Mock
	private CmRemindMsgDaoBuss cmRemindMsgDaoBussMock;
	
	/** 产品分红任务--分红提醒 */
	public static final String ARG_CPFH = "cpfh";
	/** 加载产品到期任务--到期提醒 */
	public static final String ARG_CPDQ = "cpdq";
	/** 客户生日任务--客户生日 */
	public static final String ARG_KHSR = "khsr";
	/** 更新通知:交易类型 = 强赎 到期通知 */
	public static final String ARG_JYLXQS = "jylxqs";
	/** 更新通知:交易类型 = 红利发放 分红通知 */
	public static final String ARG_JYLXHLFF = "jylxhlff";
	/** 券商产品缴费提醒任务 缴款通知 */
	public static final String ARG_PAYMENT = "payment";
	
	
	private String arg = null;

    @SneakyThrows
    @BeforeMethod
	public void setUp()  {
    	PowerMockito.mockStatic(LoggerFactory.class);
        log = mock(Logger.class);
        PowerMockito.when(LoggerFactory.getLogger(RemindMsgCollectServiceImpl.class)).thenReturn(log);
        MemberModifier.field(RemindMsgCollectServiceImpl.class, "log").set(serviceMock, log);
	}
    
    /**
     * 1.同步分红提醒
     */
   @Test
   @SneakyThrows
   public void testSyncFhtxData() {
	   final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	   arg = ARG_CPFH;

	   ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	   //验证回收当天表数据1次
       Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
       //验证同步产品分红数据1次
       Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncFundFhMsg();
       verify(log, times(0)).info("产品分红任务已停用！");
       verify(log, times(1)).info("产品到期任务已停用！");
       verify(log, times(1)).info("客户生日任务已停用！");
       verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
       verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
       verify(log, times(1)).info("券商产品缴费提醒已停用！");
   }
   
   
   /**
    * 2.同步到期提醒
    */
   @Test
   @SneakyThrows
   public void testSyncDqtxData() {
	  final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	  arg = ARG_CPDQ;

	  ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	  //验证回收当天表数据1次
      Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
      //验证同步产品到期数据1次
      Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncFundDqMsg();
      verify(log, times(1)).info("产品分红任务已停用！");
      verify(log, times(0)).info("产品到期任务已停用！");
      verify(log, times(1)).info("客户生日任务已停用！");
      verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
      verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
      verify(log, times(1)).info("券商产品缴费提醒已停用！");
   }
  
   /**
    * 3.同步客户生日提醒
    */
   @Test
   @SneakyThrows
   public void testSyncKhsrData() {
	   final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	   arg = ARG_KHSR;

	   ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	   //验证回收当天表数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
	   //验证同步客户生日数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncCustBirthDayMsg();
	   verify(log, times(1)).info("产品分红任务已停用！");
	   verify(log, times(1)).info("产品到期任务已停用！");
	   verify(log, times(0)).info("客户生日任务已停用！");
	   verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
	   verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
	   verify(log, times(1)).info("券商产品缴费提醒已停用！");
    }
 
	 /**
	  * 4.同步强赎 到期通知
	  */
	@Test
	@SneakyThrows
	public void testSyncQsdqtzData() {
	    final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
		arg = ARG_JYLXQS;
	
		ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
		//验证回收当天表数据1次
	    Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
	    //验证交易类型 = 强赎 到期通知数据1次
	    Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncJylxqsMsg();
	    verify(log, times(1)).info("产品分红任务已停用！");
	    verify(log, times(1)).info("产品到期任务已停用！");
	    verify(log, times(1)).info("客户生日任务已停用！");
	    verify(log, times(0)).info("交易类型:强赎通知任务已停用！");
	    verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
	    verify(log, times(1)).info("券商产品缴费提醒已停用！");
	 }

	/**
	 * 5.同步红利发放提醒
	 */
	@Test
	@SneakyThrows
	public void testSyncFhffData() {
	   final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	   arg = ARG_JYLXHLFF;
	
	   ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	   //验证回收当天表数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
	   //验证同步交易类型 = 红利发放 分红数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncJylxhlffMsg();
	   verify(log, times(1)).info("产品分红任务已停用！");
	   verify(log, times(1)).info("产品到期任务已停用！");
	   verify(log, times(1)).info("客户生日任务已停用！");
	   verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
	   verify(log, times(0)).info("交易类型:红利发放通知任务已停用！");
	   verify(log, times(1)).info("券商产品缴费提醒已停用！");
	}

	/**
	 * 6.同步券商产品缴费提醒
	 */
	@Test
	@SneakyThrows
	public void testSyncQscpjfData() {
	   final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	   arg = ARG_PAYMENT;
	
	   ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	   //验证回收当天表数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
	   //验证同步券商产品缴费提醒任务 缴款通知数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).syncCustPaymentMsg();
	   verify(log, times(1)).info("产品分红任务已停用！");
	   verify(log, times(1)).info("产品到期任务已停用！");
	   verify(log, times(1)).info("客户生日任务已停用！");
	   verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
	   verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
	   verify(log, times(0)).info("券商产品缴费提醒已停用！");
	}
	
	/**
	 * 7.没有匹配到类型
	 */
	@Test
	@SneakyThrows
	public void testNotMatchTypeData() {
	   final RemindMsgCollectServiceImpl spy = PowerMockito.spy(serviceMock);
	   arg = "";
	
	   ReflectionUtils.invokeMethod(MemberModifier.methods(RemindMsgCollectServiceImpl.class, "syncPushMsgData")[0], spy,arg);
	   //验证回收当天表数据1次
	   Mockito.verify(cmRemindMsgDaoBussMock, new Times(1)).recycleDayMsgToHis();
	   verify(log, times(1)).info("产品分红任务已停用！");
	   verify(log, times(1)).info("产品到期任务已停用！");
	   verify(log, times(1)).info("客户生日任务已停用！");
	   verify(log, times(1)).info("交易类型:强赎通知任务已停用！");
	   verify(log, times(1)).info("交易类型:红利发放通知任务已停用！");
	   verify(log, times(1)).info("券商产品缴费提醒已停用！");
	}


}
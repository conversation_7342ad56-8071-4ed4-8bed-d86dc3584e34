/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service.updatentcmconferencecustinfoserviceimpl;

import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.request.UpdateNtCmConferenceCustInfoRequest;
import com.howbuy.crm.nt.conference.service.UpdateNtCmConferenceCustInfoServiceImpl;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/31 19:55
 * @since JDK 1.8
 */
@PrepareForTest({UpdateNtCmConferenceCustInfoServiceImpl.class})
@PowerMockIgnore("javax.management.*")
@Test
public class UpdateConferenceSigningTest extends PowerMockTestCase {

    @InjectMocks
    private UpdateNtCmConferenceCustInfoServiceImpl serviceMock;
    @Mock
    private CmConferenceCustInfoBuss cmConferenceCustInfoBuss;

    private static final String CONFERENCE_ID = "123456789";
    private static final String HBONE_NO = "123456789";

    private static final  Integer ACTUAL_SIGNING_NUM = 1;



    private UpdateNtCmConferenceCustInfoRequest getRequest(){
        UpdateNtCmConferenceCustInfoRequest request = new UpdateNtCmConferenceCustInfoRequest();
        request.setActualnub(ACTUAL_SIGNING_NUM);
        request.setConferenceid(CONFERENCE_ID);
        request.setHboneNo(HBONE_NO);
        return request;
    }


    //测试 UpdateNtCmConferenceCustInfoServiceImpl 中 updateConferenceSigning 方法

    /**
     * 测试场景： 传入参数为空
     */
    @Test
    public void testValidateFalse() {
        UpdateNtCmConferenceCustInfoRequest request = getRequest();
        request.setHboneNo(null);
//        request.setActualnub(null);
        BaseResponse response = new BaseResponse();

//        when(serviceMock.validate(request,response)).thenReturn(false);
//        PowerMockito.when(serviceMock.validate(request,response)).thenReturn(false);

        response=serviceMock.updateConferenceSigning(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.PARAM_ERROR.getCode());
        Assert.assertEquals(response.getDescription(),"参数错误:传入参数（一账通）为空");

    }

    /**
     * 测试场景： 更新客户 返回null
     */
    @Test
    public void testUpdateCustNull() {
        UpdateNtCmConferenceCustInfoRequest request = getRequest();
        BaseResponse response = new BaseResponse();

//        when(serviceMock.validate(request,response)).thenReturn(false);
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenThrow(new RuntimeException());

        response=serviceMock.updateConferenceSigning(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.SYS_ERROR.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.SYS_ERROR.getDescription()+":修改异常！");

    }

    /**
     * 测试场景： 更新客户 返回0
     */
    @Test
    public void testUpdateCustZero() {
        UpdateNtCmConferenceCustInfoRequest request = getRequest();
        BaseResponse response = new BaseResponse();

//        when(serviceMock.validate(request,response)).thenReturn(false);
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenReturn(0);

        response=serviceMock.updateConferenceSigning(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.FAIL.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.FAIL.getDescription()+":客户未报名，签到失败！");

    }

    /**
     * 测试场景： 更新客户 返回1
     */
    @Test
    public void testUpdateCustNormal() {
        UpdateNtCmConferenceCustInfoRequest request = getRequest();
        BaseResponse response = new BaseResponse();

//        when(serviceMock.validate(request,response)).thenReturn(false);
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenReturn(1);

        response=serviceMock.updateConferenceSigning(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.SUCCESS.getCode());
        Assert.assertEquals(response.getDescription(),"操作成功");

    }


}
package com.howbuy.crm.nt.conference.service.orderconferenceserviceimpl;

import com.howbuy.crm.cache.cacheService.CacheKeyPrefix;
import com.howbuy.crm.cache.cacheService.lock.LockService;
import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceLogMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceLog;
import com.howbuy.crm.nt.conference.request.OrderConferenceRequest;
import com.howbuy.crm.nt.conference.response.OrderConferenceResponse;
import com.howbuy.crm.nt.conference.service.OrderConferenceServiceImpl;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import java.lang.reflect.Method;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@PrepareForTest({OrderConferenceServiceImpl.class})
@PowerMockIgnore("javax.management.*")
@Test
public class OrderConferenceTest extends PowerMockTestCase {

    @InjectMocks
    private OrderConferenceServiceImpl orderService;
    @Mock
    private CmConferenceConscustMapper conferenceConscustMapper;

    @Mock
    private ConscustMapper conscustMapper;

    @Mock
    private CmConferenceLogMapper cmConferenceLogMapper;

    @Mock
    private CmConferenceMapper cmConferenceMapper;

    @Mock
    private LockService lockService;

    @Mock
    private CmConferenceCustInfoBuss conferenceCustInfoBuss;


    /**
     * 接口调用默认的时间是30s
     */
    private static final int DEFAULT_EXPIRE = 30;

    private static final String CONFERENCE_ID = "456";

    private static final String HBONE_NO = "123456789";
    private static final String COURSE_ID = "123";

    private static final String CUST_NO="5555";


    private void mockGenConferenceId(String conferenceId){
        // 交易属性 没有值
        Method privateMethod = PowerMockito.method(OrderConferenceServiceImpl.class,
                "getConferenceIdByCourseId");
        PowerMockito.stub(privateMethod).toReturn(conferenceId);
    }


    private OrderConferenceRequest getReq(){
        OrderConferenceRequest request = new OrderConferenceRequest();
        request.setCourseId(COURSE_ID);
        request.setHboneNo(HBONE_NO);
        return request;
    }

    /**
     * mock private method: dealOrderConference
     * @param conferenceId
     * @param conscustNo
     */
    private void mockdealOrderConference(String conferenceId,String conscustNo){
        Method privateMethod = PowerMockito.method(OrderConferenceServiceImpl.class,
                "dealOrderConference");
        PowerMockito.stub(privateMethod);

    }

    @Test
    public void testOrderConference_success() {
        // Arrange
        OrderConferenceRequest request =getReq();

        String courseId = request.getCourseId();
        String hboneNo = request.getHboneNo();
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + courseId + hboneNo;

        when(conscustMapper.selectConscustNoByHboneNo(hboneNo)).thenReturn(CUST_NO);
//        when(cmConferenceMapper.selectValidConferenceByCourseId(courseId)).thenReturn(CONFERENCE_ID);
        mockGenConferenceId(CONFERENCE_ID);
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
        when(cmConferenceLogMapper.insert(any())).thenReturn(1);

        mockdealOrderConference(CONFERENCE_ID,CUST_NO);
        // Act
        OrderConferenceResponse response = orderService.orderConference(request);

        // Assert
        verify(lockService).getLock(uniqKey, getExpireSecond());
        verify(lockService).releaseLock(uniqKey);
        verify(conscustMapper).selectConscustNoByHboneNo(hboneNo);
//        verify(cmConferenceLogMapper).insert(any(CmConferenceLog.class));
        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        assertEquals(response.getReturnCode(), BaseConstantEnum.SUCCESS.getCode());
    }

    @Test
    public void testOrderConference_lockFailed() {
        // Arrange
        OrderConferenceRequest request =getReq();

        String courseId = request.getCourseId();
        String hboneNo = request.getHboneNo();
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + courseId + hboneNo;

        when(lockService.getLock(uniqKey, getExpireSecond())).thenReturn(false);

        // Act
        OrderConferenceResponse response = orderService.orderConference(request);

        // Assert
        verify(lockService).getLock(uniqKey, getExpireSecond());
        verify(lockService).releaseLock(uniqKey);
//        verify(conscustMapper).selectConscustNoByHboneNo(hboneNo);
        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        assertEquals(CrmNtConstant.SYSTEM_ERROR, response.getReturnCode());
        assertEquals("已经在执行此次调用录入:" + "hboneNo" + hboneNo + "courseId" + courseId,
                response.getDescription());
    }

    @Test
    public void testOrderConference_conferenceOrConscustNull() {
        // Arrange
        OrderConferenceRequest request = new OrderConferenceRequest();
        request.setCourseId("123");
        request.setHboneNo("456");

        String courseId = request.getCourseId();
        String hboneNo = request.getHboneNo();
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + courseId + hboneNo;

        when(conscustMapper.selectConscustNoByHboneNo(hboneNo)).thenReturn(CUST_NO);
//        when(cmConferenceMapper.selectValidConferenceByCourseId(courseId)).thenReturn(CONFERENCE_ID);
        //获取不到会议id
        mockGenConferenceId(null);
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
        when(cmConferenceLogMapper.insert(any())).thenReturn(1);

        mockdealOrderConference(CONFERENCE_ID,CUST_NO);

        // Act
        OrderConferenceResponse response = orderService.orderConference(request);

        // Assert
        verify(lockService).getLock(uniqKey, getExpireSecond());
        verify(lockService).releaseLock(uniqKey);
        verify(conscustMapper).selectConscustNoByHboneNo(hboneNo);
        verify(cmConferenceLogMapper).insert(any(CmConferenceLog.class));
        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        assertEquals(CrmNtConstant.NULL_ERROR, response.getReturnCode());
        assertEquals("传入一账通号和课程id获取不到数据", response.getDescription());
    }

    @Test
    public void testOrderConference_exception() {
        // Arrange
        OrderConferenceRequest request =getReq();

        String courseId = request.getCourseId();
        String hboneNo = request.getHboneNo();
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + courseId + hboneNo;

        //异常
        when(conscustMapper.selectConscustNoByHboneNo(hboneNo)).thenThrow(new RuntimeException());
//        when(cmConferenceMapper.selectValidConferenceByCourseId(courseId)).thenReturn(CONFERENCE_ID);
        mockGenConferenceId(CONFERENCE_ID);
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
        when(cmConferenceLogMapper.insert(any())).thenReturn(1);

        mockdealOrderConference(CONFERENCE_ID,CUST_NO);
        // Act
        OrderConferenceResponse response = orderService.orderConference(request);

        // Assert
        verify(lockService).getLock(uniqKey, getExpireSecond());
        verify(lockService).releaseLock(uniqKey);
        verify(conscustMapper).selectConscustNoByHboneNo(hboneNo);
        //校验 cmConferenceLogMapper.insert  调用 0次
        verify(cmConferenceLogMapper, times(0)).insert(any(CmConferenceLog.class));
        verifyNoMoreInteractions(conscustMapper, cmConferenceLogMapper);

        assertEquals(CrmNtConstant.SYSTEM_ERROR, response.getReturnCode());
        assertEquals("调用会议预约接口失败", response.getDescription());
    }

    private int getExpireSecond() {
        return 30;
    }
}
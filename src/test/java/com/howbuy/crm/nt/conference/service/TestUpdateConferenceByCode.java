package com.howbuy.crm.nt.conference.service;

import com.howbuy.crm.base.model.BaseConstantEnum;
import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.domain.UpdateConferenceCustVo;
import com.howbuy.crm.nt.conference.dto.CmConferenceCustDto;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import com.howbuy.crm.nt.conference.request.UpdateNtCmConferenceCustInfoByCodeRequest;
import com.howbuy.crm.nt.conference.response.UpdateNtCmConferenceCustByCodeResponse;
import lombok.SneakyThrows;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * 单元测试：新增扫码签到客户信息
 * <AUTHOR>
 * @date 2022/6/16 14:45
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestUpdateConferenceByCode extends PowerMockTestCase {

	@InjectMocks
    private UpdateNtCmConferenceCustInfoServiceImpl serviceMock;

    @Mock
    private CmConferenceConscustMapper cmConferenceConscustMapper;
  
	@Mock
    private CmConferenceCustInfoBuss cmConferenceCustInfoBussMock;
	
	private static UpdateNtCmConferenceCustInfoByCodeRequest req;
    private static UpdateNtCmConferenceCustByCodeResponse res;
    
    @BeforeClass
	public static  void init() {
    	req = new UpdateNtCmConferenceCustInfoByCodeRequest();
    	req.setConferenceid("111");
    	req.setAppointmentscode("222");
    	req.setActualnub(333);
    	res = new UpdateNtCmConferenceCustByCodeResponse();

	}
    
    /**
     * 1.同一会议下预约码重复
     */
   @Test
   @SneakyThrows
    public void testRepeatAppointmentscode() {
	   final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);
	   //mock参会记录：2条
	   mockCmConferenceConscustList(spy,2);

	   ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	   res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	   //验证查询参会记录1次
       Mockito.verify(cmConferenceConscustMapper, new Times(1)).getConferenceCustDtoList(stringCaptor.capture());
       
       Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.FAIL.getCode());
       Assert.assertTrue(res.getDescription().equals("操作失败:同一会议下预约码重复，签到失败！"));
    }
   
   
   /**
    * mock 参会记录
    * @param spy 测试目标类
    * @param count
    */
   @SneakyThrows
   private void mockCmConferenceConscustList(UpdateNtCmConferenceCustInfoServiceImpl spy,int count) {
	   List<CmConferenceCustDto> list = new ArrayList<CmConferenceCustDto>(8);
	   if( count > 0){
		   for( int i = 0; i < count ; i++){
               CmConferenceCustDto cmConferenceConscust = new CmConferenceCustDto();
			   list.add(cmConferenceConscust);
		   } 
	   }
	   
       MemberModifier.field(UpdateNtCmConferenceCustInfoServiceImpl.class, "cmConferenceCustInfoBuss").set(spy, cmConferenceCustInfoBussMock);

       PowerMockito.when(cmConferenceConscustMapper.getConferenceCustDtoList(Mockito.any()))
               .thenReturn(list);
   }
   
   
   /**
    * 2.预约码不存在
    */
  @Test
  @SneakyThrows
   public void testNotExistAppointmentscode() {
	   final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);
	   //mock参会记录：0条
	   mockCmConferenceConscustList(spy,0);

	   ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	   res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	   //验证查询参会记录1次
      Mockito.verify(cmConferenceConscustMapper, new Times(1)).getConferenceCustDtoList(stringCaptor.capture());
      
      Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.DATA_NOT_FUND.getCode());
      Assert.assertTrue(res.getDescription().equals("未查到数据:此预约码不存在，签到失败！"));
   }
   
  
  /**
   * 3.更新客户参会签到条数大于0
   */
  @Test
  @SneakyThrows
  public void testUpCountMoreZero() {
	 final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);
	 //mock参会记录：1条
	 mockCmConferenceConscustList(spy,1);
	 
	 //mock更新客户参会签到：1条
	 mockUpdateCmConferenceCust(spy,1);

	 ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	 ArgumentCaptor<UpdateConferenceCustVo> cmConferenceConscustCaptor = ArgumentCaptor.forClass(UpdateConferenceCustVo.class);
	 res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	 //验证查询参会记录1次
     Mockito.verify(cmConferenceConscustMapper, new Times(1)).getConferenceCustDtoList(stringCaptor.capture());
     //验证更新客户参会签到1次
     Mockito.verify(cmConferenceCustInfoBussMock, new Times(1)).updateByVo(cmConferenceConscustCaptor.capture());
     
     Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.SUCCESS.getCode());
  }
   
  
  /**
   * mock 更新客户参会签到
   * @param spy 测试目标类
   * @param count
   */
  @SneakyThrows
  private void mockUpdateCmConferenceCust(UpdateNtCmConferenceCustInfoServiceImpl spy,Integer count) {
      MemberModifier.field(UpdateNtCmConferenceCustInfoServiceImpl.class, "cmConferenceCustInfoBuss").set(spy, cmConferenceCustInfoBussMock);

      PowerMockito.when(cmConferenceCustInfoBussMock.updateByVo(Mockito.any()))
              .thenReturn(count);
  }
  
  
  /**
   * 4.更新客户参会签到条数等于0
   */
  @Test
  @SneakyThrows
  public void testUpCountEqualZero() {
	 final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);
	 //mock参会记录：1条
	 mockCmConferenceConscustList(spy,1);
	 
	 //mock更新客户参会签到：0条
	 mockUpdateCmConferenceCust(spy,0);

	 ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	 ArgumentCaptor<UpdateConferenceCustVo> cmConferenceConscustCaptor = ArgumentCaptor.forClass(UpdateConferenceCustVo.class);
	 res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	 //验证查询参会记录1次
      Mockito.verify(cmConferenceConscustMapper, new Times(1)).getConferenceCustDtoList(stringCaptor.capture());
     //验证更新客户参会签到1次
     Mockito.verify(cmConferenceCustInfoBussMock, new Times(1)).updateByVo(cmConferenceConscustCaptor.capture());
     
     Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.DATA_NOT_FUND.getCode());
     Assert.assertTrue(res.getDescription().equals("未查到数据:客户未报名，签到失败！"));
  }
   
  /**
   * 5.更新客户参会签到条数为空
   */
  @Test
  @SneakyThrows
  public void testUpCountNull() {
	 final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);
	 //mock参会记录：1条
	 mockCmConferenceConscustList(spy,1);
	 
	 //mock更新客户参会签到：null  注意： 只有异常 这里才会返回null
      MemberModifier.field(UpdateNtCmConferenceCustInfoServiceImpl.class, "cmConferenceCustInfoBuss").set(spy, cmConferenceCustInfoBussMock);

      PowerMockito.when(cmConferenceCustInfoBussMock.updateByVo(Mockito.any()))
              .thenThrow(new RuntimeException("修改异常！"));
//	 mockUpdateCmConferenceCust(spy,null);

	 ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	 ArgumentCaptor<UpdateConferenceCustVo> cmConferenceConscustCaptor = ArgumentCaptor.forClass(UpdateConferenceCustVo.class);
	 res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	 //验证查询参会记录1次
      Mockito.verify(cmConferenceConscustMapper, new Times(1)).getConferenceCustDtoList(stringCaptor.capture());
     //验证更新客户参会签到1次
     Mockito.verify(cmConferenceCustInfoBussMock, new Times(1)).updateByVo(cmConferenceConscustCaptor.capture());
     
     Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.DEAL_FAIL.getCode());
     Assert.assertTrue(res.getDescription().equals("业务处理失败:修改异常！"));
  }
  
  
  /**
   * 6.验证参数不通过
   */
  @Test
  @SneakyThrows
  public void testValidateFai() {
	 final UpdateNtCmConferenceCustInfoServiceImpl spy = PowerMockito.spy(serviceMock);

	 ArgumentCaptor<CmConferenceCustVo> stringCaptor = ArgumentCaptor.forClass(CmConferenceCustVo.class);
	 ArgumentCaptor<UpdateConferenceCustVo> cmConferenceConscustCaptor = ArgumentCaptor.forClass(UpdateConferenceCustVo.class);
	 req.setActualnub(null);
	 res = (UpdateNtCmConferenceCustByCodeResponse)ReflectionUtils.invokeMethod(MemberModifier.methods(UpdateNtCmConferenceCustInfoServiceImpl.class, "updateConferenceByCode")[0], spy,req);
	 //验证查询参会记录0次
      Mockito.verify(cmConferenceConscustMapper, new Times(0)).getConferenceCustDtoList(stringCaptor.capture());
     //验证更新客户参会签到0次
     Mockito.verify(cmConferenceCustInfoBussMock, new Times(0)).updateByVo(cmConferenceConscustCaptor.capture());
     
     Assert.assertEquals(res.getReturnCode(), BaseConstantEnum.PARAM_ERROR.getCode());
  }
}
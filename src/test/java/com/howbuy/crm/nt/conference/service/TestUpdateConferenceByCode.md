## 测试的类
> com.howbuy.crm.nt.conference.service.UpdateNtCmConferenceCustInfoServiceImpl
## 测试的方法
> updateConferenceByCode(UpdateNtCmConferenceCustInfoByCodeRequest request)  客户参会签到

## 分支伪代码
```java
if (验证参数通过) {
       通过同一个参会id和参会预约码查询参会记录
   if(同一会议下预约码重复){
        return 同一会议下预约码重复，签到失败！
   }else if(查询不到会议记录){
        retrun 此预约码不存在，签到失败！
   }else{
                 更新客户参会签到
       if(更新条数不为空){
          if(更新条数>0){
             return 更新成功
          }else if(更新条数 == 0){
             return  客户未报名，签到失败！
          }
       }else{
          return 修改异常！
       } 
   }
} 
```

## 测试案例
##### 1、testRepeatAppointmentscode：同一会议下预约码重复
##### 2、testNotExistAppointmentscode：预约码不存在
##### 3、testUpCountMoreZero：更新客户参会签到条数大于0
##### 4、testUpCountEqualZero：更新客户参会签到条数等于0
##### 5、testUpCountNull：更新客户参会签到条数为空
##### 6、testValidateFai：验证参数不通过
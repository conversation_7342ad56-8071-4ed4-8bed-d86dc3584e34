/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service.updatentcmconferencecustinfoserviceimpl;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceCustDto;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import com.howbuy.crm.nt.conference.request.UpdateNtCmConferenceCustInfoByCodeRequest;
import com.howbuy.crm.nt.conference.service.UpdateNtCmConferenceCustInfoServiceImpl;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/31 20:32
 * @since JDK 1.8
 */
@PrepareForTest({UpdateNtCmConferenceCustInfoServiceImpl.class})
@PowerMockIgnore("javax.management.*")
@Test
public class UpdateConferenceByCodeTest extends PowerMockTestCase {

    @InjectMocks
    private UpdateNtCmConferenceCustInfoServiceImpl serviceMock;
    @Mock
    private CmConferenceConscustMapper conferenceConscustMapper;
    @Mock
    private CmConferenceCustInfoBuss cmConferenceCustInfoBuss;

    private static final String CONFERENCE_ID = "123456789";
    private static final String APPOINTMENTS_CODE = "123456789";

    private static final  Integer ACTUAL_SIGNING_NUM = 1;


    private UpdateNtCmConferenceCustInfoByCodeRequest getRequest(){
        UpdateNtCmConferenceCustInfoByCodeRequest request = new UpdateNtCmConferenceCustInfoByCodeRequest();
        request.setActualnub(ACTUAL_SIGNING_NUM);
        request.setConferenceid(CONFERENCE_ID);
        request.setAppointmentscode(APPOINTMENTS_CODE);
        return request;
    }


    private CmConferenceCustVo getSearchVo(String appointmentsCode,String conferenceId){
        CmConferenceCustVo queryVo=new CmConferenceCustVo();
        queryVo.setConferenceId(conferenceId);
        queryVo.setAppointmentsCode(appointmentsCode);
        return queryVo;
    }

    /**
     * 测试场景： 传入参数为空
     */
    @Test
    public void testValidateFalse() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();
        request.setAppointmentscode(null);
        BaseResponse response = serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.PARAM_ERROR.getCode());
        Assert.assertEquals(response.getDescription(),"参数错误:传入参数（预约码）为空");

    }

    @Test
    public void testExistsDuplicateList() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();

        //返回多条 size>1
        PowerMockito.when(conferenceConscustMapper.getConferenceCustDtoList(getSearchVo(APPOINTMENTS_CODE,CONFERENCE_ID)))
                .thenReturn(Lists.newArrayList(new CmConferenceCustDto(),new CmConferenceCustDto()));
        BaseResponse response = serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.FAIL.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.FAIL.getDescription()+":同一会议下预约码重复，签到失败！");
    }

    @Test
    public void testExistsNoneList() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();

        //返回多条 size>1
        PowerMockito.when(conferenceConscustMapper.getConferenceCustDtoList(getSearchVo(APPOINTMENTS_CODE,CONFERENCE_ID)))
                .thenReturn(Lists.newArrayList());
        BaseResponse response = serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.DATA_NOT_FUND.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.DATA_NOT_FUND.getDescription()+":此预约码不存在，签到失败！");
    }

    /**
     * 测试场景： 更新客户 返回null
     */
    @Test
    public void testUpdateCustNull() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();
        BaseResponse response = new BaseResponse();
        //返回多条 size=1
        PowerMockito.when(conferenceConscustMapper.getConferenceCustDtoList(getSearchVo(APPOINTMENTS_CODE,CONFERENCE_ID)))
                .thenReturn(Lists.newArrayList(new CmConferenceCustDto()));
        //更新操作 返回 null
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenThrow(new RuntimeException());

        response=serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.DEAL_FAIL.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.DEAL_FAIL.getDescription()+":修改异常！");

    }

    /**
     * 测试场景： 更新客户 返回0
     */
    @Test
    public void testUpdateCustZero() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();
        BaseResponse response = new BaseResponse();
        //返回多条 size=1
        PowerMockito.when(conferenceConscustMapper.getConferenceCustDtoList(getSearchVo(APPOINTMENTS_CODE,CONFERENCE_ID)))
                .thenReturn(Lists.newArrayList(new CmConferenceCustDto()));
        //更新操作 返回 null
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenReturn(0);

        response=serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.DATA_NOT_FUND.getCode());
        Assert.assertEquals(response.getDescription(),BaseConstantEnum.DATA_NOT_FUND.getDescription()+":客户未报名，签到失败！");

    }

    /**
     * 测试场景： 更新客户 返回1
     */
    @Test
    public void testUpdateCustNormal() {
        UpdateNtCmConferenceCustInfoByCodeRequest request = getRequest();
        BaseResponse response = new BaseResponse();
        //返回多条 size=1
        PowerMockito.when(conferenceConscustMapper.getConferenceCustDtoList(getSearchVo(APPOINTMENTS_CODE,CONFERENCE_ID)))
                .thenReturn(Lists.newArrayList(new CmConferenceCustDto()));
        //更新操作 返回 null
        PowerMockito.when(cmConferenceCustInfoBuss.updateByVo(Mockito.any())).thenReturn(1);

        response=serviceMock.updateConferenceByCode(request);

        Assert.assertEquals(response.getReturnCode(), BaseConstantEnum.SUCCESS.getCode());
        Assert.assertEquals(response.getDescription(),"操作成功");

    }

}
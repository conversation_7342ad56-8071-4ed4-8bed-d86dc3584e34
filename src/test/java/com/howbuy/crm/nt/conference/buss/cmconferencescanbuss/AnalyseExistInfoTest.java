/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.buss.cmconferencescanbuss;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.base.enums.ConferCustStateEnum;
import com.howbuy.crm.nt.conference.buss.CmConferenceScanBuss;
import com.howbuy.crm.nt.conference.domain.MobileExistCustInfo;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/10 15:14
 * @since JDK 1.8
 */
@PrepareForTest({CmConferenceScanBuss.class})
@PowerMockIgnore("javax.management.*")
@Test
public class AnalyseExistInfoTest extends PowerMockTestCase {

    @InjectMocks
    private CmConferenceScanBuss serviceMock;
    @Mock
    private ConscustMapper conscustMapper;


    public static final String TEST_CONSCODE = "TEST_CONSCODE";
    public static final String TEST_CONSCUSTNO = "TEST_CONSCUSTNO";
    public static final String TEST_MOBILE_DIGEST = "TEST_MOBILE_DIGEST";


    /**
     * 该手机号不存在
     */
    @Test
    public void testNotExistMobile() {

        PowerMockito.when(conscustMapper.listConsCustInfoByMobile(TEST_MOBILE_DIGEST)).thenReturn(Lists.newArrayList());

        MobileExistCustInfo custInfo=serviceMock.analyseExistInfo(TEST_MOBILE_DIGEST);
        Assert.assertEquals(custInfo.getExistCustList().size(),0);
        Assert.assertNull(custInfo.getUsedCustInfo());
        Assert.assertEquals(custInfo.getCustStateEnum(), ConferCustStateEnum.NOT_EXIST);
    }

    public void testExistOneMobile() {

        ConscustInfo existCustInfo = new ConscustInfo();
        existCustInfo.setConscode(TEST_CONSCODE);
        existCustInfo.setConscustno(TEST_CONSCUSTNO);
        List<ConscustInfo> existList = new ArrayList<>();
        existList.add(existCustInfo);
        //存在一个客户信息
        PowerMockito.when(conscustMapper.listConsCustInfoByMobile(TEST_MOBILE_DIGEST)).thenReturn(existList);

        MobileExistCustInfo custInfo=serviceMock.analyseExistInfo(TEST_MOBILE_DIGEST);

        Assert.assertEquals(custInfo.getExistCustList().size(),existList.size());
        Assert.assertEquals(existCustInfo.getConscustno(),custInfo.getUsedCustInfo().getCustNo());
        Assert.assertEquals(custInfo.getCustStateEnum(), ConferCustStateEnum.EXIST_ONLY);
    }

    public void testExistMoreMobile() {

        ConscustInfo existCustInfo = new ConscustInfo();
        existCustInfo.setConscode(TEST_CONSCODE);
        existCustInfo.setConscustno(TEST_CONSCUSTNO);
        List<ConscustInfo> existList = new ArrayList<>();
        existList.add(existCustInfo);
        existList.add(existCustInfo);
        //存在一个客户信息
        PowerMockito.when(conscustMapper.listConsCustInfoByMobile(TEST_MOBILE_DIGEST)).thenReturn(existList);

        MobileExistCustInfo custInfo=serviceMock.analyseExistInfo(TEST_MOBILE_DIGEST);

        Assert.assertEquals(custInfo.getExistCustList().size(),existList.size());
        Assert.assertNull(custInfo.getUsedCustInfo());
        Assert.assertEquals(custInfo.getCustStateEnum(), ConferCustStateEnum.EXIST_MANY);
    }


}
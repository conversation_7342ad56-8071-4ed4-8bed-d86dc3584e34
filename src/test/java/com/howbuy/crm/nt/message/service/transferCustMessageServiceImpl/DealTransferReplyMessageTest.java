package com.howbuy.crm.nt.message.service.transferCustMessageServiceImpl;

import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustDO;
import com.howbuy.crm.nt.message.service.TransferCustMessageServiceImpl;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.testng.PowerMockTestCase;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 单元测试-待划转客户消息推送
 * @Date 2024/4/11 10:35
 */
@PowerMockIgnore("javax.management.*")
@RunWith(PowerMockRunner.class)
@PrepareForTest(TransferCustMessageServiceImpl.class)
public class DealTransferReplyMessageTest extends PowerMockTestCase {

    @InjectMocks
    private TransferCustMessageServiceImpl transferCustMessageService;
    @Mock
    private CmPushMsgService cmPushMsgService;

    // 待审核 带划转客户列表mock
    private static final List<CmWaitTransferCustDO> WAITTRANSFERCUSTLIST = new ArrayList<>();
    // 销售总部-系统运营 角色下的投顾信息mock
    private static final Map<String, ConsultantSimpleInfoDto> CONSULTANTMAP = new HashMap<>();
    // 高端运营中心下mock
    private static final Map<String, ConsultantSimpleInfoDto> GDCONSULTANTMAP = new HashMap<>();
    // 高端运营中心下mock
    private static final BaseResponse BASERESPONSE  = new BaseResponse();

    static {
        CmWaitTransferCustDO custMsgDTO = new CmWaitTransferCustDO();
        custMsgDTO.setConsCustNo("111111111");
        WAITTRANSFERCUSTLIST.add(custMsgDTO);

        CONSULTANTMAP.put("111111111", new ConsultantSimpleInfoDto());
        GDCONSULTANTMAP.put("111111111", new ConsultantSimpleInfoDto());

        BASERESPONSE.setReturnCode("0000000");
    }

    /**
     * case1:HBC中心下待审核数据为空时，预期返回null
     */
    @Test
    public void test1() {
        // mock 私有方法 getCmWaitTransferCustListAll
        mockGetCmWaitTransferCustListAll(null);
        String result = transferCustMessageService.dealTransferReplyMessage("1");
        // 预期调用次数0次
        Mockito.verify(cmPushMsgService, Mockito.times(0)).pushMsgByConsCodeList(Mockito.any(), Mockito.any(), Mockito.any());
        // 预期结果null
        Assert.assertNull(result);
    }

    /**
     * case2:HBC中心下待审核数据不为空并且销售总部-系统运营 角色下的投顾信息Map为空时，预期返回null
     */
    @Test
    public void test2() {
        // mock 私有方法 getCmWaitTransferCustListAll
        mockGetCmWaitTransferCustListAll(WAITTRANSFERCUSTLIST);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetStringConsultantSimpleInfoDtoMap(null);

        String result = transferCustMessageService.dealTransferReplyMessage("1");
        // 预期调用次数0次
        Mockito.verify(cmPushMsgService, Mockito.times(0)).pushMsgByConsCodeList(Mockito.any(), Mockito.any(), Mockito.any());
        // 预期结果null
        Assert.assertNull(result);
    }

    /**
     * case3:HBC中心下待审核数据不为空并且销售总部-系统运营 角色下的投顾信息Map不为空但高端运营中心下无数据时，预期返回null
     */
    @Test
    public void test3() {
        // mock 私有方法 getCmWaitTransferCustListAll
        mockGetCmWaitTransferCustListAll(WAITTRANSFERCUSTLIST);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetStringConsultantSimpleInfoDtoMap(CONSULTANTMAP);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetCenterConsultantMap(null);

        String result = transferCustMessageService.dealTransferReplyMessage("1");
        // 预期调用次数0次
        Mockito.verify(cmPushMsgService, Mockito.times(0)).pushMsgByConsCodeList(Mockito.any(), Mockito.any(), Mockito.any());
        // 预期结果null
        Assert.assertNull(result);
    }

    /**
     * case4:满足高端运营中心下有投顾满足时，预期返回null并推送mq消息
     */
    @Test
    public void test4() throws Exception {
        // mock 私有方法 getCmWaitTransferCustListAll
        mockGetCmWaitTransferCustListAll(WAITTRANSFERCUSTLIST);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetStringConsultantSimpleInfoDtoMap(CONSULTANTMAP);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetCenterConsultantMap(GDCONSULTANTMAP);
        // mock 调用消息推送方法
        PowerMockito.when(cmPushMsgService, "pushMsgByConsCodeList", Mockito.any(), Mockito.any(), Mockito.any()).thenReturn(BASERESPONSE);
        String result = transferCustMessageService.dealTransferReplyMessage("1");
        // 预期调用次数1次
        Mockito.verify(cmPushMsgService, Mockito.times(1)).pushMsgByConsCodeList(Mockito.any(), Mockito.any(), Mockito.any());
        // 预期结果null
        Assert.assertNull(result);
    }

    /**
     * case4:调用消息推送接口报错，预期throw Exception
     */
    @Test(expected = Exception.class)
    public void test5() throws Exception {
        // mock 私有方法 getCmWaitTransferCustListAll
        mockGetCmWaitTransferCustListAll(WAITTRANSFERCUSTLIST);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetStringConsultantSimpleInfoDtoMap(CONSULTANTMAP);
        // mock 私有方法 getStringConsultantSimpleInfoDtoMap
        mockGetCenterConsultantMap(GDCONSULTANTMAP);
        // mock 调用消息推送方法抛出异常
        PowerMockito.doThrow(new Exception()).when(cmPushMsgService, "pushMsgByConsCodeList", Mockito.any(), Mockito.any(), Mockito.any());
        String result = transferCustMessageService.dealTransferReplyMessage("1");
        System.out.println(result);
    }

    // mock
    private void mockGetCmWaitTransferCustListAll(List<CmWaitTransferCustDO> list) {
        Method method = PowerMockito.method(TransferCustMessageServiceImpl.class, "getCmWaitTransferCustListAll", Map.class);
        PowerMockito.stub(method).toReturn(list);
    }

    // mock
    private void mockGetStringConsultantSimpleInfoDtoMap(Map<String, ConsultantSimpleInfoDto> consultantMap) {
        Method method = PowerMockito.method(TransferCustMessageServiceImpl.class, "getStringConsultantSimpleInfoDtoMap");
        PowerMockito.stub(method).toReturn(consultantMap);
    }

    // mock
    private void mockGetCenterConsultantMap(Map<String, ConsultantSimpleInfoDto> consultantMap) {
        Method method = PowerMockito.method(TransferCustMessageServiceImpl.class, "getCenterConsultantMap", CenterOrgEnum.class, Map.class);
        PowerMockito.stub(method).toReturn(consultantMap);
    }



}

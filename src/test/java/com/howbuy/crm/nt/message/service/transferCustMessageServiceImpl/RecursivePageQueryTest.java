package com.howbuy.crm.nt.message.service.transferCustMessageServiceImpl;

import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustDO;
import com.howbuy.crm.nt.message.service.TransferCustMessageServiceImpl;
import crm.howbuy.base.db.CommPageBean;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * @Description 单元测试-递归查询入参条件数据
 * @Date 2024/4/3 09:08
 */
@RunWith(MockitoJUnitRunner.class)
@PrepareForTest({TransferCustMessageServiceImpl.class})
public class RecursivePageQueryTest extends PowerMockTestCase {

    @InjectMocks
    private TransferCustMessageServiceImpl transferCustMessageService;
    @Mock
    private ConscustMapper conscustMapper;

    private static final List<CmWaitTransferCustDO> bifunctionList = new ArrayList<>();
    private static final Map<String, Object> param = new HashMap<>();
    private final BiFunction<CommPageBean, Map<String, Object>, List<CmWaitTransferCustDO>> function = (p, map) -> conscustMapper.listCmWaitTransferCustListByPage(map, p);

    static {
        param.put("state", "1");

        CmWaitTransferCustDO transferCustDO = new CmWaitTransferCustDO();
        transferCustDO.setCustName("测试客户名称");
        bifunctionList.add(transferCustDO);
    }

    /**
     * case1:入参param为null时，预期结果null
     */
    @Test
    public void test1() throws Exception {
        CommPageBean commPageBean = new CommPageBean();
        Map param = null;
        Method recursivePageQuery = PowerMockito.method(TransferCustMessageServiceImpl.class, "recursivePageQuery", CommPageBean.class, Map.class, BiFunction.class);
        Object invoke = recursivePageQuery.invoke(transferCustMessageService, commPageBean, param, function);
        Assert.assertNull(invoke);
    }

    /**
     * case2:入参biFunction查询结果为为空时，预期结果null
     */
    @Test
    public void test2() throws Exception {
        CommPageBean commPageBean = new CommPageBean();
        Method recursivePageQuery = PowerMockito.method(TransferCustMessageServiceImpl.class, "recursivePageQuery", CommPageBean.class, Map.class, BiFunction.class);
        List<CmWaitTransferCustDO> list = new ArrayList<>();
        PowerMockito.when(conscustMapper, "listCmWaitTransferCustListByPage", Mockito.any(), Mockito.any()).thenReturn(list);
        Object invoke = recursivePageQuery.invoke(transferCustMessageService, commPageBean, param, function);
        Assert.assertNull(invoke);
    }


    /**
     * case3:入参biFunction查询结果为null时，预期结果null
     */
    @Test
    public void test3() throws Exception {
        CommPageBean commPageBean = new CommPageBean();
        Method recursivePageQuery = PowerMockito.method(TransferCustMessageServiceImpl.class, "recursivePageQuery", CommPageBean.class, Map.class, BiFunction.class);
        List<CmWaitTransferCustDO> list = null;
        PowerMockito.when(conscustMapper, "listCmWaitTransferCustListByPage", Mockito.any(), Mockito.any()).thenReturn(list);
        Object invoke = recursivePageQuery.invoke(transferCustMessageService, commPageBean, param, function);
        Assert.assertNull(invoke);
    }

}

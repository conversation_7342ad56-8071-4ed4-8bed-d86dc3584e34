## 测试的类
> com.howbuy.crm.nt.message.service.transferCustMessageServiceImpl
## 测试的方法
> ```
> recursivePageQuery(CommPageBean pageBean, Map<String, Object> param, BiFunction<CommPageBean, Map<String, Object>, List<T>> biFnc)
> ```
>
> ```
> 递归分页查询所有符合条件数据集
> ```

## 分支伪代码
```java
if (参数为空) {
  return null
}
调用DB请求数据结果
if(校验结果是否为空){
  return null
}
构建下次查询请求体
递归调用接口获取数据
if (递归结果不为空) {
   添加递归查询结果
} 
return 返回结果集
```

## 测试案例
##### 1、入参param为null时，预期结果null
##### 2、入参biFunction查询结果为为空时，预期结果null
##### 3、入参biFunction查询结果为null时，预期结果null
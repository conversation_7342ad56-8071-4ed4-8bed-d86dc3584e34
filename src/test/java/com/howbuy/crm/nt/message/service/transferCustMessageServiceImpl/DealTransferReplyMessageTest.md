## 测试的类
> com.howbuy.crm.nt.message.service.TransferCustMessageServiceImpl
## 测试的方法
> ```
> dealTransferReplyMessage(String arg)
> ```
>
> ```
> 处理划转批复消息提醒
> ```

## 分支伪代码
```java
查询DB获取待划转客户信息
if (参数为空) {
  return null
}
查询接口获取销售总部-系统运营角色的投顾信息
if(结果为空){
  return null
}
给投顾推送mq消息
return null
```

## 测试案例
##### 1、HBC中心下待审核数据为空时，预期返回null
##### 2、HBC中心下待审核数据不为空并且销售总部-系统运营 角色下的投顾信息Map为空时，预期返回null
##### 3、HBC中心下待审核数据不为空并且销售总部-系统运营 角色下的投顾信息Map不为空但高端运营中心下无数据时，预期返回null
##### 4、满足高端运营中心下有投顾满足时，预期返回null并推送mq消息
##### 5、调用消息推送接口报错，预期throw Exception
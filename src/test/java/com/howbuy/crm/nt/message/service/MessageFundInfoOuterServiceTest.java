/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.message.service;

import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.message.bizservice.MessageFundInfoOuterService;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.YesOrNoEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/6/5 16:38
 * @since JDK 1.8
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*","javax.script.*"})
public class MessageFundInfoOuterServiceTest {

    @InjectMocks
    private MessageFundInfoOuterService messageFundInfoOuterService = new MessageFundInfoOuterService();

    @Mock
    private JjxxInfoService jjxxInfoService;



    /**
     * @description: 基金Code是空
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/6/5 17:29
     * @since JDK 1.8
     */
    @Test
    public void getFundMessageIdByFundCodeParamIsEmpty() {
        String hbMsgId = messageFundInfoOuterService.getFundMessageIdByFundCode(null,null,null,null);
        Assert.assertNull(hbMsgId);
    }

    /**
     * @description: 获取好买渠道msgId
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/6/5 17:34
     * @since JDK 1.8
     */
    @Test
    public void getFundMessageIdByFundCode() {
        PowerMockito.when(jjxxInfoService.getDisCodeEnumByJjdm(Mockito.anyString())).thenReturn(DisChannelCodeEnum.HOWBUY);
        String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_ZXDQ, CrmNtConstant.MSG_BID_ZXDQ_HZ,
                CrmNtConstant.MSG_BID_ZXDQ_HW, "111111");
        Assert.assertNotNull(msgId);
        Assert.assertEquals(CrmNtConstant.MSG_BID_ZXDQ, msgId);
    }

    /**
     * @description: 获取好甄渠道msgId
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/6/5 17:35
     * @since JDK 1.8
     */
    @Test
    public void getFundHzMessageIdByFundCode() {
        PowerMockito.when(jjxxInfoService.getDisCodeEnumByJjdm(Mockito.anyString())).thenReturn(DisChannelCodeEnum.HZ);
        String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_ZXDQ, CrmNtConstant.MSG_BID_ZXDQ_HZ,
                CrmNtConstant.MSG_BID_ZXDQ_HW, "111111");
        Assert.assertNotNull(msgId);
        Assert.assertEquals(CrmNtConstant.MSG_BID_ZXDQ_HZ, msgId);
    }

    /**
     * @description: 通过基金Code获取香港海外渠道msgId
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/6/5 17:38
     * @since JDK 1.8
     */
    @Test
    public void getFundHwMessageIdByFundCode() {
        PowerMockito.when(jjxxInfoService.getDisCodeEnumByJjdm(Mockito.anyString())).thenReturn(null);
        JjxxInfo jjxxInfo = new JjxxInfo();
        jjxxInfo.setSfxg(YesOrNoEnum.YES.getCode());
        PowerMockito.when(jjxxInfoService.getJjxxByJjdm(Mockito.anyString())).thenReturn(jjxxInfo);
        String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_ZXDQ, CrmNtConstant.MSG_BID_ZXDQ_HZ,
                CrmNtConstant.MSG_BID_ZXDQ_HW, "111111");
        Assert.assertNotNull(msgId);
        Assert.assertEquals(CrmNtConstant.MSG_BID_ZXDQ_HW, msgId);
    }

    /**
     * @description: 通过基金Code获取渠道msgId不存在
     * @param
     * @return void
     * @author: jinqing.rao
     * @date: 2024/6/5 17:38
     * @since JDK 1.8
     */
    @Test
    public void getFundMessageIdNotExitByFundCode() {
        PowerMockito.when(jjxxInfoService.getDisCodeEnumByJjdm(Mockito.anyString())).thenReturn(null);
        JjxxInfo jjxxInfo = new JjxxInfo();
        PowerMockito.when(jjxxInfoService.getJjxxByJjdm(Mockito.anyString())).thenReturn(jjxxInfo);
        String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_ZXDQ, CrmNtConstant.MSG_BID_ZXDQ_HZ,
                CrmNtConstant.MSG_BID_ZXDQ_HW, "111111");
        Assert.assertNull(msgId);
    }
    
    

}

## 测试的类
> com.howbuy.crm.nt.consultant.buss.QueryConsultantExpBuss
## 测试的方法
> queryConsultantExpLead(String conscode)  根据投顾号获取  分总   区域总   区域副总

## 分支伪代码
``` java
根据投顾号查询投顾所属部门
缓冲中获取部门和所属区域的对应map
根据对应map获取投顾所属部门的区域
if(所属区域为0howbuy){
    所属区域为部门
}else{
    所属区域为对应map取到的区域
}
if(部门不为空){
    查询分总
    if(查询到的分总不为空){
        返回的domain放入分总list
    }
    查询区域执行副总
    if(查询到的区域执行副总不为空){
        返回的domain放入区域执行副总list
    }
}
if(区域不为空){
    查询区域总
    if(查询到的区域总不为空){
        返回的domain放入区域总list
    }
}
返回domain

## 测试案例
所属区域为0howbuy
### 1、testAreaZero
所属区域不为0howbuy
### 2、testAreaNotZero
部门为空
### 3、testEmptyOutlet
部门不为空 查询到的分总不为空
### 4、testNotEmptyOutletAndNotEmptyFz
部门不为空 查询到的区域执行副总不为空
### 5、testNotEmptyOutletAndNotEmptyAreaFz
区域为空
### 6、testEmptyArea
区域不为空 查询到的区域总不为空
### 7、testNotEmptyAreaAndNotEmptyAreaZ


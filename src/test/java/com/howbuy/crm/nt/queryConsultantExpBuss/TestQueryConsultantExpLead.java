package com.howbuy.crm.nt.queryConsultantExpBuss;

import com.howbuy.crm.nt.consultant.buss.QueryConsultantExpBuss;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.dto.CmConsultantExpDomain;
import com.howbuy.crm.nt.remind.service.RemindMsgCollectServiceImpl;
import com.howbuy.crm.organization.dto.HbUpLevelOrgCodeDTO;
import com.howbuy.crm.organization.response.OrganizationCatchResponse;
import com.howbuy.crm.organization.service.OrganizationCatchService;
import lombok.SneakyThrows;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单元测试：根据投顾号获取  分总   区域总   区域副总
 * <AUTHOR>
 * @date 2022-11-22 16:34:37
 */
@PrepareForTest({QueryConsultantExpBuss.class})
@PowerMockIgnore("javax.management.*")
@Test
public class TestQueryConsultantExpLead extends PowerMockTestCase {

	@InjectMocks
    private QueryConsultantExpBuss serviceMock;
    
	@Mock
	private CmConsultantMapper cmConsultantInfoDaoMock;

	@Mock
	private OrganizationCatchService organizationCatchServiceMock;

    /**
     * 1.所属区域为0howbuy
     */
   @Test
   @SneakyThrows
   public void testAreaZero() {
	   QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
	   //mock根据投顾号查询部门
	   mockGetOutletCodeByConsCode(spy, "1");
	   //mock缓冲中获取部门和所属区域的对应map
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("1","0");
	   mockCacheGetBean(spy,map);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
			   spy,"");
	   ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   //断言方法执行次数
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(2)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
	   List<Map> allValues = mapCaptor.getAllValues();
	   //断言参数
	   Assert.assertEquals(allValues.get(1).get("orgcode"), "1");
   }
    /**
     * 2.所属区域不为0howbuy
     */
   @Test
   @SneakyThrows
   public void testAreaNotZero() {
	   QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
	   //mock根据投顾号查询部门
	   mockGetOutletCodeByConsCode(spy, "1");
	   //mock缓冲中获取部门和所属区域的对应map
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("1","2");
	   mockCacheGetBean(spy,map);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
			   spy,"");
	   ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   //断言方法执行次数
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(2)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
	   List<Map> allValues = mapCaptor.getAllValues();
	   //断言参数
	   Assert.assertEquals(allValues.get(1).get("orgcode"), "2");
   }
    /**
     * 3.部门为空
     */
   @Test
   @SneakyThrows
   public void testEmptyOutlet() {
	   QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
	   //mock根据投顾号查询部门
	   mockGetOutletCodeByConsCode(spy, "");
	   //mock缓冲中获取部门和所属区域的对应map
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("1","2");
	   mockCacheGetBean(spy,map);
	   ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
			   spy,"");
	   ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   //断言方法执行次数
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(0)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(0)).listFLeadersByOrgCode(mapCaptor.capture());
   }
    /**
     * 4.部门不为空 查询到的分总不为空
     */
   @Test
   @SneakyThrows
   public void testNotEmptyOutletAndNotEmptyFz() {
	   QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
	   //mock根据投顾号查询部门
	   mockGetOutletCodeByConsCode(spy, "1");
	   //mock缓冲中获取部门和所属区域的对应map
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("1","2");
	   mockCacheGetBean(spy,map);
	   //mock查询分总 区域总
	   mockListLeadersByOrgCodeAndLevel(spy,true,false);
	   CmConsultantExpDomain cmConsultantExpDomain = (CmConsultantExpDomain)ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
			   spy, "");
	   ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   //断言方法执行次数
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(2)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(1)).listFLeadersByOrgCode(mapCaptor.capture());
	   //断言方法返回值
	   Assert.assertEquals(cmConsultantExpDomain.getOutletleaderList().size(), 1);
   }
    /**
     * 5.部门不为空 查询到的区域执行副总不为空
     */
   @Test
   @SneakyThrows
   public void testNotEmptyOutletAndNotEmptyAreaFz() {
	   QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
	   //mock根据投顾号查询部门
	   mockGetOutletCodeByConsCode(spy, "1");
	   //mock缓冲中获取部门和所属区域的对应map
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("1","2");
	   mockCacheGetBean(spy,map);
	   //mock查询分总 区域总
	   mockListLeadersByOrgCodeAndLevel(spy,true,false);
	   //mock查询区域副总
	   mockListFLeadersByOrgCode(spy,true);
	   CmConsultantExpDomain cmConsultantExpDomain = (CmConsultantExpDomain)ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
			   spy, "");
	   ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
	   //断言方法执行次数
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(2)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
	   Mockito.verify(cmConsultantInfoDaoMock, new Times(1)).listFLeadersByOrgCode(mapCaptor.capture());
	   //断言方法返回值
	   Assert.assertEquals(cmConsultantExpDomain.getAreafleaderList().size(), 1);
   }
	/**
	 * 6.区域为空
	 */
	@Test
	@SneakyThrows
	public void testEmptyArea() {
		QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
		//mock根据投顾号查询部门
		mockGetOutletCodeByConsCode(spy, "1");
		//mock缓冲中获取部门和所属区域的对应map
		Map<String, String> map = new HashMap<String, String>();
		map.put("1","");
		mockCacheGetBean(spy,map);
		//mock查询分总 区域总
		mockListLeadersByOrgCodeAndLevel(spy,true,false);
		//mock查询区域副总
		mockListFLeadersByOrgCode(spy,true);
		CmConsultantExpDomain cmConsultantExpDomain = (CmConsultantExpDomain)ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
				spy, "");
		ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
		//断言方法执行次数
		Mockito.verify(cmConsultantInfoDaoMock, new Times(1)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
		Mockito.verify(cmConsultantInfoDaoMock, new Times(1)).listFLeadersByOrgCode(mapCaptor.capture());
		//断言方法返回值
		Assert.assertEquals(cmConsultantExpDomain.getArealeaderList(), null);
	}
	/**
	 * 7.区域不为空 查询到的区域总不为空
	 */
	@Test
	@SneakyThrows
	public void testNotEmptyAreaAndNotEmptyAreaZ() {
		QueryConsultantExpBuss spy = PowerMockito.spy(serviceMock);
		//mock根据投顾号查询部门
		mockGetOutletCodeByConsCode(spy, "1");
		//mock缓冲中获取部门和所属区域的对应map
		Map<String, String> map = new HashMap<String, String>();
		map.put("1","2");
		mockCacheGetBean(spy,map);
		//mock查询分总 区域总
		mockListLeadersByOrgCodeAndLevel(spy,true,true);
		//mock查询区域副总
		mockListFLeadersByOrgCode(spy,true);
		CmConsultantExpDomain cmConsultantExpDomain = (CmConsultantExpDomain)ReflectionUtils.invokeMethod(MemberModifier.methods(QueryConsultantExpBuss.class, "queryConsultantExpLead")[0],
				spy, "");
		ArgumentCaptor<Map> mapCaptor = ArgumentCaptor.forClass(Map.class);
		//断言方法执行次数
		Mockito.verify(cmConsultantInfoDaoMock, new Times(2)).listLeadersByOrgCodeAndLevel(mapCaptor.capture());
		Mockito.verify(cmConsultantInfoDaoMock, new Times(1)).listFLeadersByOrgCode(mapCaptor.capture());
		//断言方法返回值
		Assert.assertEquals(cmConsultantExpDomain.getArealeaderList().size(), 1);
	}

	/**
	 * mock查询区域副总
	 * @param spy
	 * @param a
	 */
	@SneakyThrows
	private void mockListFLeadersByOrgCode(QueryConsultantExpBuss spy, boolean a) {
		MemberModifier.field(QueryConsultantExpBuss.class, "cmConsultantInfoDao").set(spy, cmConsultantInfoDaoMock);
		ArrayList<String> strings = new ArrayList<>();
		strings.add("1");
		PowerMockito.when(cmConsultantInfoDaoMock.listFLeadersByOrgCode(Mockito.any())) .thenReturn(a?strings:null);
	}
	/**
	 * mock查询分总 区域总
	 * @param spy
	 * @param a
	 * @param b
	 */
	@SneakyThrows
	private void mockListLeadersByOrgCodeAndLevel(QueryConsultantExpBuss spy, boolean a, boolean b) {
		MemberModifier.field(QueryConsultantExpBuss.class, "cmConsultantInfoDao").set(spy, cmConsultantInfoDaoMock);
		ArrayList<String> strings = new ArrayList<>();
		strings.add("1");
		PowerMockito.when(cmConsultantInfoDaoMock.listLeadersByOrgCodeAndLevel(Mockito.any())) .thenReturn(a?strings:null)
				.thenReturn(b?strings:null);
	}

	/*
	 * mock根据投顾号查询部门
	 */
	@SneakyThrows
	private void mockCacheGetBean(QueryConsultantExpBuss spy, Map<String, String> map) {
//		PowerMockito.when(spy, "getUpOrgCode", Mockito.any()).thenReturn(map.get("1"));
		OrganizationCatchResponse<List<HbUpLevelOrgCodeDTO>> response = new OrganizationCatchResponse<>();
		List<HbUpLevelOrgCodeDTO> list = new ArrayList<>();
		HbUpLevelOrgCodeDTO dto = new HbUpLevelOrgCodeDTO();
		Map.Entry<String,String> entry = map.entrySet().iterator().next();
		dto.setOrgCode(entry.getKey());
		dto.setUpLevelOrgCode(entry.getValue());
		list.add(dto);
		response.setData(list);
		response.success();
		PowerMockito.when(organizationCatchServiceMock.getUpOrgMapCache()).thenReturn(response);
	}

	/*
   	* mock根据投顾号查询部门
    */
   @SneakyThrows
	private void mockGetOutletCodeByConsCode(QueryConsultantExpBuss spy, String s){
		MemberModifier.field(QueryConsultantExpBuss.class, "cmConsultantInfoDao").set(spy, cmConsultantInfoDaoMock);
		PowerMockito.when(cmConsultantInfoDaoMock.getOutletCodeByConsCode(Mockito.any())).thenReturn(s);
	}


}
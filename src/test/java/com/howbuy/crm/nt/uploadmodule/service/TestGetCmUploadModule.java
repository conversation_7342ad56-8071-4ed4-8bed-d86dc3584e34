package com.howbuy.crm.nt.uploadmodule.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.howbuy.crm.nt.uploadmodule.dao.CmUploadModuleMapper;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleType;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleTypeSuffix;

import crm.howbuy.base.enums.CrmUploadModuleEnum;
import crm.howbuy.base.enums.CrmUploadTypeEnum;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.Test;


/**
 * 单元测试：根据上传模块枚举获取上传模块的配置
 * <AUTHOR>
 * @date 2023/2/6 11:00
 */
@PowerMockIgnore("javax.management.*")
@Test
public class TestGetCmUploadModule extends PowerMockTestCase {

	@InjectMocks
    private UploadModuleServiceImpl serviceMock;
  
	@Mock
    private CmUploadModuleMapper cmUploadModuleMapperMock;
	
	private CrmUploadModuleEnum TESTNULL = null;
	
	/**
	 * 投顾客户-投顾客户
	 */
	private CrmUploadModuleEnum TESTCUST_CUST = CrmUploadModuleEnum.CUST_CUST;
    
    /**
     * 传参是null
     */
	@Test
	public void test1() {
		UploadModuleServiceImpl spy = PowerMockito.spy(serviceMock);
		//传参为null
		NtCmUploadModule mod = (NtCmUploadModule)ReflectionUtils.invokeMethod(MemberModifier.methods(UploadModuleServiceImpl.class, "getCmUploadModule")[0], spy, TESTNULL);
		Assert.assertEquals(mod.getId(), null);
	}
   
	/**
	 * 	没有查询到模块id
	 */
	@Test
	public void test2() {
		UploadModuleServiceImpl spy = PowerMockito.spy(serviceMock);
		//设置根据模块id没有查询到该模块
		PowerMockito.when(cmUploadModuleMapperMock.getCmUploadModule(Mockito.any())).thenReturn(null);
		//传参是投顾客户-投顾客户枚举
		NtCmUploadModule mod = (NtCmUploadModule)ReflectionUtils.invokeMethod(MemberModifier.methods(UploadModuleServiceImpl.class, "getCmUploadModule")[0], spy, TESTCUST_CUST);
		Assert.assertEquals(mod.getId(), null);
	}
  
	/**
	 * 根据模块id，还没配置类型
	 */
	@Test
	public void test3() {
		UploadModuleServiceImpl spy = PowerMockito.spy(serviceMock);
		//设置根据模块id没有查询到该模块
		NtCmUploadModule modreturn = new NtCmUploadModule();
		PowerMockito.when(cmUploadModuleMapperMock.getCmUploadModule(Mockito.any())).thenReturn(modreturn);
		//该模块还没有配置类型和格式
		PowerMockito.when(cmUploadModuleMapperMock.listCmUploadModuleType(Mockito.any())).thenReturn(null);
		//传参是投顾客户-投顾客户枚举
		NtCmUploadModule mod = (NtCmUploadModule)ReflectionUtils.invokeMethod(MemberModifier.methods(UploadModuleServiceImpl.class, "getCmUploadModule")[0], spy, TESTCUST_CUST);
		Assert.assertEquals(mod.getId(), null);
	}
 
	/**
	 * 根据模块id，查询到配置（只有一种上传类型）
	 */
	@Test
	public void test4() {
		UploadModuleServiceImpl spy = PowerMockito.spy(serviceMock);
		//设置根据模块id没有查询到该模块
		NtCmUploadModule modreturn = new NtCmUploadModule();
		PowerMockito.when(cmUploadModuleMapperMock.getCmUploadModule(Mockito.any())).thenReturn(modreturn);
		//该模块还没有配置只配置了文档类型和pdf格式，大小限制
		//设置格式
		NtCmUploadModuleTypeSuffix suffix = new NtCmUploadModuleTypeSuffix();
		suffix.setSuffix("pdf");
		List<NtCmUploadModuleTypeSuffix> listsuffix = new ArrayList<NtCmUploadModuleTypeSuffix>();
		listsuffix.add(suffix);
		PowerMockito.when(cmUploadModuleMapperMock.listCmUploadModuleTypeSuffix(Mockito.any())).thenReturn(listsuffix);
		//设置类型
		List<NtCmUploadModuleType> listtype = new ArrayList<NtCmUploadModuleType>();
		//文档
		NtCmUploadModuleType type = new NtCmUploadModuleType();
		type.setUptype(CrmUploadTypeEnum.DOCUMENT.getCode());
		type.setMaxsize(BigDecimal.TEN);
		listtype.add(type);
		PowerMockito.when(cmUploadModuleMapperMock.listCmUploadModuleType(Mockito.any())).thenReturn(listtype);
		//传参是投顾客户-投顾客户枚举
		NtCmUploadModule mod = (NtCmUploadModule)ReflectionUtils.invokeMethod(MemberModifier.methods(UploadModuleServiceImpl.class, "getCmUploadModule")[0], spy, TESTCUST_CUST);
		Assert.assertTrue(mod.getListHasSelectTypes().contains(CrmUploadTypeEnum.DOCUMENT.getCode()),"已经选中了文档类型！");
		Assert.assertTrue(mod.getTypeSuffixsMap().containsValue("pdf"),"已经选中了pdf格式！");
	}

	/**
	 * 根据模块id，查询到配置（有多种上传类型）
	 */
	@Test
	public void test5() {
		UploadModuleServiceImpl spy = PowerMockito.spy(serviceMock);
		//设置根据模块id没有查询到该模块
		NtCmUploadModule modreturn = new NtCmUploadModule();
		PowerMockito.when(cmUploadModuleMapperMock.getCmUploadModule(Mockito.any())).thenReturn(modreturn);
		//该模块还没有配置只配置了文档类型和pdf格式，大小限制
		//设置格式
		NtCmUploadModuleTypeSuffix suffix = new NtCmUploadModuleTypeSuffix();
		suffix.setSuffix("pdf");
		List<NtCmUploadModuleTypeSuffix> listsuffix = new ArrayList<NtCmUploadModuleTypeSuffix>();
		listsuffix.add(suffix);
		PowerMockito.when(cmUploadModuleMapperMock.listCmUploadModuleTypeSuffix(Mockito.any())).thenReturn(listsuffix);
		//设置类型
		List<NtCmUploadModuleType> listtype = new ArrayList<NtCmUploadModuleType>();
		//文档
		NtCmUploadModuleType typedoc = new NtCmUploadModuleType();
		typedoc.setUptype(CrmUploadTypeEnum.DOCUMENT.getCode());
		typedoc.setMaxsize(BigDecimal.TEN);
		listtype.add(typedoc);
		//图片
		NtCmUploadModuleType typepic = new NtCmUploadModuleType();
		typepic.setUptype(CrmUploadTypeEnum.PICTURE.getCode());
		typepic.setMaxsize(BigDecimal.TEN);
		listtype.add(typepic);
		PowerMockito.when(cmUploadModuleMapperMock.listCmUploadModuleType(Mockito.any())).thenReturn(listtype);
		//传参是投顾客户-投顾客户枚举
		NtCmUploadModule mod = (NtCmUploadModule)ReflectionUtils.invokeMethod(MemberModifier.methods(UploadModuleServiceImpl.class, "getCmUploadModule")[0], spy, TESTCUST_CUST);
		Assert.assertTrue(mod.getListHasSelectTypes().contains(CrmUploadTypeEnum.DOCUMENT.getCode())&&mod.getListHasSelectTypes().contains(CrmUploadTypeEnum.PICTURE.getCode()),"已经选中了文档和图片类型！");
		Assert.assertTrue(mod.getTypeSuffixsMap().containsValue("pdf"),"已经选中了pdf格式！");
	}
}
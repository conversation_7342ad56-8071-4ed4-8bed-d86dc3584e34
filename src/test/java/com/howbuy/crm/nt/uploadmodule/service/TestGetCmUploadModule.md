## 测试的类
> com.howbuy.crm.nt.uploadmodule.service.UploadModuleServiceImpl
## 测试的方法
> getCmUploadModule(CrmUploadModuleEnum module)  获取模块上传模型

## 分支伪代码
```java
if (传的枚举不为空) {
         根据枚举解析主模块和子模块id查询模块
   if(查询到模块){
                        根据模块id查询是否配置了类型和和格式
        if(已经配置了类型和格式){
        	return 设置好各种参数的模块
        }else{
        	return 空模块
        }
   }else{
        retrun 空模块
   }
} else {
	return 空模块
}
```

## 测试案例
##### 1、test1：传参是null
##### 2、test2：没有查询到模块id
##### 3、test3：根据模块id，还没配置类型
##### 4、test4：根据模块id，查询到配置（只有一种上传类型）
##### 5、test5：根据模块id，查询到配置（有多种上传类型）
package com.howbuy.crm.nt.associationmail.service;

import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.associationmail.dto.AssociationExpMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.model.MailTaskContext;
import com.howbuy.crm.nt.associationmail.util.ReadEmailUtil;
import com.howbuy.crm.nt.associationmail.util.RegexUtil;
import com.sun.mail.imap.IMAPFolder;
import lombok.SneakyThrows;
import org.apache.commons.collections.map.MultiValueMap;
import org.apache.commons.lang3.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.springframework.util.ReflectionUtils;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import javax.mail.Message;
import javax.mail.internet.MimeMessage;
import java.util.*;
import java.util.regex.Pattern;

import static org.mockito.ArgumentMatchers.*;
import static org.powermock.api.mockito.PowerMockito.doNothing;

/**
 * 单元测试：解析邮件内容
 * <AUTHOR>
 * @date 2022/5/5 17:16
 */
@PrepareForTest({AssociationMailServiceImpl.class, ReadEmailUtil.class, RegexUtil.class})
@PowerMockIgnore("javax.management.*")
public class AssociationMailServiceImplTest extends PowerMockTestCase {

    @InjectMocks
    private AssociationMailServiceImpl serviceMock;
    @Mock
    private AssociationMailBuss associationMailBuss;

    // 邮件信息列表
    private List<Message> messageList;
    // 正常解析的邮件
    private List<AssociationMail> normalAssociationMailList;
    // 异常解析的邮件
    private List<AssociationExpMail> exceptionAssociationMailList;
    // 内容重复的邮件
    private Map<String, AssociationExpMail> contentRepeatMail;
    // 用来验证邮件数据是否重复的set
    private Set<String> set;
    // 邮件解析上下文
    private MailTaskContext mailTaskContext;
    // 邮件信息的uid
    public static final long MESSAGE_UID = 1583743516L;

    @BeforeMethod
    public void setUp() {
        messageList = new ArrayList<>();
        normalAssociationMailList = new ArrayList<>();
        exceptionAssociationMailList = new ArrayList<>();
        contentRepeatMail = new MultiValueMap();
        set = new HashSet<>();
        mailTaskContext = new MailTaskContext();
    }

    /**
     * 1.解析异常的邮件
     */
    @Test
    public void testResolveHasException() {
        AssociationMailServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock邮件信息对象
        mockMessageList(1);
        // mock邮件信息的uid
        mockMessageUid(MESSAGE_UID);
        // mock解析异常
        mockResolveHasException();
        ReflectionUtils.invokeMethod(MemberModifier.method(AssociationMailServiceImpl.class, "resolveMailContent"), spy,
                messageList, normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, mailTaskContext);
        Assert.assertEquals(exceptionAssociationMailList.size(), 1);
        Assert.assertTrue(StringUtils.isBlank(exceptionAssociationMailList.get(0).getLoginHref()));
    }

    /**
     * 2.不是解析异常的邮件
     */
    @Test
    public void testResolveNotHasException() {
        AssociationMailServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock邮件信息对象
        mockMessageList(1);
        // mock邮件信息的uid
        mockMessageUid(MESSAGE_UID);
        // mock不是解析异常
        mockResolveNotHasException();
        ReflectionUtils.invokeMethod(MemberModifier.method(AssociationMailServiceImpl.class, "resolveMailContent"), spy,
                messageList, normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, mailTaskContext);
        Assert.assertEquals(exceptionAssociationMailList.size(), 0);
        Assert.assertEquals(normalAssociationMailList.size(), 1);
    }

    /**
     * 3.内容重复的邮件
     */
    @Test
    public void testResolveRepeat() {
        AssociationMailServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock邮件信息对象
        mockMessageList(2);
        // mock邮件信息的uid
        mockMessageUid(MESSAGE_UID);
        // mock内容重复
        mockResolveRepeat();
        ReflectionUtils.invokeMethod(MemberModifier.method(AssociationMailServiceImpl.class, "resolveMailContent"), spy,
                messageList, normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, mailTaskContext);
        Assert.assertEquals(exceptionAssociationMailList.size(), 1);
        List<AssociationMail> repeatMailList = (List<AssociationMail>)contentRepeatMail.get(contentRepeatMail.keySet().toArray(new String[0])[0]);
        Assert.assertEquals(repeatMailList.size(), 1);
    }

    /**
     * 4.解析正常的邮件
     */
    @Test
    public void testResolveNormal() {
        AssociationMailServiceImpl spy = PowerMockito.spy(serviceMock);
        // mock邮件信息对象
        mockMessageList(1);
        // mock邮件信息的uid
        mockMessageUid(MESSAGE_UID);
        // mock解析正常
        mockResolveNormal();
        ReflectionUtils.invokeMethod(MemberModifier.method(AssociationMailServiceImpl.class, "resolveMailContent"), spy,
                messageList, normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, mailTaskContext);
        Assert.assertEquals(exceptionAssociationMailList.size(), 0);
        Assert.assertEquals(normalAssociationMailList.size(), 1);
    }

    /**
     * mock邮件信息对象
     * @param num 邮件数量
     */
    private void mockMessageList(int num) {
        Message message = PowerMockito.mock(MimeMessage.class);
        for (int i = 0; i < num; i++) {
            messageList.add(message);
        }
    }

    /**
     * mock邮件信息的uid
     * @param uid
     */
    @SneakyThrows
    private void mockMessageUid(long uid) {
        IMAPFolder imapFolder = PowerMockito.mock(IMAPFolder.class);
        PowerMockito.when(imapFolder.getUID(isA(Message.class))).thenReturn(uid);
        mailTaskContext.setInboxFolder(imapFolder);
    }

    /**
     * mock解析异常
     */
    @SneakyThrows
    private void mockResolveHasException() {
        ReadEmailUtil readEmailUtil = PowerMockito.mock(ReadEmailUtil.class);
        PowerMockito.whenNew(ReadEmailUtil.class).withAnyArguments().thenReturn(readEmailUtil);
        PowerMockito.when(readEmailUtil.getFrom()).thenReturn("<EMAIL>");
        PowerMockito.when(readEmailUtil.getSentDate()).thenReturn("2022-03-01 10:01:11");

        PowerMockito.mockStatic(ReadEmailUtil.class);
        doNothing().when(ReadEmailUtil.class, "getMailTextContent", any(), any());

        PowerMockito.mockStatic(RegexUtil.class);
        String newBodyText = "张华雄，您好！上海新方程股权投资管理有限公司已为您开通该管理人名下私募基金信息披露备份系统投资者查询账号，投资者账号ja1000152125，初始密码HlK7bHYZZX，管理人登记编码P1000777。请通过以下链接进行登录：。为避免密码泄露对您造成不必要影响，请您及时登录系统并修改密码。如有疑问请联系管理人。私募基金信息披露备份系统2021年03月31日注：投资者登录私募基金信息披露备份系统后仅可查看已购买的该管理人管理的私募基金信息披露报告。相关私募基金信息披露报告为私募基金管理人按照《私募投资基金信息披露管理办法》及私募基金信息披露内容与格式指引向中国证券投资基金业协会（以下简称协会）进行的备份，协会不对信息披露报告的内容进行核实和审查，信息披露报告内容的真实性和准确性由私募基金管理人自行负责。投资者如发现私募基金管理人在协会备份的信息披露报告内容与该管理人通过其他方式或渠道披露的信息不一致的，请通过信息披露报告的评价页面点击“不一致”，并在评价说明列明具体原因。投资者如发现私募基金管理人存在违法违规情况的，可通过“私募基金管理人公示平台（ http://gs.amac.org.cn/ ）”点击该管理人公示信息页面中的“投诉”按钮，在线填写并提交投诉信息；或将投诉相关文件邮寄至协会；或现场提交。邮寄或现场提交地址为北京市西城区月坛大厦A座25层，邮寄时请标明“中国证券投资基金业协会法律部收”。";
        PowerMockito.when(RegexUtil.filterHtml(isA(CharSequence.class))).thenReturn(newBodyText);
        PowerMockito.when(RegexUtil.matchText(isA(Pattern.class), isA(CharSequence.class))).thenCallRealMethod();
    }

    /**
     * mock不是解析异常
     */
    @SneakyThrows
    private void mockResolveNotHasException() {
        ReadEmailUtil readEmailUtil = PowerMockito.mock(ReadEmailUtil.class);
        PowerMockito.whenNew(ReadEmailUtil.class).withAnyArguments().thenReturn(readEmailUtil);
        PowerMockito.when(readEmailUtil.getFrom()).thenReturn("<EMAIL>");
        PowerMockito.when(readEmailUtil.getSentDate()).thenReturn("2022-03-01 10:01:11");

        PowerMockito.mockStatic(ReadEmailUtil.class);
        doNothing().when(ReadEmailUtil.class, "getMailTextContent", any(), any());

        PowerMockito.mockStatic(RegexUtil.class);
        String newBodyText = "张华雄，您好！上海新方程股权投资管理有限公司已为您开通该管理人名下私募基金信息披露备份系统投资者查询账号，投资者账号ja1000152125，初始密码HlK7bHYZZX，管理人登记编码P1000777。请通过以下链接进行登录：https://pfid.amac.org.cn/dd/disc/login.jsp。为避免密码泄露对您造成不必要影响，请您及时登录系统并修改密码。如有疑问请联系管理人。私募基金信息披露备份系统2021年03月31日注：投资者登录私募基金信息披露备份系统后仅可查看已购买的该管理人管理的私募基金信息披露报告。相关私募基金信息披露报告为私募基金管理人按照《私募投资基金信息披露管理办法》及私募基金信息披露内容与格式指引向中国证券投资基金业协会（以下简称协会）进行的备份，协会不对信息披露报告的内容进行核实和审查，信息披露报告内容的真实性和准确性由私募基金管理人自行负责。投资者如发现私募基金管理人在协会备份的信息披露报告内容与该管理人通过其他方式或渠道披露的信息不一致的，请通过信息披露报告的评价页面点击“不一致”，并在评价说明列明具体原因。投资者如发现私募基金管理人存在违法违规情况的，可通过“私募基金管理人公示平台（ http://gs.amac.org.cn/ ）”点击该管理人公示信息页面中的“投诉”按钮，在线填写并提交投诉信息；或将投诉相关文件邮寄至协会；或现场提交。邮寄或现场提交地址为北京市西城区月坛大厦A座25层，邮寄时请标明“中国证券投资基金业协会法律部收”。";
        PowerMockito.when(RegexUtil.filterHtml(isA(CharSequence.class))).thenReturn(newBodyText);
        PowerMockito.when(RegexUtil.matchText(isA(Pattern.class), isA(CharSequence.class))).thenCallRealMethod();
    }

    @SneakyThrows
    private void mockResolveRepeat() {
        ReadEmailUtil readEmailUtil = PowerMockito.mock(ReadEmailUtil.class);
        PowerMockito.whenNew(ReadEmailUtil.class).withAnyArguments().thenReturn(readEmailUtil);
        PowerMockito.when(readEmailUtil.getFrom()).thenReturn("<EMAIL>");
        PowerMockito.when(readEmailUtil.getSentDate()).thenReturn("2022-03-01 10:01:11");

        PowerMockito.mockStatic(ReadEmailUtil.class);
        doNothing().when(ReadEmailUtil.class, "getMailTextContent", any(), any());

        PowerMockito.mockStatic(RegexUtil.class);
        String newBodyText = "张华雄，您好！上海新方程股权投资管理有限公司已为您开通该管理人名下私募基金信息披露备份系统投资者查询账号，投资者账号ja1000152125，初始密码HlK7bHYZZX，管理人登记编码P1000777。请通过以下链接进行登录：https://pfid.amac.org.cn/dd/disc/login.jsp。为避免密码泄露对您造成不必要影响，请您及时登录系统并修改密码。如有疑问请联系管理人。私募基金信息披露备份系统2021年03月31日注：投资者登录私募基金信息披露备份系统后仅可查看已购买的该管理人管理的私募基金信息披露报告。相关私募基金信息披露报告为私募基金管理人按照《私募投资基金信息披露管理办法》及私募基金信息披露内容与格式指引向中国证券投资基金业协会（以下简称协会）进行的备份，协会不对信息披露报告的内容进行核实和审查，信息披露报告内容的真实性和准确性由私募基金管理人自行负责。投资者如发现私募基金管理人在协会备份的信息披露报告内容与该管理人通过其他方式或渠道披露的信息不一致的，请通过信息披露报告的评价页面点击“不一致”，并在评价说明列明具体原因。投资者如发现私募基金管理人存在违法违规情况的，可通过“私募基金管理人公示平台（ http://gs.amac.org.cn/ ）”点击该管理人公示信息页面中的“投诉”按钮，在线填写并提交投诉信息；或将投诉相关文件邮寄至协会；或现场提交。邮寄或现场提交地址为北京市西城区月坛大厦A座25层，邮寄时请标明“中国证券投资基金业协会法律部收”。";
        PowerMockito.when(RegexUtil.filterHtml(isA(CharSequence.class))).thenReturn(newBodyText);
        PowerMockito.when(RegexUtil.matchText(isA(Pattern.class), isA(CharSequence.class))).thenCallRealMethod();
    }

    /**
     * mock解析正常
     */
    @SneakyThrows
    private void mockResolveNormal() {
        ReadEmailUtil readEmailUtil = PowerMockito.mock(ReadEmailUtil.class);
        PowerMockito.whenNew(ReadEmailUtil.class).withAnyArguments().thenReturn(readEmailUtil);
        PowerMockito.when(readEmailUtil.getFrom()).thenReturn("<EMAIL>");
        PowerMockito.when(readEmailUtil.getSentDate()).thenReturn("2022-03-01 10:01:11");

        PowerMockito.mockStatic(ReadEmailUtil.class);
        doNothing().when(ReadEmailUtil.class, "getMailTextContent", any(), any());

        PowerMockito.mockStatic(RegexUtil.class);
        String newBodyText = "张华雄，您好！上海新方程股权投资管理有限公司已为您开通该管理人名下私募基金信息披露备份系统投资者查询账号，投资者账号ja1000152125，初始密码HlK7bHYZZX，管理人登记编码P1000777。请通过以下链接进行登录：https://pfid.amac.org.cn/dd/disc/login.jsp。为避免密码泄露对您造成不必要影响，请您及时登录系统并修改密码。如有疑问请联系管理人。私募基金信息披露备份系统2021年03月31日注：投资者登录私募基金信息披露备份系统后仅可查看已购买的该管理人管理的私募基金信息披露报告。相关私募基金信息披露报告为私募基金管理人按照《私募投资基金信息披露管理办法》及私募基金信息披露内容与格式指引向中国证券投资基金业协会（以下简称协会）进行的备份，协会不对信息披露报告的内容进行核实和审查，信息披露报告内容的真实性和准确性由私募基金管理人自行负责。投资者如发现私募基金管理人在协会备份的信息披露报告内容与该管理人通过其他方式或渠道披露的信息不一致的，请通过信息披露报告的评价页面点击“不一致”，并在评价说明列明具体原因。投资者如发现私募基金管理人存在违法违规情况的，可通过“私募基金管理人公示平台（ http://gs.amac.org.cn/ ）”点击该管理人公示信息页面中的“投诉”按钮，在线填写并提交投诉信息；或将投诉相关文件邮寄至协会；或现场提交。邮寄或现场提交地址为北京市西城区月坛大厦A座25层，邮寄时请标明“中国证券投资基金业协会法律部收”。";
        PowerMockito.when(RegexUtil.filterHtml(isA(CharSequence.class))).thenReturn(newBodyText);
        PowerMockito.when(RegexUtil.matchText(isA(Pattern.class), isA(CharSequence.class))).thenCallRealMethod();
    }
}
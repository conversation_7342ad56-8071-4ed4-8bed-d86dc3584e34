## 测试的类
> com.howbuy.crm.nt.associationmail.service.AssociationMailServiceImpl
## 测试的方法
> resolveMailContent(List<Message> messageList, List<AssociationMail> normalAssociationMailList, List<AssociationExpMail> exceptionAssociationMailList,
                                                         Map<String, AssociationExpMail> contentRepeatMail, Set<String> set, MailTaskContext mailTaskContext)

## 分支伪代码
```java
for (循环遍历邮件信息) {
    解析邮件内容
    if (解析异常的邮件) {
        解析内容添加到exceptionAssociationMailList
    } else {
        if (内容重复的邮件) {
            解析内容添加到exceptionAssociationMailList
            contentRepeatMail存放重复邮件情况
        } else {
            解析内容添加到normalAssociationMailList
        }
    }
}
```

## 测试案例
##### 1、testResolveHasException：解析异常的邮件
##### 2、testResolveNotHasException：不是解析异常的邮件
##### 3、testResolveRepeat：内容重复的邮件
##### 4、testResolveNormal：内容正常的邮件



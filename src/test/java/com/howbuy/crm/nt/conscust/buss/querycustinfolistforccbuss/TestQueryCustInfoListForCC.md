## 测试的类

> com.howbuy.crm.nt.conscust.buss.QueryCustInfoListForCCBuss

## 测试的方法

> QueryCustInfoListForCCResponse queryCustInfoListForCC(QueryCustInfoListForCCRequest req) 查询客户列表

## 分支伪代码

```java
//获取传参的客户列表和一账通列表
if(存在){
        if(客户列表大于限制数){
        2.客户量超过限制
        }

        if(一账通列表大于限制数){
        3.一账通量超过限制
        }

        查询有效客户
        if(存在有效客户){
        5.准确的数据结果，返回正常
        }else{
        4.未查询到准确的数据结果
        }
        }else{
        1、返回参数异常，不存在客户列表
        }

## 测试案例
##### 1、no_custandhbonelist 不存在客户列表
##### 2.more_custlist 客户量超过限制
##### 3.more_hbonelist 一账通量超过限制
##### 4.no_resultlist 未查询到准确的数据结果
##### 5.success_resultlist 查询到准确的数据结果
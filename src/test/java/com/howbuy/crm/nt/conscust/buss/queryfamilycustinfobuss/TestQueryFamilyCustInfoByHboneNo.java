package com.howbuy.crm.nt.conscust.buss.queryfamilycustinfobuss;

import com.beust.jcommander.internal.Lists;
import com.howbuy.crm.nt.base.model.NtBaseConstantEnum;
import com.howbuy.crm.nt.conscust.buss.QueryFamilyCustInfoBuss;
import com.howbuy.crm.nt.conscust.dao.QueryFamilyCustInfoMapper;
import com.howbuy.crm.nt.conscust.domain.QueryFamilyCustInfo;
import com.howbuy.crm.nt.conscust.request.QueryFamilyCustInfoRequset;
import com.howbuy.crm.nt.conscust.response.QueryFamilyCustInfoResponse;
import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

/**
 * @description: crm-nt
 * @date: 2023/2/15 10:48
 * @author: yu.zhang
 * @version: 1.0
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({QueryFamilyCustInfoBuss.class})
public class TestQueryFamilyCustInfoByHboneNo extends PowerMockTestCase {

    @InjectMocks
    private QueryFamilyCustInfoBuss serviceMock;

    @Mock
    private QueryFamilyCustInfoMapper queryFamilyCustInfoMapperMock;

    /**
     * 投顾客户号查询结果最大限制
     */
    public static final String INIT_HBONE = "79999999";

    /**
     * @param
     * @return
     * @Description: 1.传参缺失一账通
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_hbone() {

        QueryFamilyCustInfoBuss spy = PowerMockito.spy(serviceMock);
        //执行待test方法
        QueryFamilyCustInfoResponse res = spy.queryFamilyCustInfoByHboneNo(initCustInfoRequset(false), false);
        //断言验证
        Assert.assertEquals(NtBaseConstantEnum.PARAM_ERROR.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 2.一账通号不存在对应投顾客户号
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_custno() {

        QueryFamilyCustInfoBuss spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn(null).when(spy).getConscustNoByHboneNo(Mockito.any());
        //执行待test方法
        QueryFamilyCustInfoResponse res = spy.queryFamilyCustInfoByHboneNo(initCustInfoRequset(true), false);
        //断言验证
        Mockito.verify(spy).getConscustNoByHboneNo(Mockito.any());
        Assert.assertEquals(NtBaseConstantEnum.DATA_NOT_FUND.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 3.不存在家庭账户
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_familycust() {

        QueryFamilyCustInfoBuss spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn("111111").when(spy).getConscustNoByHboneNo(Mockito.any());
        PowerMockito.doReturn(initFamilyCustList(false, false)).when(queryFamilyCustInfoMapperMock).queryFamilyCustInfo(Mockito.any());
        //执行待test方法
        QueryFamilyCustInfoResponse res = spy.queryFamilyCustInfoByHboneNo(initCustInfoRequset(true), false);
        //断言验证
        Mockito.verify(spy).getConscustNoByHboneNo(Mockito.any());
        Mockito.verify(queryFamilyCustInfoMapperMock).queryFamilyCustInfo(Mockito.any());
        Assert.assertEquals(NtBaseConstantEnum.SUCCESS.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 4.存在家庭账户, 且是主账户
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void hav_mainfamilycust() {
        familycust(true, true);
    }

    /**
     * @param
     * @return
     * @Description: 5.存在家庭账户，是子账户
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void hav_subfamilycust() {
        familycust(true, false);
    }

    /**
     * @param
     * @return
     * @Description: 6.存在家庭账户，但是排除入参一账通的家庭账户
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void hav_familycust() {
        familycust(false, false);
    }

    /**
     * @return void
     * @throws
     * @Title: familycust
     * @Author: yu.zhang
     * @DateTime: 2023/2/15 11:16
     * @param: [queryAllFlag, isMain]
     */
    private void familycust(Boolean queryAllFlag, Boolean isMain) {
        QueryFamilyCustInfoBuss spy = PowerMockito.spy(serviceMock);
        PowerMockito.doReturn("111111").when(spy).getConscustNoByHboneNo(Mockito.any());
        PowerMockito.doReturn(initFamilyCustList(true, isMain)).when(queryFamilyCustInfoMapperMock).queryFamilyCustInfo(Mockito.any());
        //执行待test方法
        QueryFamilyCustInfoResponse res = spy.queryFamilyCustInfoByHboneNo(initCustInfoRequset(true), queryAllFlag);
        //断言验证
        Mockito.verify(spy).getConscustNoByHboneNo(Mockito.any());
        Mockito.verify(queryFamilyCustInfoMapperMock).queryFamilyCustInfo(Mockito.any());
        Assert.assertEquals(NtBaseConstantEnum.SUCCESS.getCode(), res.getReturnCode());
    }

    /**
     * @return com.howbuy.crm.nt.conscust.request.QueryFamilyCustInfoRequset
     * @throws
     * @Title: initCustInfoRequset
     * @Author: yu.zhang
     * @DateTime: 2023/2/15 11:16
     * @param: [havbean]
     */
    private QueryFamilyCustInfoRequset initCustInfoRequset(Boolean havbean) {
        QueryFamilyCustInfoRequset request = new QueryFamilyCustInfoRequset();
        if (havbean) {
            request.setHboneNo(INIT_HBONE);
        }
        return request;
    }

    /**
     * @return java.util.List<com.howbuy.crm.nt.conscust.domain.QueryFamilyCustInfo>
     * @throws
     * @Title: initFamilyCustList
     * @Author: yu.zhang
     * @DateTime: 2023/2/15 11:17
     * @param: [havbean, isMain]
     */
    private List<QueryFamilyCustInfo> initFamilyCustList(Boolean havbean, Boolean isMain) {
        List<QueryFamilyCustInfo> familyCustList = Lists.newArrayList();
        if (havbean) {
            QueryFamilyCustInfo familyCust = new QueryFamilyCustInfo();
            familyCust.setHboneNo(INIT_HBONE);
            familyCust.setFcFlag(isMain ? "1" : "0");
            familyCustList.add(familyCust);
        }
        return familyCustList;
    }
}

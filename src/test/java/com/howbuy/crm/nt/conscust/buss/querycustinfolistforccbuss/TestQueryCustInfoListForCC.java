package com.howbuy.crm.nt.conscust.buss.querycustinfolistforccbuss;

import com.beust.jcommander.internal.Lists;
import com.howbuy.crm.nt.base.model.NtBaseConstantEnum;
import com.howbuy.crm.nt.conscust.buss.QueryCustInfoListForCCBuss;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.CustInfo;
import com.howbuy.crm.nt.conscust.request.QueryCustInfoListForCCRequest;
import com.howbuy.crm.nt.conscust.response.QueryCustInfoListForCCResponse;
import lombok.SneakyThrows;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

import static org.mockito.Mockito.verify;

/**
 * @description: crm-nt
 * @date: 2023/2/14 9:11
 * @author: yu.zhang
 * @version: 1.0
 */
@PowerMockIgnore("javax.management.*")
@Test
@PrepareForTest({QueryCustInfoListForCCBuss.class})
public class TestQueryCustInfoListForCC extends PowerMockTestCase {

    @InjectMocks
    private QueryCustInfoListForCCBuss serviceMock;

    @Mock
    private ConscustMapper conscustMapperMock;

    /**
     * 投顾客户号查询结果最大限制
     */
    public static final int CUSTNO_QUERY_MAX_COUNT = 101;

    /**
     * @param
     * @return
     * @Description: 1.不存在客户列表
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_custandhbonelist() {

        QueryCustInfoListForCCBuss spy = PowerMockito.spy(serviceMock);
        //执行待test方法
        QueryCustInfoListForCCResponse res = spy.queryCustInfoListForCC(initCCRequest(0, 0));
        //断言验证
        Assert.assertEquals(NtBaseConstantEnum.PARAM_ERROR.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 2.客户量超过限制
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void more_custlist() {

        QueryCustInfoListForCCBuss spy = PowerMockito.spy(serviceMock);
        //执行待test方法
        QueryCustInfoListForCCResponse res = spy.queryCustInfoListForCC(initCCRequest(CUSTNO_QUERY_MAX_COUNT, 0));
        //断言验证
        Assert.assertEquals(NtBaseConstantEnum.DEAL_FAIL.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 3.一账通量超过限制
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void more_hbonelist() {

        QueryCustInfoListForCCBuss spy = PowerMockito.spy(serviceMock);
        //执行待test方法
        QueryCustInfoListForCCResponse res = spy.queryCustInfoListForCC(initCCRequest(0, CUSTNO_QUERY_MAX_COUNT));
        //断言验证
        Assert.assertEquals(NtBaseConstantEnum.DEAL_FAIL.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 4.未查询到准确的数据结果
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void no_resultlist() {

        QueryCustInfoListForCCBuss spy = PowerMockito.spy(serviceMock);

        PowerMockito.doReturn(null).when(conscustMapperMock).listCustInfoByConscustNoAndHboneNoList(Mockito.anyList(), Mockito.anyList());
        //执行待test方法
        QueryCustInfoListForCCResponse res = spy.queryCustInfoListForCC(initCCRequest(1, 1));

        //断言验证
        Mockito.verify(conscustMapperMock).listCustInfoByConscustNoAndHboneNoList(Mockito.anyList(), Mockito.anyList());
        Assert.assertEquals(NtBaseConstantEnum.DATA_NOT_FUND.getCode(), res.getReturnCode());
    }

    /**
     * @param
     * @return
     * @Description: 5.查询到准确的数据结果
     * <AUTHOR>
     * @date 2023/1/9 9:15
     */
    @Test
    @SneakyThrows
    public void success_resultlist() {

        QueryCustInfoListForCCBuss spy = PowerMockito.spy(serviceMock);

        PowerMockito.doReturn(initResultList()).when(conscustMapperMock).listCustInfoByConscustNoAndHboneNoList(Mockito.anyList(), Mockito.anyList());
        //执行待test方法
        QueryCustInfoListForCCResponse res = spy.queryCustInfoListForCC(initCCRequest(1, 1));

        //断言验证
        verify(conscustMapperMock).listCustInfoByConscustNoAndHboneNoList(Mockito.anyList(), Mockito.anyList());
        Assert.assertEquals(NtBaseConstantEnum.SUCCESS.getCode(), res.getReturnCode());
    }

    /**
     * @return com.howbuy.crm.nt.conscust.request.QueryCustInfoListForCCRequest
     * @throws
     * @Title: 初始化方法传参
     * @Author: yu.zhang
     * @DateTime: 2023/2/14 9:24
     * @param: [num]
     */
    private QueryCustInfoListForCCRequest initCCRequest(Integer custnum, Integer hbonenum) {
        QueryCustInfoListForCCRequest request = new QueryCustInfoListForCCRequest();

        List<String> listConscustNo = Lists.newArrayList();
        List<String> listHboneNo = Lists.newArrayList();
        if (custnum > 0) {
            for (int i = 0; i < custnum; i++) {
                listConscustNo.add(String.valueOf(i));
            }
        }

        if (hbonenum > 0) {
            for (int i = 0; i < hbonenum; i++) {
                listHboneNo.add(String.valueOf(i));
            }
        }
        request.setListConscustNo(listConscustNo);
        request.setListHboneNo(listHboneNo);
        return request;
    }

    private List<CustInfo> initResultList() {
        List<CustInfo> listCustInfo = Lists.newArrayList();

        CustInfo cust = new CustInfo();
        cust.setConscustNo("11111111111");
        listCustInfo.add(cust);
        return listCustInfo;
    }
}

## 测试的类

> com.howbuy.crm.nt.conscust.buss.QueryFamilyCustInfoBuss

## 测试的方法

> QueryFamilyCustInfoResponse queryFamilyCustInfoByHboneNo(QueryFamilyCustInfoRequset req, boolean queryAllFlag) 根据一账通查询家庭账户

## 分支伪代码

```java
//1.传参缺失一账通
通过一账通查询客户号
//2.一账通号不存在对应投顾客户号

根据客户号查询家庭账户
if(存在){
   遍历家庭账户
   判断当前查询账户是否为家庭主账户
   判断是否需要排除入参的那条记录
}
3.不存在家庭账户
返回成功

## 测试案例
##### 1.no_hbone 传参缺失一账通
##### 2.no_custno 一账通号不存在对应投顾客户号
##### 3.no_familycust 不存在家庭账户
##### 4.hav_mainfamilycust 存在家庭账户, 且是主账户
##### 5.hav_subfamilycust 存在家庭账户，是子账户
##### 6.hav_familycust 存在家庭账户，但是排除入参一账通的家庭账户
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.common.dao.CommonDao">
	<select id="getSeqValue" parameterType="map" resultType="String" useCache="false" flushCache="true">
	  	select to_char(${_parameter}.nextval) FROM dual
	</select>
	
	<select id="getFuncValue" parameterType="String" resultType="Double" useCache="false" flushCache="true">
	  	select ${_parameter} FROM dual
	</select>
	
	<update id="insertBySql" parameterType="map">
		${sqlstr}
	</update>
</mapper>
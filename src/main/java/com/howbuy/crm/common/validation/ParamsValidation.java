package com.howbuy.crm.common.validation;

import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang.StringUtils;

/**
 * @desc:
 * @author: weiwei.sun
 * @date: 2015年5月13日下午3:00:26
 */
public class ParamsValidation {

	/**
	 * 验证时间格式是否正确
	 * 
	 * @param dateStr
	 *            时间字符串
	 * @param formatStr
	 *            格式
	 * @return
	 */
	public static boolean isValidDate(String dateStr, String formatStr) {
		boolean convertSuccess = true;
		SimpleDateFormat format = new SimpleDateFormat(formatStr);
		try {
			// 设置lenient为false.否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format.setLenient(false);
			format.parse(dateStr);
		} catch (Exception e) {
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			convertSuccess = false;
		}
		return convertSuccess;
	}

}

package com.howbuy.crm.common.exception;

/**
 * @className BusinessException
 * @description 业务异常
 * <AUTHOR>
 * @date 2023年10月30日 16:40:05
 *
 */
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = -1944973629369163147L;
    /**
     * 错误描述
     */
    private String errorDesc;

    public BusinessException() {
        super();
    }

    public BusinessException(Throwable e) {
        super(e);
    }

    public BusinessException(String message) {
        super(message);
        this.errorDesc = message;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.errorDesc = message;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDesc(String errorDesc) {
        this.errorDesc = errorDesc;
    }
}

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.constant;

/**
 * @description: (投顾客户的类型枚举)
 * <AUTHOR>
 * @since JDK 1.8
 */
public final class CmConsuctType {
    /**
     * 公司资源类型
     */
    public static final String COMPANY_RESOURCE = "0";
    /**
     * 投顾资源类型(无划转)
     */
    public static final String ADVISORY_RESOURCE_NO_TRANS = "1";
    /**
     * 投顾资源类型(划转--潜在)
     */
    public static final String ADVISORY_RESOURCE_TRANS_POTENTIAL = "2";
    /**
     * 投顾资源类型(划转--成交)
     */
    public static final String ADVISORY_RESOURCE_TRANS_DEAL = "3";


    /**
     * 高端成交客户标签 1: 是 0：否
     */
    public static final String IS_GDCJLABEL = "1";
    public static final String NO_GDCJLABEL = "0";



}
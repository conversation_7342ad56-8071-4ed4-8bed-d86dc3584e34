/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.constant;

/**
 * @description: (hb常量翻译表 中 typeCode 常量)
 * <AUTHOR>
 * @date 2023/11/13 16:05
 * @since JDK 1.8
 */
public class HbTypeCodeConstant {


    /**
     * 全量的 会议类型  typeCode=conferenceType
     */
    public static final String FULL_CONFERENCE_TYPE = "conferenceType";

    /**
     * 理财九章 的 会议类型  typeCode=newconferenceOptType
     */
    public static final String LCJZ_CONFERENCE_TYPE = "newconferenceOptType";

    /**
     * 至臻年会 的 会议类型  typeCode=conferenceZzType
     */
    public static final String ZZNH_CONFERENCE_TYPE = "conferenceZzType";


    /**
     * 常规 的 会议类型  typeCode=normalConferenceType
     */
    public static final String NORMAL_CONFERENCE_TYPE = "normalConferenceType";


    /**
     * 会议主题类型 产品线 对应的常量类型 code
     */
    public static final String  CONFERENCE_THEME_PRODUCTLINE_TYPE="productline";

}
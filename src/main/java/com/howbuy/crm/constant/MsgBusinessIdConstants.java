package com.howbuy.crm.constant;

/**
 * @description:(消息推送  businessId 常量)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/11/16 18:08
 * @since JDK 1.8
 */
public class MsgBusinessIdConstants {

    /**
     * 路演会议-审核通过 消息推送业务id
     */
    public static final String CONFERENCE_AUDIT_PASS = "201422";

    /**
     * 路演会议-审核驳回 消息推送业务id
     */
    public static final String CONFERENCE_AUDIT_REJECT = "201423";

    /**
     * 带划转客户列表 划转批复 消息推送业务id
     */
    public static final String BUSI_ID_HZPF = "201165";

    /**
     * 带划转客户列表 二次批复 消息推送业务id
     */
    public static final String BUSI_ID_ECPF = "201166";

    /**
     *好买-售前留痕材料 消息推送业务id
     */
    public static final String HB_BUSI_ID_HGYJ = "201497";

    /**
     * 好臻-售前留痕材料 消息推送业务id todo
     */
    public static final String HZ_BUSI_ID_HGYJ = "201867";


    /**
     * 消息推送：中台 [单个客户][单日]累积{购买}超过阈值  测试：200866，产线：201691
     */
    public static final String BUSI_ID_ZT_THRESHOLD_BUY = "201691";

    /**
     * 消息推送：中台 [单个客户][单日]累积{赎回}超过阈值  测试：200867，产线：201692
     */
    public static final String BUSI_ID_ZT_THRESHOLD_SELL = "201692";


}

/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/


package com.howbuy.crm.cache.cacheService;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;

/**
 * Description: 缓存类
 * <AUTHOR>
 * @date 2016年11月7日 下午3:26:55
 * @since JDK 1.7
 */
public abstract class AbstractCacheService {

    /**
     * 24小时的秒数
     */
    protected final static int ONE_DAY_SECOND = 86400;
    /**
     * 12小时的秒数
     */
    protected final static int HALF_DAY_SECOND = 43200;
    /**
     * 3小时的秒数
     */
    protected final static int THREE_HOUR_SECOND = 10800;
    /**
     * 一小时的秒数
     */
    protected final static int ONE_HOUR_SECOND = 3600;
    /**
     * 10分钟的秒数
     */
    protected final static int TEN_MINUTES_SECOND = 600;
    /**
     * 5分钟的秒数
     */
    protected final static int FIVE_MINUTES_SECOND = 300;
    /**
     * 一分钟的秒数
     */
    protected final static int ONE_MINUTES_SECOND = 60;
    /**
     * 10秒
     */
    protected final static int TEN_SECOND = 10;
    /**
     * 缓存服务
     */
    protected final CacheService CACHE_SERVICE = CacheServiceImpl.getInstance();
}


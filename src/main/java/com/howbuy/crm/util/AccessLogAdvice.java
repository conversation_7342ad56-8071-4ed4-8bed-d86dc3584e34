package com.howbuy.crm.util;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public class AccessLogAdvice {

    private static final Logger log = LoggerFactory.getLogger("cc-access-log");
    public AccessLogAdvice() {
    }

    public Object performance(ProceedingJoinPoint jp) throws Throwable {
        List<String> msgs = new ArrayList<>();

        // 添加输入日志
        msgs.add(getInputMsg(jp));

        Object obj = null;
        long t = System.currentTimeMillis();
        boolean ret = true;
        try {
            obj = jp.proceed();
            msgs.add(" ");
            if (obj != null) {
                // 添加输出日志
                msgs.add(objToString(obj));
            }
        } catch (Throwable e) {
            ret = false;
            throw e;
        } finally {
            long runtime = System.currentTimeMillis() - t;
            // 添加执行时间
            msgs.add(String.valueOf(runtime));
            if (ret) {
                log.info("access.success,{}", JSON.toJSONString(msgs));
            } else {
                log.info("access.fail,{}", JSON.toJSONString(msgs));
            }
        }

        return obj;
    }

    private String getInputMsg(ProceedingJoinPoint jp) {
        // 被调用类名及方法名
        StringBuilder sb = new StringBuilder();
        sb.append(jp.getTarget().getClass().getCanonicalName());
        sb.append("(");
        sb.append(jp.getSignature().getName());
        sb.append(")");

        // 调用参数
        Object[] args = jp.getArgs();
        if (args != null && args.length > 0) {
            sb.append("[");
            for (int i = 0; i < args.length; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(objToString(args[i]));
            }
            sb.append("]");
        }
        return sb.toString();
    }

    private String objToString(Object obj) {
        try {
            Class<? extends Object> argClass = obj.getClass(); //npe
            if (argClass.isEnum() || argClass.isPrimitive() || argClass.isArray()
                    || argClass.isAssignableFrom(Collection.class) || argClass.isAssignableFrom(Map.class)) {
                // 枚举,或者基本类型,或者数组,或者继承了Collection,或者 继承Map
                return JSON.toJSONString(obj);
            }
            Method m = argClass.getDeclaredMethod("toString");
            return (String) m.invoke(obj);
        } catch (Exception ex) {
            return JSON.toJSONString(obj);
        }
    }

}
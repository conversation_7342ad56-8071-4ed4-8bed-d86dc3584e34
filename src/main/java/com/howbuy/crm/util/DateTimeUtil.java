package com.howbuy.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * <p>Title: date</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2014</p>
 * <p>Company: howbuy</p>
 * <AUTHOR>
 * @version 1.0
 */

public class DateTimeUtil {

	private static final Logger log = LoggerFactory.getLogger(DateTimeUtil.class);
	
	public final static String DATE_PATTERN = "yyyy-MM-dd";

	public final static String TIME_PATTERN = "HH:mm:ss";

	public final static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	public final static String YYYY_MM_DD = "yyyyMMdd";

	public final static String HHMMSS = "HHmmss";

	public final static String DATE_TIME_PATTERN = DATE_PATTERN + " "
			+ TIME_PATTERN;
	
	public static final long MINUTE_MILS = 60 * 1000L;
	public static final long HOUR_MILS = 60 * 60 * 1000L;
	public static final long DAY_MILS = 24 * 60 * 60 * 1000L;
	
	public final static String DATA_PATTERN8 = "yyyyMMdd";
	
	public static final Date convertStringToDate(String pattern, Locale locale,
			TimeZone zone, String strDate) throws ParseException {
		if (locale == null) {
			locale = Locale.getDefault();
		}
		if (zone == null) {
			zone = TimeZone.getDefault();
		}
		SimpleDateFormat df = new SimpleDateFormat(pattern, locale);
		df.setTimeZone(zone);
		try {
			return df.parse(strDate);
		} catch (ParseException pe) {
			throw new ParseException(pe.getMessage(), pe.getErrorOffset());
		}
	}

	public static final Date convertStringToDate(String strDate) {
		Locale locale = Locale.CHINESE;
		try {
			return convertStringToDate(DATE_PATTERN, locale, null, strDate);
		} catch (Exception e) {
			return null;
		}
	}

	public static final Date convertStringToDate(String strDate, String sytle) {
		Locale locale = Locale.CHINESE;
		try {
			return convertStringToDate(sytle, locale, null, strDate);
		} catch (Exception e) {
			return null;
		}
	}

	public static final String convertDateToString(String pattern,
			Locale locale, TimeZone zone, Date aDate) {
		if (locale == null) {
			locale = Locale.getDefault();
		}
		if (zone == null) {
			zone = TimeZone.getDefault();
		}
		SimpleDateFormat df = new SimpleDateFormat(pattern, locale);
		df.setTimeZone(zone);
		try {
			return df.format(aDate);
		} catch (Exception e) {
			return "";
		}
	}

	public static final String convertDateToString(String pattern, Date aDate) {
		Locale locale = Locale.CHINESE;
		return convertDateToString(pattern, locale, null, aDate);
	}

	/**
	 * 提供yyyy-MM-dd类型的日期字符串转化
	 */
	public static final Date getBeginDate(String beginDate) {
		Locale locale = Locale.CHINESE;
		try {
			return convertStringToDate("yyyy-MM-dd HH:mm:ss", locale, null,
					beginDate + " 00:00:00");
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 提供yyyy-MM-dd类型的日期字符串转化 专门提供Web页面结束日期转化 如输入2006-07-27，则转化为2006-07-28
	 * 00:00:00
	 */
	public static final Date getEndDate(String endDate) {
		Locale locale = Locale.CHINESE;
		try {
			Date date = convertStringToDate("yyyy-MM-dd HH:mm:ss", locale,
					null, endDate + " 00:00:00");
			return new Date(date.getTime() + 24 * 3600 * 1000);
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * yyyy年mm月dd日 星期w
	 */
	public static String getFullDateStr() {
		DateFormat format = DateFormat.getDateInstance(DateFormat.FULL,
				Locale.CHINESE);
		return format.format(new Date());
	}

	/**
	 * 计算两个日期之间相差的天数
	 * 
	 * @param date1
	 * @param date2
	 * @return
	 */

	public static int diffdates(Date date1, Date date2) {
		int result = 0;

		GregorianCalendar gc1 = new GregorianCalendar();
		GregorianCalendar gc2 = new GregorianCalendar();

		gc1.setTime(date1);
		gc2.setTime(date2);
		result = getDays(gc1, gc2);

		return result;
	}

	public static int getDays(GregorianCalendar g1, GregorianCalendar g2) {
		int elapsed = 0;
		GregorianCalendar gc1, gc2;

		if (g2.after(g1)) {
			gc2 = (GregorianCalendar) g2.clone();
			gc1 = (GregorianCalendar) g1.clone();
		} else {
			gc2 = (GregorianCalendar) g1.clone();
			gc1 = (GregorianCalendar) g2.clone();
		}

		gc1.clear(Calendar.MILLISECOND);
		gc1.clear(Calendar.SECOND);
		gc1.clear(Calendar.MINUTE);
		gc1.clear(Calendar.HOUR_OF_DAY);

		gc2.clear(Calendar.MILLISECOND);
		gc2.clear(Calendar.SECOND);
		gc2.clear(Calendar.MINUTE);
		gc2.clear(Calendar.HOUR_OF_DAY);

		while (gc1.before(gc2)) {
			gc1.add(Calendar.DATE, 1);
			elapsed++;
		}
		return elapsed;
	}

	/**
	 * 功能：将表示时间的字符串以给定的样式转化为java.util.Date类型
	 * 且多于或少于给定的时间多少小时（formatStyle和formatStr样式相同）
	 * 
	 * @param:formatStyle 要格式化的样式,如:yyyy-MM-dd HH:mm:ss
	 * @param:formatStr 待转化的字符串(表示的是时间)
	 * @param:hour 多于或少于的小时数(可正可负) 单位为小时
	 * @return java.util.Date
	 */
	public static Date formartDate(String formatStyle, String formatStr,
			int hour) {
		SimpleDateFormat format = new SimpleDateFormat(formatStyle,
				Locale.CHINA);
		try {
			Date date = new Date();
			date.setTime(format.parse(formatStr).getTime() + hour * 60 * 60
					* 1000);
			return date;
		} catch (Exception e) {
			return null;
		}
	}

	public static Date getString2Date(String timeStr,String format){
    	SimpleDateFormat formate = new SimpleDateFormat(format);
    	Date date = null;
    	try {
			date = formate.parse(timeStr);
		} catch (ParseException e) {
			e.printStackTrace();
		}
    	return date;
    }
	/**
	 * 获取昨天
	 */
	public static Date getYesterday() {
		return new Date(System.currentTimeMillis() - 24 * 3600 * 1000L);
	}

	/**
	 * 获取昨天
	 */
	public static Date getYesterdayDate(Date day) {
		return new Date(day.getTime() - 24 * 3600 * 1000L);
	}

	/**
	 * 获取明天
	 */
	public static Date getTomorrowDate(Date day) {
		return new Date(day.getTime() + 24 * 3600 * 1000L);
	}

	/**
	 * 获取上周
	 */
	public static Date getLastWeek(Date day) {
		return new Date(day.getTime() - 7 * 24 * 3600 * 1000L);
	}

	/**
	 * 获取下周
	 */
	public static Date getNextWeek(Date day) {
		return new Date(day.getTime() + 7 * 24 * 3600 * 1000L);
	}

	/**
	 * 获取上个月
	 */
	public static Date getLastMonth() {
		return getLastMonth(new Date());
	}

	/**
	 * 获取制定时间的上个月
	 */
	public static Date getLastMonth(Date date) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 1);
		cal.getTime();
		return cal.getTime();
	}

	/**
	 * 获取制定时间的下个月
	 */
	public static Date getNextMonth(Date date) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);
		cal.getTime();
		return cal.getTime();
	}

	/**
	 * 获取上年
	 */
	public static Date getLastYear() {
		return getLastYear(new Date());
	}

	/**
	 * 获取制定时间的上年
	 */
	public static Date getLastYear(Date date) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		cal.set(Calendar.YEAR, cal.get(Calendar.YEAR) - 1);
		cal.getTime();
		return cal.getTime();
	}

	/**
	 * 获取制定时间的下年
	 */
	public static Date getNextYear(Date date) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		cal.set(Calendar.YEAR, cal.get(Calendar.YEAR) + 1);
		cal.getTime();
		return cal.getTime();
	}

	/**
	 * 获取指定年和月中该月的最大天数
	 * 
	 * @param year
	 *            指定年
	 * @param month
	 *            指定月 1-12
	 * @return 该月最大天数
	 */
	public static int getMaxDayOfMonth(int year, int month) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.set(year, month - 1, 1);
		return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 根据指定的年份和指定的第多少周序号得到该周的第一天和最后一天日期
	 * 
	 * @param year
	 *            指定的年份,如2006
	 * @param weekNo
	 *            指定年份中的第多少周,如37
	 * @return 该周的起始日期后该周的结束日期<br>
	 *         Date[0] 起始日期<br>
	 *         Date[1] 结束日期
	 */
	public static Date[] getGivenWeekDates(int year, int weekNo) {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.set(Calendar.YEAR, year);
		cal.set(Calendar.WEEK_OF_YEAR, weekNo);
		Date begin = cal.getTime();
		cal.add(Calendar.DAY_OF_YEAR, 6);
		Date end = cal.getTime();
		return new Date[] { begin, end };
	}

	/**
	 * 根据指定日期获取其在一年中的第多少周
	 * 
	 * @param date
	 *            指定的日期,为null默认为当时日期
	 * @return 当年的第多少周序号,如37
	 */
	public static int getWeekNo(Date date) {
		if (date == null) {
			date = new Date();
		}
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		return cal.get(Calendar.WEEK_OF_YEAR);
	}

	/**
	 * 获取制定时间的年份
	 * 
	 * @param date
	 *            制定时间
	 * @return 年份
	 */
	public static int getYear(Date date) {
		if (date == null) {
			date = new Date();
		}
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.clear();
		cal.setTime(date);
		return cal.get(Calendar.YEAR);
	}


	/**
	 * 格式化日期
	 * 
	 * @param date
	 *            被格式化的日期
	 * @param style
	 *            显示的样式，如yyyyMMdd
	 */
	public static String fmtDate(Date date, String style) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(style);
		return dateFormat.format(date);
	}

	public static String fmtDate(String date, String style) {
		SimpleDateFormat dateFormat = new SimpleDateFormat(style);
		try {
			return dateFormat.format(dateFormat.parse(date));
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
	}


	public static String getTargetDateFormatter(String fullDateTimeStr,String currentTimeStr,String targetTimeStr) {
		try {
			DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern(currentTimeStr);
			DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(targetTimeStr);
			LocalDateTime dateTime = LocalDateTime.parse(fullDateTimeStr, originalFormatter);
			return dateTime.format(timeFormatter);
		} catch (Exception e) {
			log.error("getTargetDateFormatter>>>> 时间格式错误,fullDateTimeStr:{},currentTimeStr:{},targetTimeStr:{}",fullDateTimeStr,currentTimeStr,targetTimeStr);
		}
		return null;
	}
	/**
	 * 得到当前日期
	 * 
	 * @return int[] int[0] 年 int[1] 月 int[2] 日 int[3] 时 int[4] 分 int[5] 秒
	 */
	public static int[] getCurrentDate() {
		Calendar cal = Calendar
				.getInstance(TimeZone.getDefault(), Locale.CHINA);
		cal.setTime(new Date());
		int[] date = new int[6];
		date[0] = cal.get(Calendar.YEAR);
		date[1] = cal.get(Calendar.MONTH) + 1;
		date[2] = cal.get(Calendar.DATE);
		date[3] = cal.get(Calendar.HOUR_OF_DAY);
		date[4] = cal.get(Calendar.MINUTE);
		date[5] = cal.get(Calendar.SECOND);
		return date;
	}

	/**
	 * @Title: getTimeByMinute 
	 * @Description: TODO(获取当前时间前几分钟) 
	 *
	 * @param minute
	 * @return Date
	 * @throws
	 */
	public static Date getTimeByMinute(int minute) {

        Calendar calendar = Calendar.getInstance();

        calendar.add(Calendar.MINUTE, minute);

        return calendar.getTime();
    }
	/**
	 * 设置制定的年份和月份，再得到该日期的前多少月或后多少月的日期年份和月份
	 * 
	 * @param year
	 *            指定的年份，如 2006
	 * @param month
	 *            制定的月份，如 6
	 * @param monthSect
	 *            月份的差值 如：现在为2006年5月份，要得到后4月，则monthSect = 4，正确日期结果为2006年9月
	 *            如：现在为2006年5月份，要得到前4月，则monthSect = -4，正确日期结果为2006年1月
	 *            如：monthSect = 0，则表示为year年month月
	 * @return int[] int[0] 年份 int[1] 月份
	 */
	public static int[] getLimitMonthDate(int year, int month, int monthSect) {
		year = year < 1 ? 1 : year;
		month = month > 12 ? 12 : month;
		month = month < 1 ? 1 : month;
		Calendar cal = Calendar.getInstance(TimeZone.getDefault(), new Locale(
				"zh", "CN"));
		cal.set(Calendar.YEAR, year);
		cal.set(Calendar.MONTH, month);
		cal.add(Calendar.MONTH, monthSect);
		int[] yAndM = new int[2];
		yAndM[0] = cal.get(Calendar.YEAR);
		yAndM[1] = cal.get(Calendar.MONTH);
		if (yAndM[1] == 0) {
			yAndM[0] = yAndM[0] - 1;
			yAndM[1] = 12;
		}
		return yAndM;
	}

	public static Date getDate(Date date) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-ddHH:mm:ss");
		try {
			return format.parse(format.format(date));
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 获取系统当前日期：格式为yyyyMMdd
	 * @return String
	 */
	public static String getCurDate(String datefmt) {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat(datefmt);
		return format.format(curDate);
	}

	/**
	 * 获取系统当前日期：格式为yyyyMMdd
	 * @return String
	 */
	public static String getCurDate() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		return format.format(curDate);
	}
	
	/**
	 * 获取系统当前时间：格式为HHmmss
	 * @return String
	 */
	public static String getCurTime() {
		Date curTime = new Date();
		SimpleDateFormat format = new SimpleDateFormat("HHmmss");
		return format.format(curTime);
	}
	
	/**
	 * 获取系统当前日期：格式为yyyyMMddHHmmss
	 * @return String
	 */
	public static String getCurDateTime() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		return format.format(curDate);
	}
	
	/**
	 * 获取系统当前日期：格式为yyyyMMdd HHmmss
	 * @return String
	 */
	public static String getNowDateTime() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd HHmmss");
		return format.format(curDate);
	}

	/**
	 * 获取系统当前日期：格式为yyyy-MM-dd HH:mm:ss
	 * @return String
	 */
	public static String getCurDateTimeFmt() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return format.format(curDate);
	}
	
	/**
	 * 获取系统当前日期：格式为yyyyMMddHHmmssSSS
	 * @return String
	 */
	public static String getNowTime() {
		Date curDate = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		return format.format(curDate);
	}

	/**
	 * 从当前时间增加多少天
	 * 
	 * @param days
	 * @return
	 */
	public static Date addDaysFromNow(int days) {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_MONTH, days);
		return cal.getTime();
	}
	
	
	//相差的时差  (东 8 区)
	public static final long MIS_TIMING_MILS = 8 * HOUR_MILS;

	//  以 0:00 为基准 的当日 计算差量
	public static final long DAY_MILS_DIFF = MIS_TIMING_MILS;

	/**
	 * 判断 是否为同日，根据getDayNums规则。
	 * @param dateMils1
	 * @param dateMils2
	 * @return
	 */
	public static final boolean bSameDay(long dateMils1, long dateMils2) {
		return getDayNums(dateMils1) == getDayNums(dateMils2);
	}

	/**
	 * 获得从1970-01-01 09:30:00  到现在的天数 (9:30 - 9:30)
	 * @param mils 当前时间的毫秒数
	 * @return
	 */
	public static long getDayNums(long mils) {
		return (mils + DAY_MILS_DIFF) / DAY_MILS;
	}

	/**
	 * 获得所在天的 毫秒数字  (根据MIS_TIMING_MILS的不同来判断基于几点钟，MIS_TIMING_MILS=0为00:00:00)
	 * @param mils
	 * @return
	 */
	public static long getDayTimeMils(long mils) {
		mils = mils % DAY_MILS;

		if (mils < 0) {
			mils = DAY_MILS + mils;
		}
		mils += MIS_TIMING_MILS;
		if (mils >= DAY_MILS) {
			mils -= DAY_MILS;
		}

		return mils;
	}

	public static String patternDate(Date date,String pattern){
		SimpleDateFormat format = new SimpleDateFormat(pattern);
		return format.format(date);
	}

	
	public static String[] getNextYearAndMonth(String year, String month) {
		String[] yymm = new String[2];

		if ("12".equals(month)) {
			yymm[0] = String.valueOf(Integer.parseInt(year) + 1);
			yymm[1] = "01";
		} else {
			yymm[0] = year;
			yymm[1] = String.format("%1$02d", Integer.parseInt(month) + 1);
		}

		return yymm;
	}
	
	/**
     * 获取系统今天和昨天日期
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getDayStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String endTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(endTime);
    	endTime = endStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往前增加一天：整数往后推,负数往前推
        calendar.add(calendar.DATE, -1);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String startTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(startTime);
        startTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
    
    /**
     * 获取系统今天和昨天日期
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getFiveDayStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String endTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(endTime);
    	endTime = endStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往前增加一天：整数往后推,负数往前推
        calendar.add(calendar.DATE, -5);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String startTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(startTime);
        startTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
    
    /**
     * 获取系统今天和昨天日期
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getDayStartAndEndFmt() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String endTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(endTime);
    	endTime = endStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往前增加一天：整数往后推,负数往前推
        calendar.add(calendar.DATE, -1);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String startTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(startTime);
        startTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
	
    /**
     * 获取系统时间之前一周的开始和结束时间
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getWeekStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String endTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(endTime);
    	endTime = endStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往前增加六天：整数往后推,负数往前推
        calendar.add(calendar.DATE, -6);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String startTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(startTime);
        startTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
    
    /**
     * 获取系统时间之后一周的开始和结束时间
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getLastWeekStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天时间
    	Date curDate = new Date();
    	String startTime = df.format(curDate);
    	StringBuffer startStr = new StringBuffer().append(startTime);
    	startTime = startStr.toString();
    	
    	// 获取系统昨天时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往后增加六天：整数往后推,负数往前推
        calendar.add(calendar.DATE, 6);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String endTime = df.format(yestoday);
        StringBuffer endStr = new StringBuffer().append(endTime);
        endTime = endStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", startTime);
        map.put("endDate", endTime);
        return map;
    }
    
    /**
     * 获取系统最近一月开始和结束时间
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getMonthStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	
    	// 获取系统当天日期
    	Date curDate = new Date();
    	String beginTime = df.format(curDate);
    	StringBuffer endStr = new StringBuffer().append(beginTime);
    	beginTime = endStr.toString();
    	
    	// 获取系统30天之后日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
		// 把日期往后增加三十天：整数往后推,负数往前推
        calendar.add(calendar.DATE, 30);
		// 这个日期就是被调整过的日期
        Date yestoday= calendar.getTime();
        String endTime = df.format(yestoday);
        StringBuffer startStr = new StringBuffer().append(endTime);
        endTime = startStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", beginTime);
        map.put("endDate", endTime);
        return map;
    }
    
    /**
     * 获取产品分红日期（默认取系统当前10天和后20天日期）
     * @return Map<String, String>
	 * @throws 
     */
    @SuppressWarnings("static-access")
	public static Map<String, String> getBonusMonthStartAndEnd() {    	
    	SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
    	// 获取系统当天日期
    	Date curDate = new Date();
    	
    	// 获取系统10天之前日期
        Calendar beginCal = Calendar.getInstance();
        beginCal.setTime(curDate);
		// 把日期往前减去十天：整数往后推,负数往前推
        beginCal.add(beginCal.DATE, -10);
		// 这个日期就是被调整过的日期
        Date beginTime= beginCal.getTime();
        String beginDate = df.format(beginTime);
        StringBuffer beginStr = new StringBuffer().append(beginDate);
        beginDate = beginStr.toString();
    	
    	// 获取系统20天之后日期
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(curDate);
		// 把日期往后增加二十天：整数往后推,负数往前推
        endCal.add(endCal.DATE, 20);
		// 这个日期就是被调整过的日期
        Date endTime= endCal.getTime();
        String endDate = df.format(endTime);
        StringBuffer endStr = new StringBuffer().append(endDate);
        endDate = endStr.toString();
        
        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", beginDate);
        map.put("endDate", endDate);
        return map;
    }
    
    /**
     * 获取指定月份的第一天和最后一天
     * @param strMonth，如：“2013-09”
     * @return Map<String, String>
	 * @throws ParseException 
     */
    public static Map<String, String> getMonthFirstLastday(String strMonth) throws ParseException {    	
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        StringBuffer strDate = new StringBuffer().append(strMonth).append("01");        
        calendar.setTime(df.parse(strDate.toString()));       
        Date theDate = calendar.getTime();
        
        //获取指定月份的第一天
        GregorianCalendar gcLast = (GregorianCalendar) Calendar.getInstance();
        gcLast.setTime(theDate);
        gcLast.set(Calendar.DAY_OF_MONTH, 1);
        String dayFirst = df.format(gcLast.getTime());
        StringBuffer str = new StringBuffer().append(dayFirst);
        dayFirst = str.toString();

        //获取指定月份的最后一天
		// 加一个月
        calendar.add(Calendar.MONTH, 1);
		// 设置为下个月第一天
        calendar.set(Calendar.DATE, 1);
		// 再减一天即为该月最后一天
        calendar.add(Calendar.DATE, -1);
        String dayLast = df.format(calendar.getTime());
        StringBuffer endStr = new StringBuffer().append(dayLast);
        dayLast = endStr.toString();

        Map<String, String> map = new LinkedHashMap<String, String>();
        map.put("beginDate", dayFirst);
        map.put("endDate", dayLast);
        return map;
    }
    
    /**
	 * 将日期按星期形式进行拆分
	 * @param beginDate
	 * @param endDate
	 * @return Map<Integer,Map<String,Date>>
	 * @throws ParseException
	 */
	public static Map<Integer, Map<String, Date>> getWeeksBetween(
			String beginDate, String endDate) throws ParseException {
		Calendar cBegin = new GregorianCalendar();
		Calendar cEnd = new GregorianCalendar();
		cBegin.setTime(strToDate(beginDate));
		cEnd.setTime(strToDate(endDate));

		int count = 1;
		Map<Integer, Map<String, Date>> weeksMap = new LinkedHashMap<Integer, Map<String, Date>>();
		Map<String, Date> tempMap = new LinkedHashMap<String, Date>();
		boolean lockFirst = true;
		int diffDays = getDaysBetween(beginDate, endDate);
	     for(int i=1; i<=diffDays;i++){
	    	 if(lockFirst && count==1 && !(cBegin.get(Calendar.DAY_OF_WEEK)==Calendar.MONDAY)){
	    		 tempMap.put("beginDate", new java.sql.Date(cBegin.getTime().getTime()));
	    		 lockFirst=false;
	    	 }else if(cBegin.get(Calendar.DAY_OF_WEEK)==Calendar.MONDAY){
	    		 tempMap = new LinkedHashMap<String,Date>();
	    		 tempMap.put("beginDate", new java.sql.Date(cBegin.getTime().getTime()));
	    	 }
	    	 if(cBegin.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY){
	    		 tempMap.put("endDate", new java.sql.Date(cBegin.getTime().getTime()));
	    	 }else if(i==diffDays && !(cBegin.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY)){
	    		 tempMap.put("endDate", new java.sql.Date(cEnd.getTime().getTime()));
	    	 }
	    	 weeksMap.put(count, tempMap);
	      if(cBegin.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY){
	          count++;
	      }
	      cBegin.add(Calendar.DAY_OF_YEAR, 1);
	     }
		return weeksMap;
	}
	
	/**
	 * 获取两个日期之间相隔天数
	 * @param beginDate
	 * @param endDate
	 * @return int
	 * @throws ParseException
	 */
	public static int getDaysBetween(String beginDate, String endDate)
			throws ParseException {
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		Date bDate = format.parse(beginDate);
		Date eDate = format.parse(endDate);
		Calendar d1 = new GregorianCalendar();
		d1.setTime(bDate);
		Calendar d2 = new GregorianCalendar();
		d2.setTime(eDate);
		int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
		int y2 = d2.get(Calendar.YEAR);
		if (d1.get(Calendar.YEAR) != y2) {
			d1 = (Calendar) d1.clone();
			do {
				// 得到当年的实际天数
				days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);
				d1.add(Calendar.YEAR, 1);
			} while (d1.get(Calendar.YEAR) != y2);
		}
		return days;
	}
	
	/**
	 * 将字符串格式转换为日期
	 * @param strDate
	 * @return Date
	 */
	public static Date strToDate(String strDate) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		ParsePosition pos = new ParsePosition(0);
		Date strtodate = formatter.parse(strDate, pos);
		return strtodate;
	}
	
	@SuppressWarnings("finally")
	public static Date strToDate(String strDate,String partten){
		SimpleDateFormat formatter = new SimpleDateFormat(partten);
		Date date =null;
		try {
			date = formatter.parse(strDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}
	
	/**
	 * 获取小时和分钟时间信息
	 * @return List<String>
	 */
	public static List<String> getHhmmList(){
		String hh = "00";
		String mm = "00";
		List<String> hhmmList = new ArrayList<String>();
		for(int i=0; i<24; i++){
		  	 for(int j=0; j<2;j++){
		  		 if(i<10){
		  			 hh="0"+i;
		  		 }else{
		  			hh=""+i;
		  		 }
		  		 if(j%2==0){
		  			mm="00";
		  		 }else{
		  			mm="30";
		  		 }
		  	 	hhmmList.add(hh+":"+mm);
		  	 }
		}
		return hhmmList;
	}
	
	//获得两个时间的时间的天数
		public static Long getTwoDateCount(String begStr, String endStr){
				 long day = 0;
				  try {
				   SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
				   Date beginDate=format.parse(begStr);
				   Date endDate=format.parse(endStr);
				   day = (endDate.getTime() - beginDate.getTime())
				     / (24 * 60 * 60 * 1000);
				   day=day+1;
				  } catch (Exception e) {
				   return 0L;
				  }
				  return day;
				 }
		
		//获得当前时间向后推90天的字符串
		public static Map<String,String> getDateThreeMonth(){
			Date startDate  = new Date();
			Long start=startDate.getTime();
			Long value =90*24*60*60*1000L;
			long end = start-value;
			Date endDate  = new Date(end);
			SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
			String endDt=format.format(startDate);
			String startDt = format.format(endDate);
			Map<String,String> map = new HashMap<String, String>();
			map.put("startDT", startDt);
			map.put("endDT",endDt);
			return map;
		}
		//获得两个时间的分钟数
		public static Long getTwoDateMinCount(String begStr, String endStr){
			long day = 0;
			try {
				SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
				Date beginDate=format.parse(begStr);
				Date endDate=format.parse(endStr);
				day = (endDate.getTime() - beginDate.getTime())
			     / (60 * 1000);
			} catch (Exception e) {
				return 0L;
			}
			return day;
		}

	//获得当前时间前面一天的时间
	public static String getBeforeDateFmt(String datefmt){
		Date date = new Date();
		Date dBefore=null;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, -1);
		dBefore = calendar.getTime();
		//设置时间格式
		SimpleDateFormat sdf=new SimpleDateFormat(datefmt);
		//格式化前一天
		String defaultStartDate = sdf.format(dBefore);
		return defaultStartDate;
	}

		//获得当前时间前面一天的时间
		public static String getBeforeDateFmt(){
			Date date = new Date();
			Date dBefore=null;
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DAY_OF_MONTH, -1);
			dBefore = calendar.getTime();
			//设置时间格式
			SimpleDateFormat sdf=new SimpleDateFormat("yyyyMMdd");
			//格式化前一天
			String defaultStartDate = sdf.format(dBefore);
		    return defaultStartDate;
		}
		
		// 获得当前时间前面十天的时间
		public static String getTenBeforeDate() {
			Date date = new Date();
			Date dBefore = null;
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DAY_OF_MONTH, -10);
			dBefore = calendar.getTime();
			// 设置时间格式
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			// 格式化前一天
			String defaultStartDate = sdf.format(dBefore);
			return defaultStartDate;
		}
		
		// 获得当前时间前面九十天的时间
		public static String getNinetyBeforeDate() {
			Date date = new Date();
			Date dBefore = null;
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			calendar.add(Calendar.DAY_OF_MONTH, -90);
			dBefore = calendar.getTime();
			// 设置时间格式
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			// 格式化前一天
			String defaultStartDate = sdf.format(dBefore);
			return defaultStartDate;
		}
		
		//获得当前时间前面一天时间
		public static String getBeforeDateFmt(String dateStr,String fmt){
			SimpleDateFormat format = new SimpleDateFormat(fmt);
			Date date=null;
			Date dBefore=null;
			try {
				date=format.parse(dateStr);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			Calendar calendar = Calendar.getInstance();
			if(null==date){
				return "";
			}
			calendar.setTime(date);
			calendar.add(Calendar.DAY_OF_MONTH, -1);
			dBefore = calendar.getTime();
			//设置时间格式
			SimpleDateFormat sdf=new SimpleDateFormat(fmt);
			//格式化前一天
			String defaultStartDate = sdf.format(dBefore);
			return defaultStartDate;
		}

}
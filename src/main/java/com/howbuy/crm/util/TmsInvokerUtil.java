/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.util;


import com.howbuy.crm.common.enums.TmsReturnCodeEnum;

/**
 * @description: (中台调用判断)
 * <AUTHOR>
 * @date 2024/2/29 16:13
 * @since JDK 1.8
 */
public class TmsInvokerUtil {

    /**
     * @description: 判断returnCode是否是成功码
     * @param returnCode
     * @return boolean
     * @author: jin.wang03
     * @date: 2024/6/19 14:12
     * @since JDK 1.8
     */
    public static boolean isSuccess(String returnCode) {
        return TmsReturnCodeEnum.SUCC_TMS.getCode().equals(returnCode)
                || TmsReturnCodeEnum.SUCC_NEW.getCode().equals(returnCode)
                ||TmsReturnCodeEnum.SUCC.getCode().equals(returnCode);
    }

}
package com.howbuy.crm.util;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * @description:(工具类： 获取泛型的类型)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/11/7 9:47
 * @since JDK 1.8
 */
public class GenericsUtils {

   /**
    * @description:(通过反射,获得定义Class时声明的父类的范型参数的类型.)
    * @param clazz
    * @return java.lang.Class<T>
    * @author: haoran.zhang
    * @date: 2023/11/7 9:47
    * @since JDK 1.8
    */
    public static <T> Class<T> getSuperClassGenericType(Class<?> clazz) {
        return getSuperClassGenericType(clazz, 0);
    }

    /**
     * @description:(获取类上的泛型 的第[index]个泛型 class)
     * @param clazz
     * @param index
     * @return java.lang.Class<T>
     * @author: haoran.zhang
     * @date: 2023/11/7 9:45
     * @since JDK 1.8
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getSuperClassGenericType(Class<?> clazz, int index) {
        return getGenericType(clazz, index);
    }

    /**
     * @description:(获取类上的泛型 的第[index]个泛型 class)
     * @param clazz
     * @param index
     * @return java.lang.Class<T>
     * @date: 2023/11/7 9:45
     * @since JDK 1.8
     */
    private static Class getGenericType(Class<?> clazz, int index) {
        Type genType = clazz.getGenericSuperclass();

        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();

        if (index >= params.length || index < 0) {
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return (Class) params[index];
    }

    /**
     * @description:(通过反射，获得定义Class时声明的父类的范型参数的类型，若未找到，再获取父类的父类泛型)
     * @param clazz
     * @return java.lang.Class<T>
     * @author: haoran.zhang
     * @date: 2023/11/7 9:47
     * @since JDK 1.8
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getFirstGenericType(Class<?> clazz) {
        Type genType = clazz.getGenericSuperclass();

        while (!(genType instanceof ParameterizedType)) {
            genType = clazz.getSuperclass().getGenericSuperclass();
        }

        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        return (Class<T>) params[0];
    }
}
package com.howbuy.crm.util;


import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ExceptionCatchAdvice implements MethodInterceptor {

	private  Logger log = LoggerFactory.getLogger(ExceptionCatchAdvice.class);
	
	@Override
	public Object invoke(MethodInvocation invoke) throws Throwable {
		Class<?> returnType = invoke.getMethod().getReturnType();
		if (BaseResponse.class.isAssignableFrom(returnType)) {
			// 仅对返回类型为BaseResponse子类的方法进行增强。
			long start = System.currentTimeMillis();
			BaseResponse res = null;
			try {
				res = (BaseResponse) invoke.proceed();
			}  catch (Exception ex) {
				res = createErrorRes(ex, returnType, BaseConstantEnum.UNKNOWN_ERROR);
			} finally {
				if(res != null) {
					long end = System.currentTimeMillis();
					res.setDuration(end - start);					
				}
			}
			return res;
		} else {
			return invoke.proceed();
		}
	}

	private BaseResponse createErrorRes(Exception ex, Class<?> returnType, BaseConstantEnum em) throws Exception {
		log.error("", ex);
		BaseResponse res = (BaseResponse) returnType.newInstance();
		res.putBaseResult(em, ex.getMessage());
		return res;
	}
}

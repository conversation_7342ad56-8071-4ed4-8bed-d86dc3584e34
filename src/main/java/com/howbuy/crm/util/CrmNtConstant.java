package com.howbuy.crm.util;

/**
 * Nt常量
 * <AUTHOR>
 *
 */
public class CrmNtConstant {

	/**
	 * 响应成功
	 */
	public static final String RMISucc="0000";

	/**
	 * 响应成功
	 */
	public static final String RMISuccNew="0000000";
	
	/**
	 * 响应成功
	 */
	public static final String RMISuccForId="1002"; 
		
	/**
	 * 响应成功
	 */
	public static final String RMISuccForMobile="1003"; 
	
	/**
	 * 响应成功
	 */
	public static final String RMINoResult="0004";
	/**
	 * 没有查询到匹配数据
	 */
	public static final String NULL_ERROR = "0004";
	/**
	 * 系统错误
	 */
	public static final String SYSTEM_ERROR = "0003";

	/**
	 * 分销机构
	 */
	public static final String DISCODE="HB000A001";
	
	/**
	 * 高端客户掌基是否登录标签
	 */
	public static final String ZJLABLES="10051";
	
	/**
	 * 高端0存量标签
	 */
	public static final String GDLCLLABLES="21101";
	
	/**
	 * 高端0存量（3个月内）标签
	 */
	public static final String GDLCL3MLABLES="23216";
	
	/**
	 * 睡眠成交客户标签
	 */
	public static final String SMCJLABLES="21103";
	
	/**
	 * 高端成交客户标签
	 */
	public static final String GDCJLABLES="21001";
	
	/**
	 * 高端潜在客户标签
	 */
	public static final String GDQZLABLES="21102";
	
	/**
	 * 开通过定投-高端用户标签
	 */
	public static final String GDKDTLABLES="23211";
	
	/**
	 * 未开通过定投-高端用户标签
	 */
	public static final String GDWKDTLABLES="23212";
	
	/**
	 * 定投进行中-高端用户标签
	 */
	public static final String GDDTJXZLABLES="23213";
		
	/**
	 * 定投已停止-高端用户标签
	 */
	public static final String GDDTYTZLABLES="23214";
	
	/**
	 * 仍持有产品类型-股票海外
	 */
	public static final String HASGPHWLABLES="23217";
	
	/**
	 * 仍持有产品类型-股权海外
	 */
	public static final String HASGQHWLABLES="23219";
	
	/**
	 * 仍持有产品类型-对冲海外
	 */
	public static final String HASDCHWLABLES="23221";
	
	/**
	 * 仍持有产品类型-债券海外
	 */
	public static final String HASZQHWLABLES="23223";
	
	/**
	 * 仍持有产品类型-固收海外
	 */
	public static final String HASGSHWLABLES="23225";
	
	/**
	 * 仍持有产品类型-房地产海外
	 */
	public static final String HASFDCHWLABLES="23227";
	
	/**
	 * 仍持有产品类型-现金管理海外
	 */
	public static final String HASXJHWLABLES="23229";
	
	/**
	 * 仍持有产品类型-其他海外
	 */
	public static final String HASQTHWLABLES="23231";
	
	/**
	 * 仍持有产品类型-股票
	 */
	public static final String HASGPLABLES="23218";
	
	/**
	 * 仍持有产品类型-股权
	 */
	public static final String HASGQLABLES="23220";
	
	/**
	 * 仍持有产品类型-对冲
	 */
	public static final String HASDCLABLES="23222";
	
	/**
	 * 仍持有产品类型-债券
	 */
	public static final String HASZQLABLES="23224";
	
	/**
	 * 仍持有产品类型-固收
	 */
	public static final String HASGSLABLES="23226";
	
	/**
	 * 仍持有产品类型-房地产
	 */
	public static final String HASFDCLABLES="23228";
	
	/**
	 * 仍持有产品类型-现金管理
	 */
	public static final String HASXJLABLES="23230";
	
	/**
	 * 仍持有产品类型-其他
	 */
	public static final String HASQTLABLES="23232";
	
	/**
	 * 是否一手分配客户标签
	 */
	public static final String FIRASSIGNLABEL="23233";
	
	/**
	 * 是否不是一手分配客户标签
	 */
	public static final String NOTFIRASSIGNLABEL="23234";

	/**
	 * 高端0存量用户270天内
	 */
	public static final String GDLCLN9MLABLES="23237";

	/**
	 * 高端0存量NEW  旧的标签大数据有些历史问题, 所以建了个新的
	 */
	public static final String GDLCLNEWLABEL="23245";
	/**
	 * 高端存量
	 */
	public static final String GDCLLABEL="23244";
	/**
	 * 零售存量1W+
	 */
	public static final String LSCLLABEL="23246";


	/**
	 * 标签-是
	 */
	public static final String CRM_LABEL_YES = "1";

	/**
	 * 标签-否
	 */
	public static final String CRM_LABEL_NO = "0";
	
	/**
	 * 打款短信
	 */
	public static final String MSG_TYPE_PAY = "1";
	
	/**
	 * 成交短信
	 */
	public static final String MSG_TYPE_TRADE = "2";
	
	/**
	 * 赎回短信
	 */
	public static final String MSG_TYPE_SALE = "3";
	
	/**
	 * 直销产品到期短信
	 */
	public static final String MSG_TYPE_ZXDQ = "4";
	
	/**
	 * 代销产品到期提前通知
	 */
	public static final String MSG_TYPE_DXDQ = "5";
	
	/**
	 * 直销固收产品分红
	 */
	public static final String MSG_TYPE_ZXGSFH = "6";
	
	/**
	 * 直销非固收产品分红
	 */
	public static final String MSG_TYPE_ZXFGSFH = "7";
	
	/**
	 * 代销固收产品分红
	 */
	public static final String MSG_TYPE_DXGSFH = "8";
	
	/**
	 * 打款短信模板id
	 */
	public static final String MSG_BID_PAY = "20165";
	
	/**
	 * 成交短信模板id
	 */
	public static final String MSG_BID_TRADE = "20166";


	/**
	 * 成交短信模板id
	 * 好甄分销渠道
	 */
	public static final String MSG_BID_TRADE_HZ = "60499";

	/**
	 * 成交短信模板id
	 * 香港(海外)分销渠道
	 */
	public static final String MSG_BID_TRADE_HW = "60500";


	
	/**
	 * 赎回短信模板id
	 */
	public static final String MSG_BID_SALE = "20167";

	/**
	 * 赎回短信模板id
	 * 好整渠道
	 */
	public static final String MSG_BID_SALE_HZ = "60505";


	/**
	 * 赎回短信模板id
	 * 海外渠道
	 */
	public static final String MSG_BID_SALE_HW = "60506";
	
	/**
	 * 直销产品到期短信模板id
	 */
	public static final String MSG_BID_ZXDQ = "20168";

	/**
	 * 直销产品到期短信模板id
	 * 好甄
	 */
	public static final String MSG_BID_ZXDQ_HZ = "60509";


	/**
	 * 直销产品到期短信模板id
	 * 海外
	 */
	public static final String MSG_BID_ZXDQ_HW = "60510";
	
	/**
	 * 代销产品到期提前通知模板id
	 */
	public static final String MSG_BID_DXDQ = "20169";
	
	/**
	 * 直销固收产品分红模板id
	 */
	public static final String MSG_BID_ZXGSFH = "20170";
	
	/**
	 * 直销非固收产品分红模板id
	 */
	public static final String MSG_BID_ZXFGSFH = "20171";
	
	/**
	 * 代销固收产品分红模板id
	 */
	public static final String MSG_BID_DXGSFH = "20172";
	
	public static final String MSG_SEND_SUCCESS = "1";
	
	/**
	 * 打款短信模板idnew add by haibo.yu 10.2
	 */
	public static final String MSG_BID_PAY_NEW = "60382";

	/**
	 * 打款短信模板
	 * 好甄分销渠道
	 */
	public static final String MSG_BID_PAY_NEW_HZ = "60493";

	/**
	 * 打款短信模板
	 * 香港(海外)分销渠道
	 */
//	public static final String MSG_BID_PAY_NEW_HW = "60494";


	
	/**
	 * 针对销售类型 = 代销 或 直转代不在黑名单 且 是外扣（取订单） 的预约打款短信模板
	 * 好买分销渠道
	 */
	public static final String MSG_BID_PAY_DX = "60396";

	/**
	 * 针对销售类型 = 代销 或 直转代不在黑名单 且 是外扣（取订单） 的预约打款短信模板
	 * 好甄分销渠道
	 */
	public static final String MSG_BID_PAY_DX_HZ = "60491";

	/**
	 * 针对销售类型 = 代销 或 直转代不在黑名单 且 是外扣（取订单） 的预约打款短信模板
	 * 香港HK分销渠道
	 */
//	public static final String MSG_BID_PAY_DX_HX = "60492";
	
	/**
	 * 协议邮件-投资者账号通知
	 */
	public static final String MSG_INVEST_ACCOUNT_NOTIFY = "60384";

	/**
	 * 协议邮件-初始密码变更
	 */
	public static final String MSG_INIT_PASSWORD_CHANGE = "60385";

	/**
	 * 发送消息类型：文本
	 */
	public static final String TEMP_TEXT_TEXT = "text";

	/**
	 * 发送消息类型：文本卡片
	 */
	public static final String TEMP_TEXT_CARD = "textcard";


	/**
	 * 发送状态 0:待发送,1:已发送,2:取消,3:推送中 4:发送失败
	 */
	public static final String PUSH_MSG_FLAG_WAIT = "0";
	public static final String PUSH_MSG_FLAG_ALREADY = "1";
	public static final String PUSH_MSG_FLAG_CANCEL = "2";
	public static final String PUSH_MSG_FLAG_ING = "3";
	public static final String PUSH_MSG_FLAG_FAIL = "4";

	/** 处理脱敏任务时出错后的最大尝试次数 */
	public static final int ENCY_ERROR_MAX_TRY_TIMES = 5;

	/** 是否签到 0: 未签到 1: 已签到 */
	public static final int NO_CHECK = 0;


	public static final int IS_CHECK = 1;

	/** 是否已处理 0: 未处理 1: 已处理 */
	public static final int NO_DEAL = 0;


	public static final int IS_DEAL = 1;

	/**
	 * 日期有效时间14天
	 */
	public static final int VALITDATE_TIME = 14;

	public static final String LCJZSIGNDATA_TEMPLATE = "LCJZ_CHECK_IN";

	/**-- 交易类型( 1：购买，2：追加，3：赎回) */
	public static final String TRADE_TYPE_BUY = "1";
	public static final String TRADE_TYPE_ADD = "2";

	/**
	 * 产品类型：1为代销，2为直销，3为直转代
	 */
	public static final String PRODUCT_TYPE_DX = "1";
	public static final String PRODUCT_TYPE_ZX = "2";
	public static final String PRODUCT_TYPE_ZZD = "3";

	/**
	 * 消息类型：1:文本消息，2：卡片消息：3：群发消息
	 */
	public static final String MESSAGE_TYPE_TEXT = "1";
	public static final String MESSAGE_TYPE_CARD = "2";
	public static final String MESSAGE_TYPE_GROUP = "3";
}

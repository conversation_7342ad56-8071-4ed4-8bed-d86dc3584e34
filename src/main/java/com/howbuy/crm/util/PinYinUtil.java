package com.howbuy.crm.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/7/29 18:53
 */
public class PinYinUtil {

    /** (一个)中文 正则表达式 */
    private static final String CHINESE_REGEX = "[\u4e00-\u9fa5]";

    public static void main(String[] a){
        int i = 0;
        long time = System.currentTimeMillis();
        while (i < 100){
            System.out.println(getHanYuPinYin("张李王杨陈高梁吴刘" + i + "张三"));
            System.out.println(getHanYuPinYin("建涛磊艳婷海波健园" + i + "张三"));
            i++;
        }
        System.out.println(System.currentTimeMillis() - time);
    }

    public static String getHanYuPinYin(String originChinese){
        char[] chineseCharArray = originChinese.trim().toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        // 不带声调
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        // 输出拼音全部小写(默认即为小写)
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        /**
         * 含ü的字有 【女】【吕】【略】【虐】等
         *
         * WITH_V            设置  【ü】 转换为 【v】 ,           如:【女】 转换后为 【nv】
         * WITH_U_AND_COLON  设置  【ü】 转换为 【u:】,           如:【女】 转换后为 【nu:】
         * WITH_U_UNICODE    设置  【ü】 转换为 【ü】,即:原输出,   如:【女】 转换后为 【nü】
         */
        defaultFormat.setVCharType(HanyuPinyinVCharType.WITH_V);
        StringBuilder hanYuPinYinResult = new StringBuilder();
        boolean isChinese;
        String tempStr = null;
        for (char cStr : chineseCharArray) {
            isChinese = String.valueOf(cStr).matches(CHINESE_REGEX);
            // 如果字符是中文,则将中文转为汉语拼音
            if (isChinese) {
                try {
                    tempStr = PinyinHelper.toHanyuPinyinStringArray(cStr, defaultFormat)[0];
                } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
                    badHanyuPinyinOutputFormatCombination.printStackTrace();
                }
                hanYuPinYinResult.append(tempStr);
            } else {
                // 如果字符不是中文,则不转换
                hanYuPinYinResult.append(cStr);
            }
        }
        return hanYuPinYinResult.toString();
    }
}

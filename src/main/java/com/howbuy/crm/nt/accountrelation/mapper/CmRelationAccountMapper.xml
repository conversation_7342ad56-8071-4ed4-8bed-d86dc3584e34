<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.accountrelation.dao.CmRelationAccountMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount">
    <result column="HBONENO" jdbcType="VARCHAR" property="hboneno" />
    <result column="RELATIONNAME" jdbcType="VARCHAR" property="relationname" />
    <result column="RELATIONTYPE" jdbcType="VARCHAR" property="relationtype" />
    <result column="RELATIONID" jdbcType="VARCHAR" property="relationid" />
    <result column="ORDERID" jdbcType="VARCHAR" property="orderid" />
    <result column="CREATDT" jdbcType="TIMESTAMP" property="creatdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="SYNCDT" jdbcType="TIMESTAMP" property="syncdt" />
    <result column="RELATIONSTATE" jdbcType="VARCHAR" property="relationstate" />
    <result column="CHANEL" jdbcType="VARCHAR" property="chanel" />
  </resultMap>

  <update id="updateCmRelationAccount" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount">
    MERGE INTO CM_RELATION_ACCOUNT t
    using (select #{relationid} as relationid from dual) t1
    on (t.relationid = t1.relationid)
    WHEN matched THEN
    update
    <set>
      <if test="orderid!=null">ORDERID = #{orderid},</if>
      <if test="relationstate != null">RELATIONSTATE=#{relationstate},</if>
    </set>
    where t.relationid = #{relationid}
  </update>

  <update id="updateCmRelationAccountByorder" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount">
    MERGE INTO CM_RELATION_ACCOUNT t
    using (select #{orderid} as orderid from dual) t1
    on (t.orderid = t1.orderid)
    WHEN matched THEN
    update
    <set>
      <if test="relationid!=null">RELATIONID = #{relationid},</if>
      <if test="relationname!=null">RELATIONNAME = #{relationname},</if>
      <if test="relationstate != null">RELATIONSTATE=#{relationstate},</if>
    </set>
    where t.ORDERID = #{orderid}
  </update>

  <insert id="insertCmRelationAccount" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount">
    insert into CM_RELATION_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hboneno != null">
        HBONENO,
      </if>
      <if test="relationname != null">
        RELATIONNAME,
      </if>
      <if test="relationtype != null">
        RELATIONTYPE,
      </if>
      <if test="relationid != null">
        RELATIONID,
      </if>
      <if test="orderid != null">
        ORDERID,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="creater != null">
        CREATER,
      </if>
      <if test="syncdt != null">
        SYNCDT,
      </if>
      <if test="relationstate != null">
        RELATIONSTATE,
      </if>
      <if test="chanel != null">
        CHANEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hboneno != null">
        #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="relationname != null">
        #{relationname,jdbcType=VARCHAR},
      </if>
      <if test="relationtype != null">
        #{relationtype,jdbcType=VARCHAR},
      </if>
      <if test="relationid != null">
        #{relationid,jdbcType=VARCHAR},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="syncdt != null">
        #{syncdt,jdbcType=TIMESTAMP},
      </if>
      <if test="relationstate != null">
        #{relationstate,jdbcType=VARCHAR},
      </if>
      <if test="chanel != null">
        #{chanel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="listRelationAccountByPage" parameterType="Map"	resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount" useCache="false">
    SELECT T.HBONENO,
       T.RELATIONID,
       T.RELATIONNAME,
       T.RELATIONTYPE,
       T.CREATDT,
       T.RELATIONSTATE,
       T.ORDERID,
       C.CONSCUSTNO,
       C.CUSTNAME,
       C2.CONSNAME,
       C2.CONSCODE,
       H.ORGCODE,
        decode(T.chanel,'9','CRM','APP')  as chanel
  FROM CM_RELATION_ACCOUNT T
  LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
  LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
  LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
  LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE 1=1 AND T.RELATIONSTATE IN ('0','2','4')
    <if test="param.teamCode != null">
      AND C2.conscode in (${param.teamCode})
    </if>
    <if test="param.consCode != null">
      AND C2.conscode = #{param.consCode}
    </if>
    <if test="param.relatedAccount != null">
      AND T.RELATIONTYPE = #{param.relatedAccount}
    </if>
    <if test="param.conscustno != null">
      AND C.CONSCUSTNO = #{param.conscustno}
    </if>
    <if test="param.custname != null">
      AND C.CUSTNAME like '%'||#{param.custname ,jdbcType=VARCHAR}||'%'
    </if>
    ORDER BY T.CREATDT DESC
  </select>

  <select id="listRelationAccount" parameterType="Map"	resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount" useCache="false">
    SELECT T.HBONENO,
    T.RELATIONID,
    T.RELATIONNAME,
    T.RELATIONTYPE,
    T.CREATDT,
    T.RELATIONSTATE,
    T.ORDERID,
    C.CONSCUSTNO,
    C.CUSTNAME,
    C2.CONSNAME,
    C2.CONSCODE,
    H.ORGCODE
    FROM CM_RELATION_ACCOUNT T
    LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
    LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
    LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
    LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE 1=1 AND T.RELATIONSTATE IN ('0','2','4')
    <if test="hboneno != null">
      AND T.HBONENO = #{hboneno}
    </if>
    <if test="relationtype != null">
      AND T.RELATIONTYPE = #{relationtype}
    </if>
  </select>

  <select id="getRelationAccount" parameterType="Map"	resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount" useCache="false">
    SELECT T.HBONENO,
    T.RELATIONID,
    T.RELATIONNAME,
    T.RELATIONTYPE,
    T.CREATDT,
    T.RELATIONSTATE,
    T.ORDERID,
    C.CONSCUSTNO,
    C.CUSTNAME,
    C2.CONSNAME,
    C2.CONSCODE,
    H.ORGCODE
    FROM CM_RELATION_ACCOUNT T
    LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
    LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
    LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
    LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE 1=1
    AND T.RELATIONID = #{relationid}
  </select>

  <select id="getRelationAccountByOrderid" parameterType="Map"	resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount" useCache="false">
    SELECT T.HBONENO,
    T.RELATIONID,
    T.RELATIONNAME,
    T.RELATIONTYPE,
    T.CREATDT,
    T.RELATIONSTATE,
    T.ORDERID,
    C.CONSCUSTNO,
    C.CUSTNAME,
    C2.CONSNAME,
    C2.CONSCODE,
    H.ORGCODE
    FROM CM_RELATION_ACCOUNT T
    LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
    LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
    LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
    LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE 1=1
    AND T.ORDERID = #{orderid}
  </select>

  <update id="updateCmRelationAccountStatus" parameterType="hashmap">
        update CM_RELATION_ACCOUNT a
        set a.relationstate = #{updateStatus}
        <where>
        and a.relationstate = #{oldStatus}
        and a.orderid = #{orderId}
        </where>
    </update>

</mapper>
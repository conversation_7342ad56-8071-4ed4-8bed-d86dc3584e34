package com.howbuy.crm.nt.accountrelation.dao;

import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmRelationAccountMapper {

	/**
	 * 插入
	 * @param record
	 * @return
	 */
    int insertCmRelationAccount(CmRelationAccount record);

    /**
     * 更新
     * @param record
     */
    void updateCmRelationAccount(CmRelationAccount record);

    /**
     * 更新
     * @param record
     */
    void updateCmRelationAccountByorder(CmRelationAccount record);
    /**
     * listRelationAccountByPage
     * @param param
     * @param pageBean
     * @return java.util.List<com.howbuy.crm.hb.domain.relation.CmRelationAccount>
     * @Author: yu.zhang on 2021/7/13 9:40
     */
    List<CmRelationAccount> listRelationAccountByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询列表
     * @param param
     * @return
     */
    List<CmRelationAccount> listRelationAccount(Map<String, Object> param);

    /**
     * 获取单个
     * @param relationid
     * @return
     */
    CmRelationAccount getRelationAccount(String relationid);

    /**
     * 获取单个对象
     * @param relationid
     * @return
     */
    CmRelationAccount getRelationAccountByOrderid(String relationid);

    /**
     * 修改关联账户数据状态
     * @param orderId  关联账户标识Id
     * @param oldStatus 老的状态 where条件
     * @param updateStatus 新的状态   set 使用
     */
    int updateCmRelationAccountStatus(@Param("orderId") String orderId , @Param("oldStatus") String  oldStatus, @Param("updateStatus") String updateStatus);
}
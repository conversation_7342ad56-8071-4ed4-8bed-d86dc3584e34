<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.accountrelation.dao.CmRelationAccountSubMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub">
    <result column="HBONENO" jdbcType="VARCHAR" property="hboneno" />
    <result column="RELATION" jdbcType="VARCHAR" property="relation" />
    <result column="RELATIONID" jdbcType="VARCHAR" property="relationid" />
    <result column="SUBRELATIONID" jdbcType="VARCHAR" property="subrelationid" />
    <result column="RELATIONDT" jdbcType="TIMESTAMP" property="relationdt" />
    <result column="ORDERID" jdbcType="VARCHAR" property="orderid" />
    <result column="CREATDT" jdbcType="TIMESTAMP" property="creatdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="SYNCDT" jdbcType="TIMESTAMP" property="syncdt" />
    <result column="RELATIONSTATE" jdbcType="VARCHAR" property="relationstate" />
    <result column="CHANEL" jdbcType="VARCHAR" property="chanel" />
  </resultMap>

  <update id="addCmRelationAccountSub" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub">
    MERGE INTO CM_RELATION_ACCOUNT_SUB t
    using (select #{relationid} as relationid,#{hboneno} as hboneno from dual) t1
    on (t.relationid = t1.relationid and t.hboneno = t1.hboneno)
    WHEN matched THEN
    update
    <set>
      <if test="orderid!=null">ORDERID = #{orderid},</if>
      <if test="relation!=null">RELATION = #{relation},</if>
      <if test="subrelationid!=null">SUBRELATIONID = #{subrelationid},</if>
      <if test="relationstate != null">RELATIONSTATE=#{relationstate},</if>
    </set>
    where t.relationid = #{relationid} and t.hboneno = #{hboneno}
  </update>

  <update id="removeCmRelationAccountSub" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub" >
    UPDATE CM_RELATION_ACCOUNT_SUB
    <set>
      <if test="orderid!=null">ORDERID = #{orderid},</if>
      <if test="relationstate != null">RELATIONSTATE = #{relationstate},</if>
    </set>
    where RELATIONID = #{relationid}
  </update>



  <update id="updateCmRelationAccountSubByorder" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub">
    MERGE INTO CM_RELATION_ACCOUNT_SUB t
    using (select #{orderid} as orderid,#{hboneno} as hboneno from dual) t1
    on (t.orderid = t1.orderid and t.hboneno = t1.hboneno)
    WHEN matched THEN
    update
    <set>
      <if test="relationid!=null">RELATIONID = #{relationid},</if>
      <if test="subrelationid!=null">SUBRELATIONID = #{subrelationid},</if>
      <if test="relationstate != null">RELATIONSTATE=#{relationstate},</if>
    </set>
    where t.ORDERID = #{orderid} and t.hboneno = #{hboneno}
  </update>

  <insert id="insertCmRelationAccountSub" parameterType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub">
    insert into CM_RELATION_ACCOUNT_SUB
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="hboneno != null">
        HBONENO,
      </if>
      <if test="relation != null">
        RELATION,
      </if>
      <if test="relationid != null">
        RELATIONID,
      </if>
      <if test="relationdt != null">
        RELATIONDT,
      </if>
      <if test="orderid != null">
        ORDERID,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="creater != null">
        CREATER,
      </if>
      <if test="syncdt != null">
        SYNCDT,
      </if>
      <if test="relationstate != null">
        RELATIONSTATE,
      </if>
      <if test="chanel != null">
        CHANEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="hboneno != null">
        #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=VARCHAR},
      </if>
      <if test="relationid != null">
        #{relationid,jdbcType=VARCHAR},
      </if>
      <if test="relationdt != null">
        #{relationdt,jdbcType=TIMESTAMP},
      </if>
      <if test="orderid != null">
        #{orderid,jdbcType=VARCHAR},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=TIMESTAMP},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="syncdt != null">
        #{syncdt,jdbcType=TIMESTAMP},
      </if>
      <if test="relationstate != null">
        #{relationstate,jdbcType=VARCHAR},
      </if>
      <if test="chanel != null">
        #{chanel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>


  <select id="listRelationAccountSub" parameterType="Map" resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub" useCache="false">
    SELECT T.HBONENO,
       T.RELATIONID,
       T.SUBRELATIONID,
       T.RELATION,
       T.CREATDT,
       T.RELATIONDT,
       T.RELATIONSTATE,
       C.CONSCUSTNO,
       C.CUSTNAME,
       C2.CONSNAME,
       C2.CONSCODE,
       H.ORGCODE,
    decode(T.chanel,'9','CRM','APP')  as chanel
  FROM CM_RELATION_ACCOUNT_SUB T
  LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
  LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
  LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
  LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE T.RELATIONSTATE IN ('0','2','4')
    <if test="relationid != null">
      AND T.RELATIONID = #{relationid}
    </if>
    <if test="hboneno != null">
      AND T.HBONENO = #{hboneno}
    </if>
    <if test="orderid != null">
      AND T.ORDERID = #{orderid}
    </if>
    ORDER BY T.CREATDT DESC
  </select>

  <select id="getRelationAccountSub" parameterType="Map" resultType="com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub" useCache="false">
    SELECT T.HBONENO,
    T.RELATIONID,
    T.RELATION,
    T.SUBRELATIONID,
    T.CREATDT,
    T.RELATIONSTATE,
    C.CONSCUSTNO,
    C.CUSTNAME,
    C2.CONSNAME,
    C2.CONSCODE,
    H.ORGCODE
    FROM CM_RELATION_ACCOUNT_SUB T
    LEFT JOIN cm_conscust C
    ON T.HBONENO = C.HBONE_NO
    LEFT JOIN CM_custconstant C1
    ON C.CONSCUSTNO = C1.CUSTNO
    LEFT JOIN CM_CONSULTANT C2
    ON C2.CONSCODE = C1.CONSCODE
    LEFT JOIN hb_organization H
    ON H.ORGCODE = C2.OUTLETCODE
    WHERE T.RELATIONSTATE IN ('0','2','4')
    AND T.ORDERID = #{orderid}
  </select>

  
  <update id="updateCmRelationAccountSubStatus" parameterType="hashmap">
        update CM_RELATION_ACCOUNT_SUB a
        set a.relationstate = #{updateStatus}
        <where>
        and a.relationstate = #{oldStatus}
        and a.orderid = #{orderId}
       </where>
    </update>
</mapper>
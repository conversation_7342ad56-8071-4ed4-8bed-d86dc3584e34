package com.howbuy.crm.nt.accountrelation.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acccenter.common.enums.RelatedAccountRoleEnum;
import com.howbuy.acccenter.common.enums.RelatedAccountTypeEnum;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountFacade;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountRequest;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountResponse;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.RelatedAccountBean;
import com.howbuy.acccenter.facade.query.queryrelatedaccount.bean.SubRelatedAccountBean;
import com.howbuy.acccenter.facade.trade.relatedaccount.*;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.nt.accountrelation.dao.CmRelationAccountMapper;
import com.howbuy.crm.nt.accountrelation.dao.CmRelationAccountSubMapper;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub;
import com.howbuy.crm.nt.accountrelation.enums.RelationAccountStateEnum;
import com.howbuy.crm.nt.accountrelation.enums.RelationTypeEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @classname: CmRelationAccountServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-07-12 9:52
 * @since: JDK1.8
 */
@Slf4j
@Service(value="cmRelationAccountService")
@Transactional(rollbackFor = Exception.class)
public class CmRelationAccountServiceImpl implements CmRelationAccountService {

    @Autowired
    private CmRelationAccountMapper cmRelationAccountMapper;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private CmRelationAccountSubService cmRelationAccountSubService;

    @Autowired
    private CreateRelatedAccountFacade createRelatedAccountFacade;

    @Autowired
    private AddSubRelatedAccountFacade addSubRelatedAccountFacade;

    @Autowired
    private CloseSubRelatedAccountFacade closeSubRelatedAccountFacade;

    @Autowired
    private CloseRelatedAccountFacade closeRelatedAccountFacade;

    @Autowired
    private QueryRelatedAccountFacade queryRelatedAccountFacade;

    @Autowired
    private CmRelationAccountSubMapper cmRelationAccountSubMapper;


    public static final String RMISuccNew="0000000";
    public static final String RMISucc="0000";

    public static final String RMIError="9999";
    /** 子流程失败失败 */
    public static final String RMISUBError="0101";

    /**
     * 创建主账户
     * insertCmRelationAccount
     * @param conscustno 客户号
     * @param relationTypeEnum  NOT NULL 类型：-0-家庭  1-个人 2-机构
     * @param subconscustno 子账户客户号
     * @param relation
     * @param crmRelationId crm数据的唯一主键[区别与账户中心更新的  relationId ] . 表命名为 order_id . 但是作为 关联账户的业务的唯一主键。 该主键作为forId 与 资料管理关联 。
     * @param userid
     * @return void
     * @Author: yu.zhang on 2021/7/28 10:52
     */
    @Override
    public void insertCmRelationAccount(String conscustno, RelationTypeEnum relationTypeEnum, String subconscustno, String relation, String crmRelationId, String userid) {

        //修改一账通和投顾客户关系表
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();
        log.info("insertCmRelationAccount queryResponse:"+ JSON.toJSON(queryResponse));

        CmRelationAccount record = new CmRelationAccount();
        record.setHboneno(conscust.getHboneno());
        record.setRelationname(conscust.getCustname()+"的关联账户");
        record.setRelationtype(relationTypeEnum.getKey());
        record.setRelationid("C"+crmRelationId);
        record.setOrderid(crmRelationId);
        record.setRelationstate(StaticVar.RELATION_CRM_ADD);
        record.setCreater(userid);
        record.setChanel("9");
        this.insertCmRelationAccount(record);

        cmRelationAccountSubService.insertCmRelationAccount(record.getRelationid(), subconscustno, relation, crmRelationId, userid);
    }

    /**
     * 订单存在主账户,则进行创建操作，订单不存在主账户，则进行添加操作
     * chooseCreateRelationDeal
     * @param orderid
     * @return java.lang.String
     * @Author: yu.zhang on 2021/7/20 16:04
     */
    @Override
    public NtReturnMessageDto<String> chooseCreateRelationDeal(String orderid){

        CmRelationAccount relationaccount = cmRelationAccountMapper.getRelationAccountByOrderid(orderid);
        if(relationaccount != null ){
            return createRelatedAccount(relationaccount);
        }
        //根据订单查询主订单数据
        CmRelationAccountSub accountsub = cmRelationAccountSubService.getRelationAccountSub(orderid);
        //根据订单查询主订单数据
        return this.addSubRelatedAccount(orderid,accountsub.getRelationid(),accountsub);
    }




    /**
     * 创建家庭客户并创建子账户
     * createRelatedAccount
     * @param relationAccount
     * @return java.lang.String
     * @Author: yu.zhang on 2021/7/20 16:10
     */
    public NtReturnMessageDto<String> createRelatedAccount(CmRelationAccount relationAccount){

        String hbonNo=relationAccount.getHboneno();
        String relationType=relationAccount.getRelationtype();
        String orderId=relationAccount.getOrderid();

        CreateRelatedAccountRequest req = new CreateRelatedAccountRequest();
        req.setRelatedAccountName(relationAccount.getRelationname());
        req.setHboneNo(hbonNo);
        req.setTradeChannel("9");
        req.setOutletCode("CRM");
        req.setRelatedAccountType(RelatedAccountTypeEnum.getValue(relationAccount.getRelationtype()));

        //创建关联账户
        log.info("createRelatedAccount CreateRelatedAccountRequest:"+ JSON.toJSON(req));
        CreateRelatedAccountResponse rep = createRelatedAccountFacade.execute(req);
        log.info("createRelatedAccount CreateRelatedAccountResponse:"+ JSON.toJSON(rep));

        if(RMISuccNew.equals(rep.getReturnCode())){
            String accRelatedAccountId=rep.getRelatedAccountId();

            //更新订单的  relationId  . 账户中心 提供的 .    该笔关系状态--> s生效
            CmRelationAccount record = new CmRelationAccount();
            record.setOrderid(orderId);
            record.setRelationid(accRelatedAccountId);
            record.setRelationstate(RelationAccountStateEnum.AFFECTED.getKey());
            log.info("orderId:{}订单中的客户hboneNo:{},查询账户中心接口，返回relationId:{},更新crm关联数据。更新对象：{}",
                    orderId,hbonNo,accRelatedAccountId, JSONObject.toJSONString(record));
            cmRelationAccountMapper.updateCmRelationAccountByorder(record);

            //根据订单查询主订单数据
            CmRelationAccountSub accountsub = cmRelationAccountSubService.getRelationAccountSub(orderId);
            //处理单个辅账户，根据订单查询处理
            return addSubRelatedAccount(orderId,accRelatedAccountId,accountsub);
        }

        //已存在家庭账户,更新主账户关联关系，以及子账户关系
        QueryRelatedAccountRequest relationreq = new QueryRelatedAccountRequest();
        relationreq.setHboneNo(hbonNo);
        relationreq.setRelatedAccountType(RelatedAccountTypeEnum.getValue(relationType));
        log.info("QueryRelatedAccountRequest:"+ JSON.toJSON(relationreq));
        QueryRelatedAccountResponse relationrep = queryRelatedAccountFacade.execute(relationreq);
        log.info("QueryRelatedAccountResponse:"+ JSON.toJSON(relationrep));

        if(!RMISuccNew.equals(relationrep.getReturnCode())){
            return NtReturnMessageDto.fail(relationrep.getDescription());
        }

        List<RelatedAccountBean> relationaccounts = relationrep.getRelatedAccountBeanList();
        if(CollectionUtils.isEmpty(relationaccounts)){
            return  NtReturnMessageDto.fail(relationrep.getDescription());
        }

        String accRelationId="";
        for(RelatedAccountBean accountbean:relationaccounts){
             accRelationId = accountbean.getRelatedAccountId();
            //根据订单更新主账户关联ID以及关系
            CmRelationAccount record = new CmRelationAccount();
            record.setOrderid(orderId);
            record.setRelationid(accRelationId);
            record.setRelationstate(StaticVar.RELATION_ADD);
            record.setRelationname(accountbean.getRelatedAccountName());
            log.info("orderId:{}订单中的客户hboneNo:{},查询账户中心接口，返回relationId:{},更新crm关联数据。更新对象：{}",
                    orderId,hbonNo,accRelationId, JSONObject.toJSONString(record));
            cmRelationAccountMapper.updateCmRelationAccountByorder(record);

            //一账户中心全量数据为准，进行更新与新增操作，CRM测子订单数据稍后处理
            //relatedAccountBeanList:[{"relatedAccountType":"PERSONAL_1","relatedAccountName":"资产一的关联账户","relatedAccountId":"317340142647803904","hboneNo":"**********"}]
            List<SubRelatedAccountBean> subrelations = accountbean.getSubRelatedAccountList();
            if(subrelations != null && subrelations.size() > 0){
                for(SubRelatedAccountBean subrelation:subrelations){

                    CmRelationAccountSub subrecord = new CmRelationAccountSub();
                    subrecord.setRelationid(subrelation.getRelatedAccountId());
                    subrecord.setRelationstate(StaticVar.RELATION_ADD);
                    subrecord.setHboneno(subrelation.getSubHboneNo());
                    subrecord.setRelation(subrelation.getRelatedAccountRole().getValue());
                    subrecord.setSubrelationid(subrelation.getSubRelatedAccountId());
                    cmRelationAccountSubService.addCmRelationAccountSub(subrecord);
                }
            }
        }

        //根据订单查询主订单数据
        CmRelationAccountSub accountsub = cmRelationAccountSubService.getRelationAccountSub(orderId);

        //处理子账户
        return   addSubRelatedAccount(orderId,accRelationId,accountsub);
    }

    /**
     * 新增辅账户关联
     * AddSubRelatedAccount
     * @param orderid
     * @return void
     * @Author: yu.zhang on 2021/7/19 16:13
     */
    public NtReturnMessageDto<String> addSubRelatedAccount(String orderid,String reationid,CmRelationAccountSub accountsub){

        AddSubRelatedAccountRequest req = new AddSubRelatedAccountRequest();
        req.setRelatedAccountId(reationid);
        req.setSubHboneNo(accountsub.getHboneno());
        req.setRelatedAccountRole(RelatedAccountRoleEnum.getValue(accountsub.getRelation()));
        req.setTradeChannel("9");
        req.setOutletCode("CRM");

        //创建关联账户
        log.info("addSubRelatedAccount AddSubRelatedAccountRequest:"+ JSON.toJSON(req));
        AddSubRelatedAccountResponse rep = addSubRelatedAccountFacade.execute(req);
        log.info("addSubRelatedAccount AddSubRelatedAccountResponse:"+ JSON.toJSON(req));

        if(!RMISuccNew.equals(rep.getReturnCode())){
            return NtReturnMessageDto.fail(rep.getDescription());
        }
        //根据订单ID和辅账户一账通，修改辅账户关联ID等信息
        cmRelationAccountSubService.updateCmRelationAccountSubByorder(orderid,accountsub.getHboneno(),reationid,rep.getSubRelatedAccountId(),StaticVar.RELATION_ADD);
        return NtReturnMessageDto.ok();
    }

    @Override
    public NtReturnMessageDto<String>  closeSubRelatedAccount(String orderid){

        String result = RMISucc;

        //根据订单查询主订单数据
        CmRelationAccountSub accountsub = cmRelationAccountSubService.getRelationAccountSub(orderid);

        CloseSubRelatedAccountRequest req = new CloseSubRelatedAccountRequest();
        req.setRelatedAccountId(accountsub.getRelationid());
        req.setSubRelatedAccountId(accountsub.getSubrelationid());
        req.setTradeChannel("9");
        req.setOutletCode("CRM");
        //创建关联账户
        log.info("closeSubRelatedAccount CloseSubRelatedAccountRequest:"+ JSON.toJSON(req));
        CloseSubRelatedAccountResponse rep = closeSubRelatedAccountFacade.execute(req);
        log.info("closeSubRelatedAccount CloseSubRelatedAccountRequest:"+ JSON.toJSON(req));

        if(!RMISuccNew.equals(rep.getReturnCode())){
            return NtReturnMessageDto.fail(rep.getDescription());
        }
        //修改CRM侧关联账户id
        cmRelationAccountSubService.updateCmRelationAccountSubByorder(orderid,accountsub.getHboneno(),accountsub.getRelationid(),accountsub.getSubrelationid(),StaticVar.RELATION_REMOVE);
        return NtReturnMessageDto.ok();
    }

    /**
     * 解除生效家庭账户（存在关联主键），根据订单ID查询对应数据，调用账户中心接口解除家庭账户之后，修改关联关系数据
     * closeRelatedAccount
     * @param orderid
     * @return void
     * @Author: yu.zhang on 2021/7/20 15:21
     */
    @Override
    public NtReturnMessageDto<String> closeRelatedAccount(String orderid){
        //根据订单查询主订单数据
        CmRelationAccount account = cmRelationAccountMapper.getRelationAccountByOrderid(orderid);

        CloseRelatedAccountRequest req = new CloseRelatedAccountRequest();
        req.setRelatedAccountId(account.getRelationid());
        req.setTradeChannel("9");
        req.setOutletCode("CRM");
        //创建关联账户
        log.info("closeRelatedAccount CloseRelatedAccountRequest:"+ JSON.toJSON(req));
        CloseRelatedAccountResponse rep = closeRelatedAccountFacade.execute(req);
        log.info("closeRelatedAccount CloseRelatedAccountResponse:"+ JSON.toJSON(req));


        if(!RMISuccNew.equals(rep.getReturnCode())){
            return NtReturnMessageDto.fail(rep.getDescription());
        }
        //修改CRM侧关联账户id
        removeCmRelationAccountByorder(orderid,account.getRelationid(),StaticVar.RELATION_REMOVE);
        return NtReturnMessageDto.ok();
    }

    /**
     * insertCmRelationAccount
     * @param record
     * @return int
     * @Author: yu.zhang on 2021/7/28 10:52
     */
    @Override
    public int insertCmRelationAccount(CmRelationAccount record) {
        return cmRelationAccountMapper.insertCmRelationAccount(record);
    }

    /**
     * 解除主账户并解除子账户
     * 根据relationId --> 更新表【CM_RELATION_ACCOUNT】 orderId=参数[newCrmRelationId] 为最新的。 更新状态：解除中
     * 根据relationId --> 更新表【CM_RELATION_ACCOUNT_SUB】 orderId=参数[newCrmRelationId] 为最新的。 更新状态：解除中
     * removeCmRelationAccount
     * @param relationId
     * @param newCrmRelationId【orderId】
     * @param userid
     * @return void
     * @Author: yu.zhang on 2021/7/28 10:52
     */
    @Override
    public void removeCmRelationAccount(String relationId, String newCrmRelationId, String userid) {

        CmRelationAccount record = new CmRelationAccount();
        record.setRelationid(relationId);
        record.setOrderid(newCrmRelationId);
        record.setRelationstate(RelationAccountStateEnum.BREAKING.getKey());

        cmRelationAccountMapper.updateCmRelationAccount(record);
        log.info("relationId:{} ,操作人员：{} 进行关联账户解除， 关联数据更新成 新的 id:{} ",relationId,userid,newCrmRelationId);


        CmRelationAccountSub updateSub = new CmRelationAccountSub();
        updateSub.setRelationid(relationId);
        updateSub.setOrderid(newCrmRelationId);
        updateSub.setRelationstate(RelationAccountStateEnum.BREAKING.getKey());
        cmRelationAccountSubMapper.removeCmRelationAccountSub(updateSub);
    }

    /**
     * 根据订单审核修改主账户关系和辅账户关系
     * updateCmRelationAccountByorder
     * @param orderid
     * @param relationid
     * @param relationstate
     * @return void
     * @Author: yu.zhang on 2021/7/20 15:24
     */
//    public void addCmRelationAccountByorder(String orderid,String relationid,String relationstate){

//        CmRelationAccount record = new CmRelationAccount();
//        record.setOrderid(orderid);
//        record.setRelationid(relationid);
//        record.setRelationstate(relationstate);
//        cmRelationAccountMapper.updateCmRelationAccountByorder(record);

//    }

    public void removeCmRelationAccountByorder(String orderid,String relationid,String relationstate){

        CmRelationAccount record = new CmRelationAccount();
        record.setOrderid(orderid);
        record.setRelationid(relationid);
        record.setRelationstate(relationstate);
        cmRelationAccountMapper.updateCmRelationAccountByorder(record);


        CmRelationAccountSub updateSub = new CmRelationAccountSub();
        updateSub.setRelationid(relationid);
        updateSub.setOrderid(orderid);
        updateSub.setRelationstate(relationstate);
        cmRelationAccountSubMapper.removeCmRelationAccountSub(updateSub);
    }


    @Override
    public List<CmRelationAccount> listRelationAccount(Map<String, Object> param) {
        return cmRelationAccountMapper.listRelationAccount(param);
    }

    @Override
    public CmRelationAccount getRelationAccount(String relationid) {
        return cmRelationAccountMapper.getRelationAccount(relationid);
    }

    /**
     * 修改关联账户数据状态
     * @param orderId  关联账户标识Id
     * @param oldStatus 老的状态 where条件
     * @param updateStatus 新的状态   set 使用
     */
    @Override
    public int updateCmRelationAccountStatus(String orderId,String oldStatus,String updateStatus){
    	int updateCount=cmRelationAccountMapper.updateCmRelationAccountStatus(orderId,oldStatus,updateStatus);
        log.info("更新关联账户状态，关联账户的主Id[orderId]:{},修改之前状态：{}，更新状态为：{}，更新条数：{}",orderId,oldStatus,updateStatus,updateCount);
        return updateCount;
    }
}

package com.howbuy.crm.nt.accountrelation.dao;


import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmRelationAccountSubMapper {
	/**
	 * 插入
	 * @param record
	 * @return
	 */
    int insertCmRelationAccountSub(CmRelationAccountSub record);

    /**
     * 新增
     * @param record
     */
    void addCmRelationAccountSub(CmRelationAccountSub record);

    /**
     * 移除
     * @param record
     */
    void removeCmRelationAccountSub(CmRelationAccountSub record);

    /**
     * 更新
     * @param record
     */
    void updateCmRelationAccountSubByorder(CmRelationAccountSub record);

    /**
     * 查询所有辅账户
     * @param param
     * @return
     */
    List<CmRelationAccountSub> listRelationAccountSub(Map<String, Object> param);

    /**
     * 回去辅账户
     * @param orderid
     * @return
     */
    CmRelationAccountSub getRelationAccountSub(String orderid);

    /**
     * 修改管理账户数据状态
     * @param orderId  关联账户标识Id
     * @param oldStatus 老的状态 where条件
     * @param updateStatus 新的状态   set 使用
     */
    int updateCmRelationAccountSubStatus(@Param("orderId") String orderId , @Param("oldStatus") String  oldStatus, @Param("updateStatus") String updateStatus);
}
package com.howbuy.crm.nt.accountrelation.service;

import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;

import com.howbuy.crm.nt.accountrelation.dao.CmRelationAccountSubMapper;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @classname: CmRelationAccountSubServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-07-12 9:54
 * @since: JDK1.8
 */
@Slf4j
@Service(value="cmRelationAccountSubService")
@Transactional(rollbackFor = Exception.class)
public class CmRelationAccountSubServiceImpl implements CmRelationAccountSubService {

    @Autowired
    private CmRelationAccountSubMapper cmRelationAccountSubMapper;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    /**
     * insertCmRelationAccountSub
     * @param record
     * @return int
     * @Author: yu.zhang on 2021/7/28 10:53
     */
    @Override
    public int insertCmRelationAccountSub(CmRelationAccountSub record) {
        return cmRelationAccountSubMapper.insertCmRelationAccountSub(record);
    }

    /**
     * insertCmRelationAccount
     * @param relationid
     * @param subconscustno
     * @param relation
     * @param crmRelationId crm数据的唯一主键[区别与账户中心更新的  relationId ] . 表命名为 order_id . 但是作为 关联账户的业务的唯一主键。 该主键作为forId 与 资料管理关联 。
     * @param userid
     * @return void
     * @Author: yu.zhang on 2021/7/28 10:54
     */
    @Override
    public void insertCmRelationAccount(String relationid,String subconscustno,String relation,String crmRelationId, String userid) {

        //修改一账通和投顾客户关系表
        QueryConscustInfoRequest subqueryRequest = new QueryConscustInfoRequest();
        subqueryRequest.setConscustno(subconscustno);
        QueryConscustInfoResponse subqueryResponse = queryConscustInfoService.queryConscustInfo(subqueryRequest);
        ConscustInfoDomain subconscust = subqueryResponse.getConscustinfo();

        CmRelationAccountSub subrecord = new CmRelationAccountSub();
        subrecord.setHboneno(subconscust.getHboneno());
        subrecord.setRelation(relation);
        subrecord.setRelationid(relationid);
        subrecord.setOrderid(crmRelationId);
        subrecord.setRelationstate(StaticVar.RELATION_CRM_ADD);
        subrecord.setCreater(userid);
        subrecord.setChanel("9");
        this.insertCmRelationAccountSub(subrecord);
    }

    @Override
    public void addCmRelationAccountSub(CmRelationAccountSub record) {
        cmRelationAccountSubMapper.addCmRelationAccountSub(record);
    }

    /**
     * 根据订单ID和一账通ID修改数据
     * updateCmRelationAccountSubByorder
     * @param orderid
     * @param hboneno
     * @param relationid
     * @param subrelationid
     * @param relationstate
     * @return void
     * @Author: yu.zhang on 2021/7/21 11:35
     */
    @Override
    public void updateCmRelationAccountSubByorder(String orderid, String hboneno, String relationid, String subrelationid, String relationstate) {

        CmRelationAccountSub subrecord = new CmRelationAccountSub();
        subrecord.setRelationid(relationid);
        subrecord.setOrderid(orderid);
        subrecord.setHboneno(hboneno);
        subrecord.setRelationstate(relationstate);
        subrecord.setSubrelationid(subrelationid);
        cmRelationAccountSubMapper.updateCmRelationAccountSubByorder(subrecord);
    }

    /**
     * 解除主账户
     * removeCmRelationSubAccount
     * @param relationid
     * @param orderid
     * @param subconscustno
     * @return void
     * @Author: yu.zhang on 2021/7/28 10:54
     */
    @Override
    public void removeCmRelationSubAccount(String relationid, String orderid, String subconscustno) {
        //修改一账通和投顾客户关系表
        QueryConscustInfoRequest subqueryRequest = new QueryConscustInfoRequest();
        subqueryRequest.setConscustno(subconscustno);
        QueryConscustInfoResponse subqueryResponse = queryConscustInfoService.queryConscustInfo(subqueryRequest);
        ConscustInfoDomain subconscust = subqueryResponse.getConscustinfo();

        CmRelationAccountSub subrecord = new CmRelationAccountSub();
        subrecord.setRelationid(relationid);
        subrecord.setOrderid(orderid);
        subrecord.setRelationstate(StaticVar.RELATION_CRM_REMOVEING);
        subrecord.setHboneno(subconscust.getHboneno());
        cmRelationAccountSubMapper.addCmRelationAccountSub(subrecord);
    }

    @Override
    public List<CmRelationAccountSub> listRelationAccountSub(Map<String, Object> param) {
        return cmRelationAccountSubMapper.listRelationAccountSub(param);
    }

    @Override
    public CmRelationAccountSub getRelationAccountSub(String orderid) {
        return cmRelationAccountSubMapper.getRelationAccountSub(orderid);
    }

    /**
     * 修改关联账户数据状态
     * @param orderId  关联账户标识Id
     * @param oldStatus 老的状态 where条件
     * @param updateStatus 新的状态   set 使用
     */
    @Override
    public int updateCmRelationAccountSubStatus(String orderId,String oldStatus,String updateStatus){
    	int updateCount=cmRelationAccountSubMapper.updateCmRelationAccountSubStatus(orderId,oldStatus,updateStatus);
        log.info("更新SUB关联账户状态，关联账户的主Id[orderId]:{},修改之前状态：{}，更新状态为：{}，更新条数：{}",orderId,oldStatus,updateStatus,updateCount);
        return updateCount;
    }
}

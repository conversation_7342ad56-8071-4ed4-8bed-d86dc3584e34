package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.domain.SearchConferenceCustVo;
import com.howbuy.crm.nt.conference.domain.UpdateConferenceCustVo;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.dto.CmConferenceCustDto;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * @description: (路演会议-客户预约表) Mapper 接口
 * <AUTHOR>
 * @date 2023/11/7 14:14
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceConscustMapper {

    /**
     * @description:(新增 客户预约表记录)
     * @param record
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/7 14:18
     * @since JDK 1.8
     */
    int insert(CmConferenceConscust record);


    /**
     * @description:(批量插入数据)
     * @param list
     * @return int
     * @author: xufanchao
     * @date: 2023/11/10 15:30
     * @since JDK 1.8
     */
    int batchInsertList(List<CmConferenceConscust> list);


    /**
     * @description:(更新 客户预约表记录)
     * @param updateVo
     * @return com.howbuy.crm.nt.conference.dto.CmConferenceConscust
     * @throws Exception
     * @since JDK 1.8
     */
    int updateByVo(UpdateConferenceCustVo updateVo);


    /**
     * @description:(根据 客户号 和 会议id 更新客户预约表记录)
     * @param record
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/9 13:53
     * @since JDK 1.8
     */
    int updateByPrimaryKeySelective(CmConferenceConscust record);


    /**
     * @description:(根据vo查询  参会列表)
     * @param vo
     * @return java.util.List<com.howbuy.crm.nt.conference.dto.CmConferenceCustDto>
     * @author: haoran.zhang
     * @date: 2023/11/7 18:40
     * @since JDK 1.8
     */
    List<CmConferenceConscust> getConferenceCustList(CmConferenceCustVo vo);


   /**
    * @description:(根据会议id和一账通号  更新 创建人字段
    * @param conferenceId
    * @param custNo
    * @return int
    * @since JDK 1.8
    */
    int updateCreator(@Param("conferenceId") String conferenceId,
                      @Param("custNo") String custNo,
                      @Param("creator") String creator);


    /**
     * @description:(根据vo查询  参会列表)
     * @param vo
     * @return java.util.List<com.howbuy.crm.nt.conference.dto.CmConferenceCustDto>
     * @author: haoran.zhang
     * @date: 2023/11/7 18:40
     * @since JDK 1.8
     */
    List<CmConferenceCustDto> getConferenceCustDtoList(CmConferenceCustVo vo);


    /**
     * 根据一账通号查询已签到的课程id
     */
    List<String> listSignCourseIdByHboneno(@Param("hboneNo") String hboneNo);



    /**
     * @description:(根据 客户号 和 会议id 查询客户预约表记录)
     * @param custNo	
     * @param conferenceId
     * @return com.howbuy.crm.nt.conference.dto.CmConferenceConscust
     * @author: haoran.zhang
     * @date: 2023/11/7 18:49
     * @since JDK 1.8
     */
    CmConferenceConscust selectConferenceCust(@Param("custNo")String custNo, @Param("conferenceId")String conferenceId);


    /**
     * @description:(查询指定会议，指定客户号列表的参会预约记录记录)
     * @param conferenceId 会议id
     * @param custNoList 客户号列表
     * @return java.util.List<com.howbuy.crm.hb.domain.conference.CmConferenceConscust>
     * @author: haoran.zhang
     * @date: 2023/11/17 12:07
     * @since JDK 1.8
     */
    List<CmConferenceConscust> selectCustList(@Param("conferenceId") String conferenceId,@Param("custNoList") List<List<String>> custNoList);



    /**
     * 根据会议ID查询 扫码参会 count记录
     * @param conferenceId
     * @return
     */
    int selectCountByConferenceId(@Param("conferenceId") String conferenceId);


    /**
     * @description:(删除 预约表记录)
     * @param custNo	
     * @param conferenceId
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/9 10:55
     * @since JDK 1.8
     */
    int delete(@Param("custNo")String custNo, @Param("conferenceId")String conferenceId);

    /**
     * @description:(备份 删除 预约表记录，插入 his表)
     * @param custNo
     * @param conferenceId
     * @param operator
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/9 10:56
     * @since JDK 1.8
     */
    int insertHis(@Param("custNo")String custNo, @Param("conferenceId")String conferenceId,@Param("operator") String operator);


    /**
     * @description:(根据课程号和一账通号获取签到信息)
     * @param list
     * @return java.util.List<com.howbuy.crm.nt.conference.dto.CmConferenceCustDto>
     * @author: haoran.zhang
     * @date: 2023/11/7 18:44
     * @since JDK 1.8
     **/
    List<CmConferenceCustDto> listCmConferenceListByCustno(@Param("list") List<SearchConferenceCustVo> list);


}
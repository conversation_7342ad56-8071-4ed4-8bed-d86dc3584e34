package com.howbuy.crm.nt.conference.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.buss.CmConferenceBusinessBuss;
import com.howbuy.crm.nt.conference.buss.CmConferenceScanBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanHisMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceScan;
import com.howbuy.crm.nt.conference.dto.CmConferenceScanDisplayDto;
import com.howbuy.crm.nt.conference.dto.MobileCustInfoDto;
import com.howbuy.crm.nt.conference.request.CmConferenceScanPageVo;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
/**
 * @description: (扫码参会签到接口) 服务实现类
 * <AUTHOR>
 * @date 2023/11/3 18:27
 * @since JDK 1.8
 */
@Slf4j
@Service("cmConferenceScanService")
public class CmConferenceScanServiceImpl implements CmConferenceScanService{

    @Resource
    private CmConferenceScanMapper cmConferenceScanMapper;
    @Resource
    private CmConferenceScanHisMapper cmConferenceScanHisMapper;

    @Resource
    private CmConferenceBusinessBuss cmConferenceBusinessBuss;
    @Resource
    private CmConferenceScanBuss cmConferenceScanBuss;



    /**
     * @description:(路演扫码参会 页面查询 分页查询)
     * @param queryVo
     * @return crm.howbuy.base.db.PageResult<com.howbuy.crm.nt.conference.dto.CmConferenceScanDisplayDto>
     * @author: haoran.zhang
     * @date: 2023/11/21 10:25
     * @since JDK 1.8
     */
    @Override
    public PageResult<CmConferenceScanDisplayDto> selectPageByPageVo(CmConferenceScanPageVo queryVo){
        PageHelper.startPage(queryVo.getPage(), queryVo.getRows());
        Page<CmConferenceScanDisplayDto> page = cmConferenceScanMapper.selectPageByPageVo(queryVo);
        PageResult<CmConferenceScanDisplayDto> pageResult = new PageResult<>();
        pageResult.setTotal(page.getTotal());
        pageResult.setRows(page.getResult());
        return pageResult;
    }



    /**
     * @description:(根据手机号mobileDigest 查询客户信息列表 )
     * @param mobileDigest
     * @return java.util.List<com.howbuy.crm.nt.conference.dto.MobileCustInfoDto>
     * @author: haoran.zhang
     * @date: 2023/11/16 21:05
     * @since JDK 1.8
     */
    @Override
    public List<MobileCustInfoDto> listExistCustList(String mobileDigest){
        return cmConferenceScanBuss.listExistCustList(mobileDigest);
    }


    @Override
    public CmConferenceScan selectByScanId(String scanId){
        return cmConferenceScanMapper.selectByPrimaryKey(new BigDecimal(scanId));
    }



    /**
     * @description:(指定 scanId的 扫码参会表数据 ，为固定的客户号)
     * @param scanId
     * @param custNo
     * @param operator
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @since JDK 1.8
     */
    @Override
    public NtReturnMessageDto<String> executeSpecifyCustNo(BigDecimal scanId,
                                                           String custNo,
                                                           String operator){
        return  cmConferenceBusinessBuss.executeSpecifyCustNo(scanId,custNo,operator);
    }

    /**
     * @param scanIdList 扫码签到表主键id
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @description:(批量取消参会)
     * @author: xufanchao
     * @date: 2023/11/18 09:32
     * @since JDK 1.8
     */
    @Override
    public NtReturnMessageDto<String> batchCancel(List<BigDecimal> scanIdList, String userId) {
        //复制 his 表
        int backUpCount = cmConferenceScanHisMapper.insertByScanIdList(scanIdList, userId);
        //删除扫码表
        int deleteCount = cmConferenceScanMapper.deleteByScanIdList(scanIdList);
        log.info("批量取消扫码参会数据，scanId列表：{} ，扫码表数据删除条数：{}，扫码表数据备份条数：{}", scanIdList, deleteCount, backUpCount);
        return NtReturnMessageDto.ok();
    }
}

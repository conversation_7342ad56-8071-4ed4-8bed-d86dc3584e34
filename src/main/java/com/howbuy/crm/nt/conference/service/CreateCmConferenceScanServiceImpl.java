package com.howbuy.crm.nt.conference.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.buss.CmConferenceBusinessBuss;
import com.howbuy.crm.nt.conference.buss.CmConferenceScanBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.domain.InsertScanVo;
import com.howbuy.crm.nt.conference.dto.CmConference;
import com.howbuy.crm.nt.conference.request.CreateCmConferenceScanRequest;
import com.howbuy.crm.nt.conference.response.CreateCmConferenceScanResponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.enums.param.ParamAuditStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/17 18:39
 */
@Slf4j
@Service("createCmConferenceScanService")
public class CreateCmConferenceScanServiceImpl implements CreateCmConferenceScanService {

    @Autowired
    private CmConferenceScanBuss cmConferenceScanBuss;
    @Autowired
    private CmConferenceBusinessBuss cmConferenceBusinessBuss;

    @Autowired
    private CmConferenceMapper cmConferenceMapper;
    /** 手机号长度 */
    private  static final int MOBILE_LENGTH = 11;

    @Override
    public CreateCmConferenceScanResponse execute(CreateCmConferenceScanRequest request) {
        log.info("扫码签到 Start: {}", JSON.toJSONString(request));
        CreateCmConferenceScanResponse response = new CreateCmConferenceScanResponse();

        try {
            NtReturnMessageDto<String>  validateResult=validate(request);
            if(validateResult.isSuccess()){
                InsertScanVo insertScanVo= new InsertScanVo();
                insertScanVo.setConferenceId(request.getConferenceId());
                insertScanVo.setMobile(request.getMobile());
                insertScanVo.setCustName(request.getCustname());
                insertScanVo.setConsName(request.getConsname());
                insertScanVo.setMeetingNumber(request.getMeetingnumber());
                cmConferenceBusinessBuss.createCmConferenceScanInfo(insertScanVo);
                response.success();
            }else{
                response.putBaseResult(BaseConstantEnum.FAIL, validateResult.getReturnMsg());
            }
        } catch (Exception e) {
            log.error("扫码签到失败 " + e.getMessage(), e);
            response.putBaseResult(BaseConstantEnum.FAIL, "操作失败");
        }
        log.info("扫码签到 End: {}", JSON.toJSONString(response));
        return response;
    }


    /**
     * 校验参数
     * @param request
     * @return
     */
    private NtReturnMessageDto<String> validate(CreateCmConferenceScanRequest request) {
        //错误提示语 列表
        List<String> errorMsgList= Lists.newArrayList();
        String conferenceId = request.getConferenceId();
        String mobile = request.getMobile();
        if (StringUtils.isBlank(conferenceId)) {
            errorMsgList.add("路演ID为空");
        }else{
            CmConference  conference=cmConferenceMapper.selectByPrimaryKey(conferenceId);
            if (conference==null) {
                errorMsgList.add("该路演不存在");
            }else{
                if(!YesOrNoEnum.YES.getCode().equals(conference.getRecStat())){
                    errorMsgList.add("该路演已删除");
                }else{
                    if(!ParamAuditStatusEnum.AUDIT_PASS.getCode().equals(conference.getAuditStatus())){
                        errorMsgList.add("该路演尚未审核通过");
                    }
                }
            }

        }

        if (StringUtils.isBlank(mobile)) {
            errorMsgList.add("手机号为空");
        } else if (mobile.length() != MOBILE_LENGTH) {
            errorMsgList.add("手机号必须是11位");
        }
        if(CollectionUtils.isNotEmpty(errorMsgList)){
            return NtReturnMessageDto.fail(String.join("；",errorMsgList));
        }

        return NtReturnMessageDto.ok();
    }
}

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.domain;

import lombok.Data;

/**
 * @description: (扫码参会表 插入参会数据vo)
 * <AUTHOR>
 * @date 2023/11/8 18:55
 * @since JDK 1.8
 */
@Data
public class InsertScanVo {
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 路演ID
     */
    private String conferenceId;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 投顾姓名
     */
    private String consName;

    /**
     * 参会人员
     */
    private int meetingNumber;

}
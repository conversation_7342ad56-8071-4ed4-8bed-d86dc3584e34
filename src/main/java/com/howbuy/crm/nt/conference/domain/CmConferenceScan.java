package com.howbuy.crm.nt.conference.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 扫码参会客户
 * <AUTHOR>
 * @date 2021/6/17 16:05
 */
@Data
public class CmConferenceScan implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 会议主键(外键) */
    private String conferenceId;

    /** 会议名称 */
    private String conferenceName;

    /** 手机号 */
    private String mobile;
    private String mobileMask;
    private String mobileDigest;
    private String mobileCipher;

    /** 创建人 */
    private String creator;
    
    /** 客户姓名 */
    private String custname;
    
    /** 投顾姓名 */
    private String consname;
    
    /** 创建人 */
    private int meetingnumber;
}

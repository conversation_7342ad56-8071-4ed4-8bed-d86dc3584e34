package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.domain.CsCommunicateVisit;
import org.springframework.stereotype.Component;
/**
 * 
 * <AUTHOR>
 *
 */
@Component
public interface CsCommunicateVisitDao {

	/**
     * @Description:新增数据对象
     * @param CsCommunicateVisit
     * @return 
     */
	void insertCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit);

	/**
	 * @Description:单条修改数据对象
	 * @param CsCommunicateVisit
	 * @return void
	 */
	void updateCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit);
}

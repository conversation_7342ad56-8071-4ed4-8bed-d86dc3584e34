<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceScan">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE_SCAN-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="MEETINGNUMBER" jdbcType="DECIMAL" property="meetingnumber" />
    <result column="SCAN_CUST_STATE" jdbcType="VARCHAR" property="scanCustState" />
    <result column="CUST_STATE" jdbcType="VARCHAR" property="custState" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="CONS_CODE" jdbcType="VARCHAR" property="consCode"/>
    <result column="CUST_TIMESTAMP" jdbcType="TIMESTAMP" property="custTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
  </resultMap>

  <!--页面查询对象-->
  <resultMap id="PageResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceScanDisplayDto" extends="BaseResultMap">
    <result column="CONFERENCE_NAME" jdbcType="VARCHAR" property="conferenceName" />
    <result column="RELATED_CUST_NAME" jdbcType="VARCHAR" property="relatedCustName" />
    <result column="RELATED_CONS_NAME" jdbcType="VARCHAR" property="relatedConsName" />
    <result column="APPOINTMENTS_NUB" jdbcType="DECIMAL" property="appointmentsNub" />
    <result column="APPOINT" jdbcType="VARCHAR" property="appoint" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONFERENCEID, MOBILE_DIGEST, MOBILE_MASK, CREDT, CREATOR, CUSTNAME, CONSNAME, 
    MEETINGNUMBER, SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE, CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONFERENCE_SCAN
    where ID = #{id,jdbcType=DECIMAL}
  </select>

  <select id="selectCountByConferenceId" resultType="integer" parameterType="string">
    <!--@mbg.generated-->
    select count(1) from CM_CONFERENCE_SCAN
    where CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
  </select>


  <select id="selectByConferenceIdAndMobile" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONFERENCE_SCAN
    where
    CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
    and MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByScanIdList" parameterType="list">
    <!--@mbg.generated-->
    delete from CM_CONFERENCE_SCAN
    WHERE ID IN
    <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScan">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE_SCAN (ID, CONFERENCEID, MOBILE_DIGEST, 
      MOBILE_MASK, CREDT, CREATOR, 
      CUSTNAME, CONSNAME, MEETINGNUMBER, 
      SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE,
      CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP
      )
    values (#{id,jdbcType=DECIMAL}, #{conferenceid,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, 
      #{mobileMask,jdbcType=VARCHAR}, #{credt,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{custname,jdbcType=VARCHAR}, #{consname,jdbcType=VARCHAR}, #{meetingnumber,jdbcType=DECIMAL}, 
      #{scanCustState,jdbcType=VARCHAR}, #{custState,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, #{consCode,jdbcType=VARCHAR},
      #{custTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>

  <insert id="insertConferenceScanCipher" >
    INSERT INTO CM_CONFERENCE_SCAN_CIPHER(SCANID, MOBILE_CIPHER)
    VALUES(#{scanId,jdbcType=DECIMAL}, #{mobileCipher,jdbcType=VARCHAR})
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScan">
    <!--@mbg.generated-->
    update CM_CONFERENCE_SCAN
    <set>
      <if test="conferenceid != null">
        CONFERENCEID = #{conferenceid,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        CUSTNAME = #{custname,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        CONSNAME = #{consname,jdbcType=VARCHAR},
      </if>
      <if test="meetingnumber != null">
        MEETINGNUMBER = #{meetingnumber,jdbcType=DECIMAL},
      </if>
      <if test="scanCustState != null">
        SCAN_CUST_STATE = #{scanCustState,jdbcType=VARCHAR},
      </if>
      <if test="custState != null">
        CUST_STATE = #{custState,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
        <if test="consCode != null and consCode != ''">
            CONS_CODE = #{consCode,jdbcType=VARCHAR},
        </if>
      <if test="custTimestamp != null">
        CUST_TIMESTAMP = #{custTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScan">
    <!--@mbg.generated-->
    update CM_CONFERENCE_SCAN
    set CONFERENCEID = #{conferenceid,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CUSTNAME = #{custname,jdbcType=VARCHAR},
      CONSNAME = #{consname,jdbcType=VARCHAR},
      MEETINGNUMBER = #{meetingnumber,jdbcType=DECIMAL},
      SCAN_CUST_STATE = #{scanCustState,jdbcType=VARCHAR},
      CUST_STATE = #{custState,jdbcType=VARCHAR},
      CUST_NO = #{custNo,jdbcType=VARCHAR},
      CONS_CODE = #{consCode,jdbcType=VARCHAR},
      CUST_TIMESTAMP = #{custTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectListByVo" parameterType="com.howbuy.crm.nt.conference.request.CmConferenceScanVo" resultMap="BaseResultMap">
    SELECT
    C.ID, C.CONFERENCEID, C.MOBILE_DIGEST, C.MOBILE_MASK, C.CREDT, C.CREATOR, C.CUSTNAME, C.CONSNAME,
    C.MEETINGNUMBER, C.SCAN_CUST_STATE, C.CUST_STATE, C.CUST_NO, C.CONS_CODE, C.CUST_TIMESTAMP, C.MODIFIER, C.MODIFY_TIMESTAMP
    FROM CM_CONFERENCE_SCAN C
    LEFT JOIN CM_CONFERENCE S ON C.CONFERENCEID=S.CONFERENCEID
    <where>
      AND S.REC_STAT='1'
    <if test="id != null">
      AND C.ID = #{id,jdbcType=DECIMAL}
    </if>
    <if test="conferenceId != null and conferenceId != ''">
      AND C.CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
    </if>
    <if test="mobileDigest != null and mobileDigest !='' ">
      AND C.MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR}
    </if>
    <if test="meetingnumber != null">
      AND C.MEETINGNUMBER = #{meetingnumber,jdbcType=DECIMAL}
    </if>
    <if test="scanCustStateList != null and scanCustStateList.size() != 0">
      AND C.SCAN_CUST_STATE in
      <foreach collection="scanCustStateList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="custStateList != null and custStateList.size() != 0">
        AND C.CUST_STATE in
        <foreach collection="custStateList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </if>
    <if test="custNo != null">
      AND C.CUST_NO = #{custNo,jdbcType=VARCHAR}
    </if>
    </where>
  </select>

  <select id="selectPageByPageVo" parameterType="com.howbuy.crm.nt.conference.request.CmConferenceScanPageVo" resultMap="PageResultMap">
    SELECT
    CC.*,
    T.CONFERENCENAME CONFERENCE_NAME,
    CON.CONSNAME RELATED_CONS_NAME,
    C.CUSTNAME RELATED_CUST_NAME,
    APPOINT.APPOINTMENTSNUB AS APPOINTMENTS_NUB,
    CASE WHEN APPOINT.CONFERENCEID IS NULL THEN '0' ELSE '1' END AS APPOINT
    FROM CM_CONFERENCE_SCAN CC
    LEFT JOIN CM_CONFERENCE T  ON CC.CONFERENCEID = T.CONFERENCEID
    <!--关联 客户、投顾信息-->
    LEFT JOIN CM_CONSCUST C    ON CC.CUST_NO=C.CONSCUSTNO  AND C.CONSCUSTSTATUS='0'
    LEFT JOIN CM_CUSTCONSTANT NT   ON NT.CUSTNO = C.CONSCUSTNO
    LEFT JOIN CM_CONSULTANT CON    ON NT.CONSCODE = CON.CONSCODE
    <!--关联 创建人 投顾信息-->
    LEFT JOIN CM_CONSULTANT CRETECON      ON T.CREATER = CRETECON.CONSCODE
    <!--关联 预约 信息 非sys创建-->
    LEFT JOIN CM_CONFERENCE_CONSCUST APPOINT ON  APPOINT.CONFERENCEID=CC.CONFERENCEID   AND APPOINT.CONSCUSTNO = CC.CUST_NO AND APPOINT.CREATER!='sys'
    <where>
    <if test="conferenceId != null">
      AND CC.CONFERENCEID = #{conferenceId,jdbcType=DECIMAL}
    </if>
    <if test="conferenceNameLike != null and conferenceNameLike != ''">
      AND T.CONFERENCENAME LIKE '%'||#{conferenceNameLike,jdbcType=VARCHAR}||'%'
    </if>

    <if test="createOrgList != null and createOrgList.size() > 0">
      AND ( CRETECON.OUTLETCODE in
      <foreach item="item" index="index" collection="createOrgList" separator=" OR CRETECON.OUTLETCODE IN ">
        <foreach collection="item" item="mId" open="(" separator="," close=")">
          #{mId}
        </foreach>
      </foreach>
      )
    </if>
    <if test="conferenceBeginDt != null and conferenceBeginDt != ''">
      AND T.CONFERENCEDT <![CDATA[ >= ]]> #{conferenceBeginDt,jdbcType=VARCHAR}
    </if>
    <if test="conferenceEndDt != null and conferenceEndDt != ''">
      AND T.CONFERENCEDT <![CDATA[<=]]> #{conferenceEndDt,jdbcType=VARCHAR}
    </if>

    <if test="custNo != null and custNo != ''">
      AND CC.CUST_NO = #{custNo,jdbcType=VARCHAR}
    </if>
    <if test="custNameLike != null and custNameLike != ''">
      AND C.CUSTNAME LIKE '%'||#{custNameLike,jdbcType=VARCHAR}||'%'
    </if>
    <if test="conferencetypeList!=null and conferencetypeList.size()>0 ">
      <foreach collection="conferencetypeList" index="index" item="item" open=" AND (" separator="or" close=")">
        T.CONFERENCETYPE LIKE '%'||#{item}||'%'
      </foreach>
   <!--   AND T.CONFERENCETYPE IN
      <foreach collection="conferencetypeList" item="item"
               index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>-->
    </if>
    <if test="custStateList != null and custStateList.size() != 0">
      AND CC.CUST_STATE in
      <foreach collection="custStateList" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <choose>
      <when test="existCustNo == '0'.toString() "> <!--条件：存在客户-->
        AND C.CONSCUSTNO IS NOT NULL
        <include refid="AUTH_CONDITION"></include>
      </when>
      <when test="existCustNo == '1'.toString()">  <!--条件：不存在客户 则 权限条件 不需要生效-->
        AND (C.CONSCUSTNO IS NULL
        <if test="nullCustStateList != null and nullCustStateList.size() != 0">
          AND CC.CUST_STATE in
          <foreach collection="nullCustStateList" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="nullCustCreateOrgList != null and nullCustCreateOrgList.size() > 0">
          AND ( CRETECON.OUTLETCODE in
          <foreach item="item" index="index" collection="nullCustCreateOrgList" separator=" OR CRETECON.OUTLETCODE IN ">
            <foreach collection="item" item="mId" open="(" separator="," close=")">
              #{mId}
            </foreach>
          </foreach>
          )
        </if>
        )
      </when>
      <otherwise>
        <!--条件：存在客户 选择全部  则 权限条件 需要生效。 数据允许展示 客户为空的数据 -->
        AND (
        (C.CONSCUSTNO IS NOT NULL  <include refid="AUTH_CONDITION"></include> )
        OR
        <!--客户号为空，预约条件有：nullCustStateList ，nullCustCreateOrgList -->
        (C.CONSCUSTNO IS NULL
        <if test="nullCustStateList != null and nullCustStateList.size() != 0">
          AND CC.CUST_STATE in
          <foreach collection="nullCustStateList" item="item" index="index" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
        <if test="nullCustCreateOrgList != null and nullCustCreateOrgList.size() > 0">
          AND ( CRETECON.OUTLETCODE in
          <foreach item="item" index="index" collection="nullCustCreateOrgList" separator=" OR CRETECON.OUTLETCODE IN ">
            <foreach collection="item" item="mId" open="(" separator="," close=")">
              #{mId}
            </foreach>
          </foreach>
          )
        </if>
        )
        )
      </otherwise>
    </choose>
    ORDER BY  T.CONFERENCEDT DESC,T.CONFERENCEID, CC.CREDT DESC,CC.MOBILE_DIGEST
    </where>
  </select>

  <!--权限相关 查询条件-->
  <sql id="AUTH_CONDITION">
    <if test="authConsCode != null and authConsCode != ''">
      AND con.CONSCODE = #{authConsCode,jdbcType=VARCHAR}
    </if>
    <if test="authOthertearm!= null and authOthertearm != ''">
      AND  (con.OUTLETCODE = #{authOthertearm,jdbcType=VARCHAR} and con.TEAMCODE is null)
    </if>
    <if test="authOutletcodeList != null and authOutletcodeList.size() > 0">
      AND ( con.OUTLETCODE in
      <foreach item="item" index="index" collection="authOutletcodeList" separator=" OR con.OUTLETCODE in ">
        <foreach collection="item" item="mId" open="(" separator="," close=")">
          #{mId}
        </foreach>
      </foreach>
      )
    </if>
    <if test="authTeamcode != null and authTeamcode != ''">
      AND con.TEAMCODE = #{authTeamcode,jdbcType=VARCHAR}
    </if>
    <if test="notCanSeeConsNullFlag != null and notCanSeeConsNullFlag != ''">
      AND con.CONSCODE is not null
    </if>
  </sql>
</mapper>
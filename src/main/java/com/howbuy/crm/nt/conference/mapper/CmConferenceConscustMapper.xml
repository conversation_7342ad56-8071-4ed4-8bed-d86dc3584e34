<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceConscust">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE_CONSCUST-->
    <result column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="ORGCODE" jdbcType="VARCHAR" property="orgcode" />
    <result column="APPOINTMENTSNUB" jdbcType="DECIMAL" property="appointmentsnub" />
    <result column="APPOINTMENTSCODE" jdbcType="VARCHAR" property="appointmentscode" />
    <result column="ACTUALNUB" jdbcType="DECIMAL" property="actualnub" />
    <result column="CREATDT" jdbcType="VARCHAR" property="creatdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="MODIFYDT" jdbcType="VARCHAR" property="modifydt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="APPOINTMENTSTYPE" jdbcType="VARCHAR" property="appointmentstype" />
    <result column="ACTUALNUBDT" jdbcType="TIMESTAMP" property="actualnubdt" />
    <result column="GDCJLABEL" jdbcType="VARCHAR" property="gdcjlabel" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONFERENCEID, CONSCUSTNO, CONSCODE, ORGCODE, APPOINTMENTSNUB, APPOINTMENTSCODE, ACTUALNUB, 
    CREATDT, CREATER, MODIFYDT, MODIFIER, APPOINTMENTSTYPE, ACTUALNUBDT, GDCJLABEL,SOURCE
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceConscust">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE_CONSCUST (CONFERENCEID, CONSCUSTNO, CONSCODE, 
      ORGCODE, APPOINTMENTSNUB, APPOINTMENTSCODE, 
      ACTUALNUB, CREATDT, CREATER, 
      MODIFYDT, MODIFIER, APPOINTMENTSTYPE, 
      ACTUALNUBDT, GDCJLABEL,SOURCE)
    values (#{conferenceid,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, 
      #{orgcode,jdbcType=VARCHAR}, #{appointmentsnub,jdbcType=DECIMAL}, #{appointmentscode,jdbcType=VARCHAR}, 
      #{actualnub,jdbcType=DECIMAL}, #{creatdt,jdbcType=VARCHAR}, #{creater,jdbcType=VARCHAR}, 
      #{modifydt,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{appointmentstype,jdbcType=VARCHAR}, 
      #{actualnubdt,jdbcType=TIMESTAMP}, #{gdcjlabel,jdbcType=VARCHAR}, #{source,jdbcType=VARCHAR})
  </insert>

	<insert id="batchInsertList" parameterType="list">
		<!--@mbg.generated-->
		insert into CM_CONFERENCE_CONSCUST (CONFERENCEID, CONSCUSTNO, CONSCODE,
		ORGCODE, APPOINTMENTSNUB, APPOINTMENTSCODE,
		ACTUALNUB, CREATDT, CREATER,
		MODIFYDT, MODIFIER, APPOINTMENTSTYPE,
		ACTUALNUBDT, GDCJLABEL,SOURCE)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.conferenceid,jdbcType=VARCHAR}, #{item.conscustno,jdbcType=VARCHAR}, #{item.conscode,jdbcType=VARCHAR},
			 #{item.orgcode,jdbcType=VARCHAR}, #{item.appointmentsnub,jdbcType=DECIMAL},
			 #{item.appointmentscode,jdbcType=VARCHAR}, #{item.actualnub,jdbcType=DECIMAL},
			 #{item.creatdt,jdbcType=VARCHAR}, #{item.creater,jdbcType=VARCHAR}, #{item.modifydt,jdbcType=VARCHAR},#{item.modifier,jdbcType=VARCHAR},#{item.appointmentstype,jdbcType=VARCHAR},
			 #{item.actualnubdt,jdbcType=TIMESTAMP}, #{item.gdcjlabel,jdbcType=VARCHAR},#{item.source,jdbcType=VARCHAR})
		</foreach>
	</insert>

	<delete id="delete" parameterType="map">
		DELETE FROM CM_CONFERENCE_CONSCUST
		WHERE CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
		AND CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
	</delete>
	<insert id="insertHis" parameterType="map">
		insert into CM_CONFERENCE_CONSCUST_HIS (CONFERENCEID, CONSCUSTNO, CONSCODE,
		ORGCODE, APPOINTMENTSNUB, APPOINTMENTSCODE,
		ACTUALNUB, CREATDT, CREATER,
		MODIFYDT, MODIFIER, APPOINTMENTSTYPE,
		ACTUALNUBDT, GDCJLABEL,
		DELETE_OPERATOR, DELETE_TIMESTAMP,SOURCE)
		select CONFERENCEID, CONSCUSTNO, CONSCODE,
		ORGCODE, APPOINTMENTSNUB, APPOINTMENTSCODE,
		ACTUALNUB, CREATDT, CREATER,
		MODIFYDT, MODIFIER, APPOINTMENTSTYPE,
		ACTUALNUBDT, GDCJLABEL,
		#{operator,jdbcType=VARCHAR} AS DELETE_OPERATOR,
		SYSTIMESTAMP AS DELETE_TIMESTAMP,
		SOURCE
		from CM_CONFERENCE_CONSCUST
		where CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
		AND CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
	</insert>

	<select id="selectCountByConferenceId" resultType="integer" parameterType="string">
		<!--@mbg.generated-->
		select count(1) from CM_CONFERENCE_CONSCUST
		where CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
	</select>

	<select id="selectConferenceCust" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		from CM_CONFERENCE_CONSCUST T
		WHERE
		T.CONFERENCEID = #{conferenceId}
		AND T.CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
	</select>

	<select id="selectCustList" parameterType="map" resultMap="BaseResultMap" useCache="false">
		SELECT
		<include refid="Base_Column_List" />
		from CM_CONFERENCE_CONSCUST T
		WHERE
		CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
		AND ( CONSCUSTNO IN
		<foreach item="item" index="index" collection="custNoList" separator=" OR CONSCUSTNO IN ">
			<foreach collection="item" item="mId" open="(" separator="," close=")">
				#{mId}
			</foreach>
		</foreach>
		)
	</select>

	<update id="updateByVo" parameterType="com.howbuy.crm.nt.conference.domain.UpdateConferenceCustVo">
		UPDATE CM_CONFERENCE_CONSCUST
		<set>
			<if test="actualnub!=null">ACTUALNUB=#{actualnub},</if>
			<if test="actualnubdt!=null">ACTUALNUBDT=#{actualnubdt} </if>
		</set>
		<where>
		  AND CONFERENCEID=#{conferenceid,jdbcType=VARCHAR}
		<if test="hboneNo != null and hboneNo != ''">
			AND CONSCUSTNO =
			(SELECT CC.CONSCUSTNO FROM CM_CONSCUST CC WHERE CC.HBONE_NO = #{hboneNo,jdbcType=VARCHAR})
		</if>
		<if test="custNo != null and custNo != ''">
			AND CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
		</if>
		<if test="appointmentscode != null and appointmentscode != ''">
			AND APPOINTMENTSCODE = #{appointmentscode,jdbcType=VARCHAR}
		</if>
		</where>
	</update>


	<update id="updateCreator" parameterType="map">
		UPDATE CM_CONFERENCE_CONSCUST
		<set>
			creater=#{creator,jdbcType=VARCHAR}
		</set>
		WHERE  CONFERENCEID=#{conferenceId,jdbcType=VARCHAR}
		AND CONSCUSTNO = #{custNo,jdbcType=VARCHAR}
	</update>


	<update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceConscust">
		<!--@mbg.generated-->
		update CM_CONFERENCE_CONSCUST
		<set>
			<if test="conscode != null">
				CONSCODE = #{conscode,jdbcType=VARCHAR},
			</if>
			<if test="orgcode != null">
				ORGCODE = #{orgcode,jdbcType=VARCHAR},
			</if>
			<if test="appointmentsnub != null">
				APPOINTMENTSNUB = #{appointmentsnub,jdbcType=DECIMAL},
			</if>
			<if test="appointmentscode != null">
				APPOINTMENTSCODE = #{appointmentscode,jdbcType=VARCHAR},
			</if>
			<if test="actualnub != null">
				ACTUALNUB = #{actualnub,jdbcType=DECIMAL},
			</if>
			<if test="creatdt != null">
				CREATDT = #{creatdt,jdbcType=VARCHAR},
			</if>
			<if test="creater != null">
				CREATER = #{creater,jdbcType=VARCHAR},
			</if>
			<if test="modifydt != null">
				MODIFYDT = #{modifydt,jdbcType=VARCHAR},
			</if>
			<if test="modifier != null">
				MODIFIER = #{modifier,jdbcType=VARCHAR},
			</if>
			<if test="appointmentstype != null">
				APPOINTMENTSTYPE = #{appointmentstype,jdbcType=VARCHAR},
			</if>
			<if test="actualnubdt != null">
				ACTUALNUBDT = #{actualnubdt,jdbcType=TIMESTAMP},
			</if>
			<if test="gdcjlabel != null">
				GDCJLABEL = #{gdcjlabel,jdbcType=VARCHAR},
			</if>
		</set>
		where CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
		  AND CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
	</update>

	<select id="listCmConferenceListByCustno" resultType="com.howbuy.crm.nt.conference.dto.CmConferenceCustDto">
			select ccc.ACTUALNUB,ccc.ACTUALNUBDT,T.courseId,cc.HBONE_NO hboneNo
			from CM_CONFERENCE_CONSCUST ccc
			left join CM_CONFERENCE T on ccc.CONFERENCEID = T.CONFERENCEID
			left join CM_CONSCUST cc on ccc.CONSCUSTNO = cc.CONSCUSTNO
			where
			<foreach collection="list" index="index" item="item" separator="or">
				 (#{item.courseId} = T.CourseId and #{item.hboneNo} = cc.hbone_no)
			 </foreach>
	</select>

	<select id="listSignCourseIdByHboneno" resultType="string">
		select T.courseId
		from CM_CONFERENCE_CONSCUST ccc
		left join CM_CONFERENCE T on ccc.CONFERENCEID = T.CONFERENCEID
		left join CM_CONSCUST cc on ccc.CONSCUSTNO = cc.CONSCUSTNO
		where ccc.ACTUALNUB &gt; 0 and cc.hbone_no = #{hboneNo,jdbcType=VARCHAR}
	</select>


	<select id="getConferenceCustList" parameterType="com.howbuy.crm.nt.conference.request.CmConferenceCustVo" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List"/>
		FROM CM_CONFERENCE_CONSCUST T
		LEFT JOIN CM_CONFERENCE T1 ON T.CONFERENCEID=T1.CONFERENCEID
		<where>
			AND T1.REC_STAT = '1'
			<if test="conferenceId != null and conferenceId != ''">
				AND T.CONFERENCEID = #{conferenceId, jdbcType=VARCHAR}
			</if>
			<if test="conscustNo != null and conscustNo != ''">
				AND T.CONSCUSTNO = #{conscustNo, jdbcType=VARCHAR}
			</if>
			<if test="consCode != null and consCode != ''">
				AND T.CONSCODE = #{consCode, jdbcType=VARCHAR}
			</if>
			<if test="actualNubBeginDt != null ">
				AND T.ACTUALNUBDT <![CDATA[ >= ]]> #{actualNubBeginDt, jdbcType=TIMESTAMP}
			</if>
			<if test="actualNubEndDt != null ">
				AND T.ACTUALNUBDT <![CDATA[ <= ]]> #{actualNubEndDt, jdbcType=TIMESTAMP}
			</if>
			<if test="appointmentsCode != null and appointmentsCode != ''">
				AND T.APPOINTMENTSCODE = #{appointmentsCode, jdbcType=VARCHAR}
			</if>
		</where>
	</select>

	<select id="getConferenceCustDtoList" parameterType="com.howbuy.crm.nt.conference.request.CmConferenceCustVo" resultType="com.howbuy.crm.nt.conference.dto.CmConferenceCustDto">
         SELECT T.CONFERENCEID AS conferenceId,
               T1.CONFERENCENAME  AS conferenceName,
			   T.CONSCUSTNO AS conscustNo,
		       S.CUSTNAME AS custName,
			   T.CONSCODE AS consCode,
		       CT.CONSNAME AS consName,
			   T.ORGCODE AS orgCode,
			   T.APPOINTMENTSNUB AS appointmentsNub,
			   T.APPOINTMENTSCODE AS appointmentsCode,
			   T.ACTUALNUB AS actualNub,
			   T.CREATDT AS creatDt,
			   T.CREATER AS creater,
			   T.MODIFYDT AS modifyDt,
			   T.MODIFIER AS modifier,
			   T.APPOINTMENTSTYPE AS appointmentsType,
			   S.HBONE_NO AS hboneNo,
			   T.ACTUALNUBDT AS actualNubDt,
			   T.GDCJLABEL AS gdcjLabel
       FROM CM_CONFERENCE_CONSCUST T
       LEFT  JOIN CM_CONFERENCE T1 ON T.CONFERENCEID=T1.CONFERENCEID
       LEFT  JOIN CM_CONSCUST S ON S.CONSCUSTNO=T.CONSCUSTNO
	   LEFT  JOIN CM_CONSULTANT CT ON T.CONSCODE=CT.CONSCODE
       <where>
		   AND T1.REC_STAT = '1'
		   <if test="conferenceId != null and conferenceId != ''">
			   AND T.CONFERENCEID = #{conferenceId, jdbcType=VARCHAR}
		   </if>
		   <if test="conscustNo != null and conscustNo != ''">
			   AND T.CONSCUSTNO = #{conscustNo, jdbcType=VARCHAR}
		   </if>
		   <if test="consCode != null and consCode != ''">
			   AND T.CONSCODE = #{consCode, jdbcType=VARCHAR}
		   </if>
		   <if test="actualNubBeginDt != null ">
			   AND T.ACTUALNUBDT <![CDATA[ >= ]]> #{actualNubBeginDt, jdbcType=TIMESTAMP}
		   </if>
		   <if test="actualNubEndDt != null ">
			   AND T.ACTUALNUBDT <![CDATA[ <= ]]> #{actualNubEndDt, jdbcType=TIMESTAMP}
		   </if>
		   <if test="appointmentsCode != null and appointmentsCode != ''">
			   AND T.APPOINTMENTSCODE = #{appointmentsCode, jdbcType=VARCHAR}
		   </if>
	   </where>
	</select>
</mapper>
package com.howbuy.crm.nt.conference.buss;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.constant.SequenceConstants;
import com.howbuy.crm.nt.base.enums.ConferCustStateEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanHisMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper;
import com.howbuy.crm.nt.conference.domain.InsertScanResult;
import com.howbuy.crm.nt.conference.domain.InsertScanVo;
import com.howbuy.crm.nt.conference.domain.MobileExistCustInfo;
import com.howbuy.crm.nt.conference.dto.CmConferenceScan;
import com.howbuy.crm.nt.conference.dto.MobileCustInfoDto;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/17 18:41
 */
@Component
@Slf4j
public class CmConferenceScanBuss {

    @Autowired
    private EncryptSingleFacade encryptSingleFacade;
    @Autowired
    private ConscustMapper conscustMapper;

    @Autowired
    private CmConferenceScanMapper cmConferenceScanMapper;
    @Autowired
    private CmConferenceScanHisMapper cmConferenceScanHisMapper;
    
    @Autowired
	private CommonDao commondao;

    /**
     * 系统创建人
     */
    private static final String SYS_CREATOR = "sys";




    /**
     * @description:(根据手机号mobileDigest 查询客户信息列表 )
     * @param
     * @return void
     */
    public List<MobileCustInfoDto> listExistCustList(String mobileDigest){
        Assert.notNull(mobileDigest, "手机号摘要不能为空");

        List<MobileCustInfoDto> returnList= Lists.newArrayList();

        List<ConscustInfo> custInfoList=conscustMapper.listConsCustInfoByMobile(mobileDigest);
        custInfoList.forEach(custInfo->{
            MobileCustInfoDto mobileCustInfoDto=new MobileCustInfoDto();
            mobileCustInfoDto.setCustNo(custInfo.getConscustno());
            mobileCustInfoDto.setCustName(custInfo.getCustname());
            mobileCustInfoDto.setConsCode(custInfo.getConscode());
            returnList.add(mobileCustInfoDto);
        });
        return returnList;
    }



    /**
     * 根据手机号码-mobileDigest 判断 手机号码存在信息场景
     * @param mobileDigest
     * @return
     */
    public MobileExistCustInfo analyseExistInfo(String mobileDigest){
        MobileExistCustInfo mobileExistCustInfo=new MobileExistCustInfo();
        List<MobileCustInfoDto> existCustList=listExistCustList(mobileDigest);

        if(CollectionUtils.isNotEmpty(existCustList)){
            mobileExistCustInfo.getExistCustList().addAll(existCustList);
        }

        ConferCustStateEnum custStateEnum= getStateByListSize(mobileExistCustInfo.getExistCustList().size());
        mobileExistCustInfo.setCustStateEnum(custStateEnum);

        if(ConferCustStateEnum.EXIST_ONLY==custStateEnum){
            mobileExistCustInfo.setUsedCustInfo(mobileExistCustInfo.getExistCustList().get(0));
        }
        log.info("根据mobileDigest:{} 分析当前客户信息：{}",mobileDigest,JSON.toJSONString(mobileExistCustInfo));
        return mobileExistCustInfo;
    }

    /**
     * 根据 查询客户列表 条数 判断客户状态
     * @param size
     * @return
     */
    private ConferCustStateEnum getStateByListSize(int size){
        if(size==0){
            return ConferCustStateEnum.NOT_EXIST;
        }
        if(size==1){
            return ConferCustStateEnum.EXIST_ONLY;
        }
        return ConferCustStateEnum.EXIST_MANY;
    }


    /**
     * @description:(批量取消扫码参会数据)
     * @param scanIdList
     * @param operator
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/11/8 21:13
     * @since JDK 1.8
     */
    public NtReturnMessageDto<String>  cancel(List<BigDecimal> scanIdList,String operator){
        //复制 his 表
        int backUpCount=cmConferenceScanHisMapper.insertByScanIdList(scanIdList,operator);
        //删除扫码表
        int deleteCount=cmConferenceScanMapper.deleteByScanIdList(scanIdList);
        log.info("批量取消扫码参会数据，scanId列表：{} ，扫码表数据删除条数：{}，扫码表数据备份条数：{}",scanIdList,deleteCount,backUpCount);
        return NtReturnMessageDto.ok();
    }




    /**
     * @description:(新增扫码签到客户信息)
     * @param insertScanVo
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<com.howbuy.crm.nt.conference.dto.CmConferenceScan>
     * @author: haoran.zhang
     * @date: 2023/11/8 19:00
     * @since JDK 1.8
     */
    public NtReturnMessageDto<InsertScanResult> insertScan(InsertScanVo insertScanVo) {
        Assert.notNull(insertScanVo, "扫码参会信息不能为空");
        String mobile=insertScanVo.getMobile();
        String conferenceId=insertScanVo.getConferenceId();
        Assert.notNull(conferenceId, "路演ID不能为空");
        Assert.notNull(mobile, "手机号不能为空");
        String mobileDigest = DigestUtil.digest(mobile);

        CmConferenceScan existScan=cmConferenceScanMapper.selectByConferenceIdAndMobile(conferenceId,mobileDigest);
        if (existScan!=null) {
            return NtReturnMessageDto.fail("该手机号已签到!");
        }
        InsertScanResult insertScanResult=new InsertScanResult();

        //通过参会手机号判断是否能取到惟一投顾客户号且该投顾客户号、会议ID未在会议报名签到页有预约：
        MobileExistCustInfo  analyseInfo=analyseExistInfo(mobileDigest);
        //anyse分析结果 返回
        insertScanResult.setAnalyseInfo(analyseInfo);

        ConferCustStateEnum custStateEnum=analyseInfo.getCustStateEnum();
        MobileCustInfoDto usedUserInfo=analyseInfo.getUsedCustInfo();

        CmConferenceScan insertScanInfo = new CmConferenceScan();
        BigDecimal scanId=new BigDecimal(commondao.getSeqValue(SequenceConstants.SEQ_CONFERENCE_SCAN));
        insertScanInfo.setId(scanId);
        insertScanInfo.setConferenceid(conferenceId);
        insertScanInfo.setMobileDigest(mobileDigest);
        insertScanInfo.setMobileMask(MaskUtil.maskMobile(mobile));
        insertScanInfo.setCustname(insertScanVo.getCustName());
        insertScanInfo.setConsname(insertScanVo.getConsName());
        insertScanInfo.setMeetingnumber(new BigDecimal(insertScanVo.getMeetingNumber()));
        insertScanInfo.setCreator(SYS_CREATOR);
        insertScanInfo.setCredt(new Date());
        insertScanInfo.setScanCustState(custStateEnum.getCode());
        insertScanInfo.setCustState(custStateEnum.getCode());

        if(ConferCustStateEnum.EXIST_ONLY==custStateEnum){
            insertScanInfo.setCustNo(usedUserInfo.getCustNo());
            insertScanInfo.setConsCode(usedUserInfo.getConsCode());
            insertScanInfo.setCustTimestamp(new Date());
        }
        cmConferenceScanMapper.insert(insertScanInfo);
        log.info("扫码参会客户表，数据插入成功，插入数据：{}", JSON.toJSONString(insertScanInfo));
        String mobileCipher=encryptSingleFacade.encrypt(mobile).getCodecText();
        cmConferenceScanMapper.insertConferenceScanCipher(scanId,mobileCipher);
        log.info("扫码参会客户密文表，数据插入成功，插入数据：{}", JSON.toJSONString(insertScanInfo));

        //返回 扫码参会信息 插入对象
        insertScanResult.setInsertScan(insertScanInfo);
        return NtReturnMessageDto.ok("",insertScanResult);
    }

}

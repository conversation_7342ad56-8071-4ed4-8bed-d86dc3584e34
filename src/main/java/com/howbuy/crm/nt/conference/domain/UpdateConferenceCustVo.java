/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (更新 路演会议-会议预约表 vo )
 * <AUTHOR>
 * @date 2023/11/7 14:33
 * @since JDK 1.8
 */
@Data
public class UpdateConferenceCustVo {

  // 以下为  set 属性
    /**
     * 实际到场人数
     */
    private BigDecimal actualnub;


    /**
     * 签到时间
     */
    private Date actualnubdt;


    //以下 为 condition 属性
    /**
     * 参与会议(外键)
     */
    private String conferenceid;


    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 投顾客户号
     */
    private String custNo;

    /**
     * 参会预约码
     */
    private String appointmentscode;

}
package com.howbuy.crm.nt.conference.service;

import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.dto.CmConferenceCustDto;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description //路演管理-service
 * <AUTHOR>
 * @Date 14点39分$ 2022年6月27日$
 */
@Slf4j
@Service("cmConferenceInfoService")
public class CmConferenceInfoServiceImpl  implements  CmConferenceInfoService{

    @Autowired
    private CmConferenceConscustMapper conferenceConscustMapper;



    /**
     * 根据vo查询  参会列表
     * @param vo 查询vo
     * @return List<CmConferenceCustDto>
     */
    @Override
    public List<CmConferenceCustDto> getConferenceCustList(CmConferenceCustVo vo){
          return  conferenceConscustMapper.getConferenceCustDtoList(vo);
    }


    /**
     * @description:(批量往路演参会表中新增数据)
     * @param list
     * @return void
     * @author: xufanchao
     * @date: 2023/11/10 15:36
     * @since JDK 1.8
     */
    @Override
    public void bathchInsertList(List<CmConferenceConscust> list) {
        conferenceConscustMapper.batchInsertList(list);
    }
}

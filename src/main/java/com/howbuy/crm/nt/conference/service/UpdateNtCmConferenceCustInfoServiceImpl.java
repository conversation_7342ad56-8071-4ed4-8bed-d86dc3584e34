package com.howbuy.crm.nt.conference.service;

import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.domain.UpdateConferenceCustVo;
import com.howbuy.crm.nt.conference.dto.CmConferenceCustDto;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import com.howbuy.crm.nt.conference.request.UpdateNtCmConferenceCustInfoByCodeRequest;
import com.howbuy.crm.nt.conference.request.UpdateNtCmConferenceCustInfoRequest;
import com.howbuy.crm.nt.conference.response.UpdateNtCmConferenceCustByCodeResponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("updateNtCmConferenceCustInfoService")
public class UpdateNtCmConferenceCustInfoServiceImpl implements UpdateNtCmConferenceCustInfoService {


	@Autowired
	private CmConferenceCustInfoBuss cmConferenceCustInfoBuss;

	@Autowired
	private CmConferenceConscustMapper cmConferenceConscustMapper;
	
	@Override
	public BaseResponse updateConferenceSigning(UpdateNtCmConferenceCustInfoRequest request) {
		BaseResponse response = new BaseResponse();
		// 校验入参数据
		if (StringUtils.isBlank(request.getHboneNo())) {
			return new BaseResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（一账通）为空");
		}
		if (StringUtils.isBlank(request.getConferenceid())) {
			return new BaseResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（会议ID）为空");
		}
		if (request.getActualnub() == null ) {
			return new BaseResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（实到人数）为空");
		}

		UpdateConferenceCustVo updateVo = new UpdateConferenceCustVo();
		updateVo.setConferenceid(request.getConferenceid());
		updateVo.setActualnub(new BigDecimal(request.getActualnub()));
		updateVo.setHboneNo(request.getHboneNo());
		try {
			// update操作， 返回 code 不一致。无法共用
			int updateCount = cmConferenceCustInfoBuss.updateByVo(updateVo);
			if(updateCount > 0) {
				response.success();
			}else if(updateCount == 0){
				response.putBaseResult(BaseConstantEnum.FAIL, "客户未报名，签到失败！");
			}
		}catch(Exception e) {
			log.error(e.getMessage());
			response.putBaseResult(BaseConstantEnum.SYS_ERROR, "修改异常！");
		}

		return response;
	}

	@Override
	public UpdateNtCmConferenceCustByCodeResponse updateConferenceByCode(UpdateNtCmConferenceCustInfoByCodeRequest request) {

		// 校验入参数据
		if (StringUtils.isBlank(request.getAppointmentscode())) {
			return new UpdateNtCmConferenceCustByCodeResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（预约码）为空");
		}
		if (StringUtils.isBlank(request.getConferenceid())) {
			return new UpdateNtCmConferenceCustByCodeResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（会议ID）为空");
		}
		if (request.getActualnub() == null ) {
			return new UpdateNtCmConferenceCustByCodeResponse(BaseConstantEnum.PARAM_ERROR, "传入参数（实到人数）为空");
		}

		UpdateNtCmConferenceCustByCodeResponse response = new UpdateNtCmConferenceCustByCodeResponse();
		try {
			CmConferenceCustVo queryVo=new CmConferenceCustVo();
			queryVo.setConferenceId(request.getConferenceid());
			queryVo.setAppointmentsCode(request.getAppointmentscode());
			List<CmConferenceCustDto>  appointList=cmConferenceConscustMapper.getConferenceCustDtoList( queryVo);


			if(appointList.size() > 1){
				response.putBaseResult(BaseConstantEnum.FAIL, "同一会议下预约码重复，签到失败！");
			}else if(appointList.size() == 0){
				response.putBaseResult(BaseConstantEnum.DATA_NOT_FUND, "此预约码不存在，签到失败！");
			}else{
				UpdateConferenceCustVo updateVo = new UpdateConferenceCustVo();
				updateVo.setConferenceid(request.getConferenceid());
				updateVo.setActualnub(new BigDecimal(request.getActualnub()));
				updateVo.setAppointmentscode(request.getAppointmentscode());
				updateVo.setActualnubdt(new Date());

				updateCmConferenceCust(updateVo,response);
				if(BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					CmConferenceCustDto custDto= appointList.get(0);
					response.setCustname(custDto.getCustName());
					response.setConsname(custDto.getConsName());
				}
			}
		}catch(Exception e) {
			log.error(e.getMessage());
			response.processedFail();
		}
		return response;
	}

	public void  updateCmConferenceCust(UpdateConferenceCustVo updateVo ,BaseResponse response) {
		try {
			int updateCount = cmConferenceCustInfoBuss.updateByVo(updateVo);
			if(updateCount > 0) {
				response.success();
			}else if(updateCount == 0){
				response.putBaseResult(BaseConstantEnum.DATA_NOT_FUND, "客户未报名，签到失败！");
			}
		}catch(Exception e) {
			log.error(e.getMessage());
			response.putBaseResult(BaseConstantEnum.DEAL_FAIL, "修改异常！");
		}
	}


}

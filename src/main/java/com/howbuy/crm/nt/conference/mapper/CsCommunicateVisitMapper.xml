<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.conference.dao.CsCommunicateVisitDao">

	  <insert id="insertCsCommunicateVisit" parameterType="com.howbuy.crm.nt.conference.domain.CsCommunicateVisit" >
	    	INSERT INTO CS_COMMUNICATE_VISIT (
	    	<trim suffix="" suffixOverrides=",">	
	      	      <if test="id != null"> id, </if> 
	      	      <if test="conscustNo != null"> conscustNo, </if> 
	      	      <if test="consultType != null"> consultType, </if> 
	      	      <if test="commIntent != null"> commIntent, </if> 
	      	      <if test="investIntent != null"> investIntent, </if> 
	      	      <if test="amountFlag != null"> amountFlag, </if> 
	      	      <if test="specialMark != null"> specialMark, </if> 
	      	      <if test="deptFlag != null"> deptFlag, </if> 
	      	      <if test="commContent != null"> commContent, </if> 
	      	      <if test="taskId != null"> taskId, </if> 
	      	      <if test="bookingContent != null"> bookingContent, </if> 
	      	      <if test="callInId != null"> callInId, </if> 
	      	      <if test="visitType != null"> visitType, </if> 
	      	      <if test="nextDt != null"> nextDt, </if> 
	      	      <if test="consBookingId != null"> consBookingId, </if> 
	      	      <if test="nextVisitContent != null"> nextVisitContent, </if> 
	      	      <if test="nextStartTime != null"> nextStartTime, </if> 
	      	      <if test="nextEndTime != null"> nextEndTime, </if> 
	      	      <if test="nextVisitType != null"> nextVisitType, </if> 
	      	      <if test="visitClassify != null"> visitClassify, </if> 
	      	      <if test="remark != null"> remark, </if> 
	      	      <if test="modifyFlag != null"> modifyFlag, </if> 
	      	      <if test="creator != null"> creator, </if> 
	      	      creDt, 
	      	      <if test="modifier != null"> modifier, </if> 
	      	      <if test="modDt != null"> modDt, </if> 
	      	      stimeStamp,
	      	      <if test="hisFlag != null"> hisFlag, </if> 
	      	      <if test="hisId != null"> hisId, </if> 
	     	</trim>
           	) values (
         	<trim suffix="" suffixOverrides=",">
          	      <if test="id != null"> #{id}, </if> 
	      	      <if test="conscustNo != null"> #{conscustNo}, </if> 
	      	      <if test="consultType != null"> #{consultType}, </if> 
	      	      <if test="commIntent != null"> #{commIntent}, </if> 
	      	      <if test="investIntent != null"> #{investIntent}, </if> 
	      	      <if test="amountFlag != null"> #{amountFlag}, </if> 
	      	      <if test="specialMark != null"> #{specialMark}, </if> 
	      	      <if test="deptFlag != null"> #{deptFlag}, </if> 
	      	      <if test="commContent != null"> #{commContent}, </if> 
	      	      <if test="taskId != null"> #{taskId}, </if> 
	      	      <if test="bookingContent != null"> #{bookingContent}, </if> 
	      	      <if test="callInId != null"> #{callInId}, </if> 
	      	      <if test="visitType != null"> #{visitType}, </if> 
	      	      <if test="nextDt != null"> #{nextDt}, </if> 
	      	      <if test="consBookingId != null"> #{consBookingId}, </if> 
	      	      <if test="nextVisitContent != null"> #{nextVisitContent}, </if> 
	      	      <if test="nextStartTime != null"> #{nextStartTime}, </if> 
	      	      <if test="nextEndTime != null"> #{nextEndTime}, </if> 
	      	      <if test="nextVisitType != null"> #{nextVisitType}, </if> 
	      	      <if test="visitClassify != null"> #{visitClassify}, </if> 
	      	      <if test="remark != null"> #{remark}, </if> 
	      	      <if test="modifyFlag != null"> #{modifyFlag}, </if> 
	      	      <if test="creator != null"> #{creator}, </if> 
	      	      SYSDATE, 
	      	      <if test="modifier != null"> #{modifier}, </if> 
	      	      <if test="modDt != null"> #{modDt}, </if> 
	      	      SYSDATE, 
	      	      <if test="hisFlag != null"> #{hisFlag}, </if> 
	      	      <if test="hisId != null"> #{hisId}, </if> 
	     	</trim>	
         	)      
	  </insert>

	<update id="updateCsCommunicateVisit" parameterType="com.howbuy.crm.nt.conference.domain.CsCommunicateVisit" >
		UPDATE CS_COMMUNICATE_VISIT
		<set>
			<if test="conscustNo != null"> conscustNo = #{conscustNo}, </if>
			<if test="consultType != null"> consultType = #{consultType}, </if>
			<if test="commIntent != null"> commIntent = #{commIntent}, </if>
			<if test="investIntent != null"> investIntent = #{investIntent}, </if>
			<if test="amountFlag != null"> amountFlag = #{amountFlag}, </if>
			<if test="specialMark != null"> specialMark = #{specialMark}, </if>
			<if test="deptFlag != null"> deptFlag = #{deptFlag}, </if>
			<if test="commContent != null"> commContent = #{commContent}, </if>
			<if test="taskId != null"> taskId = #{taskId}, </if>
			<if test="bookingContent != null"> bookingContent = #{bookingContent}, </if>
			<if test="callInId != null"> callInId = #{callInId}, </if>
			<if test="visitType != null"> visitType = #{visitType}, </if>
			<if test="nextDt != null"> nextDt = #{nextDt}, </if>
			<if test="consBookingId != null"> consBookingId = #{consBookingId}, </if>
			<if test="nextVisitContent != null"> nextVisitContent = #{nextVisitContent}, </if>
			<if test="nextStartTime != null"> nextStartTime = #{nextStartTime}, </if>
			<if test="nextEndTime != null"> nextEndTime = #{nextEndTime}, </if>
			<if test="nextVisitType != null"> nextVisitType = #{nextVisitType}, </if>
			<if test="visitClassify != null"> visitClassify = #{visitClassify}, </if>
			<if test="remark != null"> remark = #{remark}, </if>
			<if test="modifyFlag != null"> modifyFlag = #{modifyFlag}, </if>
			<if test="creator != null"> creator = #{creator}, </if>
			<if test="creDt != null"> creDt = #{creDt}, </if>
			<if test="modifier != null"> modifier = #{modifier}, </if>
			modDt = sysdate,
			<if test="stimeStamp != null"> stimeStamp = #{stimeStamp}, </if>
		</set>
		where id = #{id}
	</update>
</mapper>


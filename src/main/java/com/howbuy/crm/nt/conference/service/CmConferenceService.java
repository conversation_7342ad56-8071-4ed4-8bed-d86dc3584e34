package com.howbuy.crm.nt.conference.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.common.exception.BusinessException;
import com.howbuy.crm.constant.SequenceConstants;
import com.howbuy.crm.nt.base.enums.ConferThemeTypeEnum;
import com.howbuy.crm.nt.base.param.CmParamBaseInfo;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceThemeMapper;
import com.howbuy.crm.nt.conference.dto.CmConference;
import com.howbuy.crm.nt.conference.dto.CmConferenceExtDto;
import com.howbuy.crm.nt.conference.dto.CmConferenceTheme;
import com.howbuy.crm.nt.param.service.AbstractParamFlowServiceImpl;
import crm.howbuy.base.enums.param.ParamTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: (路演会议service )
 * <AUTHOR>
 * @date 2023/10/30 11:18
 * @since JDK 1.8
 */
@Service
@Slf4j
public class CmConferenceService extends AbstractParamFlowServiceImpl<CmConferenceExtDto> {

    @Resource
    private CmConferenceMapper cmConferenceMapper;
    @Resource
    private CmConferenceThemeMapper cmConferenceThemeMapper;

    @Autowired
    private CommonDao commondao;
    @Autowired
    private CmConferenceScanMapper conferenceScanMapper;
    @Autowired
    private CmConferenceConscustMapper conferenceConscustMapper;


    @Override
    public ParamTypeEnum getParamType() {
        return ParamTypeEnum.CONFEREENCE;
    }

    /**
     * 获取 会议配置信息
     * @param conferenceid
     * @return
     */
    public  CmConference selectByPrimaryKey(String conferenceid){
      return cmConferenceMapper.selectByPrimaryKey(conferenceid);
    }

    @Override
    public CmConferenceExtDto getParamDtoByConfigId(String configId) {
        CmConferenceExtDto extDto=null;
        CmConference conference=selectByPrimaryKey(configId);
        if(conference!=null){
            extDto=new CmConferenceExtDto();
            BeanUtils.copyProperties(conference,extDto);
            //流程配置id
            extDto.setId(conference.getConferenceid());

            List<CmConferenceTheme> themeList=cmConferenceThemeMapper.selectByConferenceId(configId);
            //themeMap  为 按照themetype 分组，汇总 themetypevalue 的列表
            Map<String,List<CmConferenceTheme>> themeMap=
                    themeList.stream().collect(Collectors.groupingBy(CmConferenceTheme::getThemetype));
            extDto.setThemeMap(themeMap);
        }
        return extDto;
    }

    @Override
    public int updateStatus(CmParamBaseInfo baseInfo, String operator) {
        CmConference updateConfig=new CmConference();

        updateConfig.setAuditStatus(baseInfo.getAuditStatus());
        updateConfig.setRecStat(baseInfo.getRecStat());

        updateConfig.setId(baseInfo.getId());
        //流程配置id
        updateConfig.setConferenceid(baseInfo.getId());

        updateConfig.setModifier(operator);
        updateConfig.setModifyTimestamp(new Date());
        log.info("路演会议，更新配置状态：{}", JSON.toJSONString(updateConfig));
        return cmConferenceMapper.updateByPrimaryKeySelective(updateConfig);
    }

    @Override
    public NtReturnMessageDto<String> validateBeforeInsert(CmConferenceExtDto insertDto) {
        return NtReturnMessageDto.ok();
    }

    @Override
    public NtReturnMessageDto<String> validateBeforeUpdate(CmConferenceExtDto updateDto) {
        return NtReturnMessageDto.ok();
    }

    @Override
    public  NtReturnMessageDto<String> validateBeforeDelete(CmConferenceExtDto deleteDto){
//        该会议在[会议报名签到管理、客户名单管理、扫码参会客户]中已有参会信息，请取消之后再作删除
//        该会议在xxx中已有参会信息，请取消之后再作删除”，显示有数据的菜单名，如： 会议报名签到管理、客户名单管理、扫码参会客户
        List<String> menuList= Lists.newArrayList();
        //会议报名签到管理
        int  custCount=conferenceConscustMapper.selectCountByConferenceId(deleteDto.getId());
        if(custCount>0){
            menuList.add("会议报名签到管理");
        }
        //客户名单管理
        //扫码参会客户
        int  scanCount=conferenceScanMapper.selectCountByConferenceId(deleteDto.getId());
        if(scanCount>0){
            menuList.add("扫码参会客户");
        }
        if(CollectionUtils.isNotEmpty(menuList)){
            String menuStr=menuList.stream().collect(Collectors.joining("、"));
            return NtReturnMessageDto.fail(String.format("该会议在[%s]中已有参会信息，请取消之后再作删除",menuStr));
        }
        return NtReturnMessageDto.ok();
    }

    @Override
    public NtReturnMessageDto<String> insertConfig(CmConferenceExtDto dto, String operator) {
        CmConference insertPo=new CmConference();
        BeanUtils.copyProperties(dto,insertPo);
        insertPo.setCreater(operator);
        insertPo.setCreatdt(DateUtil.formatToString(new Date(),DateUtil.YYYYMMDD));
        insertPo.setCreateTimestamp(new Date());
        insertPo.setConferenceid(dto.getId());
        cmConferenceMapper.insert(insertPo);

        //插入新的主题
        int insertThemeCount=insertThemeMap(insertPo.getConferenceid(),dto.getThemeMap(),operator);
        log.info("路演会议ID:{} , 插入主题数量：{}",insertPo.getConferenceid(),insertThemeCount);
        return NtReturnMessageDto.ok();
    }

    @Override
    public int updateParam(CmConferenceExtDto cfgPo, String operator) {
        CmConference updatePo=new CmConference();
        updatePo.setId(cfgPo.getId());
        updatePo.setConferenceid(cfgPo.getId());

        //允许更新的属性 :  将 cfgPo 的属性 赋值给 updatePo
        updatePo.setConferencename(cfgPo.getConferencename());
        updatePo.setConferencetype(cfgPo.getConferencetype());
        updatePo.setCitycode(cfgPo.getCitycode());
        updatePo.setOrgcode(cfgPo.getOrgcode());
        updatePo.setProvcode(cfgPo.getProvcode());
        updatePo.setConferencedt(cfgPo.getConferencedt());
        updatePo.setCourseid(cfgPo.getCourseid());
        updatePo.setIslcjz(cfgPo.getIslcjz());

        updatePo.setCutoffdt(cfgPo.getCutoffdt());
        updatePo.setMaxnumber(cfgPo.getMaxnumber());
        updatePo.setMeetingcontents(cfgPo.getMeetingcontents());
        updatePo.setUncommitteddt(cfgPo.getUncommitteddt());
        updatePo.setNoiddt(cfgPo.getNoiddt());
        updatePo.setNoroutingdt(cfgPo.getNoroutingdt());

        updatePo.setAddress(cfgPo.getAddress());
        updatePo.setLat(cfgPo.getLat());
        updatePo.setLng(cfgPo.getLng());
//        备注	文本框
        updatePo.setRemark(cfgPo.getRemark());
        updatePo.setModifier(operator);
        int updateCount= cmConferenceMapper.updateByPrimaryKeySelective(updatePo);
        log.info("参数配置类型：{}，参数配置id：{}，更新配置数据：{}",
                getParamType().getDescription(),cfgPo.getId(), JSON.toJSONString(updatePo));

        //删除历史 主题
        int delThemeCount=cmConferenceThemeMapper.deleteByConferenceId(cfgPo.getConferenceid());
        //插入新的主题
        int insertThemeCount=insertThemeMap(cfgPo.getConferenceid(),cfgPo.getThemeMap(),operator);
        log.info("路演会议ID:{} ,删除历史主题数量：{} , 插入主题数量：{}",cfgPo.getConferenceid(),delThemeCount,insertThemeCount);

        return updateCount;
    }

    @Override
    public String generateParamId() {
        return commondao.getSeqValue(SequenceConstants.SEQ_CONFERENCE);
    }


    /**
     * 根据 Map 插入主题列表
     * @param conferenceId 会议id
     * @param themeMap 主题map
     * @param operator 操作人
     * @return 插入数量
     */
    private int insertThemeMap(String conferenceId,
                                Map<String,List<CmConferenceTheme>> themeMap,
                                String operator){
        int insertThemeCount=0;
        if(themeMap!=null && !themeMap.isEmpty()){
            List<CmConferenceTheme> themeList= Lists.newArrayList();
            themeMap.forEach((key,value)->{
                ConferThemeTypeEnum themeTypeEnum=ConferThemeTypeEnum.getEnum(key);
                if(themeTypeEnum==null){
                    throw new BusinessException(String.format("会议主题类型不存在，主题类型：%s",key));
                }
                value.forEach(themeObj->{
                    CmConferenceTheme theme=new CmConferenceTheme();
                    theme.setConferenceid(conferenceId);
                    theme.setThemetype(themeTypeEnum.getCode());
                    theme.setThemetypevalue(themeObj.getThemetypevalue());
                    theme.setThemeDesc(themeObj.getThemeDesc());
                    theme.setCreater(operator);
                    theme.setCreatdt(DateUtil.formatToString(new Date(),DateUtil.YYYYMMDD));
                    themeList.add(theme);
                });
            });
            insertThemeCount=cmConferenceThemeMapper.batchInsertList(themeList);
        }
        return insertThemeCount;
    }
}

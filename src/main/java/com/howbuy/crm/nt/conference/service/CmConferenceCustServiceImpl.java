/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service;

import com.howbuy.common.utils.Assert;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.request.ConferenceCustCancelVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: (路演会议预约service)
 * <AUTHOR>
 * @date 2023/11/17 12:22
 * @since JDK 1.8
 */
@Slf4j
@Service("cmConferenceCustService")
public class CmConferenceCustServiceImpl implements CmConferenceCustService{

    @Resource
    private CmConferenceConscustMapper cmConferenceConscustMapper;
    @Resource
    private CmConferenceCustInfoBuss conferenceCustInfoBuss;

    @Override
    public List<CmConferenceConscust> selectCustList(String conferenceId, List<List<String>> custNoList) {
        Assert.notNull(conferenceId,"会议id不能为空");
        Assert.notNull(custNoList,"客户号列表不能为空");
        return cmConferenceConscustMapper.selectCustList(conferenceId,custNoList);
    }


    @Override
    public NtReturnMessageDto<String> batchCancel(List<ConferenceCustCancelVo> cancelList){
        Assert.notNull(cancelList);
        cancelList.forEach(cancelVo -> conferenceCustInfoBuss.cancel(cancelVo));
       return NtReturnMessageDto.ok();
    }
}
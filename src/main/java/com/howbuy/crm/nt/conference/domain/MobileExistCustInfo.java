/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.domain;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.base.enums.ConferCustStateEnum;
import com.howbuy.crm.nt.conference.dto.MobileCustInfoDto;
import lombok.Data;

import java.util.List;

/**
 * @description: (根据手机号码-mobileDigest 判断 手机号码存在信息场景)
 * <AUTHOR>
 * @date 2023/11/6 16:18
 * @since JDK 1.8
 */
@Data
public class MobileExistCustInfo {


    /**
     * 实时手机号码[mobileDigest]判断客户信息状态：
     0-不存在客户 1-存在唯一客户 2-存在多个客户
     */
    private ConferCustStateEnum custStateEnum;


    /**
     * 根据 mobileDigest 查询出来的客户信息 列表
     */
    private List<MobileCustInfoDto> existCustList= Lists.newArrayList();


    /**
     * 根据 mobileDigest 查询出来的客户信息=1条时，客户信息对象。
     *
     */
    private  MobileCustInfoDto usedCustInfo;

}
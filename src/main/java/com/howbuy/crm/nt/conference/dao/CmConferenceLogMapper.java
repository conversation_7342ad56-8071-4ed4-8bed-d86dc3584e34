package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.dto.CmConferenceLog;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * @description: (cms调用记录日志表 Mapper)
 * <AUTHOR>
 * @date 2023/10/30 13:20
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceLogMapper {
    int insert(CmConferenceLog record);

    /**
     * 更新数据
     *
     * @param cmConferenceLog
     * @return
     */
    int updateCmConferenceLog(CmConferenceLog cmConferenceLog);

    /**
     *  查询未成功处理的cms调用数据
     * @return
     */
    List<CmConferenceLog> listCmConferenceLog();
}
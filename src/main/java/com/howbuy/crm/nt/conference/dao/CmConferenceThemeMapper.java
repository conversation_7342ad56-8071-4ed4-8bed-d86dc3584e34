package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.dto.CmConferenceTheme;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/30 17:01
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceThemeMapper {
    int insert(CmConferenceTheme record);


    /**
     * @description:(批量插入 会议主题列表)
     * @param list
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/13 20:22
     * @since JDK 1.8
     */
    int batchInsertList(List<CmConferenceTheme> list);

    /**
     * 根据会议id查询会议主题列表
     * @param conferenceId
     * @return
     */
    List<CmConferenceTheme> selectByConferenceId(@Param("conferenceId") String conferenceId);


    /**
     * 根据会议id列表查询会议主题列表
     * @param conferenceIdList
     * @return
     */
    List<CmConferenceTheme> selectByConferenceIdList(@Param("conferenceIdList") List<List<String>> conferenceIdList);


    /**
     * 根据会议id删除会议主题列表
     * @param conferenceId
     * @return
     */
    int deleteByConferenceId(@Param("conferenceId") String conferenceId);
}
package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.dto.CmConferenceScanHis;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/3 18:28
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceScanHisMapper {
    int deleteByPrimaryKey(BigDecimal id);

    int insert(CmConferenceScanHis record);


    /**
     * 批量插入
     * @param idList
     * @return
     */
    int insertByScanIdList(@Param("idList") List<BigDecimal> idList,@Param("operator") String operator);

    CmConferenceScanHis selectByPrimaryKey(BigDecimal id);

    int updateByPrimaryKeySelective(CmConferenceScanHis record);

    int updateByPrimaryKey(CmConferenceScanHis record);
}
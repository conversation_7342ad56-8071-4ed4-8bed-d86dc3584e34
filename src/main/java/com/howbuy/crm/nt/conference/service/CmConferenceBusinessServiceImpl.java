package com.howbuy.crm.nt.conference.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.constant.HbTypeCodeConstant;
import com.howbuy.crm.constant.MsgBusinessIdConstants;
import com.howbuy.crm.hbconstant.service.HbConstantService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.base.enums.ConferThemeTypeEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceThemeMapper;
import com.howbuy.crm.nt.conference.dto.*;
import com.howbuy.crm.nt.conference.request.CmConferenceUpdateVo;
import com.howbuy.crm.nt.conference.request.CmConferenceVo;
import com.howbuy.crm.nt.conference.request.QueryCmConferenceStudyRequest;
import com.howbuy.crm.nt.conference.request.QueryCmOfflineActivityRequest;
import com.howbuy.crm.nt.conference.response.QueryCmConferenceStudyResponse;
import com.howbuy.crm.nt.conference.response.QueryCmOfflineActivityResponse;
import com.howbuy.crm.nt.jgxx.dao.JgxxMapper;
import com.howbuy.crm.nt.jgxx.domain.Jgxx;
import com.howbuy.crm.nt.param.vo.ParamAuditVo;
import com.howbuy.crm.nt.param.vo.ParamDeleteVo;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.enums.param.ParamAuditStatusEnum;
import crm.howbuy.base.utils.ParamUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description: (路演会议service)
 * @date 2023/10/30 11:18
 * @since JDK 1.8
 */
@Service("cmConferenceBusinessService")
@Slf4j
public class CmConferenceBusinessServiceImpl implements CmConferenceBusinessService {

    @Resource
    private CmConferenceMapper cmConferenceMapper;
    @Resource
    private CmConferenceThemeMapper cmConferenceThemeMapper;
    @Resource
    private CmConferenceService conferenceService;
    @Resource
    private JjxxInfoService jjxxInfoService;
    @Resource
    private HbConstantService hbConstantService;
    @Resource
    private JgxxMapper jgxxMapper;
    @Resource
    private CmPushMsgService cmPushMsgService;


    @Override
    public PageResult<CmConferenceDisplayDto> selectPageByVo(CmConferenceVo queryVo) {
        PageHelper.startPage(queryVo.getPage(), queryVo.getRows());
        Page<CmConferenceDisplayDto> page = cmConferenceMapper.selectPageByVo(queryVo);
        PageResult<CmConferenceDisplayDto> pageResult = new PageResult<>();
        pageResult.setTotal(page.getTotal());
        List<CmConferenceDisplayDto> rows = page.getResult();

        //补充 会议主题信息
        if (CollectionUtils.isNotEmpty(rows)) {
            //汇总 会议ID 列表
            List<String> conferenceIdList = rows.stream().map(CmConference::getConferenceid).collect(Collectors.toList());
            //查询 会议id 列表 所有的会议主题列表 。按照 会议id 分组
            Map<String, List<CmConferenceTheme>> themeMap = getThemeList(conferenceIdList);

            //获取 翻译信息
            Map<String, String> lcjzTypeMap = getTranslateDescMap(HbTypeCodeConstant.LCJZ_CONFERENCE_TYPE);
            Map<String, String> nonLcjzTypeMap = getTranslateDescMap(HbTypeCodeConstant.FULL_CONFERENCE_TYPE);


            rows.forEach(row -> {
                //主题
                List<CmConferenceTheme> themeList = themeMap.get(row.getConferenceid());
                if (CollectionUtils.isNotEmpty(themeList)) {
                    Map<String, List<CmConferenceTheme>> themeTypeMap = themeList.stream()
                            .collect(Collectors.groupingBy(CmConferenceTheme::getThemetype));
                    row.setThemeMap(themeTypeMap);
                }
                //产品类型 描述
                //产品类型为  ,分割的code列表
                translateTypeDesc(row, lcjzTypeMap, nonLcjzTypeMap);

                //审核不通过时，查询最新的审核意见
                addLatestRejectAdvice(row);

            });
        }
        pageResult.setRows(rows);
        return pageResult;
    }

    /**
     * 会议类型 转换
     *
     * @param dto
     * @param lcjzTypeMap
     * @param nonLcjzTypeMap
     */
    private void translateTypeDesc(CmConferenceDisplayDto dto,
                                   Map<String, String> lcjzTypeMap,
                                   Map<String, String> nonLcjzTypeMap) {
        String conferenceType = dto.getConferencetype();
        Map<String, String> usedTypeMap = YesOrNoEnum.YES.getCode().equals(dto.getIslcjz()) ? lcjzTypeMap : nonLcjzTypeMap;
        if (StringUtil.isNotNullStr(conferenceType)) {
            List<String> typeCodeList = ParamUtil.getParamList(conferenceType, Lists.newArrayList());
            //类型code 列表
            dto.setTypeCodeList(typeCodeList);
            //类型 描述 翻译
            dto.setConferenceTypeDesc(typeCodeList.stream()
                    .map(code -> usedTypeMap.getOrDefault(code, code))
                    .collect(Collectors.joining(",")));
        }
    }

    /**
     * 会议状态为 审核不通过时，查询最新的审核意见
     *
     * @param dto
     */
    private void addLatestRejectAdvice(CmConferenceDisplayDto dto) {
        if (ParamAuditStatusEnum.AUDIT_REJECT.getCode().equals(dto.getAuditStatus())) {
            String latestRejectAdvice = conferenceService.getLatestRejectAdvice(dto.getConferenceid());
            dto.setAudtAdvice(latestRejectAdvice);
        }
    }


    /**
     * 根据会议id列表查询会议主题列表 key:会议id  value:会议主题列表
     *
     * @param conferenceIdList
     * @return
     */
    private Map<String, List<CmConferenceTheme>> getThemeList(List<String> conferenceIdList) {
        List<CmConferenceTheme> allList = cmConferenceThemeMapper.selectByConferenceIdList(Lists.partition(conferenceIdList, 1000));
        return allList.stream()
                .collect(Collectors.groupingBy(CmConferenceTheme::getConferenceid));
    }


    /**
     * 会议 主题信息 转换
     *
     * @param vo
     * @return
     */
    private Map<String, List<CmConferenceTheme>> getThemeMapByVo(CmConferenceUpdateVo vo) {
        Map<String, List<CmConferenceTheme>> themeMap = Maps.newHashMap();
        if (StringUtil.isNotNullStr(vo.getSingleProduct())) {
            List<String> singleProductList = ParamUtil.getParamList(vo.getSingleProduct(), Lists.newArrayList());
            Map<String, String> jjjcMap = Maps.newHashMap();
            singleProductList.forEach(jjdm -> {
                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(jjdm);
                if (jjxxInfo != null) {
                    jjjcMap.put(jjdm, jjxxInfo.getJjjc());
                }
            });
            putToMap(ConferThemeTypeEnum.SINGLE_PRODUCT, singleProductList, jjjcMap, themeMap);
        }
        if (StringUtil.isNotNullStr(vo.getManager())) {
            List<String> managerList = ParamUtil.getParamList(vo.getManager(), Lists.newArrayList());
            List<Jgxx> jgxxList = jgxxMapper.selectByJgdmList(managerList);
            //机构代码-机构简称
            Map<String, String> jgjcMap = jgxxList.stream()
                    .collect(Collectors.toMap(jgxx -> jgxx.getJgdm(), jgxx -> jgxx.getJgjc()));
            putToMap(ConferThemeTypeEnum.MANAGER, managerList, jgjcMap, themeMap);
        }

        if (StringUtil.isNotNullStr(vo.getProductLine())) {
            List<String> productLineList = ParamUtil.getParamList(vo.getProductLine(), Lists.newArrayList());
            Map<String, String> descMap = getTranslateDescMap(HbTypeCodeConstant.CONFERENCE_THEME_PRODUCTLINE_TYPE);

            putToMap(ConferThemeTypeEnum.PRODUCT_LINE, productLineList, descMap, themeMap);
        }
        return themeMap;
    }


    /**
     * 根据常量翻译的类型code 获取翻译的map
     *
     * @param typeCode
     * @return
     */
    private Map<String, String> getTranslateDescMap(String typeCode) {
        Assert.notNull(typeCode);
        return hbConstantService.getHbConstantMap(typeCode);
    }


    /**
     * 会议 主题信息 转换
     *
     * @param themeTypeEnum
     * @param codeList
     * @param descMap
     * @param themeMap
     */
    private static void putToMap(ConferThemeTypeEnum themeTypeEnum,
                                 List<String> codeList,
                                 Map<String, String> descMap,
                                 Map<String, List<CmConferenceTheme>> themeMap) {
        if (!themeMap.containsKey(themeTypeEnum.getCode())) {
            themeMap.put(themeTypeEnum.getCode(), Lists.newArrayList());
        }
        codeList.forEach(code -> {
            CmConferenceTheme theme = new CmConferenceTheme();
            theme.setThemetype(themeTypeEnum.getCode());
            theme.setThemetypevalue(code);
            theme.setThemeDesc(descMap.getOrDefault(code, null));
            themeMap.get(themeTypeEnum.getCode()).add(theme);
        });
    }


    @Override
    public NtReturnMessageDto<String> insertFlow(CmConferenceUpdateVo insertVo) {
        CmConferenceExtDto extDto = new CmConferenceExtDto();
        BeanUtils.copyProperties(insertVo, extDto);

        extDto.setThemeMap(getThemeMapByVo(insertVo));
        return conferenceService.insertFlow(extDto, insertVo.getOperator(), insertVo.getHoldAuth());
    }

    @Override
    public NtReturnMessageDto<String> updateFlow(CmConferenceUpdateVo updateVo) {
        CmConferenceExtDto extDto = new CmConferenceExtDto();
        BeanUtils.copyProperties(updateVo, extDto);

        extDto.setThemeMap(getThemeMapByVo(updateVo));

        //流程 处理 需要
        extDto.setId(updateVo.getConferenceid());
        return conferenceService.updateFlow(extDto, updateVo.getOperator(), updateVo.getHoldAuth());
    }

    @Override
    public NtReturnMessageDto<String> auditFlow(ParamAuditVo paramAuditVo) {
        NtReturnMessageDto<String> auditMsg = conferenceService.auditFlow(paramAuditVo);
        if (auditMsg.isSuccess()) {
            //准备消息推送参数
            CmConference conference = conferenceService.selectByPrimaryKey(paramAuditVo.getParamId());
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("meetingname", conference.getConferencename());
            paramMap.put("meetingid", conference.getConferenceid());
            //消息推送接口，给预约创建人推送消息
            if (YesOrNoEnum.YES == paramAuditVo.getAuditPass()) {
                //【路演会议通过】以下路演已审核通过，路演名称：${meetingname}，路演ID：${meetingid}，可至路演管理页下载会议二维码；
                cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.CONFERENCE_AUDIT_PASS,
                        Lists.newArrayList(conference.getCreater()), paramMap);
            }
            if (YesOrNoEnum.NO == paramAuditVo.getAuditPass()) {
                paramMap.put("reason", paramAuditVo.getAuditAdvice());
                //【路演会议驳回】以下路演审核驳回，路演：${meetingname}，路演ID：${meetingid}，驳回原因为：${reason}，可至路演管理页进行修改；
                cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.CONFERENCE_AUDIT_REJECT,
                        Lists.newArrayList(conference.getCreater()), paramMap);

            }

        }

        return auditMsg;
    }

    @Override
    public NtReturnMessageDto<String> deleteFlow(ParamDeleteVo paramDeleteVo) {
        return conferenceService.deleteFlow(paramDeleteVo);
    }

    @Override
    public NtReturnMessageDto<String> validateBeforeDelete(String conferenceId) {
        CmConferenceExtDto conferenceExtDto = conferenceService.getParamDtoByConfigId(conferenceId);
        return conferenceService.validateBeforeDelete(conferenceExtDto);
    }

    @Override
    public CmConferenceDisplayDto selectDisplayDtoByConferenceId(String conferenceId) {
        CmConferenceExtDto extDto = conferenceService.getParamDtoByConfigId(conferenceId);

        return transferToDisplayDto(extDto);
    }

    /**
     * 转换为展示dto.包括了 产品类型的翻译 审核不通过时的审核意见
     *
     * @param extDto
     * @return
     */
    private CmConferenceDisplayDto transferToDisplayDto(CmConferenceExtDto extDto) {
        if (extDto == null) {
            return null;
        }
        CmConferenceDisplayDto displayDto = new CmConferenceDisplayDto();
        BeanUtils.copyProperties(extDto, displayDto);
        displayDto.setThemeMap(extDto.getThemeMap());

        //获取 翻译信息
        Map<String, String> lcjzTypeMap = getTranslateDescMap(HbTypeCodeConstant.LCJZ_CONFERENCE_TYPE);
        Map<String, String> nonLcjzTypeMap = getTranslateDescMap(HbTypeCodeConstant.FULL_CONFERENCE_TYPE);
        //产品类型 描述
        //产品类型为  ,分割的code列表
        translateTypeDesc(displayDto, lcjzTypeMap, nonLcjzTypeMap);

        //审核不通过时，查询最新的审核意见
        addLatestRejectAdvice(displayDto);
        return displayDto;
    }

    @Override
    public NtReturnMessageDto<CmConferenceDisplayDto> selectAuditDtoByConferenceId(String conferenceId) {

        NtReturnMessageDto<CmConferenceExtDto> msg = conferenceService.getAuditData(conferenceId);
        if (!msg.isSuccess()) {
            return NtReturnMessageDto.fail(msg.getReturnMsg());
        }

        CmConferenceExtDto auditData = msg.getReturnObject();
        //当前 会议属性
        CmConferenceExtDto existData = conferenceService.getParamDtoByConfigId(conferenceId);

        //允许更新的属性 :  将 auditData 的属性 赋值给 existData
        existData.setConferencename(auditData.getConferencename());
        existData.setConferencetype(auditData.getConferencetype());
        existData.setCitycode(auditData.getCitycode());
        existData.setOrgcode(auditData.getOrgcode());
        existData.setProvcode(auditData.getProvcode());
        existData.setConferencedt(auditData.getConferencedt());
        existData.setCourseid(auditData.getCourseid());
        existData.setIslcjz(auditData.getIslcjz());

        existData.setCutoffdt(auditData.getCutoffdt());
        existData.setMaxnumber(auditData.getMaxnumber());
        existData.setMeetingcontents(auditData.getMeetingcontents());
        existData.setUncommitteddt(auditData.getUncommitteddt());
        existData.setNoiddt(auditData.getNoiddt());
        existData.setNoroutingdt(auditData.getNoroutingdt());

        existData.setAddress(auditData.getAddress());
        existData.setLat(auditData.getLat());
        existData.setLng(auditData.getLng());
//        备注	文本框
        existData.setRemark(auditData.getRemark());

        //明细列表
        existData.setThemeMap(auditData.getThemeMap());

        return NtReturnMessageDto.ok("", transferToDisplayDto(existData));

    }

    @Override
    public NtReturnMessageDto<CmPortraitStudyProgressDTO> getPortraitStudyProgress(String hboneNo) {
        List<CmPortraitStudyProgressDTO> portraitStudyProgressDTOList = cmConferenceMapper.queryPortraitStudyProgress(hboneNo);
        if (CollectionUtils.isEmpty(portraitStudyProgressDTOList)) {
            log.info("未查询到客户学习进度信息!");
            return NtReturnMessageDto.ok(null);
        }

        for (CmPortraitStudyProgressDTO dto : portraitStudyProgressDTOList) {
            String conferenceType = dto.getConferenceType();
            if (StringUtil.isEmpty(conferenceType)) {
                continue;
            }
            List<String> typeList = Stream.of(conferenceType.split(","))
                    .filter(StringUtil::isNotNullStr)
                    .distinct()
                    .collect(Collectors.toList());
            dto.setTypeCodeList(typeList);
        }

        return NtReturnMessageDto.ok("",portraitStudyProgressDTOList);
    }

    @Override
    public QueryCmConferenceStudyResponse getFinancialNineStudyPageList(QueryCmConferenceStudyRequest request) {
        QueryCmConferenceStudyResponse response = new QueryCmConferenceStudyResponse();
        PageHelper.startPage(request.getPage(), request.getRows());
        Page<CmPortraitStudyDetailDTO> page = cmConferenceMapper.queryFinancialNineStudyPageList(request.getHboneNo());
        if (null == page) {
            log.info("未查询到客户学习明细信息!");
            return response;
        }

        List<CmPortraitStudyDetailDTO> resultList = page.getResult();
        for (CmPortraitStudyDetailDTO dto : resultList) {
            String conferenceType = dto.getConferenceType();
            if (StringUtil.isEmpty(conferenceType)) {
                continue;
            }
            List<String> typeList = Stream.of(conferenceType.split(","))
                    .filter(StringUtil::isNotNullStr)
                    .distinct()
                    .collect(Collectors.toList());
            dto.setTypeCodeList(typeList);
        }

        response.setDataList(resultList);
        response.setTotal(((Long) page.getTotal()).intValue());
        response.success();
        return response;
    }

    @Override
    public QueryCmOfflineActivityResponse getOfflineActivityPageList(QueryCmOfflineActivityRequest request) {
        QueryCmOfflineActivityResponse response = new QueryCmOfflineActivityResponse();
        PageHelper.startPage(request.getPage(), request.getRows());
        Page<CmPortraitStudyDetailDTO> page = cmConferenceMapper.queryOfflineActivityPageList(request.getHboneNo());
        if (null == page) {
            log.info("未查询到客户学习明细信息!");
            return response;
        }

        List<CmPortraitStudyDetailDTO> resultList = page.getResult();
        for (CmPortraitStudyDetailDTO dto : resultList) {
            String conferenceType = dto.getConferenceType();
            if (StringUtil.isEmpty(conferenceType)) {
                continue;
            }
            List<String> typeList = Stream.of(conferenceType.split(","))
                    .filter(StringUtil::isNotNullStr)
                    .distinct()
                    .collect(Collectors.toList());
            dto.setTypeCodeList(typeList);
        }

        response.setDataList(resultList);
        response.setTotal(((Long) page.getTotal()).intValue());
        response.success();
        return response;
    }

}

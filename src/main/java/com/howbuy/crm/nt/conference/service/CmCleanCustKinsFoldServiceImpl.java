/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service;

import com.howbuy.crm.nt.conference.dao.CmCleanCustKinsFoleDao;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.domain.CmConferenceDomain;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.request.CmConferenceCustVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: (CRM至臻年会-参会人员状态批处理)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Slf4j
@Service("cmCleanCustKinsFoldService")
@Transactional(rollbackFor = Exception.class)
public class CmCleanCustKinsFoldServiceImpl implements CmCleanCustKinsFoldService{

    @Autowired
    private CmCleanCustKinsFoleDao cmCleanCustKinsFoleDao;
    @Autowired
    private CmConferenceConscustMapper conferenceConscustMapper;



    private List<CmConferenceConscust> getConferenceCustList(String conferenceId) {
        CmConferenceCustVo queryVo= new CmConferenceCustVo();
        queryVo.setConferenceId(conferenceId);
        return conferenceConscustMapper.getConferenceCustList( queryVo);
    }

    @Override
    public void dealCmCleanCustKinsFole() {

        // 查询节点1的数据
        List<CmConferenceDomain> cmConferences = cmCleanCustKinsFoleDao.selectConferencesByTypeAndDate1();
        // 节点1需要删除的数据
        List<CmConferenceConscust> cmConferenceConscustList = new ArrayList<>();
        cmConferences.forEach(it -> {
            cmConferenceConscustList.addAll(getConferenceCustList(it.getConferenceid()));
        });
        // 对节点1的数据，清理未确认客户参会信息的数据
        cmCleanCustKinsFoleDao.deleteConferenceCustkinsfolkByConferenceId(cmConferenceConscustList);
        // 对节点1的数据，参会状态为未参会，预约参会人数变更为0
        cmCleanCustKinsFoleDao.updateConferenceConscust(cmConferenceConscustList);

        // 查询节点2的数据
        cmConferenceConscustList.clear();
        List<CmConferenceDomain> cmConferences1 = cmCleanCustKinsFoleDao.selectConferencesByTypeAndDate2();
        cmConferences1.forEach(it -> {
            String conferenceid = it.getConferenceid();
            List<CmConferenceConscust> cmConferenceConscusts = getConferenceCustList(conferenceid);
            cmConferenceConscusts.forEach(it1 -> {
                String conferenceid1 = it1.getConferenceid();
                String conscustno = it1.getConscustno();
                Integer count = cmCleanCustKinsFoleDao.countCustkinsfolkByIdNo(conferenceid1, conscustno);
                if (count == 0) {
                    cmConferenceConscustList.add(it1);
                }
            });
        });
        // 删除节点二满足条件的数据
        cmCleanCustKinsFoleDao.batchDeleteCustkinsfolkByIdNoNull(cmConferenceConscustList);
        // 更新节点二满足条件的数据
        cmCleanCustKinsFoleDao.updateConferenceConscust(cmConferenceConscustList);

        // 对节点三进行相关操作
        cmConferenceConscustList.clear();
        List<CmConferenceDomain> cmConferences2 = cmCleanCustKinsFoleDao.selectConferenceByTypeAndDate3();
        cmConferences2.forEach(it -> {
            String conferenceid = it.getConferenceid();
            List<CmConferenceConscust> cmConferenceConscustList1 = getConferenceCustList(conferenceid);
            cmConferenceConscustList1.forEach(it1 -> {
                String conferenceid1 = it1.getConferenceid();
                String conscustno = it1.getConscustno();
                int countByConferenceAndCust = cmCleanCustKinsFoleDao.getCountByConferenceAndCust(conferenceid1, conscustno);
                if (countByConferenceAndCust == 0) {
                    cmConferenceConscustList.add(it1);
                }
            });
        });
        // 删除节点三满足条件的数据
        cmCleanCustKinsFoleDao.batchDeleteCmConferenceCustkinsfolk(cmConferenceConscustList);
        // 更新节点三满足条件的数据
        cmCleanCustKinsFoleDao.updateConferenceConscust(cmConferenceConscustList);

    }
}
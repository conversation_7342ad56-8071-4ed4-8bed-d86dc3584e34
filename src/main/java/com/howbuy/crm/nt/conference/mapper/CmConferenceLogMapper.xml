<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceLogMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceLog">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE_LOG-->
    <result column="HBONENO" jdbcType="VARCHAR" property="hboneno" />
    <result column="CONFERENCE_ID" jdbcType="VARCHAR" property="conferenceId" />
    <result column="IS_DEAL" jdbcType="VARCHAR" property="isDeal" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    HBONENO, CONFERENCE_ID, IS_DEAL
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceLog">
    <!--@mbg.generated-->
    insert into
    CM_CONFERENCE_LOG (HBONENO, CONFERENCE_ID, IS_DEAL)
    values (#{hboneno,jdbcType=VARCHAR}, #{conferenceId,jdbcType=VARCHAR},'0')
  </insert>

  <update id="updateCmConferenceLog" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceLog">
    update CM_CONFERENCE_LOG
    set IS_DEAL = '1'
    where CONFERENCE_ID = #{conferenceId,jdbcType=VARCHAR}
    and HBONENO = #{hboneno,jdbcType=VARCHAR}
  </update>


  <select id="listCmConferenceLog" resultMap="BaseResultMap">
    select cc.CONFERENCE_ID,
           cc.HBONENO,
           cc.IS_DEAL
    from CM_CONFERENCE_LOG cc
    where cc.is_deal = '0'
  </select>



</mapper>
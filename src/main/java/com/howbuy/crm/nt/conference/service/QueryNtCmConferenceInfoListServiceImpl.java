package com.howbuy.crm.nt.conference.service;

import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dto.CmNtConferenceDomain;
import com.howbuy.crm.nt.conference.request.QueryNtCmConferenceInfoListRequest;
import com.howbuy.crm.nt.conference.response.QueryNtCmConferenceInfoListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("queryNtCmConferenceInfoListService")
@Slf4j
public class QueryNtCmConferenceInfoListServiceImpl implements QueryNtCmConferenceInfoListService {

	@Autowired
	private CmConferenceMapper cmConferenceMapper;
	
	@Override
	public QueryNtCmConferenceInfoListResponse queryCmConferenceInfoList(QueryNtCmConferenceInfoListRequest request) {
		
		QueryNtCmConferenceInfoListResponse response = new QueryNtCmConferenceInfoListResponse();
		
		try {
			List<CmNtConferenceDomain> cmConferenceList =
					cmConferenceMapper.queryCmConferenceInfoList(request.getHboneNo(),
															   request.getConferenceidlist(),
															   request.getActualnub());
			if(CollectionUtils.isNotEmpty(cmConferenceList)) {
				response.setCmConferenceDomainList(cmConferenceList);
			}
			response.success();
		}catch(Exception e) {
			log.error("查询路演会议错误：",e);
			response.processedFail();
		}
		return response;
	}

}

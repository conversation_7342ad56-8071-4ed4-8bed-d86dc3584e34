package com.howbuy.crm.nt.conference.dao;

import com.github.pagehelper.Page;
import com.howbuy.crm.nt.conference.dto.CmConferenceScan;
import com.howbuy.crm.nt.conference.dto.CmConferenceScanDisplayDto;
import com.howbuy.crm.nt.conference.request.CmConferenceScanPageVo;
import com.howbuy.crm.nt.conference.request.CmConferenceScanVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/11/3 18:27
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceScanMapper {

    int deleteByScanIdList(@Param("idList") List<BigDecimal> idList);

    int insert(CmConferenceScan record);

    /**
     * 新增扫码参会客户表密文记录
     * @param scanId 扫码参会客户表主键
     * @param mobileCipher 手机号密文
     */
    void insertConferenceScanCipher(@Param("scanId") BigDecimal scanId,
                                    @Param("mobileCipher") String mobileCipher);


    CmConferenceScan selectByPrimaryKey(BigDecimal id);


    /**
     * 根据会议ID查询 扫码参会 count记录
     * @param conferenceId
     * @return
     */
    int selectCountByConferenceId(@Param("conferenceId") String conferenceId);


    /**
     * 根据会议id和手机号摘要查询扫码参会客户表
     * @param conferenceId
     * @param mobileDigest
     * @return
     */
    CmConferenceScan selectByConferenceIdAndMobile(@Param("conferenceId") String conferenceId,
                                                   @Param("mobileDigest") String mobileDigest);

    int updateByPrimaryKeySelective(CmConferenceScan record);

    int updateByPrimaryKey(CmConferenceScan record);


    /**
     * 根据vo 查询 扫码参会客户表
     * @param scanVo
     * @return
     */
    List<CmConferenceScan> selectListByVo(CmConferenceScanVo  scanVo);

    /**
     * 路演扫码参会 页面查询 分页查询
     * @param queryVo
     * @return
     */
    Page<CmConferenceScanDisplayDto> selectPageByPageVo(CmConferenceScanPageVo queryVo);
}
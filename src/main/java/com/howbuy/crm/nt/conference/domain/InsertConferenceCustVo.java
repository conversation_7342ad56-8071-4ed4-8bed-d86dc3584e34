/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.domain;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (路演会议预约 插入预约表数据vo)
 * <AUTHOR>
 * @date 2023/11/8 18:55
 * @since JDK 1.8
 */
@Data
public class InsertConferenceCustVo {

    /**
     * 客户号
     */
    private String custNo;

    /**
     * 路演ID
     */
    private String conferenceId;

    /**
     * 创建人
     */
    private String creator;


    /**
     * 预约 参会人数
     */
    private BigDecimal appointmentsNumber;


    /**
     * 实际 参会人数
     */
    private BigDecimal actualNumber;


    /**
     * 实际 参会 报道时间
     */
    private Date actualNubDt;




}
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmCleanCustKinsFoleDao">

    <select id="selectConferencesByTypeAndDate1" resultType="com.howbuy.crm.nt.conference.domain.CmConferenceDomain">
        SELECT *
        FROM CM_CONFERENCE t
        WHERE t.conferencetype = '6'
          AND (t.uncommitteddt || ' 20:00:00') <![CDATA[ <= ]]> to_char(sysdate, 'yyyy-MM-dd HH:mm:ss')
    </select>


    <delete id="deleteConferenceCustkinsfolkByConferenceId" parameterType="list">
        DELETE FROM cm_conference_custkinsfolk
        WHERE (conferenceid, conscustno) IN (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT #{item.conferenceid,jdbcType=VARCHAR}, #{item.conscustno,jdbcType=VARCHAR} FROM DUAL
        </foreach>
        )
    </delete>

    <update id="updateConferenceConscust" parameterType="list">
        <foreach collection="list" item="item" index="index" separator=";"  open="begin" close=";end;">
            UPDATE cm_conference_conscust t
            SET T.APPOINTMENTSTYPE = '2', T.APPOINTMENTSNUB = 0
            WHERE
             t.conferenceid = #{item.conferenceid,jdbcType=VARCHAR}
            AND t.conscustno = #{item.conscustno,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectCountByConferenceId" parameterType="string" resultType="integer">
        SELECT COUNT(*)
        FROM cm_conference_custkinsfolk T
        WHERE T.CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
    </select>

    <select id="selectConferencesByTypeAndDate2" resultType="com.howbuy.crm.nt.conference.domain.CmConferenceDomain">
        SELECT *
        FROM CM_CONFERENCE t
        WHERE t.conferencetype = '6'
          AND (t.noiddt || ' 20:00:00') &lt;= to_char(sysdate, 'yyyy-MM-dd HH:mm:ss')
    </select>

    <select id="selectConscustByConferenceId1" resultType="com.howbuy.crm.nt.conference.dto.CmConferenceConscust">
        SELECT *
        FROM cm_conference_conscust A
        WHERE A.conferenceid = #{conferenceId,jdbcType=VARCHAR}  AND A.APPOINTMENTSTYPE = '0'
    </select>

    <select id="countCustkinsfolkByIdNo" parameterType="map" resultType="integer">
        SELECT COUNT(*)
        FROM cm_conference_custkinsfolk T
        WHERE T.CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
        AND T.CONSCUSTNO = #{conscustNo,jdbcType=VARCHAR}
        AND T.IDNO IS NOT NULL
    </select>


    <delete id="batchDeleteCustkinsfolkByIdNoNull" parameterType="java.util.List">
        DELETE FROM cm_conference_custkinsfolk t
        WHERE (conferenceid, conscustno) IN (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT #{item.conferenceid,jdbcType=VARCHAR}, #{item.conscustno,jdbcType=VARCHAR} FROM DUAL
        </foreach>
        )
        AND IDNO IS NULL
    </delete>



    <select id="selectConferenceByTypeAndDate3" resultType="com.howbuy.crm.nt.conference.domain.CmConferenceDomain">
        SELECT *
        FROM CM_CONFERENCE t
        WHERE t.conferencetype = '6'
          AND (t.noroutingdt || ' 20:00:00') &lt;= to_char(sysdate, 'yyyy-MM-dd HH:mm:ss')
    </select>


    <select id="getCountByConferenceAndCust" resultType="int">
        SELECT COUNT(*)
        FROM cm_conference_custkinsfolk T
        WHERE T.CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
        AND T.CONSCUSTNO = #{consCustNo,jdbcType=VARCHAR}
        AND (T.GOTOFLIGHT IS NOT NULL
        OR T.GOTOFLIGHTDT IS NOT NULL
        OR T.RETURNFLIGHT IS NOT NULL
        OR T.RETURNFLIGHTDT IS NOT NULL)
    </select>


    <delete id="batchDeleteCmConferenceCustkinsfolk" parameterType="java.util.List">
        DELETE FROM cm_conference_custkinsfolk t
        WHERE (conferenceid, conscustno) IN (
        <foreach collection="list" item="item" separator="UNION ALL">
            SELECT #{item.conferenceid,jdbcType=VARCHAR}, #{item.conscustno,jdbcType=VARCHAR} FROM DUAL
        </foreach>
        )
        AND (T.GOTOFLIGHT IS NULL AND T.GOTOFLIGHTDT IS NULL)
        AND (T.RETURNFLIGHT IS NULL AND T.RETURNFLIGHTDT IS NULL)
    </delete>




</mapper>
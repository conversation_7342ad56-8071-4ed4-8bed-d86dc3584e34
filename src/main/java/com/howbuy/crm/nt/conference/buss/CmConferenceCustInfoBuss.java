package com.howbuy.crm.nt.conference.buss;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.model.CustVisitTypeEnum;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.conscust.service.ConscustInfoSerive;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dao.CsCommunicateVisitDao;
import com.howbuy.crm.nt.conference.domain.CsCommunicateVisit;
import com.howbuy.crm.nt.conference.domain.InsertConferenceCustVo;
import com.howbuy.crm.nt.conference.domain.UpdateConferenceCustVo;
import com.howbuy.crm.nt.conference.dto.CmConference;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.request.ConferenceCustCancelVo;
import com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao;
import com.howbuy.crm.nt.highcustlabel.domain.CmHighCustinfo;
import com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class CmConferenceCustInfoBuss{


    @Autowired
    private CmConferenceConscustMapper cmConferenceConscustMapper;

    @Autowired
    private CmHighCustinfoDao cmHighCustinfoDao;
    @Autowired
    private CmConferenceConscustMapper conferenceConscustMapper;

    @Autowired
    private CommonDao commondao;


    @Autowired
    private CmConferenceMapper cmConferenceMapper;


    @Autowired
    private CsCommunicateVisitDao csCommunicateVisitDao;


    @Autowired
    private CmPushMsgDao cmPushMsgDao;
    @Autowired
    private ConscustInfoSerive conscustInfoSerive;

    /**
     * 系统创建人
     */
    private static final String SYS_CREATOR = "sys";




    /**
     * @description:(根据 客户号和会议id 查询客户会议预约表记录)
     * @param custNo	
     * @param conferenceId
     * @return com.howbuy.crm.nt.conference.dto.CmConferenceConscust
     * @author: haoran.zhang
     * @date: 2023/11/8 20:27
     * @since JDK 1.8
     */
    public CmConferenceConscust getConferenceCust(String custNo,String conferenceId){
        return conferenceConscustMapper.selectConferenceCust(custNo,conferenceId);
    }


    /**
     * g根据投顾查询outletCode
     * @param conscode
     * @return
     */
    private String getOutletCodeByConsCode(String conscode){
        Map<String,String> paramMap= Maps.newHashMap();
        paramMap.put("conscode", conscode);
        //TODO: mapper乱定义。参数/返回 不要Map.
        Map<String,String> maporgcode = cmPushMsgDao.getOrgCodeByConscode(paramMap);
        if(maporgcode != null) {
            return maporgcode.get("OUTLETCODE");
        }
        return  null;
    }

    /**
     * @description:(新增预约表数据)
     * @param insertVo
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/11/9 19:43
     * @since JDK 1.8
     */
    public NtReturnMessageDto<String> insertConferenceCust(InsertConferenceCustVo insertVo){
         return insertConferenceCust(insertVo,true);
    }


    /**
     * @description:(新增预约表数据， )
     * @param insertVo	
     * @param isInsertCommunit	 是否插入沟通记录
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/11/9 19:42
     * @since JDK 1.8
     */
    public NtReturnMessageDto<String> insertConferenceCust(InsertConferenceCustVo insertVo,boolean isInsertCommunit){
        Assert.notNull(insertVo,"insertVo is null");
        String custNo=insertVo.getCustNo();
        String conferenceId=insertVo.getConferenceId();

        CmConferenceConscust existDto=getConferenceCust(custNo,conferenceId);
        if(existDto!=null){
            return NtReturnMessageDto.fail("该客户已经预约参会！");
        }

        CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
        //获取当前投顾
        String consCode=conscustInfoSerive.getConsCodeByCustNo(custNo);
        //客户信息
        cmConferenceConscust.setConferenceid(conferenceId);
        cmConferenceConscust.setConscode(consCode);
        cmConferenceConscust.setConscustno(custNo);
        cmConferenceConscust.setOrgcode(getOutletCodeByConsCode(consCode));
        cmConferenceConscust.setCreatdt(DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));

        cmConferenceConscust.setCreater(insertVo.getCreator());
        cmConferenceConscust.setAppointmentsnub(insertVo.getAppointmentsNumber());
        cmConferenceConscust.setActualnub(insertVo.getActualNumber());
        cmConferenceConscust.setActualnubdt(insertVo.getActualNubDt());

        //默认0：潜客
        String gdcjlabel="0";
        CmHighCustinfo cmHighCustinfo=cmHighCustinfoDao.selectByCustNo(custNo);
        if(cmHighCustinfo!=null && cmHighCustinfo.getGdcjlabel()!=null){
            gdcjlabel=cmHighCustinfo.getGdcjlabel();
        }
        //1-成交  0：潜客
        cmConferenceConscust.setGdcjlabel(gdcjlabel);

        // 则在会议报名签到页生成一条预约记录，
        //若已有预约，则将参会信息同步更新至对应预约表中，实到人数 = 接口中给的参会人数，签到时间 = 签到时间；（去掉原晚上的跑批规则）
        int insertCount=conferenceConscustMapper.insert(cmConferenceConscust);
        log.info("路演会议id:{},投顾客户号：{}，投顾：{} ，插入预约条数：{}",
                conferenceId,custNo,consCode,insertCount);
        // isInsertCommunit=true 时 该客户沟通记录表中生成一条沟通记录
        if(insertCount == 1 && isInsertCommunit){
            CmConference cmConference = cmConferenceMapper.selectByPrimaryKey(conferenceId);
            String content=String.format("预约参会： (%s)，预约参会人数：1",cmConference == null ? "" : cmConference.getConferencename());
            insertCommunicate(custNo,cmConferenceConscust.getActualnubdt(),content,SYS_CREATOR);
        }
        return NtReturnMessageDto.ok("预约成功！");
    }


    /**
     * @description:(生成沟通记录 )
     * @param custNo 客户号
     * @param creDt 日期
     * @param content 沟通记录
     * @param creator 沟通人
     * @return void
     * @author: haoran.zhang
     * @date: 2023/11/17 19:54
     * @since JDK 1.8
     */
    private void insertCommunicate(String custNo, Date creDt, String content, String creator){
        CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();
        String hisId = commondao.getSeqValue("SEQ_PCUSTREC");
        csCommunicateVisit.setId(commondao.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID").toString());
        csCommunicateVisit.setConscustNo(custNo);
        csCommunicateVisit.setCreDt(creDt);
        //拜访方式：3-参会
        csCommunicateVisit.setVisitType(CustVisitTypeEnum.CONFERENCE.getCode());
        csCommunicateVisit.setCommContent(content);
        csCommunicateVisit.setCreator(creator);
        csCommunicateVisit.setHisFlag("0");
        // 生产环境该字段设置的是不为null
        csCommunicateVisit.setHisId(hisId);
        csCommunicateVisitDao.insertCsCommunicateVisit(csCommunicateVisit);
        log.info("客户沟通记录表中生成一条沟通记录：{}", JSON.toJSON(csCommunicateVisit));
    }



    /**
     * 根据vo 更新 客户会议预约表记录
     * @param updateVo 更新对象vo
     * @return int
     */
    public int updateByVo(UpdateConferenceCustVo updateVo){
        Assert.notNull(updateVo,"updateVo is null");
        Assert.notNull(updateVo.getConferenceid(),"conferenceid is null");

        //hboneNo  appointmentscode 不能同时为空
        if(updateVo.getHboneNo() == null && updateVo.getAppointmentscode() == null && updateVo.getCustNo() == null){
            throw new IllegalArgumentException("hboneNo and appointmentscode and custNo is null");
        }
        return cmConferenceConscustMapper.updateByVo(updateVo);
    }


    /**
     * @description:(取消参会预约数据)
     * @param cancelVo
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/9 8:52
     * @since JDK 1.8
     */
    public NtReturnMessageDto<String> cancel(ConferenceCustCancelVo cancelVo ){
        String  custNo =cancelVo.getCustNo();
        String conferenceId=cancelVo.getConferenceId();
        String operator=cancelVo.getOperator();

        Assert.notNull(custNo,"custNo is null");
        Assert.notNull(conferenceId,"conferenceId is null");

        //复制 his 表
        int backUpCount=cmConferenceConscustMapper.insertHis(custNo,conferenceId,operator);
        //删除扫码表
        int deleteCount=cmConferenceConscustMapper.delete(custNo,conferenceId);
//        同时给对应客户生成沟通记录，字段取值如下：
//        沟通方式 = 参会
//        沟通分类 = 正常客户
//        沟通人 = sys
//        日期 = 当前日期，到时分秒
//        沟通记录 = 取消参会：xxx（显示会议名称）
        CmConference cmConference = cmConferenceMapper.selectByPrimaryKey(conferenceId);
        String content=String.format("取消参会： (%s)",cmConference == null ? "" : cmConference.getConferencename());
        insertCommunicate(custNo,new Date(),content,SYS_CREATOR);


        log.info("批量取消参会预约数据，custNo：{},conferenceId:{} ，预约表数据删除条数：{}，预约表数据备份条数：{}",
                custNo,conferenceId,deleteCount,backUpCount);
        return NtReturnMessageDto.ok();
    }

}

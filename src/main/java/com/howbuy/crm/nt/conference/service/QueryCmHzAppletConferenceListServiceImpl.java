/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dto.CmHzAppletConferenceInfoDTO;
import com.howbuy.crm.nt.conference.request.CmHzAppletConferenceInfoRequest;
import com.howbuy.crm.nt.conference.response.CmHzAppletConferenceInfoResponse;
import com.howbuy.crm.util.DateTimeUtil;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 好甄
 * @date 2024/6/19 13:20
 * @since JDK 1.8
 */
@Service("queryCmHzAppletConferenceListService")
public class QueryCmHzAppletConferenceListServiceImpl implements QueryCmHzAppletConferenceListService {

    /**
     * 预约会议  当天举办的会议+未来举办的会议
     */
    private static final String REGISTERED_CONFERENCE = "1";

    /**
     * 已结束的会议
     */
    private static final String END_CONFERENCE = "2";
    @Resource
    private CmConferenceMapper cmConferenceMapper;

    @Override
    public CmHzAppletConferenceInfoResponse queryCmConferenceInfoListForHzApplet(CmHzAppletConferenceInfoRequest request) {
        CmHzAppletConferenceInfoResponse infoResponse = new CmHzAppletConferenceInfoResponse();
        // 参数校验
        if (StringUtils.isBlank(request.getBizType()) || (!REGISTERED_CONFERENCE.equals(request.getBizType()) && !END_CONFERENCE.equals(request.getBizType()))) {
            return new CmHzAppletConferenceInfoResponse(BaseConstantEnum.PARAM_ERROR, "业务类型参数错误");
        }
        if (StringUtils.isBlank(request.getHboneNo())) {
            return new CmHzAppletConferenceInfoResponse(BaseConstantEnum.PARAM_ERROR, "一账通账号参数错误");
        }
        if (StringUtils.isBlank(request.getOnlineTime())) {
            return new CmHzAppletConferenceInfoResponse(BaseConstantEnum.PARAM_ERROR, "上线时间参数错误");
        }
        //查询当天举办的会议+未来举办的会议(不分页)
        if (REGISTERED_CONFERENCE.equals(request.getBizType())) {
            List<CmHzAppletConferenceInfoDTO> dto = cmConferenceMapper.queryAfterCurrentTimeConferenceInfoByHboneNo(request.getHboneNo(),request.getOnlineTime());
            infoResponse.setTotal(CollectionUtils.isEmpty(dto) ? "0" : String.valueOf(dto.size()));
            initCmHzAppletConferenceInfoResponse(dto, infoResponse);
            infoResponse.success();
            return infoResponse;
        }
        //已经结束的会议(需要分页)
        PageHelper.startPage(request.getPageNo(), request.getPageSize());
        Page<CmHzAppletConferenceInfoDTO> page = cmConferenceMapper.pageQueryBeforeCurrentTimeConferenceInfoByHboneNo(request.getHboneNo(),request.getOnlineTime());
        infoResponse.setTotal(String.valueOf(page.getTotal()));
        List<CmHzAppletConferenceInfoDTO> pageResult = page.getResult();
        initCmHzAppletConferenceInfoResponse(pageResult, infoResponse);
        infoResponse.success();
        return infoResponse;
    }

    private static void initCmHzAppletConferenceInfoResponse(List<CmHzAppletConferenceInfoDTO> pageResult, CmHzAppletConferenceInfoResponse infoResponse) {
        if(CollectionUtils.isNotEmpty(pageResult)){
            pageResult.stream().filter(f -> StringUtils.isNotBlank(f.getConferenceDate())).forEach(f -> {
                String conferenceDate = f.getConferenceDate();
                String fmtDate = DateTimeUtil.getTargetDateFormatter(conferenceDate, DateTimeUtil.YYYYMMDDHHMMSS,DateTimeUtil.YYYY_MM_DD);
                f.setConferenceDate(fmtDate);
                String fmtTime = DateTimeUtil.getTargetDateFormatter(conferenceDate,DateTimeUtil.YYYYMMDDHHMMSS,DateTimeUtil.HHMMSS);
                f.setConferenceTime(fmtTime);
            });
            infoResponse.setConferenceInfoList(pageResult);
        }
    }
}

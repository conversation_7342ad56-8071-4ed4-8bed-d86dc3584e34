<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceThemeMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceTheme">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE_THEME-->
    <result column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid" />
    <result column="THEMETYPE" jdbcType="VARCHAR" property="themetype" />
    <result column="THEMETYPEVALUE" jdbcType="VARCHAR" property="themetypevalue" />
    <result column="THEME_DESC" jdbcType="VARCHAR" property="themeDesc" />
    <result column="CREATDT" jdbcType="VARCHAR" property="creatdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="MODIFYDT" jdbcType="VARCHAR" property="modifydt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONFERENCEID, THEMETYPE, THEMETYPEVALUE, THEME_DESC,CREATDT, CREATER, MODIFYDT, MODIFIER
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceTheme">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE_THEME (CONFERENCEID, THEMETYPE, THEMETYPEVALUE, THEME_DESC,
      CREATDT, CREATER, MODIFYDT, 
      MODIFIER)
    values (#{conferenceid,jdbcType=VARCHAR}, #{themetype,jdbcType=VARCHAR}, #{themetypevalue,jdbcType=VARCHAR}, #{themeDesc,jdbcType=VARCHAR},
      #{creatdt,jdbcType=VARCHAR}, #{creater,jdbcType=VARCHAR}, #{modifydt,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR})
  </insert>

  <insert id="batchInsertList" parameterType="list">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE_THEME (CONFERENCEID, THEMETYPE, THEMETYPEVALUE, THEME_DESC,
      CREATDT, CREATER, MODIFYDT,
      MODIFIER)
    SELECT r.* from (
    <foreach close=")" collection="list" item="item" index="index"   open="(" separator="union">
      select
      #{item.conferenceid,jdbcType=VARCHAR},
      #{item.themetype,jdbcType=VARCHAR},
      #{item.themetypevalue,jdbcType=VARCHAR},
      #{item.themeDesc,jdbcType=VARCHAR},
      #{item.creatdt,jdbcType=VARCHAR},
      #{item.creater,jdbcType=VARCHAR},
      #{item.modifydt,jdbcType=VARCHAR},
      #{item.modifier,jdbcType=VARCHAR}
      FROM DUAL
    </foreach>
    ) r
  </insert>


   <select id="selectByConferenceId" parameterType="string" resultMap="BaseResultMap">
       SELECT
       <include refid="Base_Column_List"/>
       FROM CM_CONFERENCE_THEME
       WHERE CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
   </select>

  <select id="selectByConferenceIdList" parameterType="list" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM CM_CONFERENCE_THEME
    WHERE
    ( CONFERENCEID IN
    <foreach item="item" index="index" collection="conferenceIdList" separator=" OR CONFERENCEID IN ">
      <foreach collection="item" item="mId" open="(" separator="," close=")">
        #{mId}
      </foreach>
    </foreach>
    )
  </select>

  <delete id="deleteByConferenceId" parameterType="string" >
     <!--@mbg.generated-->
    delete from CM_CONFERENCE_THEME
    where CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
  </delete>
</mapper>
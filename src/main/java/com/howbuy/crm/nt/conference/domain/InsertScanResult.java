/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.domain;

import com.howbuy.crm.nt.conference.dto.CmConferenceScan;
import lombok.Data;

/**
 * @description: (扫码参会表 插入参会执行结果 )
 * <AUTHOR>
 * @date 2023/11/8 18:55
 * @since JDK 1.8
 */
@Data
public class InsertScanResult {

    /**
     * 插入的扫码参会表数据对象
     */
    private CmConferenceScan insertScan;

    /**
     * 插入扫码参会时， mobile对应的 客户信息 分析结果
     */
    private MobileExistCustInfo  analyseInfo;


}
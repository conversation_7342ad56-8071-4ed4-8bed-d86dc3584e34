/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.service;


import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.cache.cacheService.CacheKeyPrefix;
import com.howbuy.crm.cache.cacheService.lock.LockService;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.buss.CmConferenceCustInfoBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceConscustMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceLogMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.domain.InsertConferenceCustVo;
import com.howbuy.crm.nt.conference.domain.SearchConferenceCustVo;
import com.howbuy.crm.nt.conference.dto.*;
import com.howbuy.crm.nt.conference.request.OrderConferenceRequest;
import com.howbuy.crm.nt.conference.response.ConferenceSignResponse;
import com.howbuy.crm.nt.conference.response.OrderConferenceResponse;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/7/14 08:59
 * @since JDK 1.8
 */
@Slf4j
@Service("orderConferenceService")
public class OrderConferenceServiceImpl implements OrderConferenceService{


    @Autowired
    private CmConferenceConscustMapper conferenceConscustMapper;

    @Autowired
    private ConscustMapper conscustMapper;

    @Autowired
    private CmConferenceLogMapper cmConferenceLogMapper;
    @Autowired
    private CmConferenceMapper cmConferenceMapper;


    @Autowired
    protected LockService lockService;
    @Autowired
    private CmConferenceCustInfoBuss conferenceCustInfoBuss;

    /**
     * 接口调用默认的时间是30s
     */
    private static final int DEFAULT_EXPIRE = 30;

    /**
     * 更新人
     */
    private static final String UPDATE_CREATER = "cms-sys";

    @Override
    public OrderConferenceResponse orderConference(OrderConferenceRequest request) {
        OrderConferenceResponse response = new OrderConferenceResponse();
        String courseId = request.getCourseId();
        String hboneNo = request.getHboneNo();
        String uniqKey = CacheKeyPrefix.LOCK_KEY_PREFIX + courseId + hboneNo;
        try {
            // 运行锁，只有在获取了锁之后才准许执行
            if (!lockService.getLock(uniqKey, getExpireSecond())) {
                log.info("enter into orderConference,hboneNo{},courseId{} ", hboneNo, courseId);
                response.paramError("已经在执行此次调用录入:" + "hboneNo" + hboneNo + "courseId" + courseId);
                response.setReturnCode(CrmNtConstant.SYSTEM_ERROR);
                return response;
            }
            // 根据课程id获取会议id
            String conferenceId = getConferenceIdByCourseId(courseId);
            // 获取投顾客户号
            String conscustNo = conscustMapper.selectConscustNoByHboneNo(hboneNo);
            // 投顾客户号和会议id均不为空
            if (StringUtils.isNotEmpty(conferenceId) && StringUtils.isNotEmpty(conscustNo)) {
                //更新或者 插入 预约表数据
                dealOrderConference(conferenceId,conscustNo);
                response.success();
            }
            if (StringUtils.isEmpty(conferenceId) || StringUtils.isEmpty(conscustNo)) {
                CmConferenceLog cmConferenceLog = new CmConferenceLog();
                cmConferenceLog.setConferenceId(courseId);
                cmConferenceLog.setHboneno(hboneNo);
                cmConferenceLogMapper.insert(cmConferenceLog);
                response.paramError("传入一账通号和课程id获取不到数据");
                response.setReturnCode(CrmNtConstant.NULL_ERROR);
            }
        } catch (Exception e) {
            log.error("调用会议预约接口失败", e);
            response.paramError("调用会议预约接口失败");
            response.setReturnCode(CrmNtConstant.SYSTEM_ERROR);
        } finally {
            lockService.releaseLock(uniqKey);
        }
        return response;
    }


    /**
     * CmConferenceLog表记录 重新处理
     * @param cmConferenceLog
     */
    @Override
    public NtReturnMessageDto<String> reDealOrderConference(CmConferenceLog cmConferenceLog){

        //已处理的 不再处理
        if(!YesOrNoEnum.NO.getCode().equals(String.valueOf(cmConferenceLog.getIsDeal()))){
            return NtReturnMessageDto.fail(String.format("cmConferenceLogId:%s 已处理！", JSON.toJSONString(cmConferenceLog)));
        }
        String courseId = cmConferenceLog.getConferenceId();
        String hboneNo = cmConferenceLog.getHboneno();
        // 根据课程id获取会议id
        String conferenceId = getConferenceIdByCourseId(courseId);
        // 获取投顾客户号
        String conscustNo = conscustMapper.selectConscustNoByHboneNo(hboneNo);
        // 投顾客户号和会议id均不为空
        if (StringUtils.isNotEmpty(conferenceId) && StringUtils.isNotEmpty(conscustNo)) {
            //更新或者 插入 预约表数据
            dealOrderConference(conferenceId,conscustNo);
            //更新处理状态=已处理
            cmConferenceLogMapper.updateCmConferenceLog(cmConferenceLog);
        }
        return NtReturnMessageDto.ok();
    }


    /**
     * 处理 核心逻辑：插入或者更新会议预约信息
     * @param conferenceId
     * @param conscustNo
     */
    private void dealOrderConference(String conferenceId,String conscustNo){
        if (StringUtils.isEmpty(conferenceId) || StringUtils.isEmpty(conscustNo)) {
            return;
        }
        // 判断是否存在报名信息
        CmConferenceConscust conferenceConscust =  conferenceCustInfoBuss.getConferenceCust(conscustNo, conferenceId);
        if (conferenceConscust != null) {
            // 存在报名信息 判断已存的的创建时间和cms调用时的报名时间 大小关系
            String creatdt = conferenceConscust.getCreatdt();
            Date createDate = DateUtil.formatToDate(creatdt, DateUtil.YYYYMMDD);
            if (null != createDate && createDate.after(new Date())) {
                // 如果已存的创建时间小于cms调用时的报名时间 则更新报名时间
                conferenceConscust.setCreater(UPDATE_CREATER);
                conferenceConscust.setConferenceid(conferenceId);
                conferenceConscust.setConscustno(conscustNo);
                conferenceConscustMapper.updateCreator(conferenceId, conscustNo, UPDATE_CREATER);
            }
        } else {

            InsertConferenceCustVo insertCustVo=new InsertConferenceCustVo();
            insertCustVo.setCustNo(conscustNo);
            insertCustVo.setConferenceId(conferenceId);
            insertCustVo.setCreator(UPDATE_CREATER);
            insertCustVo.setAppointmentsNumber(new BigDecimal(1));
            insertCustVo.setActualNumber(new BigDecimal(1));
            insertCustVo.setActualNubDt(new Date());
            conferenceCustInfoBuss.insertConferenceCust(insertCustVo,false);
        }
    }


   /**
    * @description:(根据课程id 获取会议id)
    * @param courseId
    * @return java.lang.String
    * @author: haoran.zhang
    * @date: 2023/11/7 13:19
    * @since JDK 1.8
    */
    private String getConferenceIdByCourseId(String courseId) {
        CmConference conference= cmConferenceMapper.selectValidConferenceByCourseId(courseId);
        return conference== null ? null : conference.getConferenceid();
    }


    @Override
    public ConferenceSignResponse listSignStatus(List<OrderConferenceRequest> request) {
        ConferenceSignResponse response = new ConferenceSignResponse();
        // 根据cms 传入的课程id以及一账通号id查询对应客户是否签到
        try {
            List<SearchConferenceCustVo> seearchList = request.stream().map(item->{
                SearchConferenceCustVo vo=new SearchConferenceCustVo();
                vo.setCourseId(item.getCourseId());
                vo.setHboneNo(item.getHboneNo());
                return vo;
                }).collect(Collectors.toList());
            List<CmConferenceCustDto> cmConferenceCustDtos = conferenceConscustMapper.listCmConferenceListByCustno(seearchList);
            List<CmSignDto> collect = cmConferenceCustDtos.stream().map(this::buildCmSignDto).collect(Collectors.toList());
            response.setSignStatusList(collect);
            response.success();
            return response;
        } catch (Exception e) {
            log.error("根据课程id以及一账通号id查询对应客户是否签到失败", e);
            response.paramError("根据课程id以及一账通号id查询对应客户是否签到失败");
            response.setReturnCode(CrmNtConstant.SYSTEM_ERROR);
            return response;
        }
    }

    @Override
    public ConferenceSignResponse listSignCourseByHboneno(String hboneNo) {
        ConferenceSignResponse response = new ConferenceSignResponse();
        try {
            List<String> strings = conferenceConscustMapper.listSignCourseIdByHboneno(hboneNo);
            response.setCourseIdList(strings);
            response.success();
            return response;
        } catch (Exception e) {
            log.error("根据一账通号查询已签到课程失败", e);
            response.paramError("根据一账通号查询已签到课程失败");
            response.setReturnCode(CrmNtConstant.SYSTEM_ERROR);
            return response;
        }
    }

    public CmSignDto buildCmSignDto(CmConferenceCustDto cmConferenceCustDto){
        CmSignDto cmSignDto = new CmSignDto();
        cmSignDto.setCourseId(cmConferenceCustDto.getCourseId());
        // 根据实到人数判断是否签到
        if (cmConferenceCustDto.getActualNub() > 0) {
            cmSignDto.setSignStatus(CrmNtConstant.IS_CHECK);
        } else {
            cmSignDto.setSignStatus(CrmNtConstant.NO_CHECK);
        }
        cmSignDto.setSignDt(cmConferenceCustDto.getActualNubDt());
        cmSignDto.setHboneNo(cmConferenceCustDto.getHboneNo());
        return cmSignDto;
    }

    protected int getExpireSecond(){
        return DEFAULT_EXPIRE;
    }

}
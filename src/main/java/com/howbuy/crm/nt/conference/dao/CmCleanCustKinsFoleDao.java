/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.dao;

import com.howbuy.crm.nt.conference.domain.CmConferenceDomain;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/4/25 23:40
 * @since JDK 1.8
 */
@Component
public interface CmCleanCustKinsFoleDao {

    /**
     *  共用的更新
     *  更新参会状态为未参会，预约参会人数变更为0
     */
    void updateConferenceConscust(List<CmConferenceConscust> lis);


    /**
     * 根据会议ID查询 参会员工亲属表 count记录
     * @param conferenceId
     * @return
     */
    int selectCountByConferenceId(@Param("conferenceId") String conferenceId);

    /**
     * 查询节点一的数据
     *
     */
    List<CmConferenceDomain> selectConferencesByTypeAndDate1();

    /**
     * 节点一时间清理未确认客户参会信息
     */
    void deleteConferenceCustkinsfolkByConferenceId(List<CmConferenceConscust> list);


    /**
     * 节点二的相关操作
     */
    List<CmConferenceDomain> selectConferencesByTypeAndDate2();

    /**
     * 清理时间点2中未录入证件号码的参会人员
     */
    Integer countCustkinsfolkByIdNo(@Param("conferenceId") String conferenceId, @Param("conscustNo") String conscustNo);

    void batchDeleteCustkinsfolkByIdNoNull(List<CmConferenceConscust> list);

    /**
     * 节点三的相关操作
     */
    List<CmConferenceDomain> selectConferenceByTypeAndDate3();

    int getCountByConferenceAndCust(@Param("conferenceId") String conferenceId, @Param("consCustNo") String consCustNo);

    void batchDeleteCmConferenceCustkinsfolk(List<CmConferenceConscust> list);

}
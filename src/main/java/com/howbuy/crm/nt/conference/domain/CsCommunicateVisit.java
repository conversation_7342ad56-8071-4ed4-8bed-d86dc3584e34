package com.howbuy.crm.nt.conference.domain;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * @Description: 实体类CsCommunicateVisit.java
 * @version 1.0
 */
@Data
public class CsCommunicateVisit implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	private String conscustNo;

	private String consultType;

	private String commIntent;

	private String investIntent;

	private String amountFlag;

	private String specialMark;

	private String deptFlag;

	private String commContent;

	private String taskId;

	private String bookingContent;

	private String callInId;

	private String visitType;

	private String nextDt;

	private String consBookingId;

	private String nextVisitContent;

	private String nextStartTime;

	private String nextEndTime;

	private String nextVisitType;

	private String visitClassify;

	private String remark;

	private String modifyFlag;

	private String creator;

	private Date creDt;

	private String modifier;

	private Date modDt;

	private Date stimeStamp;

	private String hisFlag;

	private String hisId;

}

/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conference.buss;

import com.howbuy.crm.nt.conference.service.CmCleanCustKinsFoldService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (CRM至臻年会-参会人员状态批处理(时间节点) 存储过程改造)
 * 定时任务 -- PRO_CLEAN_CUSTKINSFOLK  存储过程： PRO_CLEAN_CUSTKINSFOLK
 * <AUTHOR>
 * @since JDK 1.8
 */
@Component
public class CmCleanCustKinsFoleBuss {
    private static final Logger log = LoggerFactory.getLogger(CmCleanCustKinsFoleBuss.class);

    @Autowired
    private CmCleanCustKinsFoldService cmCleanCustKinsFoldService;


    /**
     * 更新确认到账客户资源
     */
    public void batchUpdatePrebookResType() {
        try {
            log.info("CmCleanCustKinsFoleBuss start");
            cmCleanCustKinsFoldService.dealCmCleanCustKinsFole();
            log.info("CmCleanCustKinsFoleBuss end");
        } catch (Exception e) {
            log.info("error in CmCleanCustKinsFoleBuss", e);
        }
    }

}
package com.howbuy.crm.nt.conference.buss;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acccenter.common.utils.StringUtil;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.nt.base.enums.ConferCustStateEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceMapper;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper;
import com.howbuy.crm.nt.conference.domain.*;
import com.howbuy.crm.nt.conference.dto.CmConference;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.dto.CmConferenceScan;
import com.howbuy.crm.nt.conference.dto.MobileCustInfoDto;
import com.howbuy.crm.nt.conference.request.CmConferenceScanVo;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CmConferenceBusinessBuss {

    @Autowired
    private CmConferenceScanBuss conferenceScanBuss;

    @Autowired
    private CmConferenceCustInfoBuss conferenceCustInfoBuss;

    @Autowired
    private CmConferenceScanMapper cmConferenceScanMapper;

    @Autowired
    private CommonDao commondao;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Autowired
    private CmConferenceMapper cmConferenceMapper;

    /**
     * 消息ID: 客户签到成功消息通知投顾
     */
    private static final String SCAN_ONLY_CUST_MSG_ID = "202042";

    /**
     * 系统创建人
     */
    private static final String SYS_CREATOR = "sys";



    /**
     * @description:(客户号为空的数据， 实时变更客户状态)
     * @param
     * @return void
     * @author: haoran.zhang
     * @date: 2023/11/7 9:07
     * @since JDK 1.8
     */
    public void updateScanCustState(){
        CmConferenceScanVo scanVo=new CmConferenceScanVo();
        scanVo.setCustStateList(Lists.newArrayList(ConferCustStateEnum.NOT_EXIST.getCode(),ConferCustStateEnum.EXIST_MANY.getCode()));
        List<CmConferenceScan> emptyCustList= cmConferenceScanMapper.selectListByVo( scanVo);
        log.info("实时更新[新客][多客]参会状态， 查询条数：{}",emptyCustList.size());
        emptyCustList.forEach(cmConferenceScan ->{
            try {
                NtReturnMessageDto<ConferCustStateEnum>  result= executeChangeCustState(cmConferenceScan);
                log.info("实时更新[新客][多客]参会状态， 处理客户手机号码：{}，处理结果：{}",cmConferenceScan.getMobileDigest(),JSON.toJSONString(result));
            }catch (Exception e){
                log.error("更新扫码参会客户状态异常，扫码参会信息：{}", JSON.toJSONString(cmConferenceScan),e);
            }
        });

    }



    /**
     * @description:(分析手机号码是否唯一客户，更新 客户信息 ,返回 处理后的客户状态)
     * @param scanInfo
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/11/7 9:08
     * @since JDK 1.8
     */
    public NtReturnMessageDto<ConferCustStateEnum> executeChangeCustState(CmConferenceScan scanInfo){
        Assert.notNull(scanInfo,"扫码参会信息不能为空");
        String mobileDigest=scanInfo.getMobileDigest();
        Assert.notNull(mobileDigest,"手机号码摘要不能为空");

        if(ConferCustStateEnum.EXIST_ONLY.getCode().equals(scanInfo.getCustState())){
            return NtReturnMessageDto.fail(String.format("scanId:%s 客户状态：存在唯一客户！",scanInfo.getId()));
        }
        if(StringUtil.isNotEmpty(scanInfo.getCustNo())){
            return NtReturnMessageDto.fail(String.format("scanId:%s 已匹配客户号！",scanInfo.getId()));
        }
        MobileExistCustInfo  analyseInfo=conferenceScanBuss.analyseExistInfo(mobileDigest);
        ConferCustStateEnum analyseEnum=analyseInfo.getCustStateEnum();
        if(analyseEnum==ConferCustStateEnum.EXIST_ONLY){
            //更新客户号
            MobileCustInfoDto userInfo=analyseInfo.getUsedCustInfo();
            CmConferenceScan updateInfo=new CmConferenceScan();
            updateInfo.setId(scanInfo.getId());
            updateInfo.setCustNo(userInfo.getCustNo());
            updateInfo.setConsCode(userInfo.getConsCode());
            updateInfo.setCustState(analyseEnum.getCode());
            updateInfo.setCustTimestamp(new Date());
            cmConferenceScanMapper.updateByPrimaryKeySelective(updateInfo);

            //scanInfo表 后续流程使用
            scanInfo.setCustNo(userInfo.getCustNo());
            scanInfo.setConsCode(userInfo.getConsCode());
            scanInfo.setCustState(analyseEnum.getCode());
        }

        dealConferenceCustAfterScan(scanInfo,analyseInfo);
        return NtReturnMessageDto.ok("",analyseEnum);

    }

    /**
     * @description:(更新 客户信息 ,返回 处理后的客户状态)
     * @param scanId
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<com.howbuy.crm.nt.base.enums.ConferCustStateEnum>
     * @author: haoran.zhang
     * @date: 2023/11/7 10:25
     * @since JDK 1.8
     */
    public NtReturnMessageDto<ConferCustStateEnum> executeChangeCustState(BigDecimal scanId){
        CmConferenceScan scanInfo= cmConferenceScanMapper.selectByPrimaryKey(scanId);
        return executeChangeCustState(scanInfo);
    }


    /**
     * @description:(新增扫码签到客户信息  入口)
     * @param insertScanVo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/11/8 19:04
     * @since JDK 1.8
     */
    public void createCmConferenceScanInfo(InsertScanVo insertScanVo) {
        String conferenceId=insertScanVo.getConferenceId();

        //插入扫码参会客户表
        NtReturnMessageDto<InsertScanResult> scanMessageDto=conferenceScanBuss.insertScan(insertScanVo);

        //插入数据 ：
          // 未插入扫码参会 或者无需插入
        InsertScanResult insertScanResult=scanMessageDto.getReturnObject();
        if(insertScanResult==null){
            return;
        }


        CmConferenceScan insertScan=insertScanResult.getInsertScan();
        MobileExistCustInfo analyseInfo=insertScanResult.getAnalyseInfo();
        dealConferenceCustAfterScan(insertScan,analyseInfo);
        // 消息提醒：实时推送给所属投顾
        pushMsgToConsCode(insertScan, analyseInfo);

    }




    /**
     * @description:(请在此添加描述)
     * @param scanInfo	
     * @param analyseInfo
     * @return void
     * @author: haoran.zhang
     * @date: 2023/11/9 14:09
     * @since JDK 1.8
     */
    private void dealConferenceCustAfterScan(CmConferenceScan scanInfo,MobileExistCustInfo analyseInfo){
        //插入扫码表 数据 --> 客户状态
        ConferCustStateEnum custStateEnum=analyseInfo.getCustStateEnum();
        // 0-不存在客户  2-存在多个客户  不需要继续处理
        if(ConferCustStateEnum.EXIST_ONLY!=custStateEnum){
            return;
        }
        //mobile 存在的唯一客户号
        String custNo=analyseInfo.getUsedCustInfo().getCustNo();
        String conferenceId=scanInfo.getConferenceid();


        CmConferenceConscust existDto=conferenceCustInfoBuss.getConferenceCust(custNo,conferenceId);
        if(existDto!=null){
            // 若已有预约，则将参会信息同步更新至对应预约表中，实到人数 = 接口中给的参会人数，签到时间 = 签到时间；
            UpdateConferenceCustVo updateVo=new UpdateConferenceCustVo();
            updateVo.setCustNo(custNo);
            updateVo.setConferenceid(conferenceId);
            updateVo.setActualnub(scanInfo.getMeetingnumber());
            updateVo.setActualnubdt(scanInfo.getCredt());
            conferenceCustInfoBuss.updateByVo(updateVo);
        }else {
            //插入预约表
            InsertConferenceCustVo insertCustVo=new InsertConferenceCustVo();
            insertCustVo.setCustNo(custNo);
            insertCustVo.setConferenceId(conferenceId);
            insertCustVo.setCreator(SYS_CREATOR);
            insertCustVo.setAppointmentsNumber(new BigDecimal(1));
            insertCustVo.setActualNumber(scanInfo.getMeetingnumber());
            insertCustVo.setActualNubDt(scanInfo.getCredt());
            NtReturnMessageDto<String> custMessageDto=conferenceCustInfoBuss.insertConferenceCust(insertCustVo);
        }

    }



    /**
     * @description:(查询客户信息 )
     * @param custNo
     * @return com.howbuy.crm.conscust.dto.ConscustInfoDomain
     * @author: haoran.zhang
     * @date: 2023/11/16 20:31
     * @since JDK 1.8
     */
    private ConscustInfoDomain getCustInfo(String custNo){
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(custNo);
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        return queryResponse==null?null:queryResponse.getConscustinfo();
    }


    /**
     * @description:(指定 scanId的 扫码参会表数据 ，为固定的客户号)
     * @param scanId
     * @param custNo
     * @param operator
     * @return com.howbuy.crm.nt.base.response.NtReturnMessageDto<java.lang.String>
     * @since JDK 1.8
     */
    public NtReturnMessageDto<String> executeSpecifyCustNo(BigDecimal scanId,
                                                           String custNo,
                                                           String operator) {
        Assert.notNull(scanId,"scanId不能为空");
        Assert.notNull(custNo,"custNo不能为空");
        CmConferenceScan scanInfo= cmConferenceScanMapper.selectByPrimaryKey(scanId);
        //校验 scan表信息
        if(scanInfo==null){
            return NtReturnMessageDto.fail(String.format("scanId:%s 不存在扫码参会信息！",scanId));
        }
        if(ConferCustStateEnum.EXIST_ONLY.getCode().equals(scanInfo.getCustState())){
            return NtReturnMessageDto.fail(String.format("scanId:%s 客户状态：存在唯一客户！",scanId));
        }
        if(StringUtil.isNotEmpty(scanInfo.getCustNo())){
            return NtReturnMessageDto.fail(String.format("scanId:%s 已匹配客户号！",scanId));
        }
        //校验 custNo
        ConscustInfoDomain custInfo=getCustInfo(custNo);
        if(custInfo==null){
            return NtReturnMessageDto.fail(String.format("custNo:%s 不存在客户信息！",custNo));
        }
        if(!scanInfo.getMobileDigest().equals(custInfo.getMobileDigest())){
            return NtReturnMessageDto.fail(String.format("custNo:%s 与扫码参会手机号码不匹配！",custNo));
        }
        //校验通过 更新 scan表
        CmConferenceScan updateInfo=new CmConferenceScan();
        updateInfo.setId(scanId);
        updateInfo.setCustNo(custNo);
        updateInfo.setConsCode(custInfo.getConscode());
        updateInfo.setCustState(ConferCustStateEnum.EXIST_ONLY.getCode());
        updateInfo.setCustTimestamp(new Date());
        updateInfo.setModifyTimestamp(new Date());
        updateInfo.setModifier(operator);
        cmConferenceScanMapper.updateByPrimaryKeySelective(updateInfo);
        log.info("scanId:{},指定客户号：{} ，当前投顾为：{} ！,更新vo:{} ",
                scanId,custNo,custInfo.getConscode(),JSON.toJSONString(updateInfo));

        String conferenceId=scanInfo.getConferenceid();
       // 代码 重复 冗余
        CmConferenceConscust existDto=conferenceCustInfoBuss.getConferenceCust(custNo,conferenceId);
        if(existDto!=null){
            // 若已有预约，则将参会信息同步更新至对应预约表中，实到人数 = 接口中给的参会人数，签到时间 = 签到时间；
            UpdateConferenceCustVo updateVo=new UpdateConferenceCustVo();
            updateVo.setCustNo(custNo);
            updateVo.setConferenceid(conferenceId);
            updateVo.setActualnub(scanInfo.getMeetingnumber());
            updateVo.setActualnubdt(scanInfo.getCredt());
            conferenceCustInfoBuss.updateByVo(updateVo);
        }else {
            //插入预约表
            InsertConferenceCustVo insertCustVo=new InsertConferenceCustVo();
            insertCustVo.setCustNo(custNo);
            insertCustVo.setConferenceId(conferenceId);
            insertCustVo.setCreator(SYS_CREATOR);
            insertCustVo.setAppointmentsNumber(new BigDecimal(1));
            insertCustVo.setActualNumber(scanInfo.getMeetingnumber());
            insertCustVo.setActualNubDt(scanInfo.getCredt());
            NtReturnMessageDto<String> custMessageDto=conferenceCustInfoBuss.insertConferenceCust(insertCustVo);
        }
        return NtReturnMessageDto.ok();
    }


    /**
     * @description: (消息提醒：实时推送给所属投顾)
     * @param scanInfo
     * @param analyseInfo
     * @return void
     * @author: jin.wang03
     * @date: 2025/3/25 13:32
     * @since JDK 1.8
     */
    private void pushMsgToConsCode(CmConferenceScan scanInfo, MobileExistCustInfo analyseInfo) {
        String conferenceId = scanInfo.getConferenceid();
        if (ConferCustStateEnum.EXIST_ONLY != analyseInfo.getCustStateEnum()) {
            return;
        }
        try {
            // 消息提醒：实时推送给所属投顾
            // 消息内容：【会议签到通知】会议：${conferenceId}${conferenceName}，
            // 有客户 ${custname}已完成会议签到，请关注。详细请至“路演管理-会议报名签到管理”页查看。
            CmConference cmConference = cmConferenceMapper.selectByPrimaryKey(conferenceId);
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("conferenceId", conferenceId);
            paramMap.put("conferenceName", cmConference.getConferencename());
            paramMap.put("custname", analyseInfo.getUsedCustInfo().getCustName());


            cmPushMsgService.pushMsgByConsCodeList(SCAN_ONLY_CUST_MSG_ID,
                    Lists.newArrayList(analyseInfo.getUsedCustInfo().getConsCode()),
                    paramMap);
        } catch (Exception e) {
            log.error("扫码参会客户 匹配到唯一的投顾客户，触发实时消息推送，出现异常！", e);
        }
    }

}

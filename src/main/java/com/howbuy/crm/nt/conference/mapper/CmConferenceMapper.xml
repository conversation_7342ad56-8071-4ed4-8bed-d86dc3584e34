<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConference">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE-->
    <id column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid" />
    <result column="CONFERENCENAME" jdbcType="VARCHAR" property="conferencename" />
    <result column="CONFERENCEDT" jdbcType="VARCHAR" property="conferencedt" />
    <result column="CONFERENCETYPE" jdbcType="VARCHAR" property="conferencetype" />
    <result column="ORGCODE" jdbcType="VARCHAR" property="orgcode" />
    <result column="PROVCODE" jdbcType="VARCHAR" property="provcode" />
    <result column="CITYCODE" jdbcType="VARCHAR" property="citycode" />
    <result column="MAXNUMBER" jdbcType="DECIMAL" property="maxnumber" />
    <result column="CREATDT" jdbcType="VARCHAR" property="creatdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="MODIFYDT" jdbcType="VARCHAR" property="modifydt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CUTOFFDT" jdbcType="VARCHAR" property="cutoffdt" />
    <result column="MEETINGCONTENTS" jdbcType="VARCHAR" property="meetingcontents" />
    <result column="UNCOMMITTEDDT" jdbcType="VARCHAR" property="uncommitteddt" />
    <result column="NOIDDT" jdbcType="VARCHAR" property="noiddt" />
    <result column="NOROUTINGDT" jdbcType="VARCHAR" property="noroutingdt" />
    <result column="FILEAUDITSTATUS" jdbcType="VARCHAR" property="fileauditstatus" />
    <result column="AUDITTEXT" jdbcType="VARCHAR" property="audittext" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="LAT" jdbcType="VARCHAR" property="lat" />
    <result column="LNG" jdbcType="VARCHAR" property="lng" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="LAST_AUDIT_STATUS" jdbcType="VARCHAR" property="lastAuditStatus" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="AUDITOR" jdbcType="VARCHAR" property="auditor" />
    <result column="AUDIT_TIMESTAMP" jdbcType="TIMESTAMP" property="auditTimestamp" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="COURSEID" jdbcType="VARCHAR" property="courseid" />
    <result column="ISLCJZ" jdbcType="VARCHAR" property="islcjz" />
  </resultMap>

  <resultMap id="PageResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceDisplayDto" extends="BaseResultMap">

  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONFERENCEID, CONFERENCENAME, CONFERENCEDT, CONFERENCETYPE, ORGCODE, PROVCODE, CITYCODE,
    MAXNUMBER, CREATDT, CREATER, MODIFYDT, MODIFIER, CUTOFFDT, MEETINGCONTENTS, UNCOMMITTEDDT,
    NOIDDT, NOROUTINGDT, FILEAUDITSTATUS, AUDITTEXT, ADDRESS, LAT, LNG, AUDIT_STATUS,
    LAST_AUDIT_STATUS, REMARK, CREATE_TIMESTAMP, MODIFY_TIMESTAMP, AUDITOR, AUDIT_TIMESTAMP,
    REC_STAT, COURSEID, ISLCJZ
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONFERENCE
    where CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
  </select>

  <select id="selectValidConferenceById" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONFERENCE
    where CONFERENCEID = #{conferenceId,jdbcType=VARCHAR}
    and REC_STAT = '1'
  </select>

  <select id="selectValidConferenceByCourseId" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONFERENCE
    where COURSEID = #{courseId,jdbcType=VARCHAR}
    and REC_STAT = '1'
    </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_CONFERENCE
    where CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConference">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE (CONFERENCEID, CONFERENCENAME, CONFERENCEDT,
    CONFERENCETYPE, ORGCODE, PROVCODE,
    CITYCODE, MAXNUMBER, CREATDT,
    CREATER, MODIFYDT, MODIFIER,
    CUTOFFDT, MEETINGCONTENTS, UNCOMMITTEDDT,
    NOIDDT, NOROUTINGDT, FILEAUDITSTATUS,
    AUDITTEXT, ADDRESS, LAT,
    LNG, AUDIT_STATUS, LAST_AUDIT_STATUS,
    REMARK, CREATE_TIMESTAMP, MODIFY_TIMESTAMP,
    AUDITOR, AUDIT_TIMESTAMP, REC_STAT,
    COURSEID, ISLCJZ)
    values (#{conferenceid,jdbcType=VARCHAR}, #{conferencename,jdbcType=VARCHAR}, #{conferencedt,jdbcType=VARCHAR},
    #{conferencetype,jdbcType=VARCHAR}, #{orgcode,jdbcType=VARCHAR}, #{provcode,jdbcType=VARCHAR},
    #{citycode,jdbcType=VARCHAR}, #{maxnumber,jdbcType=DECIMAL}, #{creatdt,jdbcType=VARCHAR},
    #{creater,jdbcType=VARCHAR}, #{modifydt,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR},
    #{cutoffdt,jdbcType=VARCHAR}, #{meetingcontents,jdbcType=VARCHAR}, #{uncommitteddt,jdbcType=VARCHAR},
    #{noiddt,jdbcType=VARCHAR}, #{noroutingdt,jdbcType=VARCHAR}, #{fileauditstatus,jdbcType=VARCHAR},
    #{audittext,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{lat,jdbcType=VARCHAR},
    #{lng,jdbcType=VARCHAR}, #{auditStatus,jdbcType=VARCHAR}, #{lastAuditStatus,jdbcType=VARCHAR},
    #{remark,jdbcType=VARCHAR}, #{createTimestamp,jdbcType=TIMESTAMP}, #{modifyTimestamp,jdbcType=TIMESTAMP},
    #{auditor,jdbcType=VARCHAR}, #{auditTimestamp,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR},
    #{courseid,jdbcType=VARCHAR}, #{islcjz,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.conference.dto.CmConference">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conferenceid != null">
        CONFERENCEID,
      </if>
      <if test="conferencename != null">
        CONFERENCENAME,
      </if>
      <if test="conferencedt != null">
        CONFERENCEDT,
      </if>
      <if test="conferencetype != null">
        CONFERENCETYPE,
      </if>
      <if test="orgcode != null">
        ORGCODE,
      </if>
      <if test="provcode != null">
        PROVCODE,
      </if>
      <if test="citycode != null">
        CITYCODE,
      </if>
      <if test="maxnumber != null">
        MAXNUMBER,
      </if>
      <if test="creatdt != null">
        CREATDT,
      </if>
      <if test="creater != null">
        CREATER,
      </if>
      <if test="modifydt != null">
        MODIFYDT,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="cutoffdt != null">
        CUTOFFDT,
      </if>
      <if test="meetingcontents != null">
        MEETINGCONTENTS,
      </if>
      <if test="uncommitteddt != null">
        UNCOMMITTEDDT,
      </if>
      <if test="noiddt != null">
        NOIDDT,
      </if>
      <if test="noroutingdt != null">
        NOROUTINGDT,
      </if>
      <if test="fileauditstatus != null">
        FILEAUDITSTATUS,
      </if>
      <if test="audittext != null">
        AUDITTEXT,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="lat != null">
        LAT,
      </if>
      <if test="lng != null">
        LNG,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="lastAuditStatus != null">
        LAST_AUDIT_STATUS,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP,
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP,
      </if>
      <if test="auditor != null">
        AUDITOR,
      </if>
      <if test="auditTimestamp != null">
        AUDIT_TIMESTAMP,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
      <if test="courseid != null">
        COURSEID,
      </if>
      <if test="islcjz != null">
        ISLCJZ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conferenceid != null">
        #{conferenceid,jdbcType=VARCHAR},
      </if>
      <if test="conferencename != null">
        #{conferencename,jdbcType=VARCHAR},
      </if>
      <if test="conferencedt != null">
        #{conferencedt,jdbcType=VARCHAR},
      </if>
      <if test="conferencetype != null">
        #{conferencetype,jdbcType=VARCHAR},
      </if>
      <if test="orgcode != null">
        #{orgcode,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="maxnumber != null">
        #{maxnumber,jdbcType=DECIMAL},
      </if>
      <if test="creatdt != null">
        #{creatdt,jdbcType=VARCHAR},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifydt != null">
        #{modifydt,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="cutoffdt != null">
        #{cutoffdt,jdbcType=VARCHAR},
      </if>
      <if test="meetingcontents != null">
        #{meetingcontents,jdbcType=VARCHAR},
      </if>
      <if test="uncommitteddt != null">
        #{uncommitteddt,jdbcType=VARCHAR},
      </if>
      <if test="noiddt != null">
        #{noiddt,jdbcType=VARCHAR},
      </if>
      <if test="noroutingdt != null">
        #{noroutingdt,jdbcType=VARCHAR},
      </if>
      <if test="fileauditstatus != null">
        #{fileauditstatus,jdbcType=VARCHAR},
      </if>
      <if test="audittext != null">
        #{audittext,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastAuditStatus != null">
        #{lastAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTimestamp != null">
        #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTimestamp != null">
        #{auditTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="courseid != null">
        #{courseid,jdbcType=VARCHAR},
      </if>
      <if test="islcjz != null">
        #{islcjz,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.conference.dto.CmConference">
    <!--@mbg.generated-->
    update CM_CONFERENCE
    <set>
      <if test="conferencename != null">
        CONFERENCENAME = #{conferencename,jdbcType=VARCHAR},
      </if>
      <if test="conferencedt != null">
        CONFERENCEDT = #{conferencedt,jdbcType=VARCHAR},
      </if>
      <if test="conferencetype != null">
        CONFERENCETYPE = #{conferencetype,jdbcType=VARCHAR},
      </if>
      <if test="orgcode != null">
        ORGCODE = #{orgcode,jdbcType=VARCHAR},
      </if>
      <if test="provcode != null">
        PROVCODE = #{provcode,jdbcType=VARCHAR},
      </if>
      <if test="citycode != null">
        CITYCODE = #{citycode,jdbcType=VARCHAR},
      </if>
      <if test="maxnumber != null">
        MAXNUMBER = #{maxnumber,jdbcType=DECIMAL},
      </if>
      <if test="creatdt != null">
        CREATDT = #{creatdt,jdbcType=VARCHAR},
      </if>
      <if test="creater != null">
        CREATER = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="modifydt != null">
        MODIFYDT = #{modifydt,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="cutoffdt != null">
        CUTOFFDT = #{cutoffdt,jdbcType=VARCHAR},
      </if>
      <if test="meetingcontents != null">
        MEETINGCONTENTS = #{meetingcontents,jdbcType=VARCHAR},
      </if>
      <if test="uncommitteddt != null">
        UNCOMMITTEDDT = #{uncommitteddt,jdbcType=VARCHAR},
      </if>
      <if test="noiddt != null">
        NOIDDT = #{noiddt,jdbcType=VARCHAR},
      </if>
      <if test="noroutingdt != null">
        NOROUTINGDT = #{noroutingdt,jdbcType=VARCHAR},
      </if>
      <if test="fileauditstatus != null">
        FILEAUDITSTATUS = #{fileauditstatus,jdbcType=VARCHAR},
      </if>
      <if test="audittext != null">
        AUDITTEXT = #{audittext,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        LAT = #{lat,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        LNG = #{lng,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="lastAuditStatus != null">
        LAST_AUDIT_STATUS = #{lastAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        AUDITOR = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="auditTimestamp != null">
        AUDIT_TIMESTAMP = #{auditTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="courseid != null">
        COURSEID = #{courseid,jdbcType=VARCHAR},
      </if>
      <if test="islcjz != null">
        ISLCJZ = #{islcjz,jdbcType=VARCHAR},
      </if>
    </set>
    where CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.conference.dto.CmConference">
    <!--@mbg.generated-->
    update CM_CONFERENCE
    set CONFERENCENAME = #{conferencename,jdbcType=VARCHAR},
    CONFERENCEDT = #{conferencedt,jdbcType=VARCHAR},
    CONFERENCETYPE = #{conferencetype,jdbcType=VARCHAR},
    ORGCODE = #{orgcode,jdbcType=VARCHAR},
    PROVCODE = #{provcode,jdbcType=VARCHAR},
    CITYCODE = #{citycode,jdbcType=VARCHAR},
    MAXNUMBER = #{maxnumber,jdbcType=DECIMAL},
    CREATDT = #{creatdt,jdbcType=VARCHAR},
    CREATER = #{creater,jdbcType=VARCHAR},
    MODIFYDT = #{modifydt,jdbcType=VARCHAR},
    MODIFIER = #{modifier,jdbcType=VARCHAR},
    CUTOFFDT = #{cutoffdt,jdbcType=VARCHAR},
    MEETINGCONTENTS = #{meetingcontents,jdbcType=VARCHAR},
    UNCOMMITTEDDT = #{uncommitteddt,jdbcType=VARCHAR},
    NOIDDT = #{noiddt,jdbcType=VARCHAR},
    NOROUTINGDT = #{noroutingdt,jdbcType=VARCHAR},
    FILEAUDITSTATUS = #{fileauditstatus,jdbcType=VARCHAR},
    AUDITTEXT = #{audittext,jdbcType=VARCHAR},
    ADDRESS = #{address,jdbcType=VARCHAR},
    LAT = #{lat,jdbcType=VARCHAR},
    LNG = #{lng,jdbcType=VARCHAR},
    AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
    LAST_AUDIT_STATUS = #{lastAuditStatus,jdbcType=VARCHAR},
    REMARK = #{remark,jdbcType=VARCHAR},
    CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
    MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
    AUDITOR = #{auditor,jdbcType=VARCHAR},
    AUDIT_TIMESTAMP = #{auditTimestamp,jdbcType=TIMESTAMP},
    REC_STAT = #{recStat,jdbcType=VARCHAR},
    COURSEID = #{courseid,jdbcType=VARCHAR},
    ISLCJZ = #{islcjz,jdbcType=VARCHAR}
    where CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
  </update>



  <select id="selectPageByVo" resultMap="PageResultMap" parameterType="com.howbuy.crm.nt.conference.request.CmConferenceVo">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_CONFERENCE
    <where>
      <if test="conferenceid != null and conferenceid!='' ">
        AND CONFERENCEID = #{conferenceid,jdbcType=VARCHAR}
      </if>
      <if test="conferencename != null and conferencename!='' ">
        AND CONFERENCENAME = #{conferencename,jdbcType=VARCHAR}
      </if>
      <if test="conferencenameLike!=null and conferencenameLike!=''">
        AND CONFERENCENAME like   '%'||#{conferencenameLike,jdbcType=VARCHAR}||'%'
      </if>
      <if test="conferencedt != null and conferencedt != ''">
        AND CONFERENCEDT = #{conferencedt,jdbcType=VARCHAR}
      </if>
      <if test="conferencetypeList!=null and conferencetypeList.size()>0 ">
        <foreach collection="conferencetypeList" index="index" item="item" open=" AND (" separator="or" close=")">
          CONFERENCETYPE LIKE '%'||#{item}||'%'
        </foreach>
        <!--AND CONFERENCETYPE in
        <foreach collection="conferencetypeList" item="item"
                 index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>-->
      </if>
      <if test="authOutletcodeList != null and authOutletcodeList.size() > 0">
        AND ( ORGCODE in
        <foreach item="item" index="index" collection="authOutletcodeList" separator=" OR ORGCODE in ">
          <foreach collection="item" item="mId" open="(" separator="," close=")">
            #{mId}
          </foreach>
        </foreach>
        )
      </if>
      <if test="provcode != null and provcode != ''">
        AND PROVCODE = #{provcode,jdbcType=VARCHAR}
      </if>
      <if test="citycode != null and citycode != ''">
        AND CITYCODE = #{citycode,jdbcType=VARCHAR}
      </if>
      <if test="maxnumber != null">
        AND MAXNUMBER = #{maxnumber,jdbcType=DECIMAL}
      </if>
      <if test="creatdt != null and creatdt != ''">
        AND CREATDT = #{creatdt,jdbcType=VARCHAR}
      </if>
      <if test="creater != null and creater != ''">
        AND CREATER = #{creater,jdbcType=VARCHAR}
        </if>
        <if test="modifydt != null and modifydt != ''">
          AND MODIFYDT = #{modifydt,jdbcType=VARCHAR}
        </if>
        <if test="modifier != null and modifier != ''">
          AND MODIFIER = #{modifier,jdbcType=VARCHAR}
        </if>
        <if test="cutoffdt != null and cutoffdt != ''">
          AND CUTOFFDT = #{cutoffdt,jdbcType=VARCHAR}
        </if>
        <if test="conferencedt != null and conferencedt != ''">
          AND CONFERENCEDT = #{conferencedt,jdbcType=VARCHAR}
        </if>
        <if test="courseid != null and courseid != ''">
          AND COURSEID = #{courseid,jdbcType=VARCHAR}
        </if>
       <if test="islcjz != null and islcjz != ''">
         AND ISLCJZ = #{islcjz,jdbcType=VARCHAR}
       </if>
      <if test="recStat != null and recStat != ''">
        AND REC_STAT = #{recStat,jdbcType=VARCHAR}
      </if>
      <if test="auditStatus != null and auditStatus != ''">
        AND AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR}
      </if>
      <if test="conferenceBeginDt != null and conferenceBeginDt != ''"> AND to_date(CONFERENCEDT, 'YYYYMMDDHH24miss') >= to_date(#{conferenceBeginDt} || '00:00:00', 'yyyymmddhh24:mi:ss') </if>
      <if test="conferenceEndDt != null and conferenceEndDt != ''"> AND to_date(CONFERENCEDT, 'YYYYMMDDHH24miss') &lt;= to_date(#{conferenceEndDt} || '23:59:59', 'yyyymmddhh24:mi:ss') </if>
      <if test="fileauditstatus != null and fileauditstatus != ''">
        <if test="fileauditstatus == '-'.toString()">
          AND FILEAUDITSTATUS is null
        </if>
        <if test="fileauditstatus != '-'.toString()">
          AND FILEAUDITSTATUS = #{fileauditstatus}
        </if>
      </if>
    </where>
    <if test="sort != null and order != null" > ORDER BY  ${sort} ${order} </if>
  </select>


  <select id="queryCmConferenceInfoList" resultType="com.howbuy.crm.nt.conference.dto.CmNtConferenceDomain">
    SELECT T.CONFERENCEID AS conferenceid,
    T.CONFERENCENAME AS conferencename,
    T.CONFERENCEDT AS conferencedt,
    T.CUTOFFDT AS cutoffdt,
    T.MEETINGCONTENTS AS meetingcontents,
    T.ADDRESS AS address,
    T.LAT AS lat,
    T.LNG AS lng,
    T.REC_STAT AS recstat,
    (SELECT A.NAME FROM CM_PROVCITY A WHERE A.ID = T.PROVCODE AND A.LEV=1) AS provname,
    (SELECT A.NAME FROM CM_PROVCITY A WHERE A.ID = T.CITYCODE AND A.LEV=2) AS cityname
    FROM CM_CONFERENCE T
    <where>
    <if test="conferenceidlist!=null">
      AND T.CONFERENCEID in
      <foreach collection="conferenceidlist" item="item"
               index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="hboneno != null">
      AND T.CONFERENCEID IN
      (SELECT CC.CONFERENCEID
      FROM
      CM_CONFERENCE_CONSCUST CC,
      CM_CONSCUST CT
      WHERE CT.CONSCUSTNO = CC.CONSCUSTNO
      AND CT.HBONE_NO = #{hboneno}
      <if test="actualnub != null and actualnub == '0'.toString()">
        AND (CC.ACTUALNUB is null or CC.ACTUALNUB <![CDATA[=]]> 0)
      </if>
      <if test="actualnub != null and actualnub == '1'.toString()">
        AND CC.ACTUALNUB <![CDATA[>]]> 0
      </if>
      )
    </if>
    </where>
  </select>


  <select id="queryAfterCurrentTimeConferenceInfoByHboneNo"
          resultType="com.howbuy.crm.nt.conference.dto.CmHzAppletConferenceInfoDTO">
    SELECT
      T1.CONFERENCEID AS conferenceId,
      T1.CONFERENCENAME AS conferenceName,
      T1.CONFERENCEDT AS conferenceDate,
      T2.CONSCUSTNO ,
      T2.ACTUALNUB AS conferenceActualNub,
      T3.HBONE_NO ,
      CASE WHEN T4.MOBILE_DIGEST IS NOT NULL THEN '1' ELSE '0' END AS whetherSignIn
    FROM
      CM_CONFERENCE_CONSCUST T2
        LEFT JOIN CM_CONFERENCE T1 ON
        T1.CONFERENCEID = T2.CONFERENCEID
        LEFT JOIN CM_CONSCUST T3 ON T3.CONSCUSTNO = T2.CONSCUSTNO
        LEFT JOIN CM_CONFERENCE_SCAN T4 ON T3.MOBILE_DIGEST = T4.MOBILE_DIGEST AND T4.CONFERENCEID = T1.CONFERENCEID
    WHERE
      T1.CONFERENCEDT <![CDATA[>=]]> #{onlineTime,jdbcType=VARCHAR}
      and T1.CONFERENCEDT <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
      and T1.REC_STAT = '1'
      AND T1.AUDIT_STATUS = '1'
      and  T3.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
     ORDER BY T1.CONFERENCEDT ASC  ,T1.CONFERENCEID ASC
  </select>
  <select id="pageQueryBeforeCurrentTimeConferenceInfoByHboneNo"
          resultType="com.howbuy.crm.nt.conference.dto.CmHzAppletConferenceInfoDTO">
    SELECT
      T1.CONFERENCEID AS conferenceId,
      T1.CONFERENCENAME AS conferenceName,
      T1.CONFERENCEDT AS conferenceDate,
      T2.CONSCUSTNO ,
      T2.ACTUALNUB AS conferenceActualNub,
      T3.HBONE_NO,
      CASE WHEN T4.MOBILE_DIGEST IS NOT NULL THEN '1' ELSE '0' END AS whetherSignIn
    FROM
      CM_CONFERENCE_CONSCUST T2
        LEFT JOIN CM_CONFERENCE T1 ON
        T1.CONFERENCEID = T2.CONFERENCEID
        LEFT JOIN CM_CONSCUST T3 ON T3.CONSCUSTNO = T2.CONSCUSTNO
        LEFT JOIN CM_CONFERENCE_SCAN T4 ON T3.MOBILE_DIGEST = T4.MOBILE_DIGEST AND T4.CONFERENCEID = T1.CONFERENCEID
    WHERE
      T1.CONFERENCEDT <![CDATA[<]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
      and T1.CONFERENCEDT <![CDATA[>=]]> #{onlineTime,jdbcType=VARCHAR}
      and T1.REC_STAT = '1'
      AND T1.AUDIT_STATUS = '1'
      and  T3.HBONE_NO = #{hboneNo,jdbcType=VARCHAR}
    ORDER BY T1.CONFERENCEDT DESC  ,T1.CONFERENCEID DESC
  </select>
  <select id="queryPortraitStudyProgress" resultType="com.howbuy.crm.nt.conference.dto.CmPortraitStudyProgressDTO">
    select
      A.CONSCUSTNO as conscustno,
      C.HBONE_NO as hboneNo,
      T.CONFERENCEID as conferenceId,
      T.CONFERENCENAME as conferenceName,
      T.CONFERENCETYPE as conferenceType,
      T.islcjz as islcjz,
      A.ACTUALNUB as actualnub,
      A.APPOINTMENTSNUB as appointmentsnub
    from CM_CONFERENCE T
    left join CM_CONFERENCE_CONSCUST A on T.CONFERENCEID = A.CONFERENCEID
    left join CM_CONSCUST C on A.CONSCUSTNO = C.CONSCUSTNO
    where T.REC_STAT = '1'
    AND T.AUDIT_STATUS = '1'
    AND C.hbone_no = #{hboneNo,jdbcType=VARCHAR}
    AND T.CONFERENCETYPE like '%9%'
  </select>
  <select id="queryFinancialNineStudyPageList" resultType="com.howbuy.crm.nt.conference.dto.CmPortraitStudyDetailDTO">
    select
      A.CONSCUSTNO as conscustno,
      C.HBONE_NO as hboneNo,
      T.CONFERENCEID as conferenceId,
      T.CONFERENCENAME as conferenceName,
      T.CONFERENCETYPE as conferenceType,
      T.CONFERENCEDT as conferenceDt,
      T.islcjz as islcjz,
      T.PROVCODE as provCode,
      T.CITYCODE as cityCode,
      (SELECT NAME FROM CM_PROVCITY WHERE T.PROVCODE = ID AND PID = '1') AS provName,
      (SELECT NAME FROM CM_PROVCITY WHERE T.CITYCODE = ID AND PID = T.PROVCODE) AS cityName,
      T.COURSEID as courseId,
      A.ACTUALNUB as actualnub,
      A.APPOINTMENTSNUB as appointmentsnub
    from CM_CONFERENCE T
    left join CM_CONFERENCE_CONSCUST A on T.CONFERENCEID = A.CONFERENCEID
    left join CM_CONSCUST C on A.CONSCUSTNO = C.CONSCUSTNO
    where T.REC_STAT = '1'
    AND T.AUDIT_STATUS = '1'
    AND A.ACTUALNUB > 0
    AND C.hbone_no = #{hboneNo,jdbcType=VARCHAR}
    AND T.CONFERENCETYPE like '%9%'
    ORDER BY T.CONFERENCEDT DESC
  </select>
  <select id="queryOfflineActivityPageList" resultType="com.howbuy.crm.nt.conference.dto.CmPortraitStudyDetailDTO">
    select
      A.CONSCUSTNO as conscustno,
      C.HBONE_NO as hboneNo,
      T.CONFERENCEID as conferenceId,
      T.CONFERENCENAME as conferenceName,
      T.CONFERENCETYPE as conferenceType,
      T.CONFERENCEDT as conferenceDt,
      T.islcjz as islcjz,
      T.PROVCODE as provCode,
      T.CITYCODE as cityCode,
      (SELECT NAME FROM CM_PROVCITY WHERE T.PROVCODE = ID AND PID = '1') AS provName,
      (SELECT NAME FROM CM_PROVCITY WHERE T.CITYCODE = ID AND PID = T.PROVCODE) AS cityName,
      T.COURSEID as courseId,
      A.ACTUALNUB as actualnub,
      A.APPOINTMENTSNUB as appointmentsnub
    from CM_CONFERENCE T
    left join CM_CONFERENCE_CONSCUST A on T.CONFERENCEID = A.CONFERENCEID
    left join CM_CONSCUST C on A.CONSCUSTNO = C.CONSCUSTNO
    where T.REC_STAT = '1'
    AND T.AUDIT_STATUS = '1'
    AND C.hbone_no = #{hboneNo,jdbcType=VARCHAR}
    AND (
        (T.CONFERENCETYPE not like '%9%' AND T.CONFERENCETYPE != '6' AND A.ACTUALNUB > 0)
        OR
        (T.CONFERENCETYPE = '6' AND A.APPOINTMENTSTYPE = '1')
    )
    ORDER BY T.CONFERENCEDT DESC
  </select>
</mapper>
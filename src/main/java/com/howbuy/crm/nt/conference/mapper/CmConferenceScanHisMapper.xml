<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conference.dao.CmConferenceScanHisMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conference.dto.CmConferenceScanHis">
    <!--@mbg.generated-->
    <!--@Table CM_CONFERENCE_SCAN_HIS-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid" />
    <result column="MOBILE_DIGEST" jdbcType="VARCHAR" property="mobileDigest" />
    <result column="MOBILE_MASK" jdbcType="VARCHAR" property="mobileMask" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="CONSNAME" jdbcType="VARCHAR" property="consname" />
    <result column="MEETINGNUMBER" jdbcType="DECIMAL" property="meetingnumber" />
    <result column="SCAN_CUST_STATE" jdbcType="VARCHAR" property="scanCustState" />
    <result column="CUST_STATE" jdbcType="VARCHAR" property="custState" />
    <result column="CUST_NO" jdbcType="VARCHAR" property="custNo" />
    <result column="CONS_CODE" jdbcType="VARCHAR" property="consCode"/>
    <result column="CUST_TIMESTAMP" jdbcType="TIMESTAMP" property="custTimestamp" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIMESTAMP" jdbcType="TIMESTAMP" property="modifyTimestamp" />
    <result column="DELETE_OPERATOR" jdbcType="VARCHAR" property="deleteOperator" />
    <result column="DELETE_TIMESTAMP" jdbcType="TIMESTAMP" property="deleteTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, CONFERENCEID, MOBILE_DIGEST, MOBILE_MASK, CREDT, CREATOR, CUSTNAME, CONSNAME, 
    MEETINGNUMBER, SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE,CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP,
    DELETE_OPERATOR, DELETE_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.math.BigDecimal" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_CONFERENCE_SCAN_HIS
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.math.BigDecimal">
    <!--@mbg.generated-->
    delete from CM_CONFERENCE_SCAN_HIS
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScanHis">
    <!--@mbg.generated-->
    insert into CM_CONFERENCE_SCAN_HIS (ID, CONFERENCEID, MOBILE_DIGEST, 
      MOBILE_MASK, CREDT, CREATOR, 
      CUSTNAME, CONSNAME, MEETINGNUMBER, 
      SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE,
      CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP, 
      DELETE_OPERATOR, DELETE_TIMESTAMP)
    values (#{id,jdbcType=DECIMAL}, #{conferenceid,jdbcType=VARCHAR}, #{mobileDigest,jdbcType=VARCHAR}, 
      #{mobileMask,jdbcType=VARCHAR}, #{credt,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{custname,jdbcType=VARCHAR}, #{consname,jdbcType=VARCHAR}, #{meetingnumber,jdbcType=DECIMAL}, 
      #{scanCustState,jdbcType=VARCHAR}, #{custState,jdbcType=VARCHAR}, #{custNo,jdbcType=VARCHAR}, #{consCode,jdbcType=VARCHAR},
      #{custTimestamp,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTimestamp,jdbcType=TIMESTAMP}, 
      #{deleteOperator,jdbcType=VARCHAR}, #{deleteTimestamp,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertByScanIdList" parameterType="map">
    INSERT INTO CM_CONFERENCE_SCAN_HIS
    (ID, CONFERENCEID, MOBILE_DIGEST, MOBILE_MASK, CREDT, CREATOR, CUSTNAME, CONSNAME, MEETINGNUMBER,
     SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE, CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP,
     DELETE_OPERATOR, DELETE_TIMESTAMP)
    SELECT ID, CONFERENCEID, MOBILE_DIGEST, MOBILE_MASK, CREDT, CREATOR, CUSTNAME, CONSNAME, MEETINGNUMBER,
     SCAN_CUST_STATE, CUST_STATE, CUST_NO, CONS_CODE, CUST_TIMESTAMP, MODIFIER, MODIFY_TIMESTAMP,
    #{operator,jdbcType=VARCHAR} AS DELETE_OPERATOR,
    SYSTIMESTAMP AS DELETE_TIMESTAMP
    FROM CM_CONFERENCE_SCAN
    WHERE ID IN
    <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScanHis">
    <!--@mbg.generated-->
    update CM_CONFERENCE_SCAN_HIS
    <set>
      <if test="conferenceid != null">
        CONFERENCEID = #{conferenceid,jdbcType=VARCHAR},
      </if>
      <if test="mobileDigest != null">
        MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      </if>
      <if test="mobileMask != null">
        MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        CREDT = #{credt,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="custname != null">
        CUSTNAME = #{custname,jdbcType=VARCHAR},
      </if>
      <if test="consname != null">
        CONSNAME = #{consname,jdbcType=VARCHAR},
      </if>
      <if test="meetingnumber != null">
        MEETINGNUMBER = #{meetingnumber,jdbcType=DECIMAL},
      </if>
      <if test="scanCustState != null">
        SCAN_CUST_STATE = #{scanCustState,jdbcType=VARCHAR},
      </if>
      <if test="custState != null">
        CUST_STATE = #{custState,jdbcType=VARCHAR},
      </if>
      <if test="custNo != null">
        CUST_NO = #{custNo,jdbcType=VARCHAR},
      </if>
      <if test="consCode != null and consCode != ''">
        CONS_CODE = #{consCode,jdbcType=VARCHAR},
        </if>
      <if test="custTimestamp != null">
        CUST_TIMESTAMP = #{custTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTimestamp != null">
        MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteOperator != null">
        DELETE_OPERATOR = #{deleteOperator,jdbcType=VARCHAR},
      </if>
      <if test="deleteTimestamp != null">
        DELETE_TIMESTAMP = #{deleteTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.conference.dto.CmConferenceScanHis">
    <!--@mbg.generated-->
    update CM_CONFERENCE_SCAN_HIS
    set CONFERENCEID = #{conferenceid,jdbcType=VARCHAR},
      MOBILE_DIGEST = #{mobileDigest,jdbcType=VARCHAR},
      MOBILE_MASK = #{mobileMask,jdbcType=VARCHAR},
      CREDT = #{credt,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CUSTNAME = #{custname,jdbcType=VARCHAR},
      CONSNAME = #{consname,jdbcType=VARCHAR},
      MEETINGNUMBER = #{meetingnumber,jdbcType=DECIMAL},
      SCAN_CUST_STATE = #{scanCustState,jdbcType=VARCHAR},
      CUST_STATE = #{custState,jdbcType=VARCHAR},
      CUST_NO = #{custNo,jdbcType=VARCHAR},
      CONS_CODE = #{consCode,jdbcType=VARCHAR},
      CUST_TIMESTAMP = #{custTimestamp,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIMESTAMP = #{modifyTimestamp,jdbcType=TIMESTAMP},
      DELETE_OPERATOR = #{deleteOperator,jdbcType=VARCHAR},
      DELETE_TIMESTAMP = #{deleteTimestamp,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>
</mapper>
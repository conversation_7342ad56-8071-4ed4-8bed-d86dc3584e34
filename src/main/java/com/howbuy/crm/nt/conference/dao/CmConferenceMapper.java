package com.howbuy.crm.nt.conference.dao;

import com.github.pagehelper.Page;
import com.howbuy.crm.nt.conference.dto.*;
import com.howbuy.crm.nt.conference.request.CmConferenceVo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/30 11:18
 * @since JDK 1.8
 */
@MapperScan
public interface CmConferenceMapper {
    int deleteByPrimaryKey(String conferenceid);

    int insert(CmConference record);

    int insertSelective(CmConference record);

    CmConference selectByPrimaryKey(String conferenceid);

    int updateByPrimaryKeySelective(CmConference record);

    int updateByPrimaryKey(CmConference record);


    /**
     * 查询路演会议信息列表
     * @param hboneno
     * @param conferenceidlist
     * @param actualnub
     * @return
     */
    @Deprecated
    List<CmNtConferenceDomain> queryCmConferenceInfoList(@Param("hboneno")String hboneno,
                                                         @Param("conferenceidlist")List<String> conferenceidlist,
                                                         @Param("actualnub")String actualnub);

    /**
     * 分页查询路演会议信息列表
     * @param queryVo
     * @return
     */
    Page<CmConferenceDisplayDto> selectPageByVo(CmConferenceVo queryVo);


    /**
     * @description:(通过会议id 查询有效的会议信息  todo: 是否约束审核状态)
     * @param conferenceId
     * @return com.howbuy.crm.nt.conference.dto.CmConference
     * @author: haoran.zhang
     * @date: 2023/11/7 13:13
     * @since JDK 1.8
     */
    CmConference selectValidConferenceById(@Param("conferenceId") String conferenceId);

    /**
     * @description:(通过课程id 查询有效的会议信息 )
     * @param courseId
     * @return com.howbuy.crm.nt.conference.dto.CmConference
     * @author: haoran.zhang
     * @date: 2023/11/7 13:13
     * @since JDK 1.8
     */
    CmConference selectValidConferenceByCourseId(@Param("courseId") String courseId);

    /**
     * @description: 根据一账通号查询当前日期后的会议信息,包含当前日期
     * @param hboneNo 一账通号
     * @return java.util.List<com.howbuy.crm.nt.conference.dto.CmHzAppletConferenceInfoDTO>
     * @author: jinqing.rao
     * @date: 2024/6/19 13:47
     * @since JDK 1.8
     */
    List<CmHzAppletConferenceInfoDTO> queryAfterCurrentTimeConferenceInfoByHboneNo(@Param("hboneNo") String hboneNo,@Param("onlineTime") String onlineTime);

    /**
     * @description: 根据客户号分页查询已经结束的会议
     * @param hboneNo 一账通号
     * @return com.github.pagehelper.Page<com.howbuy.crm.nt.conference.dto.CmHzAppletConferenceInfoDTO>
     * @author: jinqing.rao
     * @date: 2024/6/19 15:10
     */
    Page<CmHzAppletConferenceInfoDTO> pageQueryBeforeCurrentTimeConferenceInfoByHboneNo(@Param("hboneNo") String hboneNo,@Param("onlineTime") String onlineTime);

    /**
     * 查询客户画像用户理财九章学习进度（查询用户实际到场参会的理财九章会议）
     * @param hboneNo 一账通号
     * @return List
     */
    List<CmPortraitStudyProgressDTO> queryPortraitStudyProgress(@Param("hboneNo") String hboneNo);

    /**
     * 查询客户画像用户理财九章学习明细
     * @param hboneNo 一账通号
     * @return Page
     */
    Page<CmPortraitStudyDetailDTO> queryFinancialNineStudyPageList(@Param("hboneNo") String hboneNo);

    /**
     * 查询客户画像用户理财九章线下活动列表
     * @param hboneNo 一账通号
     * @return Page
     */
    Page<CmPortraitStudyDetailDTO> queryOfflineActivityPageList(@Param("hboneNo") String hboneNo);
}
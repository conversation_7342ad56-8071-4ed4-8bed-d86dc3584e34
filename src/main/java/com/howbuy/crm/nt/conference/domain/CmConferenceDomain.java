package com.howbuy.crm.nt.conference.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CmConferenceDomain implements Serializable {


	private static final long serialVersionUID = 7252122174746513466L;

	/** 会议主键 */
	private String conferenceid;
	
	/** 会议名称 */
	private String conferencename;
	
	/** 会议时间 */
	private String conferencedt;
	
	/** 会议类型 */
	private String conferencetype;
	
	/** 举办部门 */
	private String orgcode;
	
	/** 会议省份 */
	private String provname;

	/** 会议市区 */
	private String cityname;
	
	/** 参会人数上限 */
	private int maxnumber;
	
	/** 产品 */
	private String pcodes;
	
	/** 管理人 */
	private String companys;
	
	/** 产品线 */
	private String lines;
	
	/** 截止报名时间 */
	private String cutoffdt;

	/** 会议内容 */
	private String meetingcontents;

	/**
	 * 会议详细地址
	 */
	private String address;
	/**
	 * 纬度
	 */
	private String lat;
	/**
	 * 经度
	 */
	private String lng;
}

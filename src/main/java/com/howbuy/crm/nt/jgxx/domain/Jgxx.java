package com.howbuy.crm.nt.jgxx.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/31 19:03
 * @since JDK 1.8
 */
@Data
public class Jgxx implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
    private String jlzj;

    /**
    * 机构代码
    */
    private String jgdm;

    /**
    * 机构名称
    */
    private String jgmc;

    /**
    * 机构简称
    */
    private String jgjc;

    /**
    * 机构英文名称
    */
    private String jymc;

    /**
    * 机构英文简称
    */
    private String jyjc;

    /**
    * 机构类型代码
    */
    private String jldm;

    /**
    * 机构类型
    */
    private String jglx;

    /**
    * 是否分支机构
    */
    private String sffz;

    /**
    * 是否上市
    */
    private String sfss;

    /**
    * 法定代表人
    */
    private String frdb;

    /**
    * 董事长
    */
    private String dszm;

    /**
    * 总经理
    */
    private String zjlm;

    /**
    * 信息披露人
    */
    private String xxpl;

    /**
    * 信息披露人电话
    */
    private String pldh;

    /**
    * 信息披露人传真
    */
    private String plcz;

    /**
    * 信息披露人电子邮箱
    */
    private String pldy;

    /**
    * 组织形式
    */
    private String zzxs;

    /**
    * 成立日期
    */
    private String clrq;

    /**
    * 币种
    */
    private String hbzl;

    /**
    * 注册资本
    */
    private BigDecimal zczb;

    /**
    * 法定股本
    */
    private BigDecimal fdgb;

    /**
    * 注册地址
    */
    private String zcdz;

    /**
    * 注册地址邮政编码
    */
    private String zcyb;

    /**
    * 办公地址
    */
    private String bgdz;

    /**
    * 办公地址邮政编码
    */
    private String bgyb;

    /**
    * 公司电话
    */
    private String gsdh;

    /**
    * 公司传真
    */
    private String gscz;

    /**
    * 公司电子邮箱
    */
    private String gsdy;

    /**
    * 公司网址
    */
    private String gswz;

    /**
    * 客服电话
    */
    private String kfdh;

    /**
    * 公司沿革
    */
    private String gsyg;

    /**
    * 经营范围
    */
    private String jyfw;

    /**
    * 主营业务
    */
    private String zyyw;

    /**
    * 登记日
    */
    private String djrq;

    /**
    * 营业执照号码
    */
    private String yyzz;

    /**
    * 国税登记号码
    */
    private String gsdj;

    /**
    * 地税登记号码
    */
    private String dxdj;

    /**
    * 信息披露网址
    */
    private String plwz;

    /**
    * 信息披露报纸
    */
    private String plbz;

    /**
    * 公司状态
    */
    private String gszt;

    /**
    * 机构级别
    */
    private String jgjb;

    /**
    * 国家
    */
    private String gjdm;

    /**
    * 地区
    */
    private String dqdm;

    /**
    * 公告日期
    */
    private String ggrq;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 时间戳
    */
    private Date stimestamp;

    /**
    * 机构名称拼音
    */
    private String jgpy;

    /**
    * 机构全称拼音
    */
    private String jqpy;

    /**
    * 好买机构类别
    */
    private String hjlb;

    /**
    * 大股东
    */
    private String dgds;

    /**
    * 大股东组织形式
    */
    private String gdxs;

    /**
    * 失败案例
    */
    private String sbal;

    /**
    * 股东构成
    */
    private String gdgc;

    /**
    * 是否私募基金管理人1-是0-否
    */
    private String sfsmjjglr;

    /**
    * 投资理念
    */
    private String tzln;

    /**
    * 投资策略
    */
    private String tzcl;

    /**
    * 操作类型
    */
    private String mOptType;

    /**
    * 增量ID
    */
    private BigDecimal zlid;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 披露权限（注： ‘0’ 否，‘1’是）默认 1
    */
    private String plqx;

    /**
    * 是否推荐公司（注： ‘0’ 否，‘1’是）默认 1
    */
    private String sftjgs;

    /**
    * 修改时间
    */
    private Date modifyTime;

    private String creator;

    private String modifier;

    private String checker;

    /**
    * 机构logo照片地址
    */
    private String zpdz;

    /**
    * 是否三方（1-是，0-否）
    */
    private String sfsf;

    /**
    * 1-股权公司
    */
    private String sfgqgs;



}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.jgxx.dao.JgxxMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.jgxx.domain.Jgxx">
    <!--@mbg.generated-->
    <!--@Table JGXX-->
    <result column="JLZJ" jdbcType="VARCHAR" property="jlzj" />
    <result column="JGDM" jdbcType="VARCHAR" property="jgdm" />
    <result column="JGMC" jdbcType="VARCHAR" property="jgmc" />
    <result column="JGJC" jdbcType="VARCHAR" property="jgjc" />
    <result column="JYMC" jdbcType="VARCHAR" property="jymc" />
    <result column="JYJC" jdbcType="VARCHAR" property="jyjc" />
    <result column="JLDM" jdbcType="VARCHAR" property="jldm" />
    <result column="JGLX" jdbcType="VARCHAR" property="jglx" />
    <result column="SFFZ" jdbcType="VARCHAR" property="sffz" />
    <result column="SFSS" jdbcType="VARCHAR" property="sfss" />
    <result column="FRDB" jdbcType="VARCHAR" property="frdb" />
    <result column="DSZM" jdbcType="VARCHAR" property="dszm" />
    <result column="ZJLM" jdbcType="VARCHAR" property="zjlm" />
    <result column="XXPL" jdbcType="VARCHAR" property="xxpl" />
    <result column="PLDH" jdbcType="VARCHAR" property="pldh" />
    <result column="PLCZ" jdbcType="VARCHAR" property="plcz" />
    <result column="PLDY" jdbcType="VARCHAR" property="pldy" />
    <result column="ZZXS" jdbcType="VARCHAR" property="zzxs" />
    <result column="CLRQ" jdbcType="VARCHAR" property="clrq" />
    <result column="HBZL" jdbcType="VARCHAR" property="hbzl" />
    <result column="ZCZB" jdbcType="DECIMAL" property="zczb" />
    <result column="FDGB" jdbcType="DECIMAL" property="fdgb" />
    <result column="ZCDZ" jdbcType="VARCHAR" property="zcdz" />
    <result column="ZCYB" jdbcType="VARCHAR" property="zcyb" />
    <result column="BGDZ" jdbcType="VARCHAR" property="bgdz" />
    <result column="BGYB" jdbcType="VARCHAR" property="bgyb" />
    <result column="GSDH" jdbcType="VARCHAR" property="gsdh" />
    <result column="GSCZ" jdbcType="VARCHAR" property="gscz" />
    <result column="GSDY" jdbcType="VARCHAR" property="gsdy" />
    <result column="GSWZ" jdbcType="VARCHAR" property="gswz" />
    <result column="KFDH" jdbcType="VARCHAR" property="kfdh" />
    <result column="GSYG" jdbcType="CLOB" property="gsyg" />
    <result column="JYFW" jdbcType="CLOB" property="jyfw" />
    <result column="ZYYW" jdbcType="CLOB" property="zyyw" />
    <result column="DJRQ" jdbcType="VARCHAR" property="djrq" />
    <result column="YYZZ" jdbcType="VARCHAR" property="yyzz" />
    <result column="GSDJ" jdbcType="VARCHAR" property="gsdj" />
    <result column="DXDJ" jdbcType="VARCHAR" property="dxdj" />
    <result column="PLWZ" jdbcType="VARCHAR" property="plwz" />
    <result column="PLBZ" jdbcType="VARCHAR" property="plbz" />
    <result column="GSZT" jdbcType="VARCHAR" property="gszt" />
    <result column="JGJB" jdbcType="VARCHAR" property="jgjb" />
    <result column="GJDM" jdbcType="VARCHAR" property="gjdm" />
    <result column="DQDM" jdbcType="VARCHAR" property="dqdm" />
    <result column="GGRQ" jdbcType="VARCHAR" property="ggrq" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="JGPY" jdbcType="VARCHAR" property="jgpy" />
    <result column="JQPY" jdbcType="VARCHAR" property="jqpy" />
    <result column="HJLB" jdbcType="VARCHAR" property="hjlb" />
    <result column="DGDS" jdbcType="CLOB" property="dgds" />
    <result column="GDXS" jdbcType="VARCHAR" property="gdxs" />
    <result column="SBAL" jdbcType="CLOB" property="sbal" />
    <result column="GDGC" jdbcType="CLOB" property="gdgc" />
    <result column="SFSMJJGLR" jdbcType="VARCHAR" property="sfsmjjglr" />
    <result column="TZLN" jdbcType="VARCHAR" property="tzln" />
    <result column="TZCL" jdbcType="VARCHAR" property="tzcl" />
    <result column="M_OPT_TYPE" jdbcType="VARCHAR" property="mOptType" />
    <result column="ZLID" jdbcType="DECIMAL" property="zlid" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="PLQX" jdbcType="VARCHAR" property="plqx" />
    <result column="SFTJGS" jdbcType="VARCHAR" property="sftjgs" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="ZPDZ" jdbcType="VARCHAR" property="zpdz" />
    <result column="SFSF" jdbcType="VARCHAR" property="sfsf" />
    <result column="SFGQGS" jdbcType="VARCHAR" property="sfgqgs" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    JLZJ, JGDM, JGMC, JGJC, JYMC, JYJC, JLDM, JGLX, SFFZ, SFSS, FRDB, DSZM, ZJLM, XXPL, 
    PLDH, PLCZ, PLDY, ZZXS, CLRQ, HBZL, ZCZB, FDGB, ZCDZ, ZCYB, BGDZ, BGYB, GSDH, GSCZ, 
    GSDY, GSWZ, KFDH, GSYG, JYFW, ZYYW, DJRQ, YYZZ, GSDJ, DXDJ, PLWZ, PLBZ, GSZT, JGJB, 
    GJDM, DQDM, GGRQ, RECSTAT, CREDT, MODDT, STIMESTAMP, JGPY, JQPY, HJLB, DGDS, GDXS, 
    SBAL, GDGC, SFSMJJGLR, TZLN, TZCL, M_OPT_TYPE, ZLID, CHECKFLAG, PLQX, SFTJGS, MODIFY_TIME, 
    CREATOR, MODIFIER, CHECKER, ZPDZ, SFSF, SFGQGS
  </sql>


  <select id="selectByJgdmList" parameterType="list" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from JGXX
    where JGDM in
    <foreach collection="jgdmList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <insert id="insert" parameterType="com.howbuy.crm.nt.jgxx.domain.Jgxx">
    <!--@mbg.generated-->
    insert into JGXX (JLZJ, JGDM, JGMC, 
      JGJC, JYMC, JYJC, JLDM, 
      JGLX, SFFZ, SFSS, FRDB, 
      DSZM, ZJLM, XXPL, PLDH, 
      PLCZ, PLDY, ZZXS, CLRQ, 
      HBZL, ZCZB, FDGB, ZCDZ, 
      ZCYB, BGDZ, BGYB, GSDH, 
      GSCZ, GSDY, GSWZ, KFDH, 
      GSYG, JYFW, ZYYW, DJRQ, 
      YYZZ, GSDJ, DXDJ, PLWZ, 
      PLBZ, GSZT, JGJB, GJDM, 
      DQDM, GGRQ, RECSTAT, 
      CREDT, MODDT, STIMESTAMP, 
      JGPY, JQPY, HJLB, DGDS, 
      GDXS, SBAL, GDGC, SFSMJJGLR, 
      TZLN, TZCL, M_OPT_TYPE, 
      ZLID, CHECKFLAG, PLQX, 
      SFTJGS, MODIFY_TIME, CREATOR, 
      MODIFIER, CHECKER, ZPDZ, 
      SFSF, SFGQGS)
    values (#{jlzj,jdbcType=VARCHAR}, #{jgdm,jdbcType=VARCHAR}, #{jgmc,jdbcType=VARCHAR}, 
      #{jgjc,jdbcType=VARCHAR}, #{jymc,jdbcType=VARCHAR}, #{jyjc,jdbcType=VARCHAR}, #{jldm,jdbcType=VARCHAR}, 
      #{jglx,jdbcType=VARCHAR}, #{sffz,jdbcType=VARCHAR}, #{sfss,jdbcType=VARCHAR}, #{frdb,jdbcType=VARCHAR}, 
      #{dszm,jdbcType=VARCHAR}, #{zjlm,jdbcType=VARCHAR}, #{xxpl,jdbcType=VARCHAR}, #{pldh,jdbcType=VARCHAR}, 
      #{plcz,jdbcType=VARCHAR}, #{pldy,jdbcType=VARCHAR}, #{zzxs,jdbcType=VARCHAR}, #{clrq,jdbcType=VARCHAR}, 
      #{hbzl,jdbcType=VARCHAR}, #{zczb,jdbcType=DECIMAL}, #{fdgb,jdbcType=DECIMAL}, #{zcdz,jdbcType=VARCHAR}, 
      #{zcyb,jdbcType=VARCHAR}, #{bgdz,jdbcType=VARCHAR}, #{bgyb,jdbcType=VARCHAR}, #{gsdh,jdbcType=VARCHAR}, 
      #{gscz,jdbcType=VARCHAR}, #{gsdy,jdbcType=VARCHAR}, #{gswz,jdbcType=VARCHAR}, #{kfdh,jdbcType=VARCHAR}, 
      #{gsyg,jdbcType=CLOB}, #{jyfw,jdbcType=CLOB}, #{zyyw,jdbcType=CLOB}, #{djrq,jdbcType=VARCHAR}, 
      #{yyzz,jdbcType=VARCHAR}, #{gsdj,jdbcType=VARCHAR}, #{dxdj,jdbcType=VARCHAR}, #{plwz,jdbcType=VARCHAR}, 
      #{plbz,jdbcType=VARCHAR}, #{gszt,jdbcType=VARCHAR}, #{jgjb,jdbcType=VARCHAR}, #{gjdm,jdbcType=VARCHAR}, 
      #{dqdm,jdbcType=VARCHAR}, #{ggrq,jdbcType=VARCHAR}, #{recstat,jdbcType=VARCHAR}, 
      #{credt,jdbcType=VARCHAR}, #{moddt,jdbcType=VARCHAR}, #{stimestamp,jdbcType=TIMESTAMP}, 
      #{jgpy,jdbcType=VARCHAR}, #{jqpy,jdbcType=VARCHAR}, #{hjlb,jdbcType=VARCHAR}, #{dgds,jdbcType=CLOB}, 
      #{gdxs,jdbcType=VARCHAR}, #{sbal,jdbcType=CLOB}, #{gdgc,jdbcType=CLOB}, #{sfsmjjglr,jdbcType=VARCHAR}, 
      #{tzln,jdbcType=VARCHAR}, #{tzcl,jdbcType=VARCHAR}, #{mOptType,jdbcType=VARCHAR}, 
      #{zlid,jdbcType=DECIMAL}, #{checkflag,jdbcType=VARCHAR}, #{plqx,jdbcType=VARCHAR}, 
      #{sftjgs,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{zpdz,jdbcType=VARCHAR}, 
      #{sfsf,jdbcType=VARCHAR}, #{sfgqgs,jdbcType=VARCHAR})
  </insert>
</mapper>
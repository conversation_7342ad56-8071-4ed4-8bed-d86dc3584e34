package com.howbuy.crm.nt.jgxx.dao;

import com.howbuy.crm.nt.jgxx.domain.Jgxx;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/31 19:03
 * @since JDK 1.8
 */
@MapperScan
public interface JgxxMapper {
    int insert(Jgxx record);


    /**
     * 根据机构代码查询机构信息
     * @apiNote 特殊逻辑： 只查询 JLDM = '007999' 或者 SFGQGS = '1' 的数据
     * @param jgdmList NOT NULL
     * @return
     */
    List<Jgxx>  selectByJgdmList(@Param("jgdmList") List<String> jgdmList);



}
package com.howbuy.crm.nt.yxslive.service;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.yxslive.dao.CustYxsLiveMapper;
import com.howbuy.crm.nt.yxslive.po.CustYxsLivePo;
import com.howbuy.crm.nt.yxslive.vo.CustYxsLiveVo;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *客户 直播访问数据 统计 service
 */
@Service("custYxsLiveStaticService")
@Slf4j
public class CustYxsLiveStaticServiceImpl implements  CustYxsLiveStaticService {

    @Autowired
    private CustYxsLiveMapper custYxsLiveMapper;


    @Override
    public int batchInsertList(List<CustYxsLivePo> list) {
        List<List<CustYxsLivePo>> lists = Lists.partition(list, 200);
        return lists.stream().mapToInt(custYxsLiveMapper::batchInsertList).sum();
    }



    /**
     * 根据vo查询 分页对象列表
     * @param vo
     * @return
     */
    @Override
    public PageResult<CustYxsLivePo> selectPageByVo(CustYxsLiveVo vo){
        CommPageBean pageBean = vo.getPageBeanByVo();
        List<CustYxsLivePo> list = custYxsLiveMapper.selectByPage(vo, pageBean);
        PageResult<CustYxsLivePo> pageResult = new PageResult<>();
        pageResult.setTotal(pageBean.getTotalNum());
        pageResult.setRows(list);
        return pageResult;
    }


    /**
     * 根据vo查询 对象列表
     * @param vo
     * @return
     */
    @Override
    public List<CustYxsLivePo> selectListByVo(CustYxsLiveVo vo){
        return custYxsLiveMapper.selectListByVo(vo);
    }



}

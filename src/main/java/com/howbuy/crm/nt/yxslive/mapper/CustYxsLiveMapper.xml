<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.yxslive.dao.CustYxsLiveMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.yxslive.po.CustYxsLivePo">
		<result column="COUNT_DATE" property="countDate" jdbcType="VARCHAR"/>
		<result column="ID" property="id" jdbcType="VARCHAR"/>
		<result column="TITLE" property="title" jdbcType="VARCHAR"/>
		<result column="HBONE_NO" property="hboneNo" jdbcType="VARCHAR"/>
		<result column="CONSCUSTNO" property="conscustNo" jdbcType="VARCHAR"/>
		<result column="CUSTNAME" property="custName" jdbcType="VARCHAR"/>
		<result column="CONSCODE" property="consCode" jdbcType="VARCHAR"/>
		<result column="CONSNAME" property="consName" jdbcType="VARCHAR"/>
		<result column="U1_NAME" property="u1Name" jdbcType="VARCHAR"/>
		<result column="U2_NAME" property="u2Name" jdbcType="VARCHAR"/>
		<result column="U3_NAME" property="u3Name" jdbcType="VARCHAR"/>
		<result column="SHARE_FLAG" property="shareFlag" jdbcType="VARCHAR"/>
		<result column="CUST_FLAG" property="custFlag" jdbcType="VARCHAR"/>
		<result column="TRADE_FLAG" property="tradeFlag" jdbcType="VARCHAR"/>
		<result column="SOURCE" property="source" jdbcType="VARCHAR"/>
		<result column="TIMES" property="times" jdbcType="DECIMAL"/>
		<result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
		<result column="UPDATE_DATE" property="updateDate" jdbcType="TIMESTAMP"/>
    </resultMap>

	<sql id="Base_Column_List">
		COUNT_DATE,ID,TITLE,HBONE_NO,CONSCUSTNO,CUSTNAME,CONSCODE,CONSNAME,
		U1_NAME,U2_NAME,U3_NAME,SHARE_FLAG,CUST_FLAG,TRADE_FLAG,SOURCE,TIMES,
		CREATE_DATE,UPDATE_DATE
	</sql>

	<update id="batchInsertList" parameterType="list">
		MERGE INTO CM_CUST_YXS_LIVE T
		USING
		(
		<foreach collection="list" item="item" index="index" separator="union" >
			SELECT
			#{item.countDate,jdbcType=VARCHAR} COUNT_DATE,
			#{item.id,jdbcType=VARCHAR} ID,
			#{item.title,jdbcType=VARCHAR} TITLE,
			#{item.hboneNo,jdbcType=VARCHAR} HBONE_NO,
			#{item.conscustNo,jdbcType=VARCHAR} CONSCUSTNO,
			#{item.custName,jdbcType=VARCHAR} CUSTNAME,
			#{item.consCode,jdbcType=VARCHAR} CONSCODE,
			#{item.consName,jdbcType=VARCHAR} CONSNAME,
			#{item.u1Name,jdbcType=VARCHAR} U1_NAME,
			#{item.u2Name,jdbcType=VARCHAR} U2_NAME,
			#{item.u3Name,jdbcType=VARCHAR} U3_NAME,
			#{item.shareFlag,jdbcType=VARCHAR} SHARE_FLAG,
			#{item.custFlag,jdbcType=VARCHAR} CUST_FLAG,
			#{item.tradeFlag,jdbcType=VARCHAR} TRADE_FLAG,
			#{item.source,jdbcType=VARCHAR}  SOURCE,
			#{item.times,jdbcType=DECIMAL} TIMES
			FROM DUAL
		</foreach>
		) T1
		ON (T1.COUNT_DATE = T.COUNT_DATE
		AND T1.ID = T.ID
		AND T1.HBONE_NO = T.HBONE_NO
		AND T1.SHARE_FLAG = T.SHARE_FLAG
		AND T1.SOURCE=T.SOURCE)
		WHEN MATCHED THEN
		UPDATE SET
		T.TITLE=T.TITLE,
		T.CONSCUSTNO=T1.CONSCUSTNO,
		T.CUSTNAME=T1.CUSTNAME,
		T.CONSCODE=T1.CONSCODE,
		T.CONSNAME=T1.CONSNAME,
		T.U1_NAME=T1.U1_NAME,
		T.U2_NAME=T1.U2_NAME,
		T.U3_NAME=T1.U3_NAME,
		T.CUST_FLAG=T1.CUST_FLAG,
		T.TRADE_FLAG=T1.TRADE_FLAG,
		T.TIMES=T1.TIMES,
		T.UPDATE_DATE=SYSTIMESTAMP
		WHEN NOT MATCHED THEN
		INSERT (<include refid="Base_Column_List" />)
		VALUES(
		T1.COUNT_DATE,T1.ID,T1.TITLE,T1.HBONE_NO,T1.CONSCUSTNO,T1.CUSTNAME,
		T1.CONSCODE,T1.CONSNAME,T1.U1_NAME,T1.U2_NAME,T1.U3_NAME,T1.SHARE_FLAG,
		T1.CUST_FLAG,T1.TRADE_FLAG,T1.SOURCE,T1.TIMES,
		SYSTIMESTAMP,SYSTIMESTAMP
		)
	</update>
    <select id="selectByPage" resultMap="BaseResultMap" parameterType="Map" >
         <include refid="selectList"></include>
	</select>


	<select id="selectListByVo" resultMap="BaseResultMap" parameterType="Map" >
		<include refid="selectList"></include>
	</select>

	<sql id="selectList">
		SELECT
		T.COUNT_DATE,T.ID,T.TITLE,T.HBONE_NO,T.CONSCUSTNO,T.CUSTNAME,T.CONSCODE,T.CONSNAME,
		T.U1_NAME,T.U2_NAME,T.U3_NAME,T.SHARE_FLAG,T.CUST_FLAG,T.TRADE_FLAG,T.SOURCE,T.TIMES,
		T.CREATE_DATE,T.UPDATE_DATE
		FROM CM_CUST_YXS_LIVE T
		LEFT JOIN cm_consultant con	on T.conscode = con.conscode
		<where>
			<if test="param.countDate != null and param.countDate != '' ">
				AND T.COUNT_DATE = #{param.countDate,jdbcType=VARCHAR}
			</if>
			<if test="param.countBeginDate != null and param.countBeginDate != '' ">
				AND T.COUNT_DATE <![CDATA[ >= ]]> #{param.countBeginDate,jdbcType=VARCHAR}
			</if>
			<if test="param.countEndDate != null and param.countEndDate != '' ">
				AND T.COUNT_DATE <![CDATA[ <= ]]> #{param.countEndDate,jdbcType=VARCHAR}
			</if>
			<if test="param.id != null and param.id != '' ">
				AND T.ID = #{param.id,jdbcType=VARCHAR}
			</if>
			<if test="param.title != null and param.title != '' ">
				AND T.TITLE = #{param.title,jdbcType=VARCHAR}
			</if>
			<if test="param.titleFuzzy != null and param.titleFuzzy != '' ">
				AND T.TITLE  like '%'||#{param.titleFuzzy ,jdbcType=VARCHAR}||'%'
			</if>

			<if test="param.hboneNo != null and param.hboneNo != '' ">
				AND T.HBONE_NO = #{param.hboneNo,jdbcType=VARCHAR}
			</if>
			<if test="param.conscustNo !=null and param.conscustNo != '' ">
				AND T.CONSCUSTNO = #{param.conscustNo,jdbcType=VARCHAR}
			</if>
			<if test="param.custName != null and param.custName != '' ">
				AND T.CUSTNAME = #{param.custName,jdbcType=VARCHAR}
			</if>
			<if test="param.consCode != null and param.consCode != '' ">
				AND T.CONSCODE = #{param.consCode,jdbcType=VARCHAR}
			</if>
			<if test="param.consName != null and param.consName != '' ">
				AND T.CONSNAME = #{param.consName,jdbcType=VARCHAR}
			</if>
			<if test="param.u1Name != null and param.u1Name != '' ">
				AND T.U1_NAME = #{param.u1Name,jdbcType=VARCHAR}
			</if>
			<if test="param.u2Name != null and param.u2Name != '' ">
				AND T.U2_NAME = #{param.u2Name,jdbcType=VARCHAR}
			</if>
			<if test="param.u3Name != null and param.u3Name != '' ">
				AND T.U3_NAME = #{param.u3Name,jdbcType=VARCHAR}
			</if>
			<if test="param.shareFlag != null and param.shareFlag != '' ">
				AND T.SHARE_FLAG = #{param.shareFlag,jdbcType=VARCHAR}
			</if>
			<if test="param.custFlag != null and param.custFlag != '' ">
				AND T.CUST_FLAG = #{param.custFlag,jdbcType=VARCHAR}
			</if>
			<if test="param.tradeFlag != null and param.tradeFlag != '' ">
				AND T.TRADE_FLAG = #{param.tradeFlag,jdbcType=VARCHAR}
			</if>
			<if test="param.source != null and param.source != '' ">
				AND T.SOURCE = #{param.source,jdbcType=VARCHAR}
			</if>

			<include refid="authCondition"/>
		</where>
		ORDER BY T.COUNT_DATE DESC,T.ID DESC, con.OUTLETCODE ASC,T.CONSCUSTNO
	</sql>


	<sql id="authCondition">
		<if test="param.authConsCode != null"> AND con.CONSCODE = #{param.authConsCode} </if>
		<if test="param.authOthertearm != null"> AND con.OUTLETCODE = #{param.authOthertearm} and con.TEAMCODE is null </if>
		<if test="param.authTeamcode != null"> AND con.TEAMCODE = #{param.authTeamcode}	</if>
		<if test="param.notCanSeeConsNullFlag != null"> AND con.CONSCODE is not null</if>
		<if test="param.authOutletcodeList != null and param.authOutletcodeList.size() > 0">
			AND ( con.OUTLETCODE in
			<foreach item="item" index="index" collection="param.authOutletcodeList" separator=" OR con.OUTLETCODE in ">
				<foreach collection="item" item="mId" open="(" separator="," close=")">
					#{mId}
				</foreach>
			</foreach>
			)
		</if>
	</sql>

</mapper>
package com.howbuy.crm.nt.yxslive.dao;

import com.howbuy.crm.nt.yxslive.po.CustYxsLivePo;
import com.howbuy.crm.nt.yxslive.vo.CustYxsLiveVo;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
@MapperScan
public interface CustYxsLiveMapper {


	/**
	 * 批量插入 list
	 * @param dataList
	 * @return
	 */
	int batchInsertList(List<CustYxsLivePo> dataList);


	/**
	 * 根据vo查询
	 * @param vo
	 * @param pageBean
	 * @return
	 */
	List<CustYxsLivePo> selectByPage(@Param("param") CustYxsLiveVo vo, @Param("page") CommPageBean pageBean);


	/**
	 * 根据vo查询
	 * @param vo
	 * @return
	 */
	List<CustYxsLivePo> selectListByVo(@Param("param") CustYxsLiveVo vo);





}

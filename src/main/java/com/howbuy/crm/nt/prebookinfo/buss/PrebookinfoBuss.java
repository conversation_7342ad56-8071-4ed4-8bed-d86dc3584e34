package com.howbuy.crm.nt.prebookinfo.buss;

import com.howbuy.crm.nt.prebookinfo.service.CmprebookinfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:(批量修改打款确认到账的预约的客户资源 实现方法)
 * @author: fanchao.xu
 * @since JDK 1.8
 */
@Component
public class PrebookinfoBuss {
	private static final Logger log = LoggerFactory.getLogger(PrebookinfoBuss.class);

	@Autowired
	private CmprebookinfoService cmprebookinfoService;

	/**
	 * @description:(更新确认到账客户资源)
	 * @author: fanchao.xu
	 * @date: 2023/5/4 17:19
	 * @since JDK 1.8
	 */
	public void batchUpdatePrebookResType() {
		try {
			log.info("-----PrebookinfoBuss start -----");
			cmprebookinfoService.updatePrebookProductInfoBatch();
			log.info("-----PrebookinfoBuss end-----");
		} catch (Exception e) {
			log.info("error in PrebookinfoBuss", e);
		}
	}
}

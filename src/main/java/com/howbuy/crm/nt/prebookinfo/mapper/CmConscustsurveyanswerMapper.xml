<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.nt.prebookinfo.dao.CmPrebookinfoDao">
	<select id="selectCmPrebookInfodto"  parameterType="Map" resultType="com.howbuy.crm.nt.prebookinfo.dto.CmPrebookInfodto" useCache="false">
		SELECT T.ID, T1.RESTYPE
		FROM CM_PREBOOKPRODUCTINFO T
				 LEFT JOIN CM_CONSCUST T1
						   ON T.CONSCUSTNO = T1.CONSCUSTNO
		WHERE T.RESTYPE IS NULL
		  AND T.PREBOOKSTATE = '2'
		  AND T.PAYSTATE = '3'
		  AND T.TRADE_TYPE IN ('1','2')
		  AND T1.RESTYPE IS NOT NULL
	</select>


	<update id="updatePrebookProductInfoBatch" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" separator=";"  open="begin" close=";end;">
			UPDATE CM_PREBOOKPRODUCTINFO CP
			SET CP.RESTYPE = #{item.restype,jdbcType=VARCHAR}
			WHERE CP.ID = #{item.id,jdbcType=VARCHAR}
		</foreach>
	</update>

	<!-- 查询 -->
	<select id="selectCmPrebookLegaldocList" parameterType="Map" resultType="com.howbuy.crm.nt.prebookinfo.dto.CmPrebookLegalDocDto" useCache="false">
		SELECT t.ID,T.PCODE,t.CONSCUSTNO,t.CONSCUSTNAME,t.ISDXFLAG,t.EXPECTTRADEDT,t1.SUBMITAPPFLAG, t.PRE_DIS_CODE as preDisCode
		FROM CM_PREBOOKPRODUCTINFO T
		 	LEFT JOIN CM_ZT_ORDERINFO T1 on t.id = t1.APPOINTMENTDEALNO
			LEFT join jjxx1 T2 on T.PCODE = T2.jjdm
		WHERE T.RECSTAT = '0'
		  AND T.sfxg != '1'
		  AND T.LEGALDOC_STAT = '1'
		  AND T.TRADE_TYPE = '1'
		  AND T.PREBOOKSTATE != '4'
	</select>



</mapper>
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.prebookinfo.service;

import com.howbuy.crm.nt.prebookinfo.dao.CmPrebookinfoDao;
import com.howbuy.crm.nt.prebookinfo.dto.CmPrebookInfodto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (批量修改打款确认到账的预约的客户资源)
 * @date 2023/4/25 12:04
 * @since JDK 1.8
 */
@Service("cmprebookinfoService")
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmpreBookinfoServiceImpl implements CmprebookinfoService {

    @Autowired
    private CmPrebookinfoDao cmPrebookinfoDao;

    @Override
    public void updatePrebookProductInfoBatch() {
        List<CmPrebookInfodto> cmPrebookInfodtos = cmPrebookinfoDao.selectCmPrebookInfodto();
        if (CollectionUtils.isNotEmpty(cmPrebookInfodtos)) {
            cmPrebookinfoDao.updatePrebookProductInfoBatch(cmPrebookInfodtos);
        }
    }
}
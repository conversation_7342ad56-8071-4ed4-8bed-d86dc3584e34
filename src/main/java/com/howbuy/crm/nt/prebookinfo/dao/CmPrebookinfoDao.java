package com.howbuy.crm.nt.prebookinfo.dao;

import java.util.List;

import com.howbuy.crm.nt.prebookinfo.dto.CmPrebookInfodto;
import com.howbuy.crm.nt.prebookinfo.dto.CmPrebookLegalDocDto;
import org.mybatis.spring.annotation.MapperScan;

@MapperScan
public interface CmPrebookinfoDao {

	/**
	 * 查询打款到账的预约，并且该预约的客户资源字段为空的，客户本身的资源不为空
	 * @return
	 */
	List<CmPrebookInfodto> selectCmPrebookInfodto();

	/**
	 * 批量修改打款确认到账的预约的客户资源
	 * @param cmPrebookInfodtos
	 */
	void updatePrebookProductInfoBatch(List<CmPrebookInfodto> cmPrebookInfodtos);

	/**
	 * 批量修改打款确认到账的预约的客户资源
	 */
	List<CmPrebookLegalDocDto> selectCmPrebookLegaldocList();
}

/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.joincust.dao;

import com.howbuy.crm.nt.hbjoincust.dto.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/4/19 23:12
 * @since JDK 1.8
 */
public interface CmHbJoiCustMapper {

    /**
     * 存储过程改造  查询一账通客户信息表
     *
     * @return
     */
    List<HboneCustInfoDto> listHboneCustInfo();

    /**
     * 存储过程改造  查询相关的code值
     */
    CodeDto selectLevelAndSourcenoAndConscode(Map<String, String> params);

    /**
     * 存储过程改造  生成客户号方法
     *
     * @return
     */
    String getcustno();

    /**
     * 存储过程改造 批量新增投顾客户信息表数据
     *
     * @param cmConscustDtoList
     */
    void batchInsertCmConscust(List<CmConscustDto> cmConscustDtoList);

    /**
     * 存储过程改造 投顾客户密文表
     *
     * @param cmConscustCipherDtoList
     */
    void batchInsertCmConscustCipher(List<CmConscustCipherDto> cmConscustCipherDtoList);

    /**
     * @return
     * @description:(获取appserialno)
     * @author: xufanchao
     * @date: 2023/9/14 22:32
     * @since JDK 1.8
     */
    Long getAppserialNo();

    /**
     * 存储过程改造 批量新增投顾客户信息流水表量
     */
    void insertCmConscustHis(CmConscustHisDto cmConscustHisDto);

    /**
     * @description:(新增投顾客户cipher历史记录表)
     * @param cmConscustHisDto
     * @return void
     * @author: xufanchao
     * @date: 2023/9/14 22:39
     * @since JDK 1.8
     */
    void insertCmConscustHisCipher(CmConscustHisDto cmConscustHisDto);

    /**
     * 存储过程改造 批量新增客户投顾关系表
     */
    void batchInsertCmcustconstant(CmCustConstantDto cmCustConstantDto);
}
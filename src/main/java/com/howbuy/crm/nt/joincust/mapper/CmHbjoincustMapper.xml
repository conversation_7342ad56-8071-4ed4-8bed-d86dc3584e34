<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.nt.joincust.dao.CmHbJoiCustMapper">



	<select id="listHboneCustInfo" resultType="com.howbuy.crm.nt.hbjoincust.dto.HboneCustInfoDto">
		SELECT
			TT.HBONE_NO as hboneNo,
			TT.USER_TYPE as userType,
			TT.CUST_NAME as custName,
			TT.USER_NAME as userName,
			TT.ID_TYPE as idType,
			TT.MOBILE_VERIFY_STATUS as mobileVerifyStatus,
			TT.UPDATE_DATE as updateDate,
			TT.IP,
			TT.REG_OUTLET_CODE as regOutletCode,
			TT.AUTH_OUTLET_CODE as authOutletCode,
			TT.REG_DATE as regDate,
			TT.AUTH_DATE as authDate,
			TT.DEAL_STATUS as dealStatus,
			TT.DEAL_DATE as dealDate,
			TT.ID_NO_DIGEST as idNoDigest,
			TT.ID_NO_MASK as idNoMask,
			TT.ID_NO_CIPHER as idNoCipher,
			TT.MOBILE_DIGEST as mobileDigest,
			TT.MOBILE_MASK as mobileMask,
			TT.MOBILE_CIPHER as mobileCipher,
			BB.REG_OUTLET_CODE AS outletcode
		FROM HBONE_CUSTINFO TT,
			 (SELECT A.HBONE_NO, A.REG_OUTLET_CODE
			  FROM HBONE_OPEN_SOURCE A
			  WHERE A.HBONE_NO NOT IN
					(SELECT T.HBONE_NO
					 FROM CM_CONSCUST T
					 WHERE T.HBONE_NO IS NOT NULL)
				AND A.HBONE_NO NOT IN
					(SELECT DISTINCT (CA.HBONE_NO)
					 FROM HBONE_CUST_ABNORMAL CA)) BB
		WHERE TT.HBONE_NO = BB.HBONE_NO
	</select>


	<select id="selectLevelAndSourcenoAndConscode" parameterType="map"
			resultType="com.howbuy.crm.nt.hbjoincust.dto.CodeDto">
		SELECT TT.FIRST_LEVEL_CODE  as firstlevelcode,
		TT.SOURCENO          as sourceno,
		DECODE(TT.FIRST_LEVEL_CODE,
		'C',
		'CXGN_CSLS',
		'K',
		'ZSJJN_CSLS',
		'L',
		'HZN_CSLS',
		'H',
		'HZN_CSLS',
		'F',
		'HZN_CSLS',
		'WZQTN_CSLS') AS CONSCODE
		FROM (SELECT T.SOURCENO AS SOURCEID,
		T.FIRST_LEVEL_CODE,
		T.SOURCENO
		FROM CM_SOURCEINFO_NEW T
		WHERE T.REC_STAT = '1'
		AND T.IS_MERGE = '1'
		AND T.SOURCENO = #{outletcode,jdbcType=VARCHAR}
		UNION
		SELECT T2.SOURCENO AS SOURCEID,
		T.FIRST_LEVEL_CODE,
		T.SOURCENO
		FROM CM_SOURCEINFO_NEW T
		JOIN CM_SOURCEINFO T2
		ON T2.NEWSOURCENO = T.SOURCENO
		WHERE T.REC_STAT = '1'
		AND T.IS_MERGE = '1'
		AND T2.SOURCESTATE = '1'
		AND T2.SOURCENO = #{outletcode,jdbcType=VARCHAR}) TT
	</select>


	<select id="getcustno" resultType="string">
		SELECT to_char(seq_custno.NEXTVAL, 'FM09999999')
		FROM dual
	</select>

	<insert id="batchInsertCmConscust" parameterType="java.util.List">
		INSERT INTO CM_CONSCUST (
		CONSCUSTNO,
		CONSCUSTSTATUS,
		INVSTTYPE,
		IDNO_DIGEST,
		IDNO_MASK,
		IDTYPE,
		MOBILE_DIGEST,
		MOBILE_MASK,
		CUSTNAME,
		HBONE_NO,
		NEWSOURCENO,
		REGDT
		)
		select a.* FROM (
		<foreach collection="list" item="item" index="index" separator="UNION ALL" open="" close="">
			select
			#{item.conscustno},
			'0',
			#{item.invsttype},
			#{item.idnodigest,jdbcType=VARCHAR},
			#{item.idnomask,jdbcType=VARCHAR},
			#{item.idtype,jdbcType=VARCHAR},
			#{item.mobiledigest,jdbcType=VARCHAR},
			#{item.mobilemask,jdbcType=VARCHAR},
			#{item.custname,jdbcType=VARCHAR},
			#{item.hboneno,jdbcType=VARCHAR},
			#{item.newsoureceno,jdbcType=VARCHAR},
			to_char(#{item.regdt}, 'yyyyMMdd')
			from dual
		</foreach>)a
	</insert>


	<insert id="batchInsertCmConscustCipher" parameterType="list">
		insert into CM_CONSCUST_CIPHER (
		<trim suffix="" suffixOverrides=",">
			CONSCUSTNO, IDNO_CIPHER, MOBILE_CIPHER,
		</trim>
		)
		select a.* from (
		<foreach collection="list" item="item" index="index" separator="UNION ALL" open="" close="">
			select
			#{item.conscustno,jdbcType=VARCHAR},
			#{item.idnocipher,jdbcType=VARCHAR},
			#{item.mobilecipher,jdbcType=VARCHAR}
			from dual
		</foreach>) a
	</insert>

	<select id="getAppserialNo" resultType="long">
		SELECT SEQ_CUSTREC.NEXTVAL AS appSerialNo FROM DUAL
	</select>

	<insert id="insertCmConscustHis" parameterType="com.howbuy.crm.nt.hbjoincust.dto.CmConscustHisDto">
			INSERT INTO CM_CONSCUSTHIS (
			APPSERIALNO,
			CONSCUSTNO,
			CONSCUSTSTATUS,
			INVSTTYPE,
			IDNO_DIGEST,
			IDNO_MASK,
			IDTYPE,
			MOBILE_DIGEST,
			MOBILE_MASK,
			CUSTNAME,
			HBONE_NO,
			NEWSOURCENO,
			CREATOR
			)
			VALUES (
			#{appSerialNo,jdbcType=BIGINT},
			#{consCustNo,jdbcType=VARCHAR},
			'0',
			#{userType,jdbcType=VARCHAR},
			#{idNoDigest,jdbcType=VARCHAR},
			#{idNoMask,jdbcType=VARCHAR},
			#{idType,jdbcType=VARCHAR},
			#{mobileDigest,jdbcType=VARCHAR},
			#{mobileMask,jdbcType=VARCHAR},
			#{custName,jdbcType=VARCHAR},
			#{hboneNo,jdbcType=VARCHAR},
			#{sourceNo,jdbcType=VARCHAR},
			'hbmq-sys'
			)
	</insert>

	<insert id="insertCmConscustHisCipher" parameterType="com.howbuy.crm.nt.hbjoincust.dto.CmConscustHisDto">
		INSERT INTO CM_CONSCUSTHIS_CIPHER (
		APPSERIALNO,
		CONSCUSTNO,
		IDNO_CIPHER,
		MOBILE_CIPHER
		)
		VALUES (
		#{appSerialNo,jdbcType=BIGINT},
		#{consCustNo,jdbcType=VARCHAR},
		#{idNoCipher,jdbcType=VARCHAR},
		#{mobileCipher,jdbcType=VARCHAR}
		)
	</insert>


	<insert id="batchInsertCmcustconstant" parameterType="com.howbuy.crm.nt.hbjoincust.dto.CmCustConstantDto">
		INSERT INTO CM_CUSTCONSTANT
			(CUSTNO, CONSCODE, STARTDT, RECSTAT, CREATOR, CREDT, BINDDATE)
		VALUES
			(#{custno,jdbcType=VARCHAR},
			 #{conscode,jdbcType=VARCHAR},
			 TO_CHAR(SYSDATE, 'yyyyMMdd'),
			 '1',
			 'hbmq-sys',
			 TO_CHAR(SYSDATE, 'yyyyMMdd'),
			 SYSDATE)
	</insert>


</mapper>
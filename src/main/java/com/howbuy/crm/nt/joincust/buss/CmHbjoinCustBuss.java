package com.howbuy.crm.nt.joincust.buss;


import com.howbuy.crm.nt.hbjoincust.dto.*;
import com.howbuy.crm.nt.hbjoincust.service.HbJoniCustService;

import crm.howbuy.base.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Component
@Transactional(rollbackFor = {Exception.class})
public class CmHbjoinCustBuss {
    private static final Logger log = LoggerFactory.getLogger(CmHbjoinCustBuss.class);

    @Autowired
    private HbJoniCustService hbJoniCustService;

    private static String midcustno = "";



    @Transactional(rollbackFor = Exception.class)
    public void hbJoinCostBuss() {
        log.info("-----一账通对账调用开始-----");
        List<HboneCustInfoDto> hboneCustInfoDtoList = hbJoniCustService.listHboneCustInfo();

        List<CmConscustDto> cmConscustDtoList = new ArrayList<>();
        List<CmConscustCipherDto> cmConscustCipherDtoList = new ArrayList<>();
        List<CmConscustHisDto> cmConscustHisDtoList = new ArrayList<>();
        List<CmCustConstantDto> cmCustConstantDtoList = new ArrayList<>();

        String custno = hbJoniCustService.getcustno();
        midcustno = custno;
        hboneCustInfoDtoList.forEach(it -> {
                    String conscode = "";
                    if (null != it.getOutletCode() && "RO1903W01".equals(it.getOutletCode())) {
                        conscode = "gatkh";
                    } else {
                        if (null != it.getUserType() && "1".equals(it.getUserType())) {
                            if (("GSH000001".equals(it.getOutletCode()) || "WEB000001".equals(it.getOutletCode()) || "OTCWEB001".equals(it.getOutletCode()))) {
                                it.setOutletCode("RO1509P05");
                            }
                        } else {
                            conscode = "JGKH_ORG";
                        }
                    }

                    String custno1 = getCustno(midcustno);
                    Map<String, String> params = new HashMap<>(1);
                    params.put("outletcode", it.getOutletCode());
                    CodeDto codeDto = hbJoniCustService.selectLevelAndSourcenoAndConscode(params);
                    if (null != codeDto) {
                        cmConscustDtoList.add(getCmConsucstDto(it, custno1, codeDto.getSourceno()));
                        cmConscustHisDtoList.add(getCmConscustHisDto(it, custno1, codeDto));
                        cmCustConstantDtoList.add(getCmCustConstantDto(custno1, codeDto, conscode));
                        cmConscustCipherDtoList.add(getCmscustCipherDto(it, custno1));
                    }

                }
        );
        if (!cmConscustHisDtoList.isEmpty()) {
            cmConscustHisDtoList.forEach(it -> {
                Long appserialNo = hbJoniCustService.getAppserialNo();
                it.setAppSerialNo(appserialNo);
                hbJoniCustService.insertCmConscustHis(it);
                hbJoniCustService.insertCmConscustHisCipher(it);
            });
        }
        if (!cmConscustDtoList.isEmpty()) {
            hbJoniCustService.batchInsertCmConscust(cmConscustDtoList);
        }
        if (!cmConscustCipherDtoList.isEmpty()) {
            hbJoniCustService.batchInsertCmConscustCipher(cmConscustCipherDtoList);
        }
        if (!cmCustConstantDtoList.isEmpty()) {
            cmCustConstantDtoList.forEach(it -> hbJoniCustService.batchInsertCmcustconstant(it));
        }
    }

    /**
     * 数据对象转换
     *
     */
    private CmConscustCipherDto getCmscustCipherDto(HboneCustInfoDto it, String conscustno) {
        CmConscustCipherDto cmConscustCipherDto = new CmConscustCipherDto();
        cmConscustCipherDto.setConscustno(conscustno);
        cmConscustCipherDto.setIdnocipher(it.getIdNoCipher());
        cmConscustCipherDto.setMobilecipher(it.getMobileCipher());

        return cmConscustCipherDto;
    }

    private CmCustConstantDto getCmCustConstantDto(String conscustno, CodeDto codeDto, String conscode) {
        CmCustConstantDto cmCustConstantDto = new CmCustConstantDto();
        cmCustConstantDto.setCustno(conscustno);
        if (null != codeDto.getConscode()) {
            cmCustConstantDto.setConscode(codeDto.getConscode());
        } else {
            cmCustConstantDto.setConscode(conscode);
        }
        return cmCustConstantDto;
    }

    /**
     * 数据类型转换
     */
    private CmConscustHisDto getCmConscustHisDto(HboneCustInfoDto it, String conscustno, CodeDto codeDto) {
        CmConscustHisDto hisDto = new CmConscustHisDto();
        hisDto.setConsCustNo(conscustno);
        hisDto.setIdType(it.getIdType());
        hisDto.setIdNoMask(it.getIdNoMask());
        hisDto.setIdType(it.getIdType());
        hisDto.setMobileDigest(it.getMobileDigest());
        hisDto.setMobileMask(it.getMobileMask());
        hisDto.setCustName(it.getCustName());
        hisDto.setHboneNo(it.getHboneNo());
        hisDto.setSourceNo(codeDto.getSourceno());
        hisDto.setIdNoCipher(it.getIdNoCipher());
        hisDto.setMobileCipher(it.getMobileCipher());
        return hisDto;
    }


    /**
     * 对象转换
     */
    private CmConscustDto getCmConsucstDto(HboneCustInfoDto it, String conscustno, String sourceno) {
        CmConscustDto cmConscustDto = new CmConscustDto();
        cmConscustDto.setConscustno(conscustno);
        cmConscustDto.setInvsttype(it.getUserType());
        cmConscustDto.setIdnodigest(it.getIdNoDigest());
        cmConscustDto.setIdnomask(it.getIdNoMask());
        cmConscustDto.setIdtype(it.getIdType());
        cmConscustDto.setMobiledigest(it.getMobileDigest());
        cmConscustDto.setMobilemask(it.getMobileMask());
        cmConscustDto.setCustname(it.getCustName());
        cmConscustDto.setHboneno(it.getHboneNo());
        cmConscustDto.setNewsoureceno(sourceno);
        cmConscustDto.setRegdt(it.getUpdateDate());
        return cmConscustDto;
    }

    /**
     * @return java.lang.String
     * @description:(生成客户号)
     * @author: xfc
     * @date: 2023/4/14 13:59
     * @since JDK 1.8
     */
    private String getCustno(String getcustno) {
        BigDecimal bigDecimal = new BigDecimal(getcustno);
        String no = bigDecimal.add(new BigDecimal(1)).toString();
        midcustno = no;
        StringBuilder s = new StringBuilder("1");
        int sum = 0;
        for (int i = 1; i <= 7; i++) {
            sum = sum + no.charAt(i - 1);
        }
        String sumStr = Integer.toString(sum);
        char lastStr = sumStr.charAt(sumStr.length() - 1);
        return s.append(no).append(lastStr).toString();
    }


}

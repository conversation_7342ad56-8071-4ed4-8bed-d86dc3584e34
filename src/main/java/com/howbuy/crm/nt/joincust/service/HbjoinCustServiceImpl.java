/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.joincust.service;

import com.howbuy.crm.nt.hbjoincust.dto.*;
import com.howbuy.crm.nt.hbjoincust.service.HbJoniCustService;
import com.howbuy.crm.nt.joincust.dao.CmHbJoiCustMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/4/19 23:15
 * @since JDK 1.8
 */
@Service("hbJoniCustService")
@Transactional(rollbackFor = Exception.class)
public class HbjoinCustServiceImpl implements HbJoniCustService {

    @Autowired
    private CmHbJoiCustMapper cmHbJoiCustMapper;

    @Override
    public List<HboneCustInfoDto> listHboneCustInfo() {
        return cmHbJoiCustMapper.listHboneCustInfo();
    }

    @Override
    public CodeDto selectLevelAndSourcenoAndConscode(Map<String, String> params) {
        return cmHbJoiCustMapper.selectLevelAndSourcenoAndConscode(params);
    }

    @Override
    public String getcustno() {
        return cmHbJoiCustMapper.getcustno();
    }

    @Override
    public void batchInsertCmConscust(List<CmConscustDto> cmConscustDtoList) {
        cmHbJoiCustMapper.batchInsertCmConscust(cmConscustDtoList);
    }

    @Override
    public void batchInsertCmConscustCipher(List<CmConscustCipherDto> cmConscustCipherDtoList) {
        cmHbJoiCustMapper.batchInsertCmConscustCipher(cmConscustCipherDtoList);
    }

    @Override
    public void insertCmConscustHis(CmConscustHisDto cmConscustHisDto) {
        cmHbJoiCustMapper.insertCmConscustHis(cmConscustHisDto);
    }

    @Override
    public Long getAppserialNo() {
        return cmHbJoiCustMapper.getAppserialNo();
    }

    @Override
    public void insertCmConscustHisCipher(CmConscustHisDto cmConscustHisDto) {
        cmHbJoiCustMapper.insertCmConscustHisCipher(cmConscustHisDto);
    }

    @Override
    public void batchInsertCmcustconstant(CmCustConstantDto cmCustConstantDtoList) {
        cmHbJoiCustMapper.batchInsertCmcustconstant(cmCustConstantDtoList);
    }
}
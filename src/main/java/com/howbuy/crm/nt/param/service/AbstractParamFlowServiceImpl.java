/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.param.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.common.utils.StringUtil;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.common.exception.BusinessException;
import com.howbuy.crm.nt.base.param.CmParamBaseInfo;
import com.howbuy.crm.nt.base.param.CmParamLogInfo;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.param.dao.CmParamLogInfoMapper;
import com.howbuy.crm.nt.param.dto.CmParamChangeInfo;
import com.howbuy.crm.nt.param.vo.ParamAuditVo;
import com.howbuy.crm.nt.param.vo.ParamDeleteVo;
import com.howbuy.crm.util.GenericsUtils;
import crm.howbuy.base.constants.CustSequenceConstants;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.enums.param.ParamAuditStatusEnum;
import crm.howbuy.base.enums.param.ParamChangeTypeEnum;
import crm.howbuy.base.enums.param.ParamTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @description: (参数流程)
 * <AUTHOR>
 * @date 2023/7/31 13:27
 * @since JDK 1.8
 */
@Transactional(rollbackFor = Exception.class)
@Slf4j
public abstract class AbstractParamFlowServiceImpl<T extends CmParamBaseInfo> {

     @Autowired
     private CommonDao commonMapper;
     @Autowired
     private CmParamChangeInfoService paramChangeInfoService;

     @Autowired
     private CmParamLogInfoMapper logInfoMapper;
    
     /**
      * @description:(获取参数类型)
      * @param
      * @return com.howbuy.crm.base.param.ParamTypeEnum
      * @author: haoran.zhang
      * @date: 2023/7/31 13:45
      * @since JDK 1.8
      */
     public abstract ParamTypeEnum getParamType();


     /**
      * @description:(根据配置id 获取 配置的对象)
      * @param configId
      * @return T
      * @author: haoran.zhang
      * @date: 2023/7/31 13:48
      * @since JDK 1.8
      */
     public abstract T getParamDtoByConfigId(String configId);


    /**
     * 更新 参数  状态相关
     * @param baseInfo
     * @param operator
     */
    public abstract int updateStatus(CmParamBaseInfo baseInfo, String operator);


     /**
      * @description:(新增操作的 前置校验)
      * @param insertDto
      * @return com.howbuy.crm.base.NtReturnMessageDto<java.lang.String>
      * @author: haoran.zhang
      * @date: 2023/9/18 15:01
      * @since JDK 1.8
      */
     public abstract NtReturnMessageDto<String> validateBeforeInsert(T insertDto);


     /**
      * @description:(修改更新操作的 前置校验)
      * @param updateDto
      * @return com.howbuy.crm.base.NtReturnMessageDto<java.lang.String>
      * @author: haoran.zhang
      * @date: 2023/9/19 10:19
      * @since JDK 1.8
      */
     public abstract NtReturnMessageDto<String> validateBeforeUpdate(T updateDto);


    /**
     * 生成参数配置id 下放给子类实现
     * @return
     */
    public abstract String generateParamId();



    /**
     * @description:(删除操作的 前置校验)
     * @param deleteDto  待删除对象
     * @return
     */
    public  NtReturnMessageDto<String> validateBeforeDelete(T deleteDto){
        return NtReturnMessageDto.ok();
    }


    /**
     * @description:(获取参数操作日志 记录)
     * @param configId
     * @return java.util.List<com.howbuy.crm.base.param.CmParamLogInfo>
     * @author: haoran.zhang
     * @date: 2023/9/18 11:09
     * @since JDK 1.8
     */
     public List<CmParamLogInfo> getoptLogList(String configId){
         return logInfoMapper.selectLogListByByParamId(configId,getParamType().getCode());
     }


    /**
     * 查找最新的审核拒绝的记录
     * @param paramId
     * @return
     */
    public String getLatestRejectAdvice(String paramId){
        CmParamChangeInfo  latestRejectDto=paramChangeInfoService.selectLatestUnAuditReject(paramId, getParamType().getCode());
        return latestRejectDto==null?"":latestRejectDto.getAuditRemark();
    }


     /**
      * @description:(获取 待审核对象 )
      * @param configId
      * @return T
      * @author: haoran.zhang
      * @date: 2023/9/11 13:16
      * @since JDK 1.8
      */
     public NtReturnMessageDto<T> getAuditData(String configId){
          T paramConfig=getParamDtoByConfigId(configId);
          if(configId==null){
               return NtReturnMessageDto.fail(String.format("配置Id:%s 不存在！",configId));
          }
          if(YesOrNoEnum.NO.getCode().equals(paramConfig.getRecStat())){
               return NtReturnMessageDto.fail(String.format("配置Id:%s 已删除！",configId));
          }
          if(!ParamAuditStatusEnum.AUDIT_WAIT.getCode().equals(paramConfig.getAuditStatus())){
               return NtReturnMessageDto.fail(String.format("配置Id:%s 审核状态不为：待审核！",configId));
          }

          CmParamChangeInfo changePo=paramChangeInfoService.selectUnAuditByParamId(configId,getParamType().getCode());
          if(changePo==null){
               return NtReturnMessageDto.fail(String.format("配置Id:%s 无待审核数据！",configId));
          }
           //映射 --> 更新原纪录
          Class<T> paramClass = getParamClass();
          T t = parseObject(changePo.getChangeValue(), paramClass);
          return NtReturnMessageDto.ok("",t);
     }

     /**
      * @description:(插入配置数据)
      * @param dto
      * @return int
      * @author: haoran.zhang
      * @date: 2023/7/31 17:12
      * @since JDK 1.8
      */
     public abstract NtReturnMessageDto<String> insertConfig(T dto, String operator);

     /**
      * 根据变动信息存储的属性 映射更新参数配置.
      * <notice>
      * 子类决定具体允许更新那些属性值
      * 改方法不需考虑recStat更新问题
      * </notice>
      *
      * @param cfgPo 变动信息对象
      * @return affected count
      */
     public abstract int updateParam(T cfgPo,String operator);

     /**
      * sequence 获取 param 的id
      * @return
      */
     public String getParamId() {
          return commonMapper.getSeqValue(CustSequenceConstants.SEQ_PARAM_ID);
     }


     /**
      * 是否只有审核权限 --> 审核状态
      * @param holdAuth
      * @return
      */
     private static ParamAuditStatusEnum getAuditStatus(Boolean holdAuth){
          return Boolean.TRUE.equals(holdAuth)?ParamAuditStatusEnum.AUDIT_PASS:ParamAuditStatusEnum.AUDIT_WAIT;
     }

     private Class<T> getParamClass() {
          return GenericsUtils.getSuperClassGenericType(this.getClass());
     }


     protected T parseObject(String changeValue, Class<T> clazz) {
          if (StringUtil.isNotEmpty(changeValue)) {
               return JSON.parseObject(changeValue, clazz);
          }
          return BeanUtils.instantiate(clazz);
     }

     /**
      * 映射变动信息
      *
      * @param changePo 变动信息表的Sid
      */
     public void reflectChange(CmParamChangeInfo changePo,String operator) {
          String paramId = changePo.getParamId();
          ParamChangeTypeEnum changeTypeEnum=ParamChangeTypeEnum.getEnum(changePo.getChangeType());
          log.info("映射参数配置，配置类型：{}，配置ID：{}，变动类型：{}，变动属性[更新操作]：{}，操作人：{}",
                  getParamType().getDescription(),paramId,changeTypeEnum.getDescription(),changePo.getChangeValue(),operator);
          switch (changeTypeEnum){
              case UPDATE:
                  //映射 --> 更新原纪录
                  Class<T> paramClass = getParamClass();
                  T t = parseObject(changePo.getChangeValue(), paramClass);
                  //业务数据更新
                  updateParam(t,operator);
                  //更新原记录： 审核状态=已审核通过
                  updateAudtiStatus(paramId,ParamAuditStatusEnum.AUDIT_PASS,operator);
                  return;
              case DELETE:
                  //删除： 只更新 有效状态=0-删除
                  updateRecState(paramId,YesOrNoEnum.NO,operator);
                  return;
              case INSERT:
                  //新增： 只更新 审核状态=已审核通过
                  updateAudtiStatus(paramId,ParamAuditStatusEnum.AUDIT_PASS,operator);
                  return;
              default:
                  throw new BusinessException(String.format("未知变动类型：changeType: %s " , changePo.getChangeType()));
          }
     }


       /**
      * 更新记录 有效状态
      * @param paramId
      * @param recStateEnum
      */
     public void updateRecState(String paramId, YesOrNoEnum recStateEnum,String operator){
          Class<T> paramClass = getParamClass();
          T t = BeanUtils.instantiate(paramClass);
          t.setId(paramId);
          t.setRecStat(recStateEnum.getCode());
          int updateCount=updateStatus(t,operator);
          log.info("更新参数配置-[删除]状态，配置类型：{}，配置ID：{}，更新REC_STAT ：{}，操作人：{},更新条数：{}",
                  getParamType().getDescription(),paramId,recStateEnum,operator,updateCount);

     }



     /**
      * 更新记录： 审核状态
      * @param paramId
      * @param auditStatusEnum
      */
     public void updateAudtiStatus(String paramId,ParamAuditStatusEnum auditStatusEnum,String operator){
          Class<T> paramClass = getParamClass();
          T t = BeanUtils.instantiate(paramClass);
          t.setId(paramId);
          t.setAuditStatus(auditStatusEnum.getCode());
         int updateCount=updateStatus(t,operator);
         log.info("更新参数配置-[审核]状态，配置类型：{}，配置ID：{}，更新AUDIT_STAT ：{}，操作人：{},更新条数：{}",
                 getParamType().getDescription(),paramId,auditStatusEnum.getDescription(),operator,updateCount);
     }

     /**
      * @description:(插入日志  操作时的 审核状态)
      * @param logInfo
      * @param operator
      * @param auditStatusEnum
      * @return void
      * @author: haoran.zhang
      * @date: 2023/9/19 19:59
      * @since JDK 1.8
      */
     private void insertLog(CmParamLogInfo logInfo,String operator,ParamAuditStatusEnum auditStatusEnum){
          logInfo.setId(getParamId());
          logInfo.setCreator(operator);
          logInfo.setAuditStatus(auditStatusEnum.getCode());
          logInfoMapper.insert(logInfo);
     }


     /**
      * 新增流程
      * @param dto 参数对象
      * @param operator  操作人
      * @param holdAuth 是否持有审核权限
      * @return
      */
     public NtReturnMessageDto<String> insertFlow(T dto, String operator, Boolean holdAuth) {
         //新增 前置校验
         NtReturnMessageDto<String> validateResult=validateBeforeInsert(dto);
         if(!validateResult.isSuccess()){
             return validateResult;
         }

          String paramId = generateParamId();
          dto.setId(paramId);
          dto.setRecStat(YesOrNoEnum.YES.getCode());

          ParamAuditStatusEnum auditStatusEnum=getAuditStatus(holdAuth);
          dto.setAuditStatus(auditStatusEnum.getCode());
          log.info("新增参数配置，配置类型：{}，配置属性：{}，是否持有审核权限：{}，操作人：{}",
                  getParamType().getDescription(),JSON.toJSONString(dto),holdAuth,operator);

          //如果待审核，需要插入 审核表
          if(auditStatusEnum==ParamAuditStatusEnum.AUDIT_WAIT){
            //插入待审核信息
               CmParamChangeInfo changeInfo = constructChangePo(dto, operator, ParamChangeTypeEnum.INSERT);
               changeInfo.setId(getParamId());
               paramChangeInfoService.insert(changeInfo);
          }
          //插入日志
         CmParamLogInfo logInfo=new CmParamLogInfo(getParamType().getCode(),paramId,ParamChangeTypeEnum.INSERT.getCode());
         logInfo.setChangeValue(JSON.toJSONString(dto));
         insertLog(logInfo,operator,auditStatusEnum);

          //插入配置数据
          return insertConfig(dto,operator);
     }


     /**
      * 修改流程
      * @param dto 参数 更新对象
      * @param operator  操作人
      * @param holdAuth 是否持有审核权限
      * @return
      */
     public NtReturnMessageDto<String>  updateFlow(T dto, String operator,Boolean holdAuth) {

         //新增 前置校验
         NtReturnMessageDto<String> validateResult=validateBeforeUpdate(dto);
         if(!validateResult.isSuccess()){
             return validateResult;
         }

          String paramId = dto.getId();

          ParamAuditStatusEnum auditStatusEnum=getAuditStatus(holdAuth);

          //构建 审核信息
          CmParamChangeInfo changeInfo = constructChangePo(dto, operator, ParamChangeTypeEnum.UPDATE);

         log.info("修改参数配置，配置类型：{}，配置属性：{}，是否持有审核权限：{}，操作人：{}",
                 getParamType().getDescription(),JSON.toJSONString(dto),holdAuth,operator);

          //如果持有审核权限
          if(auditStatusEnum==ParamAuditStatusEnum.AUDIT_PASS){
               //映射修改
               reflectChange(changeInfo,operator);
          }else{
               //merge --> 待审核  merge只保留最新一条
//               changeInfo.setId(getParamId());
               paramChangeInfoService.merge(changeInfo);

               //更新原记录--> 待审核
               updateAudtiStatus(paramId,ParamAuditStatusEnum.AUDIT_WAIT,operator);

          }

         //插入日志
         CmParamLogInfo logInfo=new CmParamLogInfo(getParamType().getCode(),paramId,ParamChangeTypeEnum.UPDATE.getCode());
         logInfo.setChangeValue(JSON.toJSONString(dto));
         insertLog(logInfo,operator,auditStatusEnum);

          return NtReturnMessageDto.ok();
     }

     /**
      * 审核流程
      * @param auditVo    审核对象 vo
      */
     public NtReturnMessageDto<String>  auditFlow(ParamAuditVo auditVo) {

          String paramId=auditVo.getParamId();
          YesOrNoEnum auditPass=auditVo.getAuditPass();
          String auditor=auditVo.getAuditor();
          String auditAdvice=auditVo.getAuditAdvice();
          Assert.notNull(paramId, "待审核的原纪录表id不能为空");
          Assert.notNull(auditPass, "审核结果不能为空");
          //获取变动Obj
          CmParamChangeInfo changePo = paramChangeInfoService.selectUnAuditByParamId(paramId, getParamType().getCode());
          if(changePo==null){
               return NtReturnMessageDto.fail(String.format("配置Id:%s 无待审核数据！",paramId));
          }
          //审核拒绝时，审核意见不能为空
          if(YesOrNoEnum.NO==auditPass){
              //审核拒绝
              if(StringUtil.isEmpty(auditAdvice)){
                  return NtReturnMessageDto.fail("审核拒绝时，审核意见不能为空！");
              }
          }

//          if (StringUtil.equalsIgnoreCase(changePo.getCreator(), auditor)) {
//               throw new ValidateException("审核人不能为修改人！");
//          }
          //pre 校验： 只有未审核才能审核操作
          if (YesOrNoEnum.YES.getCode().equals(changePo.getAuditStatus())) {
               return NtReturnMessageDto.fail("该参数变动已审核，不能继续审核操作！");
          }
         log.info("审核参数配置，配置类型：{}，审核信息：{}，操作人：{}",
                 getParamType().getDescription(),JSON.toJSONString(auditVo),auditor);

          //更新待审核表
          CmParamChangeInfo updateChangeInfo=new CmParamChangeInfo();
          updateChangeInfo.setId(changePo.getId());
          updateChangeInfo.setAuditor(auditor);
          updateChangeInfo.setAuditStatus(YesOrNoEnum.YES.getCode());
          updateChangeInfo.setAuditPass(auditPass.getCode());
          updateChangeInfo.setAuditRemark(auditAdvice);
          int updateChangeCount = paramChangeInfoService.updateByPrimaryKeySelective(updateChangeInfo);
         log.info("审核参数配置，配置类型：{}，配置ID：{}，更新待审核表Vo{}，更新条数：{}",
                 getParamType().getDescription(),paramId,JSON.toJSONString(updateChangeInfo),updateChangeCount);

          //审核通过 ： 变动标记为已审核 审核成功/失败; 原纪录映射更新属性值
          //审核成功
          if (YesOrNoEnum.YES==auditPass) {
               //映射修改原信息
               reflectChange(changePo,auditor);
          } else if (YesOrNoEnum.NO==auditPass) {
              //更新原记录： 审核状态=审核拒绝
               updateAudtiStatus(paramId,ParamAuditStatusEnum.AUDIT_REJECT,auditor);
          }

         //插入日志
         CmParamLogInfo logInfo=new CmParamLogInfo(getParamType().getCode(),paramId,ParamChangeTypeEnum.AUDIT.getCode());
         logInfo.setAuditPass(auditPass.getCode());
         logInfo.setAuditRemark(auditVo.getAuditAdvice());
         insertLog(logInfo,auditor,YesOrNoEnum.YES==auditPass?ParamAuditStatusEnum.AUDIT_PASS:ParamAuditStatusEnum.AUDIT_REJECT);

         return NtReturnMessageDto.ok();
     }


    /**
     * @description:(删除流程)
     * @param deleteVo 删除对象vo
     * @return T
     * @author: haoran.zhang
     * @date: 2023/8/1 10:05
     * @since JDK 1.8
     */
     public NtReturnMessageDto<String> deleteFlow(ParamDeleteVo deleteVo) {
         String paramId=deleteVo.getParamId();
         String operator=deleteVo.getOperator();
         String deleteAdvice=deleteVo.getDeleteAdvice();
         Boolean holdAuth = deleteVo.getHoldAuth();

         T dto = getParamDtoByConfigId(paramId);

         //删除 前置校验
         NtReturnMessageDto<String> validateResult=validateBeforeDelete(dto);
         if(!validateResult.isSuccess()){
             return validateResult;
         }

          ParamAuditStatusEnum auditStatusEnum=getAuditStatus(holdAuth);

          //构建 审核信息
          CmParamChangeInfo changeInfo = constructChangePo(dto, operator, ParamChangeTypeEnum.DELETE);

         log.info("删除参数配置，配置类型：{}，配置ID：{}，是否持有审核权限：{}，操作人：{}",
                 getParamType().getDescription(),paramId,holdAuth,operator);

          //如果持有审核权限
          if(auditStatusEnum==ParamAuditStatusEnum.AUDIT_PASS){
               //映射修改
               reflectChange(changeInfo,operator);
          }else{
               //merge --> 待审核  merge只保留最新一条
               changeInfo.setId(getParamId());
               paramChangeInfoService.merge(changeInfo);

               //更新原记录--> 待审核
               updateAudtiStatus(paramId,ParamAuditStatusEnum.AUDIT_WAIT,operator);

          }
         //插入日志
         CmParamLogInfo logInfo=new CmParamLogInfo(getParamType().getCode(),paramId,ParamChangeTypeEnum.DELETE.getCode());
         logInfo.setAuditRemark(deleteAdvice);
         insertLog(logInfo,operator,auditStatusEnum);
          return NtReturnMessageDto.ok();

     }

     /**
      * 构建 待审核信息
      */
     private CmParamChangeInfo constructChangePo(T dto, String operator, ParamChangeTypeEnum changeTypeEnum) {
          CmParamChangeInfo po = new CmParamChangeInfo();
          po.setParamType(getParamType().getCode());
          po.setParamId(dto.getId());
          po.setCreator(operator);
          po.setChangeValue(JSON.toJSONString(dto));
          po.setChangeType(changeTypeEnum.getCode());
          //审核状态 0-待审核 1-已审核
          po.setAuditStatus(YesOrNoEnum.NO.getCode());
          po.setRecStat(YesOrNoEnum.YES.getCode());
          return po;
     }

}
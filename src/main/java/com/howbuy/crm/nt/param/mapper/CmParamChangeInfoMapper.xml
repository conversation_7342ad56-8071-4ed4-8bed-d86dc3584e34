<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.param.dao.CmParamChangeInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.param.dto.CmParamChangeInfo">
    <!--@mbg.generated-->
    <!--@Table CM_PARAM_CHANGE_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PARAM_TYPE" jdbcType="VARCHAR" property="paramType" />
    <result column="PARAM_ID" jdbcType="VARCHAR" property="paramId" />
    <result column="CHANGE_VALUE" jdbcType="CLOB" property="changeValue" />
    <result column="CHANGE_TYPE" jdbcType="VARCHAR" property="changeType" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="AUDIT_PASS" jdbcType="VARCHAR" property="auditPass" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="AUDITOR" jdbcType="VARCHAR" property="auditor" />
    <result column="UPDATED_STIMESTAMP" jdbcType="TIMESTAMP" property="updatedStimestamp" />
    <result column="AUDIT_REMARK" jdbcType="VARCHAR" property="auditRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PARAM_TYPE, PARAM_ID, CHANGE_VALUE, CHANGE_TYPE, AUDIT_STATUS, AUDIT_PASS, REC_STAT, 
    CREATOR, CREATE_TIMESTAMP, AUDITOR, UPDATED_STIMESTAMP, AUDIT_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="string" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_PARAM_CHANGE_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectUnAuditByParamId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_PARAM_CHANGE_INFO
    <where>
      AND PARAM_TYPE=#{paramType,jdbcType=VARCHAR}
      AND PARAM_ID=#{paramId,jdbcType=VARCHAR}
      AND AUDIT_STATUS='0'
      AND REC_STAT='1'
    </where>
  </select>

  <select id="selectLatestUnAuditReject" parameterType="map" resultMap="BaseResultMap">
    SELECT * FROM (
    select
    ROWNUM as rn,
    <include refid="Base_Column_List"/>
    from CM_PARAM_CHANGE_INFO
    <where>
      AND PARAM_TYPE=#{paramType,jdbcType=VARCHAR}
      AND PARAM_ID=#{paramId,jdbcType=VARCHAR}
      AND AUDIT_STATUS='1'
      AND AUDIT_PASS='0'
      AND REC_STAT='1'
    </where>
    ORDER BY CREATE_TIMESTAMP DESC
    ) WHERE rn=1
  </select>



  <delete id="deleteByPrimaryKey" parameterType="string">
    <!--@mbg.generated-->
    delete from CM_PARAM_CHANGE_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.param.dto.CmParamChangeInfo">
    <!--@mbg.generated-->
    insert into CM_PARAM_CHANGE_INFO (ID, PARAM_TYPE, PARAM_ID, 
      CHANGE_VALUE, CHANGE_TYPE, AUDIT_STATUS, 
      AUDIT_PASS, REC_STAT, CREATOR, 
      CREATE_TIMESTAMP, AUDITOR, UPDATED_STIMESTAMP, 
      AUDIT_REMARK)
    values (SEQ_PARAM_ID.Nextval, #{paramType,jdbcType=VARCHAR}, #{paramId,jdbcType=VARCHAR},
      #{changeValue,jdbcType=CLOB}, #{changeType,jdbcType=VARCHAR}, #{auditStatus,jdbcType=VARCHAR},
      #{auditPass,jdbcType=VARCHAR}, #{recStat,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      SYSTIMESTAMP, #{auditor,jdbcType=VARCHAR}, #{updatedStimestamp,jdbcType=TIMESTAMP},
      #{auditRemark,jdbcType=VARCHAR})
  </insert>

  <insert id="batchInsert"  parameterType="java.util.List">
    <!--@mbg.generated-->
    begin
    <foreach collection="recordList" item="row" index="index" separator=";">
        insert into CM_PARAM_CHANGE_INFO (ID, PARAM_TYPE, PARAM_ID,
        CHANGE_VALUE, CHANGE_TYPE, AUDIT_STATUS,
        AUDIT_PASS, REC_STAT, CREATOR,
        CREATE_TIMESTAMP, AUDITOR, UPDATED_STIMESTAMP,
        AUDIT_REMARK)
        values (SEQ_PARAM_ID.Nextval, #{row.paramType,jdbcType=VARCHAR}, #{row.paramId,jdbcType=VARCHAR},
        #{row.changeValue,jdbcType=CLOB}, #{row.changeType,jdbcType=VARCHAR}, #{row.auditStatus,jdbcType=VARCHAR},
        #{row.auditPass,jdbcType=VARCHAR}, #{row.recStat,jdbcType=VARCHAR}, #{row.creator,jdbcType=VARCHAR},
        SYSTIMESTAMP, #{row.auditor,jdbcType=VARCHAR}, #{row.updatedStimestamp,jdbcType=TIMESTAMP},
        #{row.auditRemark,jdbcType=VARCHAR})
    </foreach>
    ;end;
    </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.param.dto.CmParamChangeInfo">
    <!--@mbg.generated-->
    update CM_PARAM_CHANGE_INFO
    <set>
      <if test="paramType != null">
        PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="paramId != null">
        PARAM_ID = #{paramId,jdbcType=VARCHAR},
      </if>
      <if test="changeValue != null">
        CHANGE_VALUE = #{changeValue,jdbcType=CLOB},
      </if>
      <if test="changeType != null">
        CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditPass != null">
        AUDIT_PASS = #{auditPass,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="auditor != null">
        AUDITOR = #{auditor,jdbcType=VARCHAR},
      </if>
      UPDATED_STIMESTAMP = SYSTIMESTAMP,
      <if test="auditRemark != null">
        AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.param.dto.CmParamChangeInfo">
    <!--@mbg.generated-->
    update CM_PARAM_CHANGE_INFO
    set PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      PARAM_ID = #{paramId,jdbcType=VARCHAR},
      CHANGE_VALUE = #{changeValue,jdbcType=CLOB},
      CHANGE_TYPE = #{changeType,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      AUDIT_PASS = #{auditPass,jdbcType=VARCHAR},
      REC_STAT = #{recStat,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      AUDITOR = #{auditor,jdbcType=VARCHAR},
      UPDATED_STIMESTAMP = #{updatedStimestamp,jdbcType=TIMESTAMP},
      AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
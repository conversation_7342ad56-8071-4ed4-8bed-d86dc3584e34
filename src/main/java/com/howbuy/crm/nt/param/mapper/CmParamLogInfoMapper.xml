<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.param.dao.CmParamLogInfoMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.base.param.CmParamLogInfo">
    <!--@mbg.generated-->
    <!--@Table CM_PARAM_LOG_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="PARAM_TYPE" jdbcType="VARCHAR" property="paramType" />
    <result column="PARAM_ID" jdbcType="VARCHAR" property="paramId" />
    <result column="OPT_TYPE" jdbcType="VARCHAR" property="optType" />
    <result column="CHANGE_VALUE" jdbcType="CLOB" property="changeValue" />
    <result column="FORMER_VALUE" jdbcType="CLOB" property="formerValue" />
    <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus" />
    <result column="FORMER_AUDIT_STATUS" jdbcType="VARCHAR" property="formerAuditStatus" />
    <result column="AUDIT_PASS" jdbcType="VARCHAR" property="auditPass" />
    <result column="AUDIT_REMARK" jdbcType="VARCHAR" property="auditRemark" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIMESTAMP" jdbcType="TIMESTAMP" property="createTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, PARAM_TYPE, PARAM_ID, OPT_TYPE, CHANGE_VALUE, FORMER_VALUE,AUDIT_STATUS, FORMER_AUDIT_STATUS,
    AUDIT_PASS, AUDIT_REMARK, CREATOR, CREATE_TIMESTAMP
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from CM_PARAM_LOG_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>

  <select id="selectLogListByByParamId" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from CM_PARAM_LOG_INFO
    <where>
      AND PARAM_ID = #{paramId,jdbcType=VARCHAR}
      AND PARAM_TYPE = #{paramType,jdbcType=VARCHAR}
    </where>
    ORDER BY CREATE_TIMESTAMP DESC
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from CM_PARAM_LOG_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.base.param.CmParamLogInfo">
    <!--@mbg.generated-->
    insert into CM_PARAM_LOG_INFO (ID, PARAM_TYPE, PARAM_ID, 
      OPT_TYPE, CHANGE_VALUE, FORMER_VALUE,
      AUDIT_STATUS,FORMER_AUDIT_STATUS, AUDIT_PASS, AUDIT_REMARK,
      CREATOR, CREATE_TIMESTAMP)
    values (#{id,jdbcType=VARCHAR}, #{paramType,jdbcType=VARCHAR}, #{paramId,jdbcType=VARCHAR}, 
      #{optType,jdbcType=VARCHAR}, #{changeValue,jdbcType=CLOB}, #{formerValue,jdbcType=CLOB},
      #{auditStatus,jdbcType=VARCHAR},#{formerAuditStatus,jdbcType=VARCHAR}, #{auditPass,jdbcType=VARCHAR}, #{auditRemark,jdbcType=VARCHAR},
      #{creator,jdbcType=VARCHAR}, SYSTIMESTAMP)
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.base.param.CmParamLogInfo">
    <!--@mbg.generated-->
    update CM_PARAM_LOG_INFO
    <set>
      <if test="paramType != null">
        PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="paramId != null">
        PARAM_ID = #{paramId,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        OPT_TYPE = #{optType,jdbcType=VARCHAR},
      </if>
      <if test="changeValue != null">
        CHANGE_VALUE = #{changeValue,jdbcType=CLOB},
      </if>
      <if test="formerValue != null">
        FORMER_VALUE = #{formerValue,jdbcType=CLOB},
      </if>
      <if test="auditStatus != null and auditStatus != '' ">
        AUDIT_STATUS = #{auditStatus,jdbcType=VARCHAR},
      </if>
      <if test="formerAuditStatus != null">
        FORMER_AUDIT_STATUS = #{formerAuditStatus,jdbcType=VARCHAR},
      </if>
      <if test="auditPass != null">
        AUDIT_PASS = #{auditPass,jdbcType=VARCHAR},
      </if>
      <if test="auditRemark != null">
        AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTimestamp != null">
        CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.base.param.CmParamLogInfo">
    <!--@mbg.generated-->
    update CM_PARAM_LOG_INFO
    set PARAM_TYPE = #{paramType,jdbcType=VARCHAR},
      PARAM_ID = #{paramId,jdbcType=VARCHAR},
      OPT_TYPE = #{optType,jdbcType=VARCHAR},
      CHANGE_VALUE = #{changeValue,jdbcType=CLOB},
      FORMER_VALUE = #{formerValue,jdbcType=CLOB},
      AUDIT_STATUS =#{auditStatus,jdbcType=VARCHAR},
      FORMER_AUDIT_STATUS = #{formerAuditStatus,jdbcType=VARCHAR},
      AUDIT_PASS = #{auditPass,jdbcType=VARCHAR},
      AUDIT_REMARK = #{auditRemark,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIMESTAMP = #{createTimestamp,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
package com.howbuy.crm.nt.param.service;

import com.howbuy.crm.nt.param.dao.CmParamChangeInfoMapper;
import com.howbuy.crm.nt.param.dto.CmParamChangeInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
/**
 * @description: (参数变更记录表)服务类
 * <AUTHOR>
 * @date 2023/7/27 10:29
 * @since JDK 1.8
 */
@Service
public class CmParamChangeInfoService{

    @Resource
    private CmParamChangeInfoMapper cmParamChangeInfoMapper;


    /**
     * @description:(根据id删除)
     * @param id
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/7 9:31
     * @since JDK 1.8
     */
    public int deleteByPrimaryKey(String id) {
        return cmParamChangeInfoMapper.deleteByPrimaryKey(id);
    }


   /**
    * @description:(插入 参数变更记录表)
    * @param record
    * @return int
    * @author: haoran.zhang
    * @date: 2023/11/7 9:33
    * @since JDK 1.8
    */
    public int insert(CmParamChangeInfo record) {
        return cmParamChangeInfoMapper.insert(record);
    }


    /**
     * @description:(批量插入 参数变更记录表)
     * @param recordList
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/7 9:33
     * @since JDK 1.8
     */
    public int batchInsert(List<CmParamChangeInfo> recordList) {
        return cmParamChangeInfoMapper.batchInsert(recordList);
    }


    /**
     * @description:(更新 或者 新增  参数变更记录表)
     * @param record
     * @return int
     * @author: haoran.zhang
     * @date: 2023/11/7 9:33
     * @since JDK 1.8
     */
    public int merge(CmParamChangeInfo record) {
        //根据 paramType paramId 查询 有效的 未审核的记录
        CmParamChangeInfo unAuditInfo = selectUnAuditByParamId(record.getParamId(), record.getParamType());
        if(unAuditInfo==null){
            return cmParamChangeInfoMapper.insert(record);
        }
        CmParamChangeInfo updateChangeInfo = new CmParamChangeInfo();
        updateChangeInfo.setId(unAuditInfo.getId());
        updateChangeInfo.setChangeValue(record.getChangeValue());
        updateChangeInfo.setChangeType(record.getChangeType());
        updateChangeInfo.setCreator(record.getCreator());
        return cmParamChangeInfoMapper.updateByPrimaryKeySelective(updateChangeInfo);
    }

   /**
    * @description:(根据id 查询 参数变更记录表)
    * @param id
    * @return com.howbuy.crm.nt.param.dto.CmParamChangeInfo
    * @author: haoran.zhang
    * @date: 2023/11/7 9:35
    * @since JDK 1.8
    */
    public CmParamChangeInfo selectByPrimaryKey(String id) {
        return cmParamChangeInfoMapper.selectByPrimaryKey(id);
    }


    /**
     * @description:(根据paramId paramType 查询 有效的 未审核的记录)
     * @param paramId
     * @param paramType
     * @return com.howbuy.crm.nt.param.dto.CmParamChangeInfo
     * @date: 2023/11/7 9:35
     * @since JDK 1.8
     */
    public CmParamChangeInfo selectUnAuditByParamId(String paramId,String paramType){
        return cmParamChangeInfoMapper.selectUnAuditByParamId(paramId,paramType);
    }


    /**
     * @description:(根据paramId paramType 查找最新的审核拒绝的记录)
     * @param paramId
     * @param paramType
     * @return com.howbuy.crm.nt.param.dto.CmParamChangeInfo
     * @date: 2023/11/7 9:35
     * @since JDK 1.8
     */
    public CmParamChangeInfo selectLatestUnAuditReject(String paramId,String paramType){
        return cmParamChangeInfoMapper.selectLatestUnAuditReject(paramId,paramType);
    }


   /**
    * @description:(部分更新 参数变更记录表)
    * @param record
    * @return int
    * @author: haoran.zhang
    * @date: 2023/11/7 9:37
    * @since JDK 1.8
    */
    public int updateByPrimaryKeySelective(CmParamChangeInfo record) {
        return cmParamChangeInfoMapper.updateByPrimaryKeySelective(record);
    }


    /**
     * @description:(覆盖式更新 参数变更记录表)
     * @param record
     * @return int
     * @date: 2023/11/7 9:37
     * @since JDK 1.8
     */
    public int updateByPrimaryKey(CmParamChangeInfo record) {
        return cmParamChangeInfoMapper.updateByPrimaryKey(record);
    }

}

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.param.service;

import com.howbuy.crm.nt.param.dao.ChannelManagerDao;
import com.howbuy.crm.nt.param.dto.PartnerInfo;
import com.howbuy.crm.nt.param.request.QueryPartnerRequest;
import com.howbuy.crm.nt.param.response.QueryPartnerResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 渠道管理服务类实现类
 * @date 2023/8/30 18:46
 * @since JDK 1.8
 */
@Slf4j
@Service("channelManagerService")
public class ChannelManagerServiceImpl implements ChannelManagerService {
    @Autowired
    private ChannelManagerDao channelManagerDao;

    /**
     * @api {DUBBO} com.howbuy.crm.nt.param.service.ChannelManagerService.queryPartnerByPartnerNo(queryPartnerRequest)
     * @apiName 查询合作伙伴信息
     *
     * @apiParam (queryPartnerRequest) {com.howbuy.crm.nt.param.request.QueryPartnerRequest} queryPartnerRequest
     * @apiParam (queryPartnerRequest) {String{1..100}} queryPartnerRequest.partnerNo 合作伙伴号码
     *
     * @apiParamExample {json} Request Example
     *   {
     *    "partnerNo" : "P001",
     *   }
     *
     * @apiSuccess {com.howbuy.crm.nt.param.response.QueryPartnerResponse} QueryPartnerResponse
     * @apiSuccess (QueryPartnerResponse) {com.howbuy.crm.nt.param.dto.PartnerInfo} QueryPartnerResponse.partnerInfo 合作伙伴信息
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.partnerNo 合作伙伴号码
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.partnerName 合作伙伴名称
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.partnerType 合作伙伴类型
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.credt 信用
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.activityNum 活动数量
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.conspireStat 合谋状态
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.recStat 渠道是否被删除
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.checkStat 检查状态
     * @apiSuccess (PartnerInfo) {String{1..100}} QueryPartnerResponse.partnerInfo.channelTagName 渠道标签名称
     *
     * @apiSuccessExample {json} 响应结果示例:
     *   {
     *     "returnCode": "0000",
     *     "description": "查询成功",
     *     "duration": 600,
     *     "partnerInfo": {
     *       "partnerNo": "P001",
     *       "partnerName": "合作伙伴1",
     *       "partnerType": "A",
     *       "credt": "123",
     *       "activityNum": "5",
     *       "conspireStat": "正常",
     *       "recStat": "未删除",
     *       "checkStat": "已检查",
     *       "channelTagName": "标签1"
     *     }
     *   }
     */
    @Override
    public QueryPartnerResponse queryPartnerByPartnerNo(QueryPartnerRequest queryPartnerRequest) {
        QueryPartnerResponse result = new QueryPartnerResponse();

        String partnerNo = queryPartnerRequest.getPartnerNo();
        if (StringUtils.isNotEmpty(partnerNo)) {
            PartnerInfo partnerInfo = channelManagerDao.queryPartnerByPartnerNo(partnerNo);
            log.info("-----partnerInfo-----:" + (partnerInfo == null ? "空" : partnerInfo.toString()));
            if (partnerInfo != null) {
                result.setPartnerInfo(partnerInfo);
                result.success();
            } else {
                result.noData("未找到相关渠道信息！");
            }
        } else {
            result.invalidReqParams("传入参数不能为空！");
        }

        return result;
    }

}
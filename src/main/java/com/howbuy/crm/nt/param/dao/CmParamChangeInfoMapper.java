package com.howbuy.crm.nt.param.dao;

import com.howbuy.crm.nt.param.dto.CmParamChangeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/7/27 10:29
 * @since JDK 1.8
 */
public interface CmParamChangeInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmParamChangeInfo record);


    int batchInsert(@Param("recordList") List<CmParamChangeInfo> recordList);

    CmParamChangeInfo selectByPrimaryKey(String id);


    /**
     * 查找待审核的id
     * @param paramId
     * @param paramType
     * @return
     */
    CmParamChangeInfo selectUnAuditByParamId(@Param("paramId") String paramId, @Param("paramType") String paramType);


    /**
     * 查找最新的审核拒绝的记录
     * @param paramId
     * @param paramType
     * @return
     */
    CmParamChangeInfo selectLatestUnAuditReject(@Param("paramId") String paramId, @Param("paramType") String paramType);


    int updateByPrimaryKeySelective(CmParamChangeInfo record);

    int updateByPrimaryKey(CmParamChangeInfo record);
}
package com.howbuy.crm.nt.param.dao;


import com.howbuy.crm.nt.base.param.CmParamLogInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/9/18 10:57
 * @since JDK 1.8
 */
public interface CmParamLogInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(CmParamLogInfo record);


    CmParamLogInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CmParamLogInfo record);

    int updateByPrimaryKey(CmParamLogInfo record);


    /**
     * 根据 configId 获取 操作日志明细列表
     * @param paramId
     * @param paramType
     * @return
     */
    List<CmParamLogInfo> selectLogListByByParamId(@Param("paramId") String paramId, @Param("paramType") String paramType);
}
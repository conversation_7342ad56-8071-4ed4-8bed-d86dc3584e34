package com.howbuy.crm.nt.basedetail.service;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.enums.OuterSysTypeEnum;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusFacade;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusRequest;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusResponse;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusFacade;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusRequest;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusResponse;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoFacade;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoRequest;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindFacade;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindRequest;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindResponse;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.bean.OuterAcctLoginBean;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindRequest;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.WechatAcctBindInfo;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.cc.center.feature.answer.request.QueryLastAnswerRequest;
import com.howbuy.cc.center.feature.answer.response.QueryLastAnswerResponse;
import com.howbuy.cc.center.feature.answer.service.QueryLastAnswerService;
import com.howbuy.cc.center.feature.asset.request.QueryCurrentAssetCertificateStatusRequest;
import com.howbuy.cc.center.feature.asset.response.QueryCurrentAssetCertificateStatusResponse;
import com.howbuy.cc.center.feature.asset.service.QueryCurrentAssetCertificateStatusService;
import com.howbuy.cc.center.feature.kycinfo.domain.InvestorTypeEnum;
import com.howbuy.cc.center.feature.question.domain.ExamType;
import com.howbuy.cc.center.feature.question.domain.OptionInfoDomain;
import com.howbuy.cc.center.feature.question.domain.QuestionInfoDomain;
import com.howbuy.cc.center.member.tag.request.QueryUserTagsListRequest;
import com.howbuy.cc.center.member.tag.response.QueryUserTagsListResponse;
import com.howbuy.cc.center.member.tag.service.QueryUserTagsListService;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.nt.basedetail.dto.CustBaseDetailDomain;
import com.howbuy.crm.nt.basedetail.dto.CustKycInfo;
import com.howbuy.crm.nt.basedetail.request.QueryCustBaseDetailRequest;
import com.howbuy.crm.nt.basedetail.response.QueryCustBaseDetailResponse;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DisCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.ParseException;
import java.util.*;

@Service("queryCustBaseDetailService")
public class QueryCustBaseDetailServiceImpl implements QueryCustBaseDetailService {

	private static final Logger log = LoggerFactory.getLogger(QueryCustBaseDetailServiceImpl.class);
	
	@Autowired
    private QueryAccHboneInfoFacade queryAccHboneInfoFacade;
	
	@Autowired
	private KycInfoFacade kycInfoFacade;

	@Autowired
	private QueryLastAnswerService queryLastAnswerService;
	
	@Autowired
    private QueryOuterAcctLoginBindFacade queryOuterAcctLoginBindFacade;
	
	@Autowired
    private QueryCurrentAssetCertificateStatusService queryCurrentAssetCertificateStatusService;

	@Autowired
	private QueryAssetManagementCertificateStatusFacade queryAssetManagementCertificateStatusFacade;

	@Autowired
	private QueryCustAssetCertificateStatusFacade queryCustAssetCertificateStatusFacade;
	
	@Autowired
	private QueryUserTagsListService queryUserTagsListService;
	
	@Autowired
    private QueryCustInfoFacade queryCustInfoFacade;

	@Autowired
	private ConscustMapper conscustMapper;

	@Autowired
	private QueryWechatAcctBindFacade queryWechatAcctBindFacade;

	@Override
	public QueryCustBaseDetailResponse queryConscustInfo(QueryCustBaseDetailRequest request) {
		QueryCustBaseDetailResponse response = new QueryCustBaseDetailResponse();
		
		if(checkparam(request)) {
			CustBaseDetailDomain custbasedetail = new CustBaseDetailDomain();
			
			//获取关联一账通号
			String hboneno = request.getHboneno();
			
			if(StringUtils.isNotBlank(hboneno)) {
				//填充一账通信息，手机校验
				this.queryHboneInfo(hboneno, custbasedetail);
				//查询是否登陆过掌机
				this.queryUserTags(hboneno, custbasedetail);
				//查询KYC信息
				Map<String,CustKycInfo> kycInfoMap= Maps.newHashMap();
				ConscustInfo custInfo=getCustInfoByHboneNo(hboneno);
				List<DisCodeEnum> disCodeList= DisCodeUtil.getFullBusiDisCodeList(CrmCustInvestTypeEnum.getEnum(custInfo.getInvsttype()));
				disCodeList.forEach(disCodeEnum -> kycInfoMap.put(disCodeEnum.getChannelEnum().getCode(),getCustKycInfo(hboneno,disCodeEnum)));
				custbasedetail.setKycInfoMap(kycInfoMap);
				//历史domain中为HOWBUY的kyc .保留之前的属性赋值
				if(kycInfoMap.get(DisChannelCodeEnum.HOWBUY.getCode())!=null){
					BeanUtils.copyProperties(kycInfoMap.get(DisChannelCodeEnum.HOWBUY.getCode()),custbasedetail);
				}

				//查询是否臻财
				this.queryBindWechat(hboneno, custbasedetail);
				//查询资产证明信息
				this.queryCurrentAsset(hboneno, custbasedetail);
				//查询回款方式
				this.queryPubCustInfo(hboneno, custbasedetail);
				//查询问卷可投资金额
				this.queryKycAnswer(hboneno, custbasedetail);
			}
			
			response.setCustbasedetaildomain(custbasedetail);
			response.success();
		}else {
			response.paramError("传参一账通不允许为空！");
		}
		
		return response;
	}

	private boolean checkparam(QueryCustBaseDetailRequest request) {
		boolean flag = false;
		if(StringUtils.isNotBlank(request.getHboneno())) {
			flag = true;
		}
		return flag;
	}

	/**
	 *
	 * @param hboneno
	 * @param custbasedetail
	 */
    public void queryHboneInfo(String hboneno, CustBaseDetailDomain custbasedetail) {
    	try {
    		QueryAccHboneInfoRequest queryHboneInfo = new QueryAccHboneInfoRequest();
            queryHboneInfo.setHboneNo(hboneno);
            log.info("QueryAccHboneInfoRequest:"+ JSON.toJSONString(queryHboneInfo));
            QueryAccHboneInfoResponse baseHboneInfo = queryAccHboneInfoFacade.execute(queryHboneInfo);
            log.info("QueryAccHboneInfoResponse:"+ JSON.toJSONString(baseHboneInfo));
            if(CrmNtConstant.RMISuccNew.equals(baseHboneInfo.getReturnCode())) {
            	custbasedetail.setMobileVerifyStatus(baseHboneInfo.getMobileVerifyStatus());
            	custbasedetail.setIdVerifyStatus(baseHboneInfo.getIdNoDigest());
            }
    	}catch(Exception e) {
    		log.error("查询接口queryAccHboneInfoFacade错误："+e.getMessage());
    		log.error(e.getMessage(),e);
    	}
    }

	/**
	 *
	 * @param hboneno
	 * @param custbasedetail
	 */
	public void queryUserTags(String hboneno, CustBaseDetailDomain custbasedetail) {
    	try {
    		QueryUserTagsListRequest tagRequest = new QueryUserTagsListRequest();
            tagRequest.setHboneNo(hboneno);
            List<String> tagNos = new ArrayList<String>();
            tagNos.add("10051");
            tagRequest.setTagNos(tagNos);

            log.info("QueryUserTagsListRequest:"+ JSON.toJSONString(tagRequest));
            QueryUserTagsListResponse tagResponse = queryUserTagsListService.execute(tagRequest);
            log.info("QueryUserTagsListResponse:"+ JSON.toJSONString(tagResponse));

            if (CrmNtConstant.RMISucc.equals(tagResponse.getReturnCode()) && tagResponse.getUserTagsSet().size() == 1) {
            	custbasedetail.setLoginplatform("1");
            }else {
            	custbasedetail.setLoginplatform("0");
            }
    		
    	}catch(Exception e) {
    		log.error("查询接口queryUserTagsListService错误："+e.getMessage());
    		log.error(e.getMessage(),e);
    	}
    }


	/**
	 * 客户的kyc信息
	 * @param hboneno
	 * @param disCodeEnum
	 * @return
	 */
	private CustKycInfo getCustKycInfo(String hboneno, DisCodeEnum disCodeEnum){
		Assert.notNull(hboneno,"一账通账号不能为空！");
		Assert.notNull(disCodeEnum,"分销渠道不能为空！");
		KycInfoRequest queryUserRequest = new KycInfoRequest();
		queryUserRequest.setHboneNo(hboneno);
		queryUserRequest.setDisCode(disCodeEnum.getCode());
		log.info("KycInfoRequest:"+ JSON.toJSONString(queryUserRequest));

		KycInfoResponse infoResponse;
		try{
			infoResponse=kycInfoFacade.execute(queryUserRequest);
		}catch(Exception e) {
			log.error(e.getMessage(),e);
			return null;
		}
		log.info("KycInfoResponse:"+ JSON.toJSONString(infoResponse));

		if(!CrmNtConstant.RMISuccNew.equals(infoResponse.getReturnCode())){
			return null;
		}
		CustKycInfo returnKycInfo=new CustKycInfo();
		returnKycInfo.setDisCode(disCodeEnum.getCode());
		returnKycInfo.setDisChannelCode(disCodeEnum.getChannelEnum().getCode());
		if ((StringUtils.isNotBlank(infoResponse.getFundFlag()))) {
			returnKycInfo.setFundsurvey(infoResponse.getFundFlag());
		} else {
			returnKycInfo.setFundsurvey("0");
		}

		if ((StringUtils.isNotBlank(infoResponse.getSignFlag()))) {
			returnKycInfo.setInvestorsurvey(infoResponse.getSignFlag());
		} else {
			returnKycInfo.setInvestorsurvey("0");
		}

		String riskToleranceExamType = infoResponse.getRiskToleranceExamType();
		if (StringUtils.isNotBlank(riskToleranceExamType)) {
			String riskToleranceTerm = infoResponse.getRiskToleranceTerm();
			if (ExamType.RETAIL.getValue().equals(riskToleranceExamType)) {
				returnKycInfo.setRetailrisktoleranceterm(riskToleranceTerm);
			}else {
				returnKycInfo.setHighrisktoleranceterm(riskToleranceTerm);
				returnKycInfo.setHighrisktolerancetermShowStr(generateRisktolerancetermShowStr(riskToleranceTerm));
			}
		}
		String riskToleranceFrom = infoResponse.getRiskToleranceFrom();
		if (StringUtils.isNotBlank(riskToleranceFrom) && StringUtils.isNotBlank(riskToleranceExamType)) {
			if(ExamType.RETAIL.getValue().equals(riskToleranceExamType)) {
				returnKycInfo.setRetailassessmentsurvey("1");
				/**
				 * added by wu.long at 20190905
				 */
				returnKycInfo.setRetailGpsrisklevel(infoResponse.getRiskToleranceLevel());
			}else if(ExamType.HIGH_END.getValue().equals(riskToleranceExamType) || ExamType.INSTITUTION.getValue().equals(riskToleranceExamType)){
				returnKycInfo.setHighassessmentsurvey("1");
				/**
				 * added by wu.long at 20190905
				 */
				returnKycInfo.setHighGpsrisklevel(infoResponse.getRiskToleranceLevel());
			}
		}

		String investorType = infoResponse.getInvestorType();
		if (StringUtils.isNotBlank(investorType)) {
			if (InvestorTypeEnum.NORMAL.getValue().equals(investorType)) {
				/*custbasedetail.setInvestorType(InvestorTypeEnum.NORMAL.getDescription());*/
				String investorQualifiedDate = infoResponse.getInvestorQualifiedDate();
				if("20991231".equals(investorQualifiedDate)){
					returnKycInfo.setInvestorType("专业（待验证）");
				}else{
					returnKycInfo.setInvestorType(InvestorTypeEnum.NORMAL.getDescription());
				}
			}
			if (InvestorTypeEnum.PRO.getValue().equals(investorType)) {
				returnKycInfo.setInvestorType(InvestorTypeEnum.PRO.getDescription());
			}
		}

			return returnKycInfo;
	}

	/**
	 * 生成风险评测是否到期的字符串
	 * 问卷到期时间 < 当天，则为“已过期”；
	 * 0 ≤ 问卷到期时间 - 当天 ≤ 30，则为“即将过期”；
	 * 其他情况，则为空
	 * @param risktoleranceterm 问卷到期时间
	 * @return
	 */
	private static String generateRisktolerancetermShowStr(String risktoleranceterm) {
		if (StringUtils.isNotBlank(risktoleranceterm)) {
			try {
				// 问卷到期时间
				Date termDate = DateUtils.parseDate(risktoleranceterm, "yyyyMMdd");
				// 当前日期
				Date now = new Date();
				long between = DateUtil.between(now, termDate, DateUnit.DAY, false);
				if (between < 0) {
					return "已过期";
				} else if (between >= 0 && between <= 30) {
					return "即将过期";
				}
			} catch (ParseException e) {
				log.error("日期格式不对，解析出错" + risktoleranceterm, e);
			}
		}
		return null;
	}

	/**
	 * 根据一账通账号 查找客户信息
	 * @param hboneNo
	 * @return
	 */
	private ConscustInfo getCustInfoByHboneNo(String hboneNo){
		Map<String, String> params = new HashMap<>();
		params.put("hboneno", hboneNo);
		List<ConscustInfo> listCustInfo = conscustMapper.listConsCustInfoByHboneNo(params);
		if (CollectionUtils.isNotEmpty(listCustInfo)) {
			return listCustInfo.get(0);
		}
		return null;
	}



	/**
	 *
	 * @param hboneno
	 * @param custbasedetail
	 */
	public void queryKycAnswer(String hboneno, CustBaseDetailDomain custbasedetail) {
		try {
			QueryLastAnswerRequest lastAnswerRequest = new QueryLastAnswerRequest();
			lastAnswerRequest.setHboneNo(hboneno);
			lastAnswerRequest.setShowExam("Y");
			QueryLastAnswerResponse lastAnswerResponse = queryLastAnswerService.queryLastAnswer(lastAnswerRequest);

			if (CrmNtConstant.RMISucc.equals(lastAnswerResponse.getReturnCode())) {

				if (lastAnswerResponse.getAnswerHisInfoDomain() != null && lastAnswerResponse.getAnswerHisInfoDomain().getAnswers() != null) {

					if(ExamType.HIGH_END.getValue().equals(lastAnswerResponse.getAnswerHisInfoDomain().getExamType())){

						Map<String, OptionInfoDomain> answermap = lastAnswerResponse.getAnswerHisInfoDomain().getAnswers();
						List<QuestionInfoDomain> questionInfoList = lastAnswerResponse.getExamInfoDomain().getQuestions();
						lastAnswerResponse.getExamInfoDomain().getQuestions();

						if(questionInfoList != null && questionInfoList.size() >0){
							for(QuestionInfoDomain infoDomain:questionInfoList){

								if(infoDomain.getSortNum() == 5){
									OptionInfoDomain answer5 = answermap.get(infoDomain.getQuestionId());
									log.info("infoDomain:"+infoDomain.getQuestion()+"-answer5:"+ (null==answer5? "空":answer5.getOptionId()));
									if(answer5 != null){
										if(StringUtils.isNotBlank(answer5.getOptionChar())) {

											if("A".equals(answer5.getOptionChar()) || "B".equals(answer5.getOptionChar())){
												custbasedetail.setGpsinvestlevel("<100万");
											}else if("C".equals(answer5.getOptionChar()) || "D".equals(answer5.getOptionChar())){
												custbasedetail.setGpsinvestlevel("100-1000万");
											}else if("E".equals(answer5.getOptionChar())){
												custbasedetail.setGpsinvestlevel(">1000万");
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}catch(Exception e) {
			log.error("查询接口queryLastAnswerService错误："+e.getMessage());
			log.error(e.getMessage(),e);
		}
	}

	/**
	 * @param hboneno
	 * @param custbasedetail
	 */
	public void queryBindWechat(String hboneno, CustBaseDetailDomain custbasedetail) {
		try {
			custbasedetail.setWechat("0");

			// 调用账户中心接口判断微信绑定臻财或APP
			QueryOuterAcctLoginBindRequest req = new QueryOuterAcctLoginBindRequest();
			req.setHboneNo(hboneno);
			req.setOuterSysType(OuterSysTypeEnum.WeChat);
			log.info("QueryOuterAcctLoginBindRequest：{}", JSON.toJSONString(req));
			QueryOuterAcctLoginBindResponse rep = queryOuterAcctLoginBindFacade.execute(req);
			log.info("QueryOuterAcctLoginBindResponse：{}", JSON.toJSONString(rep));
			String wechatUnionID = null;
			if (rep != null && CrmNtConstant.RMISuccNew.equals(rep.getReturnCode())) {
				if (CollectionUtils.isNotEmpty(rep.getOuterAcctLoginBeanList()) && rep.getOuterAcctLoginBeanList().size() > 0) {
					ArrayList<String> strings = new ArrayList<>();
					for( OuterAcctLoginBean dto :rep.getOuterAcctLoginBeanList()){
						strings.add(dto.getOuterAcct());
					}
					custbasedetail.setWechat("1");
					wechatUnionID = String.join(",",strings);
				}
			}
			// 查询微信关注
			if (StringUtils.isEmpty(wechatUnionID)) {
				wechatUnionID = getUnionIdsbyHboneNo(hboneno);
			}
			if (StringUtils.isNotEmpty(wechatUnionID)) {
				custbasedetail.setWechat("1");
			}

			custbasedetail.setWechatUnionID(wechatUnionID);
		} catch (Exception e) {
			log.error("查询接口queryOuterAcctLoginBindFacade错误：{}", e.getMessage());
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * @description: 查询一账通下面对应的微信绑定信息
	 * @param hboneNo
	 * @return java.lang.String
	 * @author: hongdong.xie
	 * @date: 2024/12/18 15:22
	 * @since JDK 1.8
	 */
	public String getUnionIdsbyHboneNo(String hboneNo){
		QueryWechatAcctBindRequest wechatreq = new QueryWechatAcctBindRequest();
		wechatreq.setHboneNo(hboneNo);
		QueryWechatAcctBindResponse wechatrep = queryWechatAcctBindFacade.execute(wechatreq);
		ArrayList<String> strings = new ArrayList<>();
		if(wechatrep != null && CrmNtConstant.RMISuccNew.equals(wechatrep.getReturnCode()) && !org.springframework.util.CollectionUtils.isEmpty(wechatrep.getWechatList())){
			for(WechatAcctBindInfo info:wechatrep.getWechatList()){
				if(StringUtils.isNotBlank(info.getUnionId())){
					strings.add(info.getUnionId());
					break;
				}
			}
		}
		return String.join(",",strings);
	}

	/**
	 *
	 * @param hboneno
	 * @param custbasedetail
	 */
	public void queryCurrentAsset(String hboneno, CustBaseDetailDomain custbasedetail) {
    	try {
    		//查询资产证明信息 TODO YU.ZHANG
            QueryCurrentAssetCertificateStatusRequest queryCurrentAssetReq = new QueryCurrentAssetCertificateStatusRequest();
            queryCurrentAssetReq.setHboneNo(hboneno);
            log.info("QueryCurrentAssetCertificateStatusRequest:"+ JSON.toJSONString(queryCurrentAssetReq));
            QueryCurrentAssetCertificateStatusResponse queryCurrentAssetResponse = queryCurrentAssetCertificateStatusService.execute(queryCurrentAssetReq);
            log.info("QueryCurrentAssetCertificateStatusResponse:"+ JSON.toJSONString(queryCurrentAssetResponse));
            if (CrmNtConstant.RMISucc.equals(queryCurrentAssetResponse.getReturnCode())) {
            	custbasedetail.setCertificatestatus(queryCurrentAssetResponse.getLatestCertificateStatus());
            //	custbasedetail.setCurrentassetstatus(queryCurrentAssetResponse.getStatus());
            //	custbasedetail.setCurrentassetdt(queryCurrentAssetResponse.getEndDate());
            }
			//查询资产证明信息(私募) add by jianjian.yang
			QueryCustAssetCertificateStatusRequest queryCustAssetCertificateStatusRequest = new QueryCustAssetCertificateStatusRequest();
			queryCustAssetCertificateStatusRequest.setHboneNo(hboneno);
			log.info("QueryCustAssetCertificateStatusRequest:"+ JSON.toJSONString(queryCustAssetCertificateStatusRequest));
			QueryCustAssetCertificateStatusResponse queryCustAssetCertificateStatusResponse = queryCustAssetCertificateStatusFacade.execute(queryCustAssetCertificateStatusRequest);
			log.info("QueryCustAssetCertificateStatusResponse:"+ JSON.toJSONString(queryCustAssetCertificateStatusResponse));
            
            if (CrmNtConstant.RMISuccNew.equals(queryCustAssetCertificateStatusResponse.getReturnCode())) {
            	custbasedetail.setCurrentassetstatus(queryCustAssetCertificateStatusResponse.getStatus());
            	custbasedetail.setCurrentassetdt(queryCustAssetCertificateStatusResponse.getEndDate());
            }

			//查询资产证明信息(资管) add by jianjian.yang
			QueryAssetManagementCertificateStatusRequest queryCurrentAssetManagementReq = new QueryAssetManagementCertificateStatusRequest();
			queryCurrentAssetManagementReq.setHboneNo(hboneno);

			log.info("QueryAssetManagementCertificateStatusRequest:"+ JSON.toJSONString(queryCurrentAssetManagementReq));
			QueryAssetManagementCertificateStatusResponse queryAssetManagementResponse = queryAssetManagementCertificateStatusFacade.execute(queryCurrentAssetManagementReq);
			log.info("QueryAssetManagementCertificateStatusResponse:"+ JSON.toJSONString(queryAssetManagementResponse));

			if (CrmNtConstant.RMISuccNew.equals(queryAssetManagementResponse.getReturnCode())) {
				custbasedetail.setAssetCertStatus(queryAssetManagementResponse.getAssetCertStatus());
				custbasedetail.setIncomeCertStatus(queryAssetManagementResponse.getIncomeCertStatus());
				custbasedetail.setInvestExpCertStatus(queryAssetManagementResponse.getInvestExpCertStatus());
				custbasedetail.setCurrentAssetsManageStatus(queryAssetManagementResponse.getStatus());
				custbasedetail.setCurrentAssetManageDt(queryAssetManagementResponse.getEndDate());
			}
    	}catch(Exception e) {
    		log.error("查询资产证明接口错误："+e.getMessage());
    		log.error(e.getMessage(),e);
    	}
    }
    
    public void queryPubCustInfo(String hboneno, CustBaseDetailDomain custbasedetail) {
    	try {
    		//查回款协议
            QueryCustInfoRequest custInfoReq = new QueryCustInfoRequest();
            custInfoReq.setHboneNo(hboneno);
            custInfoReq.setDisCode("HB000A001");
            custInfoReq.setTxCode("520043");
            
            
            log.info("QueryCustInfoRequest:"+ JSON.toJSONString(custInfoReq));
            QueryCustInfoResponse custInfoResponse = queryCustInfoFacade.execute(custInfoReq);
            log.info("QueryCustInfoResponse:"+ JSON.toJSONString(custInfoResponse));
            
            if (custInfoResponse != null) {

            	String crs = "";
                String collectprotocolmethod = "";

                if (custInfoResponse.getCustInfoBean() != null) {
                    collectprotocolmethod = custInfoResponse.getCustInfoBean().getCollectProtocolMethod();
                    log.info("获取回款方式collectprotocolmethod:" + collectprotocolmethod);
                }

                if (custInfoResponse.getAcCustIndiTaxQueryBean() != null) {
                    crs = custInfoResponse.getAcCustIndiTaxQueryBean().getIndiTaxResidType();
                }

                if (custInfoResponse.getAcCustInstTaxQueryBean() != null) {
                    crs = custInfoResponse.getAcCustInstTaxQueryBean().getInstTaxResidType();
                }

                custbasedetail.setCrs(crs);
                custbasedetail.setCollectprotocol(collectprotocolmethod);
            }
            
            
    	}catch(Exception e) {
    		log.error("查询接口queryCustInfoFacade错误："+e.getMessage());
    		log.error(e.getMessage(),e);
    	}
    }
}

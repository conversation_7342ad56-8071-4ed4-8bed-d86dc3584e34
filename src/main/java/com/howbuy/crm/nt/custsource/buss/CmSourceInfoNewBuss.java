package com.howbuy.crm.nt.custsource.buss;

import com.howbuy.crm.nt.custsource.dao.CmSourceInfoNewDao;
import com.howbuy.crm.nt.custsource.domain.CmSourceInfoNew;
import com.howbuy.crm.nt.custsource.request.CustomerSourceLevelRequset;
import com.howbuy.crm.nt.custsource.response.CustomerSourceLevelResponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import org.apache.dubbo.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class CmSourceInfoNewBuss {
	public static final Logger logger = LoggerFactory.getLogger(CmSourceInfoNewBuss.class);

	@Autowired
	private CmSourceInfoNewDao cmSourceInfoNewDao;

	/**
	 * 根据请求的OUTLETCODE获取新来源一级来源字典名称
	 */
	public CustomerSourceLevelResponse getPrimarySourceName(CustomerSourceLevelRequset req) {
		CustomerSourceLevelResponse res = new CustomerSourceLevelResponse();
		try {
			if (StringUtils.isBlank(req.getOutletcode())) {
				res.invalidReqParams("传入的outletcode为空！");
				return res;
			}
			
			String outletcode = req.getOutletcode().trim();
			// 则查询数据, 将原有读写缓存[CMSOURCE_NO_]的逻辑直接删除，数据量1千条 无需优化：2023-11-30
            CmSourceInfoNew queryCmSourceInfoNew = cmSourceInfoNewDao.queryDictNameByNewSourceNo(outletcode);
            if (queryCmSourceInfoNew != null && !StringUtils.isBlank(queryCmSourceInfoNew.getSourceNo())){
                res.setPrimarySourceName(queryCmSourceInfoNew.getDictName());
                res.success();
                return res;
            }
            queryCmSourceInfoNew = cmSourceInfoNewDao.queryDictNameBySourceNo(outletcode);
            if (queryCmSourceInfoNew!=null && !StringUtils.isBlank(queryCmSourceInfoNew.getSourceNo())) {
                res.setPrimarySourceName(queryCmSourceInfoNew.getDictName());
                res.success();
                return res;
            } else {// 查询不到数据，则返回没有找到数据给调用方
                res.noData("查询不到数据");
            }
        } catch (Exception e) {
			logger.error("获取来源缓存出现异常", e);
			res.putBaseResult(BaseConstantEnum.SYS_ERROR, "获取来源缓存出现异常！");
		}
		return res;
		
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.custsource.dao.CmSourceInfoNewDao">

	<resultMap type="com.howbuy.crm.nt.custsource.domain.CmSourceInfoNew" id="cmSourceInfoNewResult">
		<result property="sourceNo" column="SOURCENO" />
		<result property="dictName" column="DICT_NAME" />
	</resultMap>

	<select id="queryDictNameBySourceNo" parameterType="String" resultMap="cmSourceInfoNewResult">
		SELECT T1.SOURCENO, T3.DICT_NAME
         FROM CM_SOURCEINFO T1
         LEFT JOIN CM_SOURCEINFO_NEW T2
           ON T2.SOURCENO = T1.NEWSOURCENO
         LEFT JOIN CM_SOURCEINFO_DICT T3
           ON T3.DICT_NO = T2.FIRST_LEVEL_CODE
        WHERE T1.SOURCESTATE = '1'
          AND T2.IS_MERGE = '1'
          AND T2.REC_STAT = '1'
          AND T3.ISVALID = '1'
          AND T3.DICT_LEVEL = '1'
          AND T1.SOURCENO = #{sourceNo}
	</select>

    <select id="queryDictNameByNewSourceNo" parameterType="String" resultMap="cmSourceInfoNewResult">
        SELECT T2.SOURCENO, T3.DICT_NAME
        FROM CM_SOURCEINFO_NEW T2
        LEFT JOIN CM_SOURCEINFO_DICT T3
        ON T3.DICT_NO = T2.FIRST_LEVEL_CODE
        WHERE T2.IS_MERGE = '1'
        AND T2.REC_STAT = '1'
        AND T3.ISVALID = '1'
        AND T3.DICT_LEVEL = '1'
        AND T2.SOURCENO = #{sourceNo}
    </select>

</mapper>
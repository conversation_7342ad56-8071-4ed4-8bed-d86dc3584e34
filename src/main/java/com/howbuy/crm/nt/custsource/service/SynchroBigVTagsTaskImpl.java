package com.howbuy.crm.nt.custsource.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cc.center.member.tag.request.QueryTagsHboneListByPageRequest;
import com.howbuy.cc.center.member.tag.response.QueryTagsHboneListByPageResponse;
import com.howbuy.cc.center.member.tag.service.QueryTagsHboneListByPageService;
import com.howbuy.crm.nt.custsource.buss.CmBigvCustBuss;
import com.howbuy.crm.util.DateTimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  同步客户标签任务
 */
@Service("synchroCustTagsTaskService")
public class SynchroBigVTagsTaskImpl implements SynchroCustTagsTaskService {
	static Log log = LogFactory.getLog(SynchroBigVTagsTaskImpl.class);
	
	@Autowired
	private  QueryTagsHboneListByPageService queryTagsHboneListByPageService;
	
	@Autowired
	private  CmBigvCustBuss cmBigvCustBuss;
	

	/**
	 * 执行同步客户标签方法
	 */
	@Override
	@SuppressWarnings("deprecation")
	public void autoSynchroCustTags(String arg) {
		
		log.info("-----同步大V客户任务开始-----startDate:" + DateTimeUtil.convertDateToString(DateTimeUtil.DATE_TIME_PATTERN, new Date()));
		boolean status = false;
		
		// 大V
		if (arg != null && arg.contains("bigV")) {
			cmBigvCustBuss.delAllCmBigvCust();
			QueryTagsHboneListByPageResponse response = null;
			QueryTagsHboneListByPageRequest request = new QueryTagsHboneListByPageRequest();
			request.setPageNo(1);
			request.setPageSize(1000);
			List<String> includeTagsList = new ArrayList<String>();
			includeTagsList.add("10250");
			request.setIncludeTagsList(includeTagsList);
			
			int totalPage = 1;
			for(int pageNo = 1; pageNo <= totalPage; pageNo++){
				try{
					response = queryHboneList(includeTagsList, null,  pageNo);
					if (response != null && response.isSuccessful() && CollectionUtils.isNotEmpty(response.getHboneNoList())) {
						List<String> listHboneno = response.getHboneNoList();
						status = cmBigvCustBuss.handleCmBigvCust(listHboneno);
						if (!status) {
							break;
						}
					}

					totalPage = response == null ? 0 : (response.getTotalPage() == null ? 0 : response.getTotalPage());
				}catch(Exception e){
					log.error("调用接口queryTagsHboneListByPageService.execute异常：" + e.getMessage(),e);
					status = false;
					break;
				}
			}
			
			if(!status){
				log.info("-----同步大V客户任务失败-----totalPage:" + totalPage);
			}
		}	
		
		
		log.info("-----同步大V客户任务结束-----endDate:" + DateTimeUtil.convertDateToString(DateTimeUtil.DATE_TIME_PATTERN, new Date()));
	}
	
	
	
	
	public QueryTagsHboneListByPageResponse queryHboneList(List<String> includeTagsList, List<String> excludeTagsList, int pageNo) throws InterruptedException{
	    QueryTagsHboneListByPageRequest queryTagsHboneListByPageRequest = new QueryTagsHboneListByPageRequest();
	    queryTagsHboneListByPageRequest.setExcludeTagsList(excludeTagsList);
	    queryTagsHboneListByPageRequest.setIncludeTagsList(includeTagsList);
	    queryTagsHboneListByPageRequest.setPageNo(pageNo);
	    QueryTagsHboneListByPageResponse response;
	    int i = 0;
	    do {
	        i++;
	        log.info("queryTagsHboneListByPageService.execute(request):" + JSON.toJSON(queryTagsHboneListByPageRequest));
	        response = queryTagsHboneListByPageService.execute(queryTagsHboneListByPageRequest);
	        log.info("queryTagsHboneListByPageService.execute(response):" + JSON.toJSON(response));
	        if ((("0022").equals(response.getReturnCode()) || ("0023").equals(response.getReturnCode()))){
	        	Thread.sleep(30000);
	        }
	    }while ((("0022").equals(response.getReturnCode()) || ("0023").equals(response.getReturnCode())) && i<10);
	    
	    return response;
	}

}

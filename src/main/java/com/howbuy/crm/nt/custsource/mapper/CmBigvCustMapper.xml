<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.custsource.dao.CmBigvCustDao">
    <insert id="batchInsertCmBigvCust" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO CM_BIGV_CUST (hboneno,conscustno)
		select a.* from (								
		<foreach collection="list" item="item" index="index"  separator="union all" >
		  SELECT
		   #{item.hboneno, jdbcType=VARCHAR},
		   #{item.conscustno, jdbcType=VARCHAR}
		   FROM DUAL
		</foreach>
		) a
	</insert>
	
	
	
	<delete id="delAllCmBigvCust" >
	    DELETE  from CM_BIGV_CUST
	</delete>
	  

</mapper>
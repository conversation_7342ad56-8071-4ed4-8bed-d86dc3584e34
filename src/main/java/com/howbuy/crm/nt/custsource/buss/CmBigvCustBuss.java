package com.howbuy.crm.nt.custsource.buss;

import java.util.ArrayList;
import java.util.List;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.nt.conscust.buss.QueryFamilyCustInfoBuss;
import com.howbuy.crm.nt.custsource.dao.CmBigvCustDao;
import com.howbuy.crm.nt.custsource.domain.CmBigvCust;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class CmBigvCustBuss {
	public static final Logger logger = LoggerFactory.getLogger(CmBigvCustBuss.class);

	@Autowired
	private CmBigvCustDao cmBigvCustDao;
	
	@Autowired
	private QueryFamilyCustInfoBuss queryFamilyCustInfoBuss;
 
	/**
	 * 批量插入大V客户
	 */
	public boolean handleCmBigvCust(List<String> list) {
		boolean status = true;

		try {
			List<CmBigvCust> listCmBigvCust = new ArrayList<>();
			for (String hboneno : list) {
				if (StringUtil.isBlank(hboneno)) {
					continue;
				}
				//查询对应投顾客户号
				String conscustno = queryFamilyCustInfoBuss.getConscustNoByHboneNo(hboneno);
				if (StringUtils.isNotBlank(conscustno)) {
					CmBigvCust cmBigvCust = new CmBigvCust();
					cmBigvCust.setHboneno(hboneno);
					cmBigvCust.setConscustno(conscustno);
					listCmBigvCust.add(cmBigvCust);
				}

			}

			if (CollectionUtils.isNotEmpty(listCmBigvCust)) {
				cmBigvCustDao.batchInsertCmBigvCust(listCmBigvCust);
			}
		} catch (Exception e) {
			logger.error("批量插入大V客户异常：" + e.getMessage(), e);
			status = false;
		}

		return status;
	}
	
	public void delAllCmBigvCust(){
		cmBigvCustDao.delAllCmBigvCust();
	}
}

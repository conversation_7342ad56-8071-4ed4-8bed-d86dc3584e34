package com.howbuy.crm.nt.custsource.service;

import com.howbuy.crm.nt.custsource.buss.CmSourceInfoNewBuss;
import com.howbuy.crm.nt.custsource.request.CustomerSourceLevelRequset;
import com.howbuy.crm.nt.custsource.response.CustomerSourceLevelResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("customerSourceLevelService")
public class CmSourceInfoNewServiceImpl implements CustomerSourceLevelService {
	
	@Autowired
	private CmSourceInfoNewBuss cmSourceInfoNewBuss;
	
	@Override
	public CustomerSourceLevelResponse getPrimarySourceByOutLetCode(
			CustomerSourceLevelRequset req) {
		return cmSourceInfoNewBuss.getPrimarySourceName(req);
	}

}

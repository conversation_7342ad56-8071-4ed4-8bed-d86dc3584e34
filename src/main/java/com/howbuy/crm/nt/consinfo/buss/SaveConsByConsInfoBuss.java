package com.howbuy.crm.nt.consinfo.buss;

import java.util.HashMap;
import java.util.Map;

import com.howbuy.crm.nt.consinfo.dao.ConsInfoMapper;
import com.howbuy.crm.nt.consinfo.request.SaveConsInfoRequest;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SaveConsByConsInfoBuss {

	public static final Logger logger = LoggerFactory.getLogger(SaveConsByConsInfoBuss.class);
	/** 投顾特点描述最大长度 */
	public static final int CHARACTER_MAX_LENGTH = 800;
	/** 投顾头像URL地址最大长度 */
	public static final int PIC_ADDR_MAX_LENGTH = 800;
	
	@Autowired
	private ConsInfoMapper consInfoMapper;

	public BaseResponse saveConsByConsInfo(SaveConsInfoRequest req) {
		BaseResponse res = new BaseResponse();
		String conscode = req.getConscode();
		String character  = req.getCharacter();
		String picaddr = req.getPicaddr();
		if (StringUtils.isBlank(conscode)) {
			res.invalidReqParams("投顾号为空");
			return res;
		}
		if (StringUtils.isBlank(character)) {
			res.invalidReqParams("投顾特点为空");
			return res;
		}
		if (character.length() > CHARACTER_MAX_LENGTH) {
			res.invalidReqParams("投顾特点描述太多");
			logger.info("character too long:", character+" "+DateTime.now().toString());
			return res;
		}
		
		if (picaddr != null && picaddr.length() > PIC_ADDR_MAX_LENGTH) {
			res.invalidReqParams("投顾头像URL地址太长");
			logger.info("picaddr too long:", picaddr+" "+DateTime.now().toString());
			return res;
		}
		Map<String,Object> param = new HashMap<String,Object>();
		param.put("conscode", conscode);
		param.put("character", character);
		param.put("picaddr", picaddr);
		consInfoMapper.updateConsInfo(param);
		res.success();
		logger.info("updateconsinfo:", req.getConscode()+" "+DateTime.now().toString());
		return res;
		
	}

}

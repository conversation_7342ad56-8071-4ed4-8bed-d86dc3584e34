package com.howbuy.crm.nt.consinfo.service;

import com.howbuy.crm.nt.consinfo.buss.SaveConsByConsInfoBuss;
import com.howbuy.crm.nt.consinfo.request.SaveConsInfoRequest;
import crm.howbuy.base.dubbo.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("consInfoService")
public class ConsInfoServiceImpl implements ConsInfoService {
	
	@Autowired
	private SaveConsByConsInfoBuss saveConsByConsInfoBuss;

	@Override
	public BaseResponse saveConsByConsInfo(SaveConsInfoRequest req) {
		return saveConsByConsInfoBuss.saveConsByConsInfo(req);
	}

}

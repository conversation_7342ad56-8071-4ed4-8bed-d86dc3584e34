package com.howbuy.crm.nt.onwayaccount.service;

import com.howbuy.crm.nt.onwayaccount.buss.OnwayAccountCloseBuss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 在途账户关闭提示定时任务
 * <AUTHOR>
 * @date 2021/9/3 13:35
 */
@Slf4j
@Service("onwayAccountCloseScheduleService")
public class OnwayAccountCloseScheduleServiceImpl implements OnwayAccountCloseScheduleService {

    @Autowired
    private OnwayAccountCloseBuss onwayAccountCloseBuss;

    @Override
    public void execute(String arg) {
        log.info("=====================【在途账户关闭提示】----定时任务开始=====================");
        // 推送消息
        onwayAccountCloseBuss.pushNoEntryMsg();
        log.info("=====================【在途账户关闭提示】----定时任务结束=====================");
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.onwayaccount.dao.OnwayAccountCloseDao">

    <select id="findOnwayValidUsers" resultType="Map">
        select c.conscode,
            c.consname,
            to_char(months_between(sysdate, to_date(c.startdt, 'yyyymmdd'))) as numOfMonths,
            h.orgname
          from cm_consultant c
          left join hb_userrole hu
            on hu.usercode = c.conscode
          left join hb_organization h
            on c.outletcode = h.orgcode
         where hu.rolecode = 'ROLE_SIC_TEMP' <!-- 在途理财师 -->
           and c.consstatus = '1'
           and c.startdt is not null
           <!-- 当前时间 - 入职时间 = 5个月或6个月 -->
           and (months_between(sysdate, to_date(c.startdt, 'yyyymmdd')) = 5 or
               months_between(sysdate, to_date(c.startdt, 'yyyymmdd')) = 6)
    </select>

    <select id="checkDirectOrgIsTeam" parameterType="String" resultType="boolean">
        select count(*) from cm_consultant c where c.conscode = #{conscode} and c.teamcode is not null
    </select>

    <select id="findTeamHead" parameterType="String" resultType="String">
        select c.conscode
        from cm_consultant c
        left join hb_userrole hu
          on hu.usercode = c.conscode
        where c.consstatus = '1' and c.teamcode = (select teamcode from cm_consultant where conscode = #{conscode} and teamcode is not null)
        and hu.rolecode = 'ROLE_SIC_TEAM_HEAD'
    </select>

    <select id="findDirectDepSicHead" parameterType="String" resultType="String">
        select c.conscode
        from cm_consultant c
        left join hb_userrole hu
          on c.conscode = hu.usercode
        where c.consstatus = '1' and c.outletcode = (select outletcode from cm_consultant where conscode = #{conscode})
        and hu.rolecode = 'ROLE_SIC_HEAD'
    </select>

    <select id="findUpDepSicHead" parameterType="String" resultType="String">
        select c.conscode
        from cm_consultant c
        left join hb_userrole hu
          on c.conscode = hu.usercode
        where c.consstatus = '1' and c.outletcode = (
            select h.parentorgcode from hb_organization h
            where h.orgcode = (select outletcode from cm_consultant where conscode = #{conscode})
        )
        and hu.rolecode = 'ROLE_SIC_HEAD'
    </select>

    <select id="findUsersByRole" parameterType="String" resultType="String">
        select c.conscode
        from cm_consultant c
        left join hb_userrole hu
          on c.conscode = hu.usercode
        where c.consstatus = '1'
        <if test="param.roleCode != null">and hu.rolecode = #{param.roleCode}</if>
        <!-- 是否在同一中心 -->
        <if test="param.isSameCenter">and f_get_belong_center(c.conscode) = f_get_belong_center(#{param.conscode})</if>
    </select>

    <select id="queryConsultInfoByConscode" parameterType="String" resultType="map">
        select c.conscode,
               c.consname,
               h.orgname
        from cm_consultant c
        left join hb_organization h
        on c.outletcode = h.orgcode
        where c.conscode = #{conscode}
    </select>
</mapper>
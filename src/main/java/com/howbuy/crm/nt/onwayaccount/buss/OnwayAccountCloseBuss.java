package com.howbuy.crm.nt.onwayaccount.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.onwayaccount.dao.OnwayAccountCloseDao;
import com.howbuy.crm.nt.onwayaccount.domain.OnwayAccountCloseConstant;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.enums.CrmUserRoleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 在途账户关闭提示
 * <AUTHOR>
 * @date 2021/9/3 15:49
 */
@Component
@Slf4j
public class OnwayAccountCloseBuss {

    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Autowired
    private OnwayAccountCloseDao onwayAccountCloseDao;

    /**
     * 推送5个月/6个月未入职的消息
     */
    public void pushNoEntryMsg() {
        // 获取符合条件的在途账户
        List<Map<String, String>> onwayValidUsers = onwayAccountCloseDao.findOnwayValidUsers();
        if (CollectionUtils.isEmpty(onwayValidUsers)) {
            log.info("没有符合条件的在途账户");
            return;
        }

        for (Map<String, String> onwayValidUser : onwayValidUsers) {
            // 已入职几个月
            String numOfMonths = onwayValidUser.get("NUMOFMONTHS");
            String conscode = onwayValidUser.get("CONSCODE");

            Map<String, String> param = new HashMap<>();
            // 在途账户所在部门
            param.put("orgname", onwayValidUser.get("ORGNAME"));
            // 在途账户姓名
            param.put("consname", onwayValidUser.get("CONSNAME"));
            // 在途账户编码
            param.put("conscode", onwayValidUser.get("CONSCODE"));

            // 获取消息接收人
            List<String> messageReceiver = findMessageReceiver(conscode);

            OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum messageEnum;
            if ("5".equals(numOfMonths)) {
                // 入职5个月
                messageEnum = OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum.PUSH_MSG_FIVE_MONTH;
            } else {
                // 入职6个月
                messageEnum = OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum.PUSH_MSG_SIX_MONTH;
            }
            log.info("conscode--{} 【{}】，给{}推送通知，参数为：{}", conscode, messageEnum.getDescription(), messageReceiver, param);
            batchPushMsg(messageEnum, messageReceiver, param);
        }
    }

    /**
     * 推送账户关闭的消息
     */
    public void pushAccountCloseMsg(String conscode) {
        // 获取消息接收人
        List<String> messageReceiver = findMessageReceiver(conscode);
        Map<String, String> consultInfoMap = onwayAccountCloseDao.queryConsultInfoByConscode(conscode);

        Map<String, String> param = new HashMap<>();
        // 在途账户所在部门
        param.put("orgname", consultInfoMap.get("ORGNAME"));
        // 在途账户姓名
        param.put("consname", consultInfoMap.get("CONSNAME"));
        // 在途账户编码
        param.put("conscode", consultInfoMap.get("CONSCODE"));

        log.info("conscode--{} 【账户关闭】，给{}推送通知，参数为：{}", conscode, messageReceiver, param);
        batchPushMsg(OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum.PUSH_MSG_ACCOUNT_CLOSE, messageReceiver, param);
    }

    /**
     * 根据指定在途账户获取消息接收人
     * @param conscode 投顾编码
     * @return
     */
    private List<String> findMessageReceiver(String conscode) {
        List<String> resultList = new ArrayList<>();
        Set<String> tempSet = new HashSet<>();

        // 推送账户直属领导
        List<String> directLeader = findDirectLeader(conscode);

        Map<String, Object> param = new HashMap<>();
        // 角色包含“在途新人账号管理员”（ROLE_ZTXR_ADMIN）且与该账户属同一中心的用户
        param.put("roleCode", "ROLE_ZTXR_ADMIN");
        param.put("conscode", conscode);
        param.put("isSameCenter", true);
        List<String> ztxrUsers = onwayAccountCloseDao.findUsersByRole(param);

        // 角色包含“售后负责人”（ROLE_PS_HEAD）的用户
        param.clear();
        param.put("roleCode", CrmUserRoleEnum.ROLE_PS_HEAD.getCode());
        List<String> psLeaderUsers = onwayAccountCloseDao.findUsersByRole(param);

        // 将以上获取的所有人员先做去重处理，然后再返回
        tempSet.addAll(directLeader);
        tempSet.addAll(ztxrUsers);
        tempSet.addAll(psLeaderUsers);
        resultList.addAll(tempSet);
        return resultList;
    }

    /**
     * 批量发送消息
     * @param messageEnum
     * @param receivers
     * @param paramMap
     */
    private void batchPushMsg(OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum messageEnum, List<String> receivers, Map<String, String> paramMap) {
        for (String receiver : receivers) {
            pushMsgBase(messageEnum, receiver, paramMap);
        }
    }

    private void pushMsgBase(OnwayAccountCloseConstant.OnwayAccountCloseMessageEnum messageEnum, String receiver, Map<String, String> paramMap) {
        CmPushMsgRequest request = new CmPushMsgRequest();
        BaseResponse response;
        // 文字描述
        String description = messageEnum.getDescription();
        request.setBusinessId(messageEnum.getBusinessId());
        //1、PC端；2、微信
        // 不设置pushChannel，表示实际配置了什么模板，就发什么模板的消息
        // request.setPushChannel("1");
        //1、一账通号；2、投顾号
        request.setAccountType("2");
        // 设置消息接收人
        request.setAccount(receiver);
        // 设置消息参数
        request.setParamJson(JSON.toJSONString(paramMap));

        log.info("(调用推送【{}】通知)请求: {}", description, JSON.toJSONString(request));
        response = cmPushMsgService.pushMsg(request);
        log.info("(调用推送【{}】通知)回参: {}", description, JSON.toJSONString(response));

        if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
            log.info("推送【{}】通知成功:", description);
        } else{
            log.info("推送【{}】通知失败，失败原因:{}", description, null== response? "response为空": response.getDescription());
        }
    }

    /**
     * 获取账户直属领导
     * @param conscode 投顾编码
     * @return
     */
    private List<String> findDirectLeader(String conscode) {
        if (onwayAccountCloseDao.checkDirectOrgIsTeam(conscode)) {
            // 若该账户直属组织架构为团队，则取该团队角色包含“销售-团队长”（ROLE_SIC_TEAM_HEAD）的用户
            return onwayAccountCloseDao.findTeamHead(conscode);
        } else {
            // 否则，取该账户所在组织架构中包含“销售-区域总/分总”（ROLE_SIC_HEAD）的用户（若直属部门没有，则取上级部门）
            List<String> directDepSicHead = onwayAccountCloseDao.findDirectDepSicHead(conscode);
            if (CollectionUtils.isNotEmpty(directDepSicHead)) {
                return directDepSicHead;
            } else {
                return onwayAccountCloseDao.findUpDepSicHead(conscode);
            }
        }
    }
}

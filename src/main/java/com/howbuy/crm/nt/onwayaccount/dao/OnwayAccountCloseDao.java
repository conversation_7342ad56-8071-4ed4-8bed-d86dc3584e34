package com.howbuy.crm.nt.onwayaccount.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 在途账户关闭提示
 * <AUTHOR>
 * @date 2021/9/3 13:55
 */
public interface OnwayAccountCloseDao {

    /**
     * 获取符合条件的在途账户
     * 需要找的在途账户：角色包含“在途理财师”（ROLE_SIC_TEMP），且 当前时间 - 入职时间 = 5个月（推“5个月未入职”消息）或 = 6个月（推“6个月未入职”消息）
     * @return
     * map {"CONSCODE":"123213","CONSNAME":"张三","NUMOFMONTHS":"5","ORGNAME":"上海一部"}
     * CONSCODE为投顾编码，CONSNAME为投顾姓名，NUMOFMONTHS为该投顾入职了几个月，ORGNAME为投顾所在部门
     */
    List<Map<String,String>> findOnwayValidUsers();

    /**
     * 判断该账户直属组织架构是否为团队
     * @param conscode 投顾编码
     * @return
     */
    boolean checkDirectOrgIsTeam(String conscode);

    /**
     * 取该团队角色包含“销售-团队长”（ROLE_SIC_TEAM_HEAD）的用户
     * @param conscode 投顾编码
     * @return
     */
    List<String> findTeamHead(String conscode);

    /**
     * 取该账户所在组织架构中包含“销售-区域总/分总”（ROLE_SIC_HEAD）的用户（找直属部门的）
     * @param conscode 投顾编码
     * @return
     */
    List<String> findDirectDepSicHead(String conscode);

    /**
     * 取该账户所在组织架构中包含“销售-区域总/分总”（ROLE_SIC_HEAD）的用户（找上级部门的）
     * @param conscode 投顾编码
     * @return
     */
    List<String> findUpDepSicHead(String conscode);

    /**
     * 根据角色获取用户
     * @param param
     * @return
     */
    List<String> findUsersByRole(@Param("param") Map<String, Object> param);

    /**
     * 查询投顾信息
     * @param conscode 投顾编码
     * @return
     */
    Map<String, String> queryConsultInfoByConscode(String conscode);
}

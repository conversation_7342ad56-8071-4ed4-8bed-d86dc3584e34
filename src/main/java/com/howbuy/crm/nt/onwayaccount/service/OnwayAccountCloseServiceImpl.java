package com.howbuy.crm.nt.onwayaccount.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.onwayaccount.buss.OnwayAccountCloseBuss;
import com.howbuy.crm.nt.onwayaccount.request.OnwayAccountCloseRequest;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 在途账户关闭提示
 * <AUTHOR>
 * @date 2021/9/6 13:51
 */
@Slf4j
@Service("onwayAccountCloseService")
public class OnwayAccountCloseServiceImpl implements OnwayAccountCloseService {

    @Autowired
    private OnwayAccountCloseBuss onwayAccountCloseBuss;

    @Override
    public BaseResponse execute(OnwayAccountCloseRequest request) {
        String conscode = request.getConscode();
        BaseResponse response = new BaseResponse();
        try {
            if (validate(request, response)) {
                onwayAccountCloseBuss.pushAccountCloseMsg(conscode);
                response.success();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.putBaseResult(BaseConstantEnum.FAIL, "操作失败");
        }
        log.info(JSON.toJSONString(response));
        return response;
    }

    private boolean validate(OnwayAccountCloseRequest request, BaseResponse response) {
        boolean flag = true;
        StringBuilder stringBuilder = new StringBuilder();
        String conscode = request.getConscode();
        if (StringUtils.isBlank(conscode)) {
            stringBuilder.append("投顾号为空");
            flag = false;
        }

        if (!flag) {
            response.invalidReqParams(stringBuilder.toString());
        }
        return flag;
    }
}

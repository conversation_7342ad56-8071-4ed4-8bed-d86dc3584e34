<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.cmsyncorder.dao.CmZtSyncOrderMapper">
    <insert id="insertCmZtSyncOrderLog">
        INSERT INTO CM_ZT_SYNC_ORDER_LOG
            (DEALNO, TXACCTNO, USEFLAG, CREDT)
        SELECT T.DEALNO, T.TXACCTNO, T.USEFLAG, T.CREDT
        FROM CM_ZT_SYNC_ORDER T
    </insert>

    <update id="cleanCmZtSyncOrder">
        TRUNCATE TABLE CM_ZT_SYNC_ORDER
    </update>

    <insert id="insertCmZtSyncOrder">
        INSERT INTO CM_ZT_SYNC_ORDER
            (DEALNO, TXACCT<PERSON>O, TXPMTFLAG, PMTCOMPFLAG)
        SELECT T.DE<PERSON>, T.TXACCTNO, T.TXPMTFLAG, T.PM<PERSON>OM<PERSON>AG
        FROM CM_ZT_ORDERINFO T
        WHERE T.ORDERSTATUS = '1'
          AND T.APPOINTMENTDEALNO IS NOT NULL
        ORDER BY T.DEALDT ASC
    </insert>
</mapper>
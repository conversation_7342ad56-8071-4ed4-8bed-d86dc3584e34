package com.howbuy.crm.nt.cmsyncorder.dao;

public interface CmZtSyncOrderMapper {


    /**
     * @description:(备份中台订单每晚对账表的数据到对应的log表中)
     * @param
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 14:25
     * @since JDK 1.8
     */
    int insertCmZtSyncOrderLog();


    /**
     * @description:(清除中台每晚的对账信息表)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 14:25
     * @since JDK 1.8
     */
    void cleanCmZtSyncOrder();


    /**
     * @description:(从中台订单数据最新表 更新数据到中台订单每晚对账表中)
     * @param
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 14:24
     * @since JDK 1.8
     */
    int insertCmZtSyncOrder();


}
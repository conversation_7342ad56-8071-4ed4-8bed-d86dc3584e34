/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.cmsyncorder.service;

import com.howbuy.crm.nt.cmsyncorder.dao.CmZtSyncOrderMapper;
import com.howbuy.crm.nt.cmztsync.service.CmZtSyncOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (CRM预约与中台订单每日对账)
 * <AUTHOR>
 * @date 2023/6/6 11:26
 * @since JDK 1.8
 */
@Service("cmZtSyncOrderService")
@Slf4j
public class CmZtSyncOrderServiceImpl implements CmZtSyncOrderService {

    @Autowired
    private CmZtSyncOrderMapper cmZtSyncOrderMapper;

    /**
     * @description:(CRM预约与中台订单每日对账)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/06/01 11:28
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealZtSyncOrder() {
        try {
            // 1.备份中台订单每晚对账表的数据到对应的log表中
            cmZtSyncOrderMapper.insertCmZtSyncOrderLog();
            // 2.清除中台每晚的对账信息表
            cmZtSyncOrderMapper.cleanCmZtSyncOrder();
            // 3. 从中台订单数据最新表 更新数据到中台订单每晚对账表中
            cmZtSyncOrderMapper.insertCmZtSyncOrder();
        } catch (Exception e) {
            log.error("dealZtSyncOrder error is ", e);
        }
    }
}
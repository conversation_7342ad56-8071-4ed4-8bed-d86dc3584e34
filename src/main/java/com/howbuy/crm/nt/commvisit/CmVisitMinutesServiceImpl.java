package com.howbuy.crm.nt.commvisit;

import com.howbuy.crm.nt.outerservice.commvisit.CmVisitMinutesOuterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/29 19:54
 * @since JDK 1.8
 */
@Service
public class CmVisitMinutesServiceImpl implements CmVisitMinutesService {

    @Autowired
    private CmVisitMinutesOuterService cmVisitMinutesOuterService;

    /**
     * @description 推送未反馈的访问记录
     * @param curPreDayParam 当前日期前N天的日期参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/29 20:02
     * @since JDK 1.8
     */
    public void execPushNoFeedback(String curPreDayParam) {
        cmVisitMinutesOuterService.pushNoFeedback(curPreDayParam);
    }
}
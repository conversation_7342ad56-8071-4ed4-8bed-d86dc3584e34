<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.system.dao.SystemDataBaseMapper">
    
    
    <select id="selectSubNameAsPnum1ByAcFundHis" resultType="String">
        select subname
        from (select owner,
                     table_name,
                     subname,
                     a1||a2||a3||a4||a5||a6||a7||a8 as date1
              from (SELECT u.name owner,
                           o.name table_name,
                           o.subname,
                           tp.part#,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 3, 2), 'XX'))  a1,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 5, 2), 'XX'))  a2,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 7, 2), 'XX')) a3,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 9, 2), 'XX')) a4,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 11, 2), 'XX'))  a5,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 13, 2), 'XX'))  a6,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 15, 2), 'XX'))  a7,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 17, 2), 'XX'))  a8
                    from sys.tabpart$ tp, sys.obj$ o, sys.user$ u
                    where tp.obj# = o.obj#
                      and o.owner# = u.user#
                      and o.name = 'SYNC_AC_FUND_ACCT_BAL_HIS'
                      and u.name = 'CUST')
              order by part#)
    </select>

    <select id="selectSubNameAsPnum2ByAcDisFundHis" resultType="String">
        select subname
        from (select owner,
                     table_name,
                     subname,
                     a1||a2||a3||a4||a5||a6||a7||a8 as date1
              from (SELECT u.name owner,
                           o.name table_name,
                           o.subname,
                           tp.part#,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 3, 2), 'XX'))  a1,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 5, 2), 'XX'))  a2,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 7, 2), 'XX')) a3,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 9, 2), 'XX')) a4,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 11, 2), 'XX'))  a5,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 13, 2), 'XX'))  a6,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 15, 2), 'XX'))  a7,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 17, 2), 'XX'))  a8
                    from sys.tabpart$ tp, sys.obj$ o, sys.user$ u
                    where tp.obj# = o.obj#
                      and o.owner# = u.user#
                      and o.name = 'SYNC_AC_DIS_FUND_ACCT_BAL_HIS'
                      and u.name = 'CUST')
              order by part#)
    </select>

    <select id="selectSubNameAsPnum3ByAcPiggyHis" resultType="String">
        select subname
        from (select owner,
                     table_name,
                     subname,
                     a1||a2||a3||a4||a5||a6||a7||a8 as date1
              from (SELECT u.name owner,
                           o.name table_name,
                           o.subname,
                           tp.part#,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 3, 2), 'XX'))  a1,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 5, 2), 'XX'))  a2,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 7, 2), 'XX')) a3,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 9, 2), 'XX')) a4,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 11, 2), 'XX'))  a5,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 13, 2), 'XX'))  a6,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 15, 2), 'XX'))  a7,
                           chr(TO_NUMBER(SUBSTR(RAWTOHEX(CAST(bhiboundval as raw(9))), 17, 2), 'XX'))  a8
                    from sys.tabpart$ tp, sys.obj$ o, sys.user$ u
                    where tp.obj# = o.obj#
                      and o.owner# = u.user#
                      and o.name = 'SYNC_AC_PIGGY_VOL_HIS'
                      and u.name = 'CUST')
              order by part#)
    </select>



    <update id="truncateAcFundHis" parameterType="String">
        ALTER TABLE SYNC_AC_FUND_ACCT_BAL_HIS
        truncate PARTITION #{pNum1}
        update global indexes
    </update>

    <update id="truncateAcDisFundHis" parameterType="String">
        ALTER TABLE SYNC_AC_DIS_FUND_ACCT_BAL_HIS
        truncate PARTITION #{pNum2}
        update global indexes
    </update>

    <update id="truncateAcPiggyHis" parameterType="String">
        ALTER TABLE SYNC_AC_PIGGY_VOL_HIS
        truncate PARTITION #{pNum3}
        update global indexes
    </update>

</mapper>
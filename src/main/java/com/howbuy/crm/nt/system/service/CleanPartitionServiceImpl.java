/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.system.service;

import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.nt.system.dao.SystemDataBaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/8/17 09:05
 * @since JDK 1.8
 */
@Service("cleanPartitionService")
@Slf4j
public class CleanPartitionServiceImpl implements CleanPartitionService{
    @Autowired
    private SystemDataBaseMapper systemDataBaseMapper;

    /**
     * @description:(crm_clear_partition_his 删除数据历史分区的业务逻辑处理
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/17 09:43
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanPartition() {
        // 查询出SYNC_AC_FUND_ACCT_BAL_HIS分区
        String pNum1 = systemDataBaseMapper.selectSubNameAsPnum1ByAcFundHis();
        // 查询出SYNC_AC_DIS_FUND_ACCT_BAL_HIS分区
        String pNum2 = systemDataBaseMapper.selectSubNameAsPnum2ByAcDisFundHis();
        // 查询出SYNC_AC_PIGGY_ACCT_BAL_HIS分区
        String pNum3 = systemDataBaseMapper.selectSubNameAsPnum3ByAcPiggyHis();
        // 清除SYNC_AC_FUND_ACCT_BAL_HIS的历史分区
        systemDataBaseMapper.truncateAcFundHis(pNum1);
        // 清除SYNC_AC_DIS_FUND_ACCT_BAL_HIS的历史分区
        systemDataBaseMapper.truncateAcDisFundHis(pNum2);
        // 清除SYNC_AC_PIGGY_ACCT_BAL_HIS的历史分区
        systemDataBaseMapper.truncateAcPiggyHis(pNum3);
    }

    /**
     * @description:(获取对应的分区时间)
     * @param
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2023/08/09 10:43
     * @since JDK 1.8
     */
    public String getDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -4);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH) + 1);
        Date date = calendar.getTime();
        return DateUtil.formatToString(date, DateUtil.YYYYMMDD);
    }




}
package com.howbuy.crm.nt.system.dao;

import org.apache.ibatis.annotations.Param;

/**
 * @description:(处理删除oracle 几个表的历史分区mapper)
 * @return
 * @author: xufanchao
 * @date: 2023/8/17 09:01
 * @since JDK 1.8
 */
public interface SystemDataBaseMapper {

    /**
     * @description:(查询出AC_FUND_HIS表的历史分区)
     * @param
     * @return java.lang.String
     * @author: xufancha<PERSON>
     * @date: 2023/8/17 08:58
     * @since JDK 1.8
     */
    String selectSubNameAsPnum1ByAcFundHis();

    /**
     * @description:(查出SYNC_AC_DIS_FUND_ACCT_BAL_HIS 的历史分区)
     * @param
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2023/8/17 09:35
     * @since JDK 1.8
     */
    String selectSubNameAsPnum2ByAcDisFundHis();

    /**
     * @description:(请在此添加描述)
     * @param 	
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2023/8/17 09:43
     * @since JDK 1.8
     */
    String selectSubNameAsPnum3ByAcPiggyHis();

    /**
     * @description:(清除SYNC_AC_FUND_ACCT_BAL_HIS的历史分区)
     * @param pNum1
     * @return void
     * @author: xufanchao
     * @date: 2023/8/17 09:33
     * @since JDK 1.8
     */
    void truncateAcFundHis(@Param("pNum1") String pNum1);

    /**
     * @description:(清除 SYNC_AC_DIS_FUND_ACCT_BAL_HIS表的历史分区)
     * @param pNum2
     * @return void
     * @author: xufanchao
     * @date: 2023/8/17 09:33
     * @since JDK 1.8
     */
    void truncateAcDisFundHis(@Param("pNum2") String pNum2);

    /**
     * @description:(请在此添加描述)
     * @param pNum3
     * @return void
     * @author: xufanchao
     * @date: 2023/8/17 09:33
     * @since JDK 1.8
     */
    void truncateAcPiggyHis(@Param("pNum3") String pNum3);
}

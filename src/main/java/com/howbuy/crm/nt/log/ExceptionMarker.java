/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.log;

import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

/**
 * @description: 告警日志异常标记
 * <AUTHOR>
 * @date 2023/6/7 11:15
 * @since JDK 1.8
 */
public class ExceptionMarker {

    private ExceptionMarker(){}
    /**
     * 调用外部系统错误或者超时报警key
     */
    public static final String COMMON_CALL_OUTSERVICE_ERROR_KEY = "nt.commom.callOuterServiceError";
    /**
     * CRM系统错误报警key
     */
    public static final String SYSTEM_ERROR_KEY = "crm.nt.SystemError";

    /**
     * @description: 获取日志答应Marker
     * @param logErrorKey
     * @return org.slf4j.Marker
     * @author: hongdong.xie
     * @date: 2023/6/7 11:20
     * @since JDK 1.8
     */
    public static Marker getMarker(String logErrorKey) {
        return MarkerFactory.getMarker(logErrorKey);
    }
}
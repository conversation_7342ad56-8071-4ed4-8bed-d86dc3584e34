package com.howbuy.crm.nt.associationmail.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailLogBuss;
import com.howbuy.crm.nt.associationmail.dto.AssociationExpMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.dto.ResolveMailLog;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.ExpStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.HandleStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.InfoStatusEnum;
import com.howbuy.crm.nt.associationmail.model.MailTaskContext;
import com.howbuy.crm.nt.associationmail.model.MailTaskParam;
import com.howbuy.crm.nt.associationmail.util.ReadEmailUtil;
import com.howbuy.crm.nt.associationmail.util.RegexUtil;
import com.sun.mail.imap.IMAPFolder;
import crm.howbuy.base.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.MultiValueMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import java.text.ParseException;
import java.util.*;

/**
 * 从邮箱中读取并解析邮件内容
 * <AUTHOR>
 */
@Slf4j
@Service("associationMailService")
public class AssociationMailServiceImpl implements AssociationMailService {

    public static final ThreadLocal<MailTaskContext> CONTEXT_HOLDER = new ThreadLocal<MailTaskContext>(){
        @Override
        protected MailTaskContext initialValue() {
            return new MailTaskContext();
        }
    };

    public static MailTaskContext getMailTaskContext() {
        return CONTEXT_HOLDER.get();
    }

    public static void clearMailTaskContext() {
        CONTEXT_HOLDER.remove();
    }

    @Autowired
    private AssociationMailBuss associationMailBuss;

    @Autowired
    private AssociationMailLogBuss associationMailLogBuss;

    @Value("${associationmail.imap.username}")
    private String imapMailUsername;

    @Value("${associationmail.imap.password}")
    private String imapMailPassword;

    @Override
    public void handleMailContent(String arg) {
        Store store = null;
        IMAPFolder inbox = null;
        try {
            // 解析任务参数
            MailTaskParam mailTaskParam = resolveTaskParam(arg);
            // 邮件解析上下文
            MailTaskContext mailTaskContext = getMailTaskContext();
            mailTaskContext.setMailTaskParam(mailTaskParam);

            // 获取连接
            Properties props = ReadEmailUtil.prepareSmtpProperties(AssociationMailConstant.IMAP_MAIL_HOST);
            Session session = Session.getInstance(props);
            session.setDebug(false);

            // 获取所有已经解析过的邮件的唯一标识的集合
            Set<String> allExistMailUid = associationMailBuss.queryAllExistMailUid();
            mailTaskContext.setAllExistMailUid(allExistMailUid);

            // 获取Store对象
            store = session.getStore(AssociationMailConstant.IMAP_MAIL_PROTOCOL);
            store.connect(AssociationMailConstant.IMAP_MAIL_HOST, imapMailUsername, imapMailPassword);
            inbox = (IMAPFolder)store.getFolder("INBOX");
            mailTaskContext.setInboxFolder(inbox);

            //如果需要在取得邮件数后将邮件置为已读则这里需要使用READ_WRITE,否则READ_ONLY就可以
            inbox.open(Folder.READ_ONLY);

            ResolveMailLog resolveMailLog = mailTaskContext.getResolveMailLog();
            // 全部邮件数
            int messageCount = inbox.getMessageCount();
            log.info("实际邮件总数：{}", messageCount);
            resolveMailLog.setTotalNum(messageCount);

            List<Message> messageList;
            Message[] message = inbox.getMessages();

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("过滤邮件");
            messageList = filterMail(mailTaskContext, message);
            resolveMailLog.setValidNum(messageList.size());
            stopWatch.stop();

            stopWatch.start("解析邮件");
            // 正常解析的邮件
            List<AssociationMail> normalAssociationMailList = new ArrayList<>();
            // 异常解析的邮件
            List<AssociationExpMail> exceptionAssociationMailList = new ArrayList<>();
            // 内容重复的邮件
            Map<String, AssociationExpMail> contentRepeatMail = new MultiValueMap();
            // 用来验证邮件数据是否重复的set
            Set<String> set = new HashSet<>();

            // 解析邮件内容
            resolveMailContent(messageList, normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, mailTaskContext);
            stopWatch.stop();

            stopWatch.start("插入数据");
            // 插入数据
            associationMailBuss.insertResolvedData(normalAssociationMailList, exceptionAssociationMailList);
            stopWatch.stop();

            int normalResolveNum = normalAssociationMailList.size();
            int expResolveNum = exceptionAssociationMailList.size();
            int repeatResolveNum = contentRepeatMail.values().size();
            log.info("正常解析数据：{}条", normalResolveNum);
            log.info("异常解析数据：{}条", expResolveNum);
            log.info("重复邮件详情：{}条 {}", repeatResolveNum, contentRepeatMail);
            resolveMailLog.setNormalResolveNum(normalResolveNum);
            resolveMailLog.setExpResolveNum(expResolveNum);
            resolveMailLog.setRepeatResolveNum(repeatResolveNum);

            long elapsedTime = stopWatch.getTotalTimeMillis();
            log.info("stopWatch.getTotalTimeMillis():{}", elapsedTime);
            log.info("stopWatch.prettyPrint():{}", stopWatch.prettyPrint());
            resolveMailLog.setElapsedTime(elapsedTime);
            // 插入日志统计数据
            associationMailLogBuss.insertResolveMailLog(resolveMailLog);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        } finally {
            clearMailTaskContext();
            try {
                if (inbox != null) {
                    inbox.close(true);
                }
            } catch (MessagingException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
            try {
                if (store != null) {
                    store.close();
                }
            } catch (MessagingException e) {
                log.error(e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 解析任务参数
     * @param arg 原始的定时任务参数
     * @return
     */
    private MailTaskParam resolveTaskParam(String arg) {
        // 解析任务参数
        JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");

        String handleType = taskParam == null ? null : (String) taskParam.get("handleType");
        String startDateStr = taskParam == null ? null : (String) taskParam.get("startDateStr");
        String endDateStr = taskParam == null ? null : (String) taskParam.get("endDateStr");
        String fromMailStr = taskParam == null ? null : (String) taskParam.get("fromMailStr");

        return MailTaskParam.builder()
                .handleType(handleType)
                .startDateStr(startDateStr)
                .endDateStr(endDateStr)
                .fromMailStr(fromMailStr)
                .build();
    }

    /**
     * 邮件过滤
     * @param mailTaskContext
     * @return
     */
    private List<Message> filterMail(MailTaskContext mailTaskContext, Message[] message) throws MessagingException, ParseException {
        MailTaskParam mailTaskParam = mailTaskContext.getMailTaskParam();
        String handleType = mailTaskParam.getHandleType();
        String startDateStr = mailTaskParam.getStartDateStr();
        String endDateStr = mailTaskParam.getEndDateStr();
        String fromMailStr = mailTaskParam.getFromMailStr();
        IMAPFolder inboxFolder = (IMAPFolder) mailTaskContext.getInboxFolder();
        Set<String> allExistMailUid = mailTaskContext.getAllExistMailUid();
        List<Message> completeRepeatMail = mailTaskContext.getCompleteRepeatMail();
        // 用于比较的收件email
        String compareFromMail = StringUtils.defaultIfEmpty(fromMailStr, "<EMAIL>");
        // 过滤条件
        List<Message> messageList;
        if (AssociationMailConstant.HANDLE_TYPE_ALL.equals(handleType)) {
            messageList = ReadEmailUtil.filterMessage(message, m -> {
                String from;
                try {
                    // 排除掉已经解析过的邮件
                    String uidStr = inboxFolder.getUID(m) + "";
                    if (allExistMailUid.contains(uidStr)) {
                        completeRepeatMail.add(m);
                        return false;
                    }

                    ReadEmailUtil pmm = new ReadEmailUtil((MimeMessage) m);
                    from = pmm.getFrom();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return compareFromMail.equals(from);
            });
            log.info("完全重复的邮件数量：{}", completeRepeatMail.size());
            log.info("符合条件 收件人为{}的邮件总数：{}", compareFromMail, messageList.size());
        } else {
            // 筛选指定时间段的Message
            Date endDate;
            Date startDate;
            if (StrUtil.hasBlank(startDateStr, endDateStr)) {
                endDate = new Date();
                // 向前推25个小时
                startDate = DateUtils.addHours(endDate, -25);
            } else {
                endDate = DateUtils.parseDate(endDateStr, "yyyy-MM-dd HH:mm:ss");
                startDate = DateUtils.parseDate(startDateStr, "yyyy-MM-dd HH:mm:ss");
            }

            messageList = ReadEmailUtil.filterMessage(message, m -> {
                Date sentDate;
                String from;
                try {
                    // 排除掉已经解析过的邮件
                    String uidStr = inboxFolder.getUID(m) + "";
                    if (allExistMailUid.contains(uidStr)) {
                        completeRepeatMail.add(m);
                        return false;
                    }

                    ReadEmailUtil pmm = new ReadEmailUtil((MimeMessage) m);
                    from = pmm.getFrom();
                    sentDate = m.getSentDate();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                return compareFromMail.equals(from) && (sentDate.compareTo(endDate) <= 0) && (sentDate.compareTo(startDate) >= 0);
            });
            log.info("完全重复的邮件数量：{}", completeRepeatMail.size());
            log.info("符合条件 收件人为{} 且时间范围在 {}-{}的邮件总数：{}",
                    compareFromMail,
                    DateUtil.getDateFormat(startDate,"yyyy-MM-dd HH:mm:ss"),
                    DateUtil.getDateFormat(startDate,"yyyy-MM-dd HH:mm:ss"),
                    messageList.size());
        }
        return messageList;
    }

    /**
     * 解析邮件内容
     * @param messageList
     * @param normalAssociationMailList
     * @param exceptionAssociationMailList
     * @param contentRepeatMail
     * @param set
     * @throws Exception
     */
    private void resolveMailContent(List<Message> messageList, List<AssociationMail> normalAssociationMailList, List<AssociationExpMail> exceptionAssociationMailList,
                                    Map<String, AssociationExpMail> contentRepeatMail, Set<String> set, MailTaskContext mailTaskContext) throws Exception {
        IMAPFolder inboxFolder = (IMAPFolder) mailTaskContext.getInboxFolder();
        for (int i = 0; i < messageList.size(); i++) {
            Message message = messageList.get(i);
            ReadEmailUtil pmm = new ReadEmailUtil((MimeMessage) message);
            String from = pmm.getFrom();
            StringBuffer bodyText = new StringBuffer();
            ReadEmailUtil.getMailTextContent(message, bodyText);

            // 过滤邮件内容中的所有html标签
            String newBodyText = RegexUtil.filterHtml(bodyText);
            newBodyText = StringUtils.replace(newBodyText, "\ufeff", "");
            String sentDate = pmm.getSentDate();
            // 解析邮件内容
            String custName = RegexUtil.matchText(AssociationMailConstant.CUSTNAME_REG, newBodyText);
            String orgName = RegexUtil.matchText(AssociationMailConstant.ORG_NAME_REG, newBodyText);
            String investorAccount = RegexUtil.matchText(AssociationMailConstant.INVESTOR_ACCOUNT_REG, newBodyText);
            String initPassword = RegexUtil.matchText(AssociationMailConstant.INIT_PASSWORD_REG, newBodyText);
            String managerRegno = RegexUtil.matchText(AssociationMailConstant.MANAGER_REGNO_REG, newBodyText);
            String loginHref = RegexUtil.matchText(AssociationMailConstant.LOGIN_HREF_REG, newBodyText);

            String subject = pmm.getSubject();
            String toMail = pmm.getMailAddress("TO");
            String mailUid = inboxFolder.getUID(message) + "";

            // 解析异常的邮件
            if (StrUtil.hasBlank(sentDate, from, custName, orgName, investorAccount, initPassword, managerRegno, loginHref)) {
                AssociationExpMail ae = new AssociationExpMail();
                ae.setCreator("sys");
                ae.setMailDateStr(sentDate);
                ae.setSourceMail(from);
                ae.setCustName(custName);
                ae.setOrgName(orgName);
                ae.setInvestorAccount(investorAccount);
                ae.setInitPassword(initPassword);
                ae.setManagerRegno(managerRegno);
                ae.setLoginHref(loginHref);
                ae.setSubject(subject);
                ae.setToMail(toMail);
                ae.setMailUid(mailUid);
                ae.setHandleStatus(HandleStatusEnum.NEED_HANDLE.getCode());
                ae.setExpStatus(ExpStatusEnum.RESOLVE_EXP.getCode());
                exceptionAssociationMailList.add(ae);
            } else {
                String key = StringUtils.join(new String[]{custName, orgName, investorAccount, managerRegno}, "#");
                Map<String, String> paramMap = new HashMap<String, String>(4){{
                    put("custName", custName);
                    put("orgName", orgName);
                    put("investorAccount", investorAccount);
                    put("managerRegno", managerRegno);
                }};
                boolean addSuccess = set.add(key);
                if (!addSuccess || associationMailBuss.existAssociationMail(paramMap)) {
                    // 内容重复的邮件
                    AssociationExpMail ae = new AssociationExpMail();
                    ae.setCreator("sys");
                    ae.setMailDateStr(sentDate);
                    ae.setSourceMail(from);
                    ae.setCustName(custName);
                    ae.setOrgName(orgName);
                    ae.setInvestorAccount(investorAccount);
                    ae.setInitPassword(initPassword);
                    ae.setManagerRegno(managerRegno);
                    ae.setLoginHref(loginHref);
                    ae.setSubject(subject);
                    ae.setToMail(toMail);
                    ae.setMailUid(mailUid);
                    ae.setHandleStatus(HandleStatusEnum.NEED_HANDLE.getCode());
                    ae.setExpStatus(ExpStatusEnum.REPEAT_MAIL.getCode());
                    exceptionAssociationMailList.add(ae);
                    contentRepeatMail.put(key, ae);
                } else {
                    // 正常邮件
                    AssociationMail am = new AssociationMail();
                    am.setCreator("sys");
                    am.setMailDateStr(sentDate);
                    am.setSourceMail(from);
                    am.setCustName(custName);
                    am.setOrgName(orgName);
                    am.setInvestorAccount(investorAccount);
                    am.setInitPassword(initPassword);
                    am.setManagerRegno(managerRegno);
                    am.setLoginHref(loginHref);
                    am.setSubject(subject);
                    am.setToMail(toMail);
                    am.setMailUid(mailUid);
                    am.setInfoStatus(InfoStatusEnum.NEED_MATCH.getCode());
                    normalAssociationMailList.add(am);
                }
            }
        }
    }
}

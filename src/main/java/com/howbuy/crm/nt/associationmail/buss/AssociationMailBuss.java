package com.howbuy.crm.nt.associationmail.buss;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.crm.nt.associationmail.dao.AssociationMailDao;
import com.howbuy.crm.nt.associationmail.dto.*;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.BDPMatchStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.ExpStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.HandleStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.InfoStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/4/2 15:41
 */
@Component
@Slf4j
public class AssociationMailBuss {

    @Autowired
    private AssociationMailDao associationMailDao;

    @Autowired
    private AssociationMailLogBuss associationMailLogBuss;

    public static final int DEV_INFO_MAX_LENGTH = 1500;

    /**
     * 查询解析后的邮件内容列表
     * @return
     */
    public List<AssociationMail> queryAssociationMailList() {
        return associationMailDao.queryAssociationMailList();
    }

    /**
     * 插入邮件解析数据
     * @param associationMail
     */
    public void insertAssociationMail(AssociationMail associationMail) {
        associationMailDao.insertAssociationMail(associationMail);
    }

    /**
     * 修改邮件解析数据
     * @param associationMail
     */
    public void updateAssociationMail(AssociationMail associationMail) {
        associationMailDao.updateAssociationMail(associationMail);
    }

    /**
     * 该邮件是否已经解析过
     * @param param
     */
    public boolean existAssociationMail(Map<String, String> param) {
        return associationMailDao.existAssociationMail(param);
    }

    /**
     * 查询解析异常的邮件内容列表
     * @return
     */
    public List<AssociationMail> queryAssociationExpList() {
        return associationMailDao.queryAssociationExpList();
    }

    /**
     * 插入邮件解析异常数据
     * @param associationExpMail
     */
    public void insertAssociationExp(AssociationExpMail associationExpMail) {
        associationMailDao.insertAssociationExp(associationExpMail);
    }

    /**
     * 插入解析后的数据
     * @param normalAssociationMailList 正常解析的数据
     * @param exceptionAssociationMailList 异常解析的数据
     */
    public void insertResolvedData(List<AssociationMail> normalAssociationMailList, List<AssociationExpMail> exceptionAssociationMailList) throws ParseException {
        // 正常解析的数据
        for (AssociationMail normalAssociationMail : normalAssociationMailList) {
            associationMailDao.insertAssociationMail(normalAssociationMail);
        }

        // 解析出现问题的数据
        for (AssociationExpMail exceptionAssociationMail : exceptionAssociationMailList) {
            associationMailDao.insertAssociationExp(exceptionAssociationMail);
            String expId = exceptionAssociationMail.getId();

            // 重复邮件数据需要特殊处理
            if (ExpStatusEnum.REPEAT_MAIL.getCode().equals(exceptionAssociationMail.getExpStatus())) {
                AssociationMail existNormalMail = associationMailDao.findExistRepeatMail(exceptionAssociationMail);
                // 最终用于合并的正常数据
                AssociationMail mergeNormalMail = new AssociationMail();
                // 正常邮件的邮件日期
                Date normalMailDate = existNormalMail.getMailDate();
                // 正常邮件的信息状态
                String normalInfoStatus = existNormalMail.getInfoStatus();
                // 重复邮件的邮件日期
                Date repeatMailDate = DateUtils.parseDate(exceptionAssociationMail.getMailDateStr(), "yyyy-MM-dd HH:mm:ss");
                AssociationExpOverRideMail associationExpOverRideMail = new AssociationExpOverRideMail();
                //覆盖备份表   异常表ID
                associationExpOverRideMail.setExpid(expId);
                associationExpOverRideMail.setId(existNormalMail.getId());
                if (repeatMailDate.after(normalMailDate)
                        && StringUtils.equalsAny(normalInfoStatus, InfoStatusEnum.NEED_MATCH.getCode(), InfoStatusEnum.NEED_SEND_MESSAGE.getCode(), InfoStatusEnum.NEED_RESEND_MESSAGE.getCode())) {
                    // 若后解析的邮件时间>老数据的邮件时间，并且老数据的状态=0-待匹配 or 1-待发短信的 or 2-待重发短信
                    // 则覆盖正常表的老数据；信息状态不变（和老数据保持一致）
                    BeanUtils.copyProperties(exceptionAssociationMail, mergeNormalMail);
                    mergeNormalMail.setInfoStatus(normalInfoStatus);
                    mergeNormalMail.setId(existNormalMail.getId());
                    mergeNormalMail.setModifier("sys");
                    associationMailDao.insertAssociationExpOverRideMail(associationExpOverRideMail);

                    associationMailDao.updateAssociationMail(mergeNormalMail);
                } else if (repeatMailDate.after(normalMailDate)
                        && StringUtils.equalsAny(normalInfoStatus, InfoStatusEnum.HAS_SEND_MESSAGE.getCode(), InfoStatusEnum.SEND_FAIL.getCode())) {
                    // 若后解析的邮件时间>老数据的邮件时间，并且老数据状态=3-已发短信 or 4-短信发送失败，
                    // 新邮件的【初始密码】和crm本地数据的【初始密码】是否一致
                    // 如果不一致，则覆盖正常表的老数据，信息状态修改为2-待重发短信
                    String newInitPassword = exceptionAssociationMail.getInitPassword();
                    String oldInitPassword = existNormalMail.getInitPassword();
                    if (!StringUtils.equals(newInitPassword, oldInitPassword)) {
                        BeanUtils.copyProperties(exceptionAssociationMail, mergeNormalMail);
                        mergeNormalMail.setInfoStatus(InfoStatusEnum.NEED_RESEND_MESSAGE.getCode());
                        mergeNormalMail.setId(existNormalMail.getId());
                        mergeNormalMail.setModifier("sys");
                        associationMailDao.insertAssociationExpOverRideMail(associationExpOverRideMail);

                        associationMailDao.updateAssociationMail(mergeNormalMail);
                    }
                } else if (repeatMailDate.compareTo(normalMailDate) <= 0) {
                    // 若后解析的邮件时间 <=老数据的邮件时间
                }
            }

        }
    }

    /**
     * 批量处理BDP的匹配数据
     * @param list
     */
    public void batchHandleMatchData(List<Map> list) {
        BdpMatchLog bdpMatchLog = new BdpMatchLog();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("处理BDP匹配数据");

        // 记录跳过了几条数据（记录BDP推送过来的错误数据条数）
        int skipNum = 0;
        List<String> skipIdList = new ArrayList<>();
        // 记录第几条数据出错了
        boolean hasError = false;
        int errorNum = 0;
        // 按正常流程进到表里的数据（可能是只更新正常表，也可能是更新正常表并插到同名表）
        int actualNormalDataCount = 0;
        // 数据异常且插到表里的数据（其他异常）
        int actualAbnormalDataCount = 0;
        // 数据异常，未插到表里的数据
        int actualAbnormalStatusDataCount = 0;

        Map<String, Integer> statisticsMap = new HashMap<>();
        try {
            // 统计BDP推过来的数据
            statisticsMap = calculateBDPData(list);
            if(CollectionUtils.isNotEmpty(list)){
                for(Map map :list){
                    ++errorNum;
                    String checkResult = (String) map.get("check_result");
                    String conscustno = (String) map.get("conscustno");
                    Integer id = (Integer) map.get("id");
                    String idStr = (id == null) ? null : String.valueOf(id);
                    AssociationMail existMail = associationMailDao.findAssociationMailById(idStr);

                    // 跳过BDP的错误数据
                    if (existMail == null) {
                        ++skipNum;
                        skipIdList.add(idStr);
                        continue;
                    }

                    String delFlag = existMail.getDelFlag();
                    String infoStatus = existMail.getInfoStatus();
                    String bdpPushData = JSON.toJSONString(map, SerializerFeature.WriteMapNullValue);
                    if (StringUtils.isNotBlank(bdpPushData) && bdpPushData.length() > 1000) {
                        bdpPushData = bdpPushData.substring(0, 1000);
                    }

                    // 数据正常
                    if (StringUtils.equals(delFlag, "0")) {
                        if (StringUtils.equals(infoStatus, InfoStatusEnum.NEED_MATCH.getCode())) {
                            // 正常进行数据处理
                            if (BDPMatchStatusEnum.NORMAL_MATCH.getCode().equals(checkResult)) {
                                ++actualNormalDataCount;
                                // 匹配状态为：0-正常匹配
                                handleBDPNormalMatch(idStr, conscustno);
                            } else if (BDPMatchStatusEnum.NOT_MATCH_ANY.getCode().equals(checkResult)) {
                                ++actualNormalDataCount;
                                // 匹配状态为：1-无匹配账号
                                handleBDPExpData(idStr, existMail, ExpStatusEnum.HAS_NO_MATCH.getCode(), conscustno, bdpPushData, true);
                            } else if (BDPMatchStatusEnum.MATCH_MANY.getCode().equals(checkResult)) {
                                ++actualNormalDataCount;
                                // 匹配状态为：2-多个投顾客户号
                                handleBDPRepeat(idStr, conscustno, existMail, bdpPushData, true);
                            } else {
                                // checkResult为未知状态
                                ++actualAbnormalStatusDataCount;
                            }
                        } else {
                            ++actualAbnormalDataCount;
                            handleBDPExpData(idStr, existMail, ExpStatusEnum.OTHER.getCode(), conscustno,  bdpPushData,false);
                        }
                    } else { // 数据已删除
                        if (StringUtils.equalsAny(checkResult, BDPMatchStatusEnum.NORMAL_MATCH.getCode(), BDPMatchStatusEnum.NOT_MATCH_ANY.getCode())) {
                            ++actualAbnormalDataCount;
                            handleBDPExpData(idStr, existMail, ExpStatusEnum.OTHER.getCode(), conscustno, bdpPushData,false);
                        } else if (StringUtils.equals(checkResult, BDPMatchStatusEnum.MATCH_MANY.getCode())) {
                            ++actualNormalDataCount;
                            // 匹配状态为：2-多个投顾客户号
                            handleBDPRepeat(idStr, conscustno, existMail, bdpPushData,false);
                        } else {
                            // checkResult为未知状态
                            ++actualAbnormalStatusDataCount;
                        }
                    }
                }
            }

            stopWatch.stop();
        } catch (Exception e) {
            hasError = true;
            log.error(e.getMessage(), e);
        } finally {
            Integer totalCount = statisticsMap.get("totalCount");
            Integer normalMatchCount = statisticsMap.get("normalMatchCount");
            Integer notMatchCount = statisticsMap.get("notMatchCount");
            Integer repeatMailCount = statisticsMap.get("repeatMailCount");
            long elapsedTime = stopWatch.getTotalTimeMillis();

            log.info("stopWatch.getTotalTimeMillis():{}", elapsedTime);
            log.info("stopWatch.prettyPrint():{}", stopWatch.prettyPrint());

            // 多个投顾号情形有多条，但对应的是同一封邮件，只能算成一条（业务需要）
            log.info("BDP反推数据总量 {}，匹配成功 {} 条，匹配不到 {} 条，多个投顾客户号 {} 条",
                    totalCount, normalMatchCount, notMatchCount, repeatMailCount);
            // 排错需要（多个投顾号情形有多条，但对应的是同一封邮件，这里还是算多条）
            Map errorData = new HashMap();
            String showErrorNum;
            if (hasError && errorNum > 0) {
                errorData = list.get(errorNum - 1);
                showErrorNum = errorNum + "";
            } else {
                showErrorNum = "-1";
            }
            // 开发便于调试用的信息
            String devInfo = String.format("本次接收数据量 %s，已跳过BDP的数据数量为 %s，正常数据数量为 %s，异常数据数量为 %s，状态不对的数据数量为 %s，" +
                    "跳过BDP的数据对应的id集合为：%s，第 %s 条解析出现错误，错误数据为 %s",
                    list.size(), skipNum, actualNormalDataCount, actualAbnormalDataCount, actualAbnormalStatusDataCount,
                    skipIdList,showErrorNum, errorData);
            log.error(devInfo);

            bdpMatchLog.setTotalNum(totalCount);
            bdpMatchLog.setMatchSuccessNum(normalMatchCount);
            bdpMatchLog.setMatchFailNum(notMatchCount);
            bdpMatchLog.setMatchManyNum(repeatMailCount);
            bdpMatchLog.setElapsedTime(elapsedTime);
            if (devInfo.length() > DEV_INFO_MAX_LENGTH) {
                devInfo = devInfo.substring(0, DEV_INFO_MAX_LENGTH);
            }
            bdpMatchLog.setDevInfo(devInfo);
            associationMailLogBuss.insertBdpMatchLog(bdpMatchLog);
        }
    }

    /**
     * 统计BDP的匹配数据
     * @param list
     * @return
     */
    private Map<String, Integer> calculateBDPData(List<Map> list) {
        Map<String ,Integer> resultMap = new HashMap<>();
        // 匹配成功
        int normalMatchCount = 0;
        // 匹配不到
        int notMatchCount = 0;
        // 多个投顾客户号
        int repeatMailCount = 0;
        // 用来过滤重复的id（有多个相同的id，只算1次）
        Set<String> idSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map map :list) {
                String checkResult = (String) map.get("check_result");
                Integer id = (Integer) map.get("id");
                String idStr = (id == null) ? null : String.valueOf(id);
                if (StringUtils.equals(checkResult, BDPMatchStatusEnum.NORMAL_MATCH.getCode())) {
                    ++normalMatchCount;
                } else if (StringUtils.equals(checkResult, BDPMatchStatusEnum.NOT_MATCH_ANY.getCode())) {
                    ++notMatchCount;
                } else if (StringUtils.equals(checkResult, BDPMatchStatusEnum.MATCH_MANY.getCode())) {
                    if (idSet.add(idStr)) {
                        // 第一次遇到这个id
                        ++repeatMailCount;
                    }
                }
            }
        }
        resultMap.put("normalMatchCount", normalMatchCount);
        resultMap.put("notMatchCount", notMatchCount);
        resultMap.put("repeatMailCount", repeatMailCount);
        resultMap.put("totalCount", normalMatchCount + notMatchCount + repeatMailCount);
        return resultMap;
    }

    /**
     * 处理BDP正常匹配的数据
     * @param idStr 正常表的主键
     * @param conscustno BDP匹配好的客户号
     */
    private void handleBDPNormalMatch(String idStr, String conscustno) {
        AssociationMail am = new AssociationMail();
        am.setId(idStr);
        am.setConscustno(conscustno);
        am.setInfoStatus(InfoStatusEnum.NEED_SEND_MESSAGE.getCode());
        am.setModifier("sys");
        updateAssociationMail(am);
    }

    /**
     * 处理BDP的异常数据
     * @param idStr 正常表的主键
     * @param existMail 正常表里对应主键为idStr的记录
     * @param expStatus 异常状态
     * @param conscustno 投顾客户号
     * @param bdpPushData BDP推送过来的数据
     * @param needRemove 是否要将正常表里主键为idStr的记录标记为删除
     */
    private void handleBDPExpData(String idStr, AssociationMail existMail, String expStatus, String conscustno, String bdpPushData, boolean needRemove) {
        // 获取该异常数据对应的正常表数据
        AssociationMail existNormalMail = existMail;
        // 需要插到异常表的数据
        AssociationExpMail mergeExpMail = new AssociationExpMail();
        BeanUtils.copyProperties(existNormalMail, mergeExpMail);
        mergeExpMail.setExpStatus(expStatus);
        mergeExpMail.setConscustno(conscustno);
        mergeExpMail.setHandleStatus(HandleStatusEnum.NEED_HANDLE.getCode());
        mergeExpMail.setBdpPushData(bdpPushData);

        insertAssociationExp(mergeExpMail);
        if (needRemove) {
            associationMailDao.removeAssociationMail(idStr);
        }
    }

    /**
     * 处理BDP多个投顾客户号的数据
     * @param idStr 正常表的主键
     * @param existMail 正常表里对应主键为idStr的记录
     * @param bdpPushData BDP推送过来的数据
     * @param needRemove 是否要将正常表里主键为idStr的记录标记为删除
     */
    private void handleBDPRepeat(String idStr, String conscustno, AssociationMail existMail, String bdpPushData, boolean needRemove) {
        // 获取该异常数据对应的正常表数据
        AssociationMail existNormalMail = existMail;
        // 需要插到同名表的数据
        AssociationRepeatMail mergeRepeatMail = new AssociationRepeatMail();
        BeanUtils.copyProperties(existNormalMail, mergeRepeatMail);
        mergeRepeatMail.setConscustno(conscustno);
        mergeRepeatMail.setBdpPushData(bdpPushData);

        associationMailDao.insertAssociationRepeat(mergeRepeatMail);
        if (needRemove) {
            associationMailDao.removeAssociationMail(idStr);
        }
    }

    /**
     * 查询所有解析过的邮件的uid
     * @return
     */
    public Set<String> queryAllExistMailUid() {
        return associationMailDao.queryAllExistMailUid();
    }
}

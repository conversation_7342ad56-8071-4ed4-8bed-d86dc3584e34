package com.howbuy.crm.nt.associationmail.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailLogBuss;
import com.howbuy.crm.nt.associationmail.dao.AssociationMailMessageDao;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.dto.SendMessageLog;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.InfoStatusEnum;
import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.util.CrmNtConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 将正常解析的邮件信息推送给短信平台
 * <AUTHOR>
 */
@Slf4j
@Service("associationMailMessageService")
public class AssociationMailMessageServiceImpl implements AssociationMailMessageService {

    @Autowired
    private AssociationMailBuss associationMailBuss;
    @Autowired
    private AssociationMailMessageDao associationMailMessageDao;
    @Autowired
    private AssociationMailLogBuss associationMailLogBuss;

    @Autowired
    private MessageCommonBuss messageCommonBuss;

    @Override
    public void pushMessage(String arg) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("协会邮件短信");
        // 查询符合条件的数据
        List<AssociationMail> needSendMessageList = associationMailMessageDao.findNeedSendMessage();
        // 待发送总数
        int needSendCount = 0;
        // 没有一账通号的记录数
        int notHaveHboneCount = 0;
        // 开通通知数
        int openAccountCount = 0;
        // 密码重置通知数
        int resetPasswordCount = 0;
        // 成功推送数
        int pushSuccessCount = 0;
        // 推送失败数
        int pushFailCount = 0;
        if (CollectionUtils.isNotEmpty(needSendMessageList)) {
            needSendCount = needSendMessageList.size();
            for (AssociationMail associationMail : needSendMessageList) {
                String hboneNo = associationMail.getHboneNo();
                if (StringUtils.isNotBlank(hboneNo)) {
                    String id = associationMail.getId();
                    String custName = associationMail.getCustName();
                    String orgName = associationMail.getOrgName();
                    String investorAccount = associationMail.getInvestorAccount();
                    String initPassword = associationMail.getInitPassword();
                    String managerRegno = associationMail.getManagerRegno();
                    String infoStatus = associationMail.getInfoStatus();
                    Map<String, String> templateVarMap = new HashMap<>();
                    templateVarMap.put("CUSTNAME", custName);
                    templateVarMap.put("ORGNAME", orgName);
                    templateVarMap.put("INVESTORACCOUNT", investorAccount);
                    templateVarMap.put("INITPASSWORD", initPassword);
                    templateVarMap.put("MANAGERREGNO", managerRegno);
                    String templateVarStr = JSON.toJSONString(templateVarMap);

                    // 短信业务id
                    String businessId = null;
                    if (InfoStatusEnum.NEED_SEND_MESSAGE.getCode().equals(infoStatus)) {
                        // 投资者账号通知
                        businessId = CrmNtConstant.MSG_INVEST_ACCOUNT_NOTIFY;
                        ++openAccountCount;
                    } else if (InfoStatusEnum.NEED_RESEND_MESSAGE.getCode().equals(infoStatus)) {
                        // 初始密码变更
                        businessId = CrmNtConstant.MSG_INIT_PASSWORD_CHANGE;
                        ++resetPasswordCount;
                    }

                    // 发送短信
                    if (StringUtils.isNotBlank(businessId)) {
                        int result = -1;
                        try {
                            result = messageCommonBuss.sendMessageByHboneNo(businessId, templateVarStr, hboneNo);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                        if (result == 0) {
                            // 更新短信状态
                            AssociationMail updateMailInfoStatusData = new AssociationMail();
                            updateMailInfoStatusData.setId(id);
                            updateMailInfoStatusData.setInfoStatus(InfoStatusEnum.HAS_SEND_MESSAGE.getCode());
                            associationMailBuss.updateAssociationMail(updateMailInfoStatusData);

                            ++pushSuccessCount;
                        } else {
                            ++pushFailCount;
                        }
                    }
                } else {
                    ++notHaveHboneCount;
                }
            }
        }
        stopWatch.stop();
        long elapsedTime = stopWatch.getTotalTimeMillis();
        log.info("stopWatch.getTotalTimeMillis():{}", elapsedTime);
        log.info("stopWatch.prettyPrint():{}", stopWatch.prettyPrint());
        log.info("数据已推送至消息中心：待发送总数{}，无一账通号数量{}，开通通知数 {} 条，密码重置通知数 {} 条，成功推送数 {} 条，推送失败数 {} 条",
                needSendCount, notHaveHboneCount, openAccountCount, resetPasswordCount, pushSuccessCount, pushFailCount);

        SendMessageLog sendMessageLog = new SendMessageLog();
        sendMessageLog.setNeedSendTotalNum(needSendCount);
        sendMessageLog.setNotHaveHboneNum(notHaveHboneCount);
        sendMessageLog.setNeedSendNum(openAccountCount);
        sendMessageLog.setNeedResendNum(resetPasswordCount);
        sendMessageLog.setPushSuccessNum(pushSuccessCount);
        sendMessageLog.setPushFailNum(pushFailCount);
        sendMessageLog.setElapsedTime(elapsedTime);
        associationMailLogBuss.insertSendMessageLog(sendMessageLog);
    }
}

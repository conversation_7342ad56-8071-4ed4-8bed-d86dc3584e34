<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.associationmail.dao.AssociationMailMessageDao">

    <select id="findNeedSendMessage" resultType="com.howbuy.crm.nt.associationmail.dto.AssociationMail">
        select ca.id,
               ca.conscustno,
               ca.custname,
               ca.orgname,
               ca.investoraccount,
               ca.initpassword,
               ca.managerregno,
               ca.infostatus,
               cc.hbone_no as hboneNo
          from cm_association_mail ca
          left join cm_conscust cc
          on ca.conscustno = cc.conscustno
          where infoStatus = '1' or infoStatus = '2'
    </select>
</mapper>
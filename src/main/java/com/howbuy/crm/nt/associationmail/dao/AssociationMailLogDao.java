package com.howbuy.crm.nt.associationmail.dao;

import com.howbuy.crm.nt.associationmail.dto.BdpMatchLog;
import com.howbuy.crm.nt.associationmail.dto.ResolveMailLog;
import com.howbuy.crm.nt.associationmail.dto.SendMessageLog;

/**
 * 协会解析-系统日志
 * <AUTHOR>
 * @date 2021/6/15 17:37
 */
public interface AssociationMailLogDao {

    /**
     * 插入邮件解析日志
     * @param resolveMailLog
     */
    void insertResolveMailLog(ResolveMailLog resolveMailLog);

    /**
     * 插入BDP匹配日志
     * @param bdpMatchLog
     */
    void insertBdpMatchLog(BdpMatchLog bdpMatchLog);

    /**
     * 插入短信发送日志
     * @param sendMessageLog
     */
    void insertSendMessageLog(SendMessageLog sendMessageLog);
}

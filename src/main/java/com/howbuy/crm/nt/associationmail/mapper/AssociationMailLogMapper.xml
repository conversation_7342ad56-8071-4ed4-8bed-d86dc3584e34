<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.associationmail.dao.AssociationMailLogDao">

    <insert id="insertResolveMailLog" parameterType="com.howbuy.crm.nt.associationmail.dto.ResolveMailLog">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_resolve_mail_log.nextval as id from dual
        </selectKey>
        insert into cm_resolve_mail_log (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="totalNum != null">totalNum,</if>
            <if test="validNum != null">validNum,</if>
            <if test="normalResolveNum != null">normal_resolve_num,</if>
            <if test="expResolveNum != null">exp_resolve_num,</if>
            <if test="repeatResolveNum != null">repeat_resolve_num,</if>
            <if test="elapsedTime != null">elapsedTime,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="totalNum != null">#{totalNum},</if>
            <if test="validNum != null">#{validNum},</if>
            <if test="normalResolveNum != null">#{normalResolveNum},</if>
            <if test="expResolveNum != null">#{expResolveNum},</if>
            <if test="repeatResolveNum != null">#{repeatResolveNum},</if>
            <if test="elapsedTime != null">#{elapsedTime},</if>
        </trim>
        )
    </insert>

    <insert id="insertBdpMatchLog" parameterType="com.howbuy.crm.nt.associationmail.dto.BdpMatchLog">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_bdp_match_log.nextval as id from dual
        </selectKey>
        insert into cm_bdp_match_log (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="totalNum != null">totalNum,</if>
            <if test="matchSuccessNum != null">match_success_num,</if>
            <if test="matchFailNum != null">match_fail_num,</if>
            <if test="matchManyNum != null">match_many_num,</if>
            <if test="elapsedTime != null">elapsedTime,</if>
            <if test="devInfo != null">devInfo,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="totalNum != null">#{totalNum},</if>
            <if test="matchSuccessNum != null">#{matchSuccessNum},</if>
            <if test="matchFailNum != null">#{matchFailNum},</if>
            <if test="matchManyNum != null">#{matchManyNum},</if>
            <if test="elapsedTime != null">#{elapsedTime},</if>
            <if test="devInfo != null">#{devInfo},</if>
        </trim>
        )
    </insert>

    <insert id="insertSendMessageLog" parameterType="com.howbuy.crm.nt.associationmail.dto.SendMessageLog">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_send_message_log.nextval as id from dual
        </selectKey>
        insert into cm_send_message_log (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="needSendTotalNum != null">need_send_total_num,</if>
            <if test="notHaveHboneNum != null">not_have_hbone_num,</if>
            <if test="needSendNum != null">need_send_num,</if>
            <if test="needResendNum != null">need_resend_num,</if>
            <if test="pushSuccessNum != null">push_success_num,</if>
            <if test="pushFailNum != null">push_fail_num,</if>
            <if test="elapsedTime != null">elapsedTime,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="needSendTotalNum != null">#{needSendTotalNum},</if>
            <if test="notHaveHboneNum != null">#{notHaveHboneNum},</if>
            <if test="needSendNum != null">#{needSendNum},</if>
            <if test="needResendNum != null">#{needResendNum},</if>
            <if test="pushSuccessNum != null">#{pushSuccessNum},</if>
            <if test="pushFailNum != null">#{pushFailNum},</if>
            <if test="elapsedTime != null">#{elapsedTime},</if>
        </trim>
        )
    </insert>
</mapper>
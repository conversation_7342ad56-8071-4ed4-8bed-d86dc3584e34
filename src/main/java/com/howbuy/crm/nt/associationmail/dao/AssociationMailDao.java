package com.howbuy.crm.nt.associationmail.dao;

import com.howbuy.crm.nt.associationmail.dto.AssociationExpMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationExpOverRideMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationRepeatMail;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/4/2 14:37
 */
public interface AssociationMailDao {

    /**
     * 查询解析后的邮件内容列表
     * @return
     */
    List<AssociationMail> queryAssociationMailList();

    /**
     * 根据主键获取正常解析邮件
     * @param id
     * @return
     */
    AssociationMail findAssociationMailById(String id);

    /**
     * 插入邮件解析数据
     * @param associationMail
     */
    void insertAssociationMail(AssociationMail associationMail);

    /**
     * 修改邮件解析数据
     * @param associationMail
     */
    void updateAssociationMail(AssociationMail associationMail);

    /**
     * 逻辑删除邮件解析数据
     * @param id
     */
    void removeAssociationMail(String id);

    /**
     * 该邮件是否已经解析过
     * @param param
     * @return
     */
    boolean existAssociationMail(Map<String, String> param);

    /**
     * 查询解析异常的邮件内容列表
     * @return
     */
    List<AssociationMail> queryAssociationExpList();

    /**
     * 插入邮件解析异常数据
     * @param associationExpMail
     */
    void insertAssociationExp(AssociationExpMail associationExpMail);

    /**
     * 插入同名表
     * @param associationRepeatMail
     */
    void insertAssociationRepeat(AssociationRepeatMail associationRepeatMail);

    /**
     * 查询所有解析过的邮件的uid
     * @return
     */
    Set<String> queryAllExistMailUid();

    /**
     * 根据已有的异常信息获取正常表里的被重复邮件
     * @param associationExpMail
     * @return
     */
    AssociationMail findExistRepeatMail(AssociationExpMail associationExpMail);

    /**
     * 插入覆盖备份表
     * @param associationExpOverRideMail
     */
    void insertAssociationExpOverRideMail(AssociationExpOverRideMail associationExpOverRideMail);
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.associationmail.dao.AssociationMailDao">

    <resultMap id="AssociationMailResultMap" type="com.howbuy.crm.nt.associationmail.dto.AssociationMail">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="mailDate" property="mailDate" jdbcType="TIMESTAMP"/>
        <result column="mailDateStr" property="mailDateStr" jdbcType="VARCHAR"/>
        <result column="sourceMail" property="sourceMail" jdbcType="VARCHAR"/>
        <result column="conscustno" property="conscustno" jdbcType="VARCHAR"/>
        <result column="custName" property="custName" jdbcType="VARCHAR"/>
        <result column="orgName" property="orgName" jdbcType="VARCHAR"/>
        <result column="investorAccount" property="investorAccount" jdbcType="VARCHAR"/>
        <result column="initPassword" property="initPassword" jdbcType="VARCHAR"/>
        <result column="managerRegno" property="managerRegno" jdbcType="VARCHAR"/>
        <result column="loginHref" property="loginHref" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="credt" property="credt" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="moddt" property="moddt" jdbcType="VARCHAR"/>
        <result column="subject" property="subject" jdbcType="VARCHAR"/>
        <result column="toMail" property="toMail" jdbcType="VARCHAR"/>
        <result column="delFlag" property="delFlag" jdbcType="VARCHAR"/>
        <result column="mailUid" property="mailUid" jdbcType="VARCHAR"/>
        <result column="infoStatus" property="infoStatus" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="AssociationExpMailResultMap" type="com.howbuy.crm.nt.associationmail.dto.AssociationExpMail">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="mailDate" property="mailDate" jdbcType="TIMESTAMP"/>
        <result column="mailDateStr" property="mailDateStr" jdbcType="VARCHAR"/>
        <result column="sourceMail" property="sourceMail" jdbcType="VARCHAR"/>
        <result column="conscustno" property="conscustno" jdbcType="VARCHAR"/>
        <result column="custName" property="custName" jdbcType="VARCHAR"/>
        <result column="orgName" property="orgName" jdbcType="VARCHAR"/>
        <result column="investorAccount" property="investorAccount" jdbcType="VARCHAR"/>
        <result column="initPassword" property="initPassword" jdbcType="VARCHAR"/>
        <result column="managerRegno" property="managerRegno" jdbcType="VARCHAR"/>
        <result column="loginHref" property="loginHref" jdbcType="VARCHAR"/>
        <result column="creator" property="creator" jdbcType="VARCHAR"/>
        <result column="credt" property="credt" jdbcType="VARCHAR"/>
        <result column="modifier" property="modifier" jdbcType="VARCHAR"/>
        <result column="moddt" property="moddt" jdbcType="VARCHAR"/>
        <result column="subject" property="subject" jdbcType="VARCHAR"/>
        <result column="toMail" property="toMail" jdbcType="VARCHAR"/>
        <result column="handleStatus" property="handleStatus" jdbcType="VARCHAR"/>
        <result column="expStatus" property="expStatus" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryAssociationMailList" resultMap="AssociationMailResultMap">
        select id,
           mailDate,
           to_char(mailDate, 'yyyy-mm-dd hh24:mi') as mailDateStr,
           sourceMail,
           conscustno,
           custname,
           orgname,
           investorAccount,
           initPassword,
           managerRegno,
           loginHref,
           creator,
           to_char(credt, 'yyyy-mm-dd hh24:mi') as credt,
           modifier,
           to_char(moddt, 'yyyy-mm-dd hh24:mi') as moddt,
           subject,
           toMail,
           mailUid,
           infoStatus
        from cm_association_mail
        where delFlag = '0'
    </select>

    <select id="findAssociationMailById" parameterType="String" resultMap="AssociationMailResultMap">
        select id,
           mailDate,
           to_char(mailDate, 'yyyy-mm-dd hh24:mi:ss') as mailDateStr,
           sourceMail,
           conscustno,
           custname,
           orgname,
           investorAccount,
           initPassword,
           managerRegno,
           loginHref,
           creator,
           to_char(credt, 'yyyy-mm-dd hh24:mi:ss') as credt,
           modifier,
           to_char(moddt, 'yyyy-mm-dd hh24:mi:ss') as moddt,
           subject,
           toMail,
           delFlag,
           mailUid,
           infoStatus
        from cm_association_mail
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <insert id="insertAssociationMail" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationMail">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_association_mail.nextval as id from dual
        </selectKey>
        insert into cm_association_mail (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="mailDateStr != null and mailDateStr != ''">mailDate,</if>
            <if test="sourceMail != null and sourceMail != ''">sourceMail,</if>
            <if test="conscustno != null and conscustno != ''">conscustno,</if>
            <if test="custName != null and custName != ''">custName,</if>
            <if test="orgName != null and orgName != ''">orgName,</if>
            <if test="investorAccount != null and investorAccount != ''">investorAccount,</if>
            <if test="initPassword != null and initPassword != ''">initPassword,</if>
            <if test="managerRegno != null and managerRegno != ''">managerRegno,</if>
            <if test="loginHref != null and loginHref != ''">loginHref,</if>
            <if test="subject != null and subject != ''">subject,</if>
            <if test="toMail != null and toMail != ''">toMail,</if>
            delFlag,
            <if test="mailUid != null and mailUid != ''">mailUid,</if>
            <if test="infoStatus != null and infoStatus != ''">infoStatus,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="mailDateStr != null and mailDateStr != ''">to_date(#{mailDateStr}, 'yyyy-mm-dd hh24:mi:ss'),</if>
            <if test="sourceMail != null and sourceMail != ''">#{sourceMail},</if>
            <if test="conscustno != null and conscustno != ''">#{conscustno},</if>
            <if test="custName != null and custName != ''">#{custName},</if>
            <if test="orgName != null and orgName != ''">#{orgName},</if>
            <if test="investorAccount != null and investorAccount != ''">#{investorAccount},</if>
            <if test="initPassword != null and initPassword != ''">#{initPassword},</if>
            <if test="managerRegno != null and managerRegno != ''">#{managerRegno},</if>
            <if test="loginHref != null and loginHref != ''">#{loginHref},</if>
            <if test="subject != null and subject != ''">#{subject},</if>
            <if test="toMail != null and toMail != ''">#{toMail},</if>
            '0',
            <if test="mailUid != null and mailUid != ''">#{mailUid},</if>
            <if test="infoStatus != null and infoStatus != ''">#{infoStatus},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
        )
    </insert>

    <update id="updateAssociationMail" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationMail">
        update cm_association_mail
        <set>
            <if test="modifier!=null">modifier=#{modifier},</if>
            moddt = sysdate,
            <if test="mailDateStr != null">mailDate = to_date(#{mailDateStr}, 'yyyy-mm-dd hh24:mi:ss'),</if>
            <if test="sourceMail != null">sourceMail = #{sourceMail},</if>
            <if test="conscustno != null">conscustno = #{conscustno},</if>
            <if test="custName != null">custName = #{custName},</if>
            <if test="orgName != null">orgName = #{orgName},</if>
            <if test="investorAccount != null">investorAccount = #{investorAccount},</if>
            <if test="initPassword != null">initPassword = #{initPassword},</if>
            <if test="managerRegno != null">managerRegno = #{managerRegno},</if>
            <if test="loginHref != null">loginHref = #{loginHref},</if>
            <if test="subject != null">subject = #{subject},</if>
            <if test="toMail != null">toMail = #{toMail},</if>
            <if test="mailUid != null">mailUid = #{mailUid},</if>
            <if test="infoStatus != null">infoStatus = #{infoStatus},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 逻辑删除正常表解析数据（这边删除正常数据时，更新moddt是为了便于BDP增量同步数据用的） -->
    <update id="removeAssociationMail" parameterType="String">
        update cm_association_mail
        set delFlag = '1',
        moddt = sysdate
        where id = #{id}
    </update>

    <!-- 数据库中是否存在相同的邮件 -->
    <select id="existAssociationMail" parameterType="Map" resultType="boolean">
        select count(*) from cm_association_mail
        where custName = #{custName}
            and orgName = #{orgName}
            and investorAccount = #{investorAccount}
            and managerRegno = #{managerRegno}
            and delFlag != '1'
    </select>

    <select id="queryAssociationExpList" resultMap="AssociationExpMailResultMap">
        select id,
           mailDate,
           to_char(mailDate, 'yyyy-mm-dd hh24:mi') as mailDateStr,
           sourceMail,
           conscustno,
           custname,
           orgname,
           investorAccount,
           initPassword,
           managerRegno,
           loginHref,
           creator,
           to_char(credt, 'yyyy-mm-dd hh24:mi') as credt,
           modifier,
           to_char(moddt, 'yyyy-mm-dd hh24:mi') as credt,
           subject,
           toMail,
           mailUid,
           handleStatus,
           expStatus
        from cm_association_mail
        where delFlag = '0'
    </select>

    <insert id="insertAssociationExp" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationExpMail">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_association_exp.nextval as id from dual
        </selectKey>
        insert into cm_association_exp (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="mailDateStr != null and mailDateStr != ''">mailDate,</if>
            <if test="sourceMail != null and sourceMail != ''">sourceMail,</if>
            <if test="conscustno != null and conscustno != ''">conscustno,</if>
            <if test="custName != null and custName != ''">custName,</if>
            <if test="orgName != null and orgName != ''">orgName,</if>
            <if test="investorAccount != null and investorAccount != ''">investorAccount,</if>
            <if test="initPassword != null and initPassword != ''">initPassword,</if>
            <if test="managerRegno != null and managerRegno != ''">managerRegno,</if>
            <if test="loginHref != null and loginHref != ''">loginHref,</if>
            <if test="subject != null and subject != ''">subject,</if>
            <if test="toMail != null and toMail != ''">toMail,</if>
            delFlag,
            <if test="mailUid != null and mailUid != ''">mailUid,</if>
            <if test="handleStatus != null and handleStatus != ''">handleStatus,</if>
            <if test="expStatus != null and expStatus != ''">expStatus,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="bdpPushData != null and bdpPushData != ''">bdpPushData,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="mailDateStr != null and mailDateStr != ''">to_date(#{mailDateStr}, 'yyyy-mm-dd hh24:mi:ss'),</if>
            <if test="sourceMail != null and sourceMail != ''">#{sourceMail},</if>
            <if test="conscustno != null and conscustno != ''">#{conscustno},</if>
            <if test="custName != null and custName != ''">#{custName},</if>
            <if test="orgName != null and orgName != ''">#{orgName},</if>
            <if test="investorAccount != null and investorAccount != ''">#{investorAccount},</if>
            <if test="initPassword != null and initPassword != ''">#{initPassword},</if>
            <if test="managerRegno != null and managerRegno != ''">#{managerRegno},</if>
            <if test="loginHref != null and loginHref != ''">#{loginHref},</if>
            <if test="subject != null and subject != ''">#{subject},</if>
            <if test="toMail != null and toMail != ''">#{toMail},</if>
            '0',
            <if test="mailUid != null and mailUid != ''">#{mailUid},</if>
            <if test="handleStatus != null and handleStatus != ''">#{handleStatus},</if>
            <if test="expStatus != null and expStatus != ''">#{expStatus},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="bdpPushData != null and bdpPushData != ''">#{bdpPushData},</if>
        </trim>
        )
    </insert>

    <insert id="insertAssociationRepeat" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationRepeatMail">
        <selectKey keyColumn="id" keyProperty="id" resultType="String" order="BEFORE">
            SELECT seq_cm_association_repeat.nextval as id from dual
        </selectKey>
        insert into cm_association_repeat (
        <trim suffix="" suffixOverrides=",">
            id,
            <if test="creator != null and creator != ''">creator,</if>
            credt,
            <if test="mailDateStr != null and mailDateStr != ''">mailDate,</if>
            <if test="sourceMail != null and sourceMail != ''">sourceMail,</if>
            <if test="conscustno != null and conscustno != ''">conscustno,</if>
            <if test="custName != null and custName != ''">custName,</if>
            <if test="orgName != null and orgName != ''">orgName,</if>
            <if test="investorAccount != null and investorAccount != ''">investorAccount,</if>
            <if test="initPassword != null and initPassword != ''">initPassword,</if>
            <if test="managerRegno != null and managerRegno != ''">managerRegno,</if>
            <if test="loginHref != null and loginHref != ''">loginHref,</if>
            <if test="subject != null and subject != ''">subject,</if>
            <if test="toMail != null and toMail != ''">toMail,</if>
            delFlag,handleStatus,
            <if test="mailUid != null and mailUid != ''">mailUid,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="bdpPushData != null and bdpPushData != ''">bdpPushData,</if>
        </trim>
        ) values (
        <trim suffix="" suffixOverrides=",">
            #{id},
            <if test="creator != null and creator != ''">#{creator},</if>
            sysdate,
            <if test="mailDateStr != null and mailDateStr != ''">to_date(#{mailDateStr}, 'yyyy-mm-dd hh24:mi:ss'),</if>
            <if test="sourceMail != null and sourceMail != ''">#{sourceMail},</if>
            <if test="conscustno != null and conscustno != ''">#{conscustno},</if>
            <if test="custName != null and custName != ''">#{custName},</if>
            <if test="orgName != null and orgName != ''">#{orgName},</if>
            <if test="investorAccount != null and investorAccount != ''">#{investorAccount},</if>
            <if test="initPassword != null and initPassword != ''">#{initPassword},</if>
            <if test="managerRegno != null and managerRegno != ''">#{managerRegno},</if>
            <if test="loginHref != null and loginHref != ''">#{loginHref},</if>
            <if test="subject != null and subject != ''">#{subject},</if>
            <if test="toMail != null and toMail != ''">#{toMail},</if>
            '0','0',
            <if test="mailUid != null and mailUid != ''">#{mailUid},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="bdpPushData != null and bdpPushData != ''">#{bdpPushData},</if>
        </trim>
        )
    </insert>

    <select id="queryAllExistMailUid" resultType="String">
        select ca.mailuid from cm_association_mail ca where ca.mailuid is not null
        union
        select ce.mailuid from cm_association_exp ce where ce.mailuid is not null
    </select>

    <select id="findExistRepeatMail" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationExpMail"
            resultType="com.howbuy.crm.nt.associationmail.dto.AssociationMail">
        select * from cm_association_mail ca
        where custName = #{custName}
            and orgName = #{orgName}
            and investorAccount = #{investorAccount}
            and managerRegno = #{managerRegno}
            and delFlag != '1'
    </select>


    <insert id="insertAssociationExpOverRideMail" parameterType="com.howbuy.crm.nt.associationmail.dto.AssociationExpOverRideMail">
        insert into CM_ASSOCIATION_EXP_OVERRIDE (
        select seq_cm_asso_exp_override.nextval,a.*,#{expid} from Cm_Association_Mail a where a.id=#{id}
        )
    </insert>
</mapper>
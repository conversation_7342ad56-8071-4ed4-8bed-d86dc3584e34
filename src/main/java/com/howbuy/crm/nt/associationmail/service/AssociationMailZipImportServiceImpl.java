package com.howbuy.crm.nt.associationmail.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.associationmail.dto.AssociationExpMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.request.AssociationMailZipImportRequest;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 批量导入eml数据（给hb中压缩包、eml导入用的）
 * <AUTHOR>
 * @date 2021/10/9 17:51
 */
@Slf4j
@Service("associationMailZipImportService")
public class AssociationMailZipImportServiceImpl implements AssociationMailZipImportService {

    @Autowired
    private AssociationMailBuss associationMailBuss;

    @Override
    public BaseResponse execute(AssociationMailZipImportRequest request) {
        BaseResponse response = new BaseResponse();

        try {
            // 正常解析的邮件
            List<AssociationMail> normalAssociationMailList = request.getNormalAssociationMailList();
            // 异常解析的邮件
            List<AssociationExpMail> exceptionAssociationMailList = request.getExceptionAssociationMailList();
            // 插入数据
            associationMailBuss.insertResolvedData(normalAssociationMailList, exceptionAssociationMailList);

            response.success();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            response.putBaseResult(BaseConstantEnum.FAIL, "操作失败");
        }
        log.info(JSON.toJSONString(response));
        return response;
    }
}

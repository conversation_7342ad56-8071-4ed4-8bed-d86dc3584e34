package com.howbuy.crm.nt.associationmail.mq;

import kafka.consumer.ConsumerConfig;
import kafka.consumer.ConsumerIterator;
import kafka.consumer.KafkaStream;
import kafka.javaapi.consumer.ConsumerConnector;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @Description: 消息消费者基类
 * @reason:
 * @Date: 2020/10/27 15:26
 */
@Slf4j
public abstract class KafkaConsumer extends Thread {

    private  ConsumerConnector consumer;

    @PostConstruct
    private void createConsumerConfig(){
        Properties props = new Properties();
        props.put("zookeeper.connect", getZkUrl());
        props.put("group.id", "defaultGroup");
        props.put("zookeeper.session.timeout.ms", "30000");
        props.put("zookeeper.sync.time.ms", "200");
        props.put("auto.commit.interval.ms", "1000");
        props.put("rebalance.backoff.ms", "10000");
        props.put("rebalance.max.retries", "5");
        consumer = kafka.consumer.Consumer.createJavaConsumerConnector(new ConsumerConfig(props));
        this.start();
    }

    @Override
    public void run() {
        Map<String, Integer> topicCountMap = new HashMap<String, Integer>(2);
        topicCountMap.put(getTopic(), Integer.valueOf(1));
        Map<String, List<KafkaStream<byte[], byte[]>>> consumerMap = consumer.createMessageStreams(topicCountMap);
        KafkaStream<byte[], byte[]> stream = consumerMap.get(getTopic()).get(0);
        ConsumerIterator<byte[], byte[]> it = stream.iterator();
        while (it.hasNext()) {
            try {
                handleMessage(it.next().message());
            }catch (Exception e){
                log.error("Kafka消费异常！",e);
            }
        }
    }

    /**
     * 接收到消息处理
     */
    public abstract void handleMessage(byte[] bytes);

    /**
     * topic
     * @return
     */
    public abstract String getTopic();

    /**
     * zk地址
     * @return
     */
    public abstract String getZkUrl();
}

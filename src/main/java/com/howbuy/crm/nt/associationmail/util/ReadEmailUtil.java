package com.howbuy.crm.nt.associationmail.util;

import com.howbuy.crm.nt.associationmail.model.ReadEmailConstant;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.text.*;
import java.util.*;
import java.util.function.Predicate;
import javax.mail.*;
import javax.mail.internet.*;

/**
 * 邮件读取工具类
 * 说明：有一封邮件就需要建立一个ReciveMail对象
 */
public class ReadEmailUtil {
    private MimeMessage mimeMessage = null;
    /** 附件下载后的存放目录 */
    private String saveAttachPath = ""; 
    /** 存放邮件内容 */
    private StringBuffer bodytext = new StringBuffer();
    /** 默认的日前显示格式 */
    private String dateformat = "yyyy-MM-dd HH:mm:ss"; 

    public ReadEmailUtil(MimeMessage mimeMessage) {
        this.mimeMessage = mimeMessage;
    }

    public void setMimeMessage(MimeMessage mimeMessage) {
        this.mimeMessage = mimeMessage;
    }

    /**
     * 获得发件人的地址和姓名
     */
    public String getFrom() throws Exception {
        return getFrom(true);
    }

    public String getFrom(boolean onlyNeedMail) throws Exception {
        InternetAddress[] address = (InternetAddress[]) mimeMessage.getFrom();
        String from = address[0].getAddress();
        if (from == null) {
            from = "";
        }
        if (onlyNeedMail) {
            return from;
        } else {
            String personal = address[0].getPersonal();
            if (personal == null) {
                personal = "";
            }
            String fromaddr = personal + "<" + from + ">";
            return fromaddr;
        }
    }

    /**
     * 获得邮件的收件人，抄送，和密送的地址和姓名，根据所传递的参数的不同 "to"----收件人 "cc"---抄送人地址 "bcc"---密送人地址
     * @param type：类型
     * 这里 onlyNeedMail 取true，只要email地址；onlyNeedMail为false，还会带上别的信息
     */
    public String getMailAddress(String type) throws Exception {
        return getMailAddress(type, true);
    }

    public String getMailAddress(String type, boolean onlyNeedMail) throws Exception {
        StringBuffer mailaddr = new StringBuffer();
        String addtype = type.toUpperCase();
        InternetAddress[] address = null;
        if (StringUtils.equalsAny(addtype, ReadEmailConstant.RECIPIENT_TYPE_TO,
                ReadEmailConstant.RECIPIENT_TYPE_CC, ReadEmailConstant.RECIPIENT_TYPE_BCC)) {
            if (ReadEmailConstant.RECIPIENT_TYPE_TO.equals(addtype)) {
                address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.TO);
            } else if (ReadEmailConstant.RECIPIENT_TYPE_CC.equals(addtype)) {
                address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.CC);
            } else {
                address = (InternetAddress[]) mimeMessage.getRecipients(Message.RecipientType.BCC);
            }
            if (address != null) {
                for (int i = 0; i < address.length; i++) {
                    String email = address[i].getAddress();
                    if (email == null) {
                        email = "";
                    } else {
                        email = MimeUtility.decodeText(email);
                    }

                    String compositeto, personal;
                    if (onlyNeedMail) {
                        compositeto = email;
                    } else {
                        personal = address[i].getPersonal();
                        if (personal == null) {
                            personal = "";
                        } else {
                            personal = MimeUtility.decodeText(personal);
                        }
                        compositeto = personal + "<" + email + ">";
                    }
                    if(mailaddr.length()==0){
                        mailaddr.append(compositeto);
                    }else{
                        mailaddr.append(",").append(compositeto);
                    }
                }
            }
        } else {
            throw new Exception("Error emailaddr type!");
        }
        return mailaddr.toString();
    }

    /**
     * 获得邮件主题
     */
    public String getSubject() throws MessagingException {
        String subject = "";
        try {
            subject = MimeUtility.decodeText(mimeMessage.getSubject());
            if (subject == null) {
                subject = "";
            }
        } catch (Exception exce) {}
        return subject;
    }

    /**
     * 获得邮件发送日期
     */
    public String getSentDate() throws Exception {
        Date sentdate = mimeMessage.getSentDate();
        SimpleDateFormat format = new SimpleDateFormat(dateformat);
        return format.format(sentdate);
    }

    /**
     * 获得邮件正文内容
     */
    public String getBodyText() {
        return bodytext.toString();
    }

    /**
     * 解析邮件，把得到的邮件内容保存到一个StringBuffer对象中，解析邮件 主要是根据MimeType类型的不同执行不同的操作，一步一步的解析（方法1）
     */
    public void getMailContent(Part part) throws Exception {
        String contenttype = part.getContentType();
        int nameindex = contenttype.indexOf("name");
        boolean conname = false;
        if (nameindex != -1) {
            conname = true;
        }
        System.out.println("CONTENTTYPE: " + contenttype);
        if (part.isMimeType(ReadEmailConstant.MIME_TYPE_TEXT_PLAIN) && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_TEXT_HTML) && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART)) {
            Multipart multipart = (Multipart) part.getContent();
            int counts = multipart.getCount();
            for (int i = 0; i < counts; i++) {
                getMailContent(multipart.getBodyPart(i));
            }
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MESSAGE_RFC822)) {
            getMailContent((Part) part.getContent());
        } else {}
    }

    /**
     * 获得邮件文本内容（方法2）
     * @param part 邮件体
     * @param content 存储邮件文本内容的字符串
     * @throws MessagingException
     * @throws IOException
     */
    public static void getMailTextContent(Part part, StringBuffer content) throws MessagingException, IOException {
        //如果是文本类型的附件，通过getContent方法可以取到文本内容，但这不是我们需要的结果，所以在这里要做判断
        boolean isContainTextAttach = part.getContentType().indexOf("name") > 0;
        if (part.isMimeType(ReadEmailConstant.MIME_TYPE_TEXT) && !isContainTextAttach) {
            content.append(part.getContent().toString());
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MESSAGE_RFC822)) {
            getMailTextContent((Part)part.getContent(),content);
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART)) {
            Multipart multipart = (Multipart) part.getContent();
            int partCount = multipart.getCount();
            for (int i = 0; i < partCount; i++) {
                BodyPart bodyPart = multipart.getBodyPart(i);
                getMailTextContent(bodyPart,content);
            }
        }
    }

    /**
     * 判断此邮件是否需要回执，如果需要回执返回"true",否则返回"false"
     */
    public boolean getReplySign() throws MessagingException {
        boolean replysign = false;
        String[] needreply = mimeMessage
                .getHeader("Disposition-Notification-To");
        if (needreply != null) {
            replysign = true;
        }
        return replysign;
    }

    /**
     * 获得此邮件的Message-ID
     */
    public String getMessageId() throws MessagingException {
        return mimeMessage.getMessageID();
    }

    /**
     * 【判断此邮件是否已读，如果未读返回返回false,反之返回true】
     */
    public boolean isNew() throws MessagingException {
        boolean isnew = false;
        Flags flags = ((Message) mimeMessage).getFlags();
        Flags.Flag[] flag = flags.getSystemFlags();
        System.out.println("flags's length: " + flag.length);
        for (int i = 0; i < flag.length; i++) {
            if (flag[i] == Flags.Flag.SEEN) {
                isnew = true;
                System.out.println("seen Message.......");
                break;
            }
        }
        return isnew;
    }

    /**
     * 判断此邮件是否包含附件
     */
    public boolean isContainAttach(Part part) throws Exception {
        boolean attachflag = false;
        String contentType = part.getContentType();
        if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART)) {
            Multipart mp = (Multipart) part.getContent();
            for (int i = 0; i < mp.getCount(); i++) {
                BodyPart mpart = mp.getBodyPart(i);
                String disposition = mpart.getDisposition();
                if ((disposition != null)
                        && ((disposition.equals(Part.ATTACHMENT)) || (disposition
                        .equals(Part.INLINE)))) {
                    attachflag = true;
                } else if (mpart.isMimeType("multipart/*")) {
                    attachflag = isContainAttach((Part) mpart);
                } else {
                    String contype = mpart.getContentType();
                    if (contype.toLowerCase().indexOf("application") != -1) {
                        attachflag = true;
                    }
                    if (contype.toLowerCase().indexOf("name") != -1) {
                        attachflag = true;
                    }
                }
            }
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MESSAGE_RFC822)) {
            attachflag = isContainAttach((Part) part.getContent());
        }
        return attachflag;
    }

    /**
     * 【保存附件】
     */
    public void saveAttachMent(Part part) throws Exception {
        String fileName = "";
        if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART)) {
            Multipart mp = (Multipart) part.getContent();
            for (int i = 0; i < mp.getCount(); i++) {
                BodyPart mpart = mp.getBodyPart(i);
                String disposition = mpart.getDisposition();
                if ((disposition != null)
                        && ((disposition.equals(Part.ATTACHMENT)) || (disposition
                        .equals(Part.INLINE)))) {
                    fileName = mpart.getFileName();
                    if (fileName.toLowerCase().indexOf("gb2312") != -1) {
                        fileName = MimeUtility.decodeText(fileName);
                    }
                    saveFile(fileName, mpart.getInputStream());
                } else if (mpart.isMimeType("multipart/*")) {
                    saveAttachMent(mpart);
                } else {
                    fileName = mpart.getFileName();
                    if ((fileName != null)
                            && (fileName.toLowerCase().indexOf("GB2312") != -1)) {
                        fileName = MimeUtility.decodeText(fileName);
                        saveFile(fileName, mpart.getInputStream());
                    }
                }
            }
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MESSAGE_RFC822)) {
            saveAttachMent((Part) part.getContent());
        }
    }

    /**
     * 【设置附件存放路径】
     */

    public void setAttachPath(String attachpath) {
        this.saveAttachPath = attachpath;
    }

    /**
     * 【设置日期显示格式】
     */
    public void setDateFormat(String format) throws Exception {
        this.dateformat = format;
    }

    /**
     * 【获得附件存放路径】
     */
    public String getAttachPath() {
        return saveAttachPath;
    }

    /**
     * 【真正的保存附件到指定目录里】
     */
    private void saveFile(String fileName, InputStream in) throws Exception {
        String osName = System.getProperty("os.name");
        String storedir = getAttachPath();
        String separator = "";
        if (osName == null) {
            osName = "";
        }
        if (osName.toLowerCase().indexOf(ReadEmailConstant.OS_WINDOWS) != -1) {
            separator = "\\";
            if (storedir == null || "".equals(storedir)) {
                storedir = "c:\\tmp";
            }
        } else {
            separator = "/";
            storedir = "/tmp";
        }
        File storefile = new File(storedir + separator + fileName);
        System.out.println("storefile's path: " + storefile.toString());
        // for(int i=0;storefile.exists();i++){
        // storefile = new File(storedir+separator+fileName+i);
        // }
        BufferedOutputStream bos = null;
        BufferedInputStream bis = null;
        try {
            bos = new BufferedOutputStream(new FileOutputStream(storefile));
            bis = new BufferedInputStream(in);
            int c;
            while ((c = bis.read()) != -1) {
                bos.write(c);
                bos.flush();
            }
        } catch (Exception exception) {
            exception.printStackTrace();
            throw new Exception("文件保存失败!");
        } finally {
            if (bos != null) {
                bos.close();
            }
            if (bis != null) {
                bis.close();
            }
        }
    }

    /**
     * 获取指定Date
     * 使用示例：
     * Date endDate = ReadEmailUtil.getDate(0, 1, 0, 0); // 今天凌晨1点
     * Date startDate = ReadEmailUtil.getDate(-1, 1, 0, 0); // 昨天凌晨1点
     *
     * Date endDate = ReadEmailUtil.getDate(0, 10, 15, 5); // 今天10:15:05
     * Date startDate = ReadEmailUtil.getDate(0, 10, 15, 3); // 今天10:15:03
     *
     * @param offset 日期的相对偏移
     * @param hour 时
     * @param minute 分
     * @param second 秒
     * @return
     */
    public static Date getDate(int offset, int hour, int minute, int second) {
        Calendar time = Calendar.getInstance();
        time.add(Calendar.DATE, offset);
        time.set(Calendar.HOUR_OF_DAY, hour);
        time.set(Calendar.MINUTE, minute);
        time.set(Calendar.SECOND, second);
        time.set(Calendar.MILLISECOND, 0);
        return time.getTime();
    }

    /**
     * 准备imap连接需要用到的属性
     * @param host 连接主机
     * @return
     */
    public static Properties prepareSmtpProperties(String host) {
        Properties props = new Properties();
        // 发件人的邮箱的 SMTP服务器地址
        props.setProperty("mail.imap.host", host);
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        props.setProperty("mail.imap.socketFactory.port", "993");
        props.setProperty("mail.imap.port", "993");
        props.setProperty("mail.imap.auth.login.disable", "true");
        props.put("mail.smtp.auth", "true");
        return props;
    }

    /**
     * 筛选符合指定条件的邮件
     * @param messages
     * @param predicate
     * @return
     */
    public static List<Message> filterMessage(Message[] messages, Predicate<Message> predicate) throws MessagingException {
        List<Message> list = new ArrayList<>();
        if (ArrayUtils.isNotEmpty(messages)) {
            for (Message message : messages) {
                if (predicate.test(message)) {
                    list.add(message);
                }
            }
        }
        return list;
    }
}

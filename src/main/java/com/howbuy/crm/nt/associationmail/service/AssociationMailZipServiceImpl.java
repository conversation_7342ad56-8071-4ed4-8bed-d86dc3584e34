package com.howbuy.crm.nt.associationmail.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.associationmail.dto.AssociationExpMail;
import com.howbuy.crm.nt.associationmail.dto.AssociationMail;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.ExpStatusEnum;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant.HandleStatusEnum;
import com.howbuy.crm.nt.associationmail.model.MailTaskParam;
import com.howbuy.crm.nt.associationmail.util.ReadEmailUtil;
import com.howbuy.crm.nt.associationmail.util.ReadEmlUtil;
import com.howbuy.crm.nt.associationmail.util.RegexUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.MultiValueMap;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.mail.Message;
import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.*;

/**
 * 从压缩包中读取并解析邮件内容
 * <AUTHOR>
 */
@Slf4j
@Service("associationMailZipService")
public class AssociationMailZipServiceImpl implements AssociationMailZipService {

    @Autowired
    private AssociationMailBuss associationMailBuss;

    @Override
    public void handleZipFileContent(String arg) {
        try {
            // 解析任务参数
            MailTaskParam mailTaskParam = resolveTaskParam(arg);

            // 正常解析的邮件
            List<AssociationMail> normalAssociationMailList = new ArrayList<>();
            // 异常解析的邮件
            List<AssociationExpMail> exceptionAssociationMailList = new ArrayList<>();
            // 内容重复的邮件
            Map<String, AssociationExpMail> contentRepeatMail = new MultiValueMap();
            // 用来验证邮件数据是否重复的set
            Set<String> set = new HashSet<>();

            StopWatch stopWatch = new StopWatch();
            stopWatch.start("读取压缩包获取邮件");
            List<Message> messageList = filterMail(mailTaskParam);
            stopWatch.stop();

            stopWatch.start("解析邮件");
            resolveMailContent(normalAssociationMailList, exceptionAssociationMailList, contentRepeatMail, set, messageList);
            stopWatch.stop();

            stopWatch.start("插入数据");
            // 插入数据
            associationMailBuss.insertResolvedData(normalAssociationMailList, exceptionAssociationMailList);
            stopWatch.stop();

            log.info("正常解析数据：{}条", normalAssociationMailList.size());
            log.info("异常解析数据：{}条", exceptionAssociationMailList.size());
            log.info("重复邮件详情：{}条 {}", contentRepeatMail.values().size(), contentRepeatMail);

            log.info("stopWatch.getTotalTimeMillis():{}", stopWatch.getTotalTimeMillis());
            log.info("stopWatch.prettyPrint():{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析任务参数
     * @param arg 原始的定时任务参数
     * @return
     */
    private MailTaskParam resolveTaskParam(String arg) {
        // 解析任务参数
        JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");

        String zipFileDirPath = taskParam == null ?
                "C:\\Users\\<USER>\\Downloads" :
                (String) taskParam.get("zipFileDir");
        return MailTaskParam.builder()
                .zipFileDirPath(zipFileDirPath)
                .build();
    }

    /**
     * 邮件过滤
     * @param mailTaskParam
     * @return
     */
    private List<Message> filterMail(MailTaskParam mailTaskParam) {
        List<Message> messageList = new ArrayList<>();

        String zipFileDirPath = mailTaskParam.getZipFileDirPath();
        File zipFileDir = new File(zipFileDirPath);
        File[] files = zipFileDir.listFiles((dir, name) -> name.endsWith(".zip"));
        log.info("检测到{}个zip压缩包", files.length);

        // 所有压缩包中获取eml文件，然后解析成Message，放到messageList，便于后续解析数据
        String compareFromMail = "<EMAIL>";
        // 用于比较的发件email
        if (ArrayUtils.isNotEmpty(files)) {
            for (File file : files) {
                final int[] emailNum = {0};
                ReadEmlUtil.readZipFileList(file.getAbsolutePath(), ".eml", inputStream -> {
                    try {
                        Session session = Session.getDefaultInstance(new Properties(), null);
                        Message msg = new MimeMessage(session, inputStream);

                        ReadEmailUtil pmm = new ReadEmailUtil((MimeMessage) msg);
                        // 筛选指定发件人
                        if (compareFromMail.equals(pmm.getFrom())) {
                            messageList.add(msg);
                            emailNum[0]++;
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                });
                log.info("{}解析出{}封邮件", file.getName(), emailNum[0]);
            }
        }
        log.info("总共解析出{}封邮件", messageList.size());
        return messageList;
    }

    /**
     * 解析邮件内容
     * @param normalAssociationMailList
     * @param exceptionAssociationMailList
     * @param contentRepeatMail
     * @param set
     * @param messageList
     * @throws Exception
     */
    private void resolveMailContent(List<AssociationMail> normalAssociationMailList, List<AssociationExpMail> exceptionAssociationMailList, Map<String, AssociationExpMail> contentRepeatMail, Set<String> set, List<Message> messageList) throws Exception {
        for (int i = 0; i < messageList.size(); i++) {
            MimeMessage message = (MimeMessage) messageList.get(i);
            ReadEmailUtil pmm = new ReadEmailUtil(message);
            String from = pmm.getFrom();
            StringBuffer bodyText = ReadEmlUtil.parseEmlContent(message);

            // 过滤邮件内容中的所有html标签
            String newBodyText = RegexUtil.filterHtml(bodyText);
            newBodyText = StringUtils.replace(newBodyText, "\ufeff", "");
            String sentDate = pmm.getSentDate();
            String custName = RegexUtil.matchText(AssociationMailConstant.CUSTNAME_REG, newBodyText);
            String orgName = RegexUtil.matchText(AssociationMailConstant.ORG_NAME_REG, newBodyText);
            String investorAccount = RegexUtil.matchText(AssociationMailConstant.INVESTOR_ACCOUNT_REG, newBodyText);
            String initPassword = RegexUtil.matchText(AssociationMailConstant.INIT_PASSWORD_REG, newBodyText);
            String managerRegno = RegexUtil.matchText(AssociationMailConstant.MANAGER_REGNO_REG, newBodyText);
            String loginHref = RegexUtil.matchText(AssociationMailConstant.LOGIN_HREF_REG, newBodyText);

            String subject = pmm.getSubject();
            String toMail = pmm.getMailAddress("TO");

            // 解析异常的邮件
            if (StrUtil.hasBlank(sentDate, from, custName, orgName, investorAccount, initPassword, managerRegno, loginHref)) {
                AssociationExpMail ae = new AssociationExpMail();
                ae.setCreator("sys");
                ae.setMailDateStr(sentDate);
                ae.setSourceMail(from);
                ae.setCustName(custName);
                ae.setOrgName(orgName);
                ae.setInvestorAccount(investorAccount);
                ae.setInitPassword(initPassword);
                ae.setManagerRegno(managerRegno);
                ae.setLoginHref(loginHref);
                ae.setSubject(subject);
                ae.setToMail(toMail);
                ae.setHandleStatus(HandleStatusEnum.NEED_HANDLE.getCode());
                ae.setExpStatus(ExpStatusEnum.RESOLVE_EXP.getCode());
                exceptionAssociationMailList.add(ae);
            } else {
                String key = StringUtils.join(new String[]{custName, orgName, investorAccount, managerRegno}, "#");
                Map<String, String> paramMap = new HashMap<String, String>(4){{
                    put("custName", custName);
                    put("orgName", orgName);
                    put("investorAccount", investorAccount);
                    put("managerRegno", managerRegno);
                }};
                boolean addSuccess = set.add(key);
                if (!addSuccess || associationMailBuss.existAssociationMail(paramMap)) {
                    // 内容重复的邮件
                    AssociationExpMail ae = new AssociationExpMail();
                    ae.setCreator("sys");
                    ae.setMailDateStr(sentDate);
                    ae.setSourceMail(from);
                    ae.setCustName(custName);
                    ae.setOrgName(orgName);
                    ae.setInvestorAccount(investorAccount);
                    ae.setInitPassword(initPassword);
                    ae.setManagerRegno(managerRegno);
                    ae.setLoginHref(loginHref);
                    ae.setSubject(subject);
                    ae.setToMail(toMail);
                    ae.setHandleStatus(HandleStatusEnum.NEED_HANDLE.getCode());
                    ae.setExpStatus(ExpStatusEnum.REPEAT_MAIL.getCode());
                    exceptionAssociationMailList.add(ae);
                    contentRepeatMail.put(key, ae);
                } else {
                    // 正常邮件
                    AssociationMail am = new AssociationMail();
                    am.setCreator("sys");
                    am.setMailDateStr(sentDate);
                    am.setSourceMail(from);
                    am.setCustName(custName);
                    am.setOrgName(orgName);
                    am.setInvestorAccount(investorAccount);
                    am.setInitPassword(initPassword);
                    am.setManagerRegno(managerRegno);
                    am.setLoginHref(loginHref);
                    am.setSubject(subject);
                    am.setToMail(toMail);
                    am.setInfoStatus(AssociationMailConstant.InfoStatusEnum.NEED_MATCH.getCode());
                    normalAssociationMailList.add(am);
                }
            }
        }
    }
}

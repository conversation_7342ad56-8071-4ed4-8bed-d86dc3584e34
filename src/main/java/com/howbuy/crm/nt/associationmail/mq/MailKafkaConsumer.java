package com.howbuy.crm.nt.associationmail.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.nt.associationmail.buss.AssociationMailBuss;
import com.howbuy.crm.nt.yxslive.po.CustYxsLivePo;
import com.howbuy.crm.nt.yxslive.service.CustYxsLiveStaticService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/21 9:55
 */
@Component
@Slf4j
public class MailKafkaConsumer extends KafkaConsumer {

    @Value("${associationmail.kafka.topic}")
    private String topic;

    @Value("${associationmail.kafka.zk}")
    private String zkUrl;

    @Autowired
    private AssociationMailBuss associationMailBuss;

    @Autowired
    private CustYxsLiveStaticService custYxsLiveStaticService;

    @Override
    public void handleMessage(byte[] bytes) {
        String message = new String(bytes);
        log.info("接收来自BDP的推送数据：{}" , message);
        Map<String,Object> jsonObject = (Map<String,Object>) JSON.parse(message);

        //消息示例
//        {"message":"","status":"0","template":"MAIL_CUST_MATCH","batchNo":"100000000003851532","cdcStart":"20221117000000","cdcEnd":"20221118000000","orderNo":"100000000004933792","extra":"100000000000004220","orderSize":2,
//         "data":[{"id":15212,"conscustno":"1174802463","check_result":"0","orgname":"上海新方程私募基金管理有限公司","credt":"20221118012418","maildate":"20221117173841","custname":"上海好晓买软件系统有限公司"},
//                 {"id":15213,"conscustno":"1172727579","check_result":"0","orgname":"上海新方程私募基金管理有限公司","credt":"20221118012418","maildate":"20221117175515","custname":"北京大学教育基金会"}],
//          "dataSize":2,"pageIndex":1}"}

        //temlpate=MAIL_CUST_MATCH|
        String template =(String) jsonObject.get("template");
        String batchNo =(String) jsonObject.get("batchNo");
        // 待发送 总条数
        Integer orderSize =(Integer) jsonObject.get("orderSize");
        //单条消息 条数
        Integer dataSize =(Integer) jsonObject.get("dataSize");
         log.info("SYNC FROM BDP 。template:{} ,batchNo:{}. totalCount:{}. current msg count:{} ",
                 template,batchNo,orderSize,dataSize);
        List<Map> list = (List<Map>)jsonObject.get("data");
        //直播访问数据
        if(template.equals("DM_CRM_YXS_LIVE")){
            List<CustYxsLivePo> dataList= Lists.newArrayList();
            list.stream().forEach(dataMap-> dataList.add(transferYxslPoByMap(dataMap)));
            int affectedSize=custYxsLiveStaticService.batchInsertList(dataList);
            log.info("直播数据message 条数：{}， 实际[插入|更新]条数：{}",dataList.size(),affectedSize);
        }else if(template.equals("MAIL_CUST_MATCH")){
            associationMailBuss.batchHandleMatchData(list);
        }else {
            throw new BusinessException("9999","kafka消费，不支持的TEMPLATE类型!");
        }
    }

    /**
     * 消息 --> Po
     * @param dataMap
     * @return
     */
    private CustYxsLivePo transferYxslPoByMap(Map<String,Object> dataMap){
        CustYxsLivePo livePo=new CustYxsLivePo();
        livePo.setConsCode((String)dataMap.get("conscode"));
        livePo.setConscustNo((String)dataMap.get("conscustno"));
        livePo.setConsName((String)dataMap.get("consname"));
        livePo.setCountDate((String)dataMap.get("count_date"));
        livePo.setCustFlag((String)dataMap.get("cust_flag"));
        livePo.setCustName((String)dataMap.get("custname"));
        livePo.setHboneNo((String)dataMap.get("hbone_no"));
        livePo.setId((String)dataMap.get("id"));
        livePo.setShareFlag((String)dataMap.get("share_flag"));
        livePo.setSource((String)dataMap.get("source"));
        livePo.setTimes(dataMap.get("times")==null?null:Long.valueOf((Integer)dataMap.get("times")));
        livePo.setTitle((String)dataMap.get("title"));
        livePo.setTradeFlag((String)dataMap.get("trade_flag"));
        livePo.setU1Name((String)dataMap.get("u1_name"));
        livePo.setU2Name((String)dataMap.get("u2_name"));
        livePo.setU3Name((String)dataMap.get("u3_name"));
        return livePo;
    }

    @Override
    public String getTopic() {
        return this.topic;
    }

    @Override
    public String getZkUrl() {
        return this.zkUrl;
    }
}

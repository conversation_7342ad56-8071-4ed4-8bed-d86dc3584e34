package com.howbuy.crm.nt.associationmail.util;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by shu<PERSON> on 2021/4/3 14:32
 */
public class RegexUtil {

    public static final String REGEX_FOR_HTML = "<([^>]*)>";

    /**
     * 过滤所有以“<”开头 以“>”结尾的标签
     * @param input
     * @return
     */
    public static String filterHtml(CharSequence input) {
        Pattern pattern = Pattern.compile(REGEX_FOR_HTML);
        Matcher m = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            m.appendReplacement(sb, "");
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /**
     * 使用正则查找匹配到的字符串（这里如果匹配出多个结果只返回第1个）
     * @param regExpression 正则表达式
     * @return
     */
    public static String matchText(String regExpression, CharSequence input) {
        if (StringUtils.isEmpty(regExpression) || StringUtils.isEmpty(input)) {
            return null;
        }
        Pattern p = Pattern.compile(regExpression);
        Matcher m = p.matcher(input);
        while (m.find()) {
            return m.group(1);
        }
        return null;
    }

    /**
     * 使用正则查找匹配到的字符串（这里如果匹配出多个结果只返回第1个）
     * @param pattern 正则表达式
     * @return
     */
    public static String matchText(Pattern pattern, CharSequence input) {
        if (Objects.isNull(pattern) || StringUtils.isEmpty(input)) {
            return null;
        }
        Matcher m = pattern.matcher(input);
        while (m.find()) {
            return m.group(1);
        }
        return null;
    }
}

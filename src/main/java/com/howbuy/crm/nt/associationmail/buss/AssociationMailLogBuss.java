package com.howbuy.crm.nt.associationmail.buss;

import com.howbuy.crm.nt.associationmail.dao.AssociationMailLogDao;
import com.howbuy.crm.nt.associationmail.dto.BdpMatchLog;
import com.howbuy.crm.nt.associationmail.dto.ResolveMailLog;
import com.howbuy.crm.nt.associationmail.dto.SendMessageLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/15 18:12
 */
@Component
@Slf4j
public class AssociationMailLogBuss {

    @Autowired
    private AssociationMailLogDao associationMailLogDao;

    /**
     * 插入邮件解析日志
     * @param resolveMailLog
     */
    public void insertResolveMailLog(ResolveMailLog resolveMailLog) {
        resolveMailLog.setCreator("sys");
        associationMailLogDao.insertResolveMailLog(resolveMailLog);
    }

    /**
     * 插入BDP匹配日志
     * @param bdpMatchLog
     */
    public void insertBdpMatchLog(BdpMatchLog bdpMatchLog) {
        bdpMatchLog.setCreator("sys");
        associationMailLogDao.insertBdpMatchLog(bdpMatchLog);
    }

    /**
     * 插入短信发送日志
     * @param sendMessageLog
     */
    public void insertSendMessageLog(SendMessageLog sendMessageLog) {
        sendMessageLog.setCreator("sys");
        associationMailLogDao.insertSendMessageLog(sendMessageLog);
    }
}

package com.howbuy.crm.nt.associationmail.util;

import com.howbuy.crm.nt.associationmail.model.ReadEmailConstant;
import org.springframework.util.StringUtils;

import javax.mail.Message;
import javax.mail.Multipart;
import javax.mail.Part;
import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.io.*;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.Properties;
import java.util.function.Consumer;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 读取Eml文件的工具类
 * Created by shucheng on 2021/4/6 16:06
 */
public class ReadEmlUtil {

    public static StringBuffer parseEml(File file) throws Exception {
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props, null);
        InputStream inMsg;
        inMsg = new FileInputStream(file);
        return parseEml(session, inMsg);
    }

    public static StringBuffer parseEml(InputStream inMsg) throws Exception {
        Properties props = new Properties();
        Session session = Session.getDefaultInstance(props, null);
        return parseEml(session, inMsg);
    }

    public static void main(String[] args) {
        System.out.println(ReadEmailConstant.MIME_TYPE_MULTIPART.toUpperCase());
    }

    public static StringBuffer parseEml(Session session, InputStream inMsg) throws Exception {
        StringBuffer result = new StringBuffer();

        Message msg = new MimeMessage(session, inMsg);
        Object o = msg.getContent();
        if (msg.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART) || msg.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART.toUpperCase())) {
            Multipart mp = (Multipart) o;

            int totalAttachments = mp.getCount();
            if (totalAttachments > 0) {
                for (int i = 0; i < totalAttachments; i++) {
                    Part part = mp.getBodyPart(i);
                    String s = getMailContent(part);
                    result.append(s);
                    // System.out.println(s);
                }
                inMsg.close();
            }
        } else if (o instanceof Part) {
            /*Part part = (Part) o;
            rePart(part);*/
        } else {
            /*System.out.println("类型" + msg.getContentType());
            System.out.println("内容" + msg.getContent());*/
        }

        inMsg.close();
        return result;
    }

    public static StringBuffer parseEmlContent(MimeMessage msg) throws Exception {
        StringBuffer result = new StringBuffer();

        Object o = msg.getContent();
        if (msg.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART) || msg.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART.toUpperCase())) {
            Multipart mp = (Multipart) o;

            int totalAttachments = mp.getCount();
            if (totalAttachments > 0) {
                for (int i = 0; i < totalAttachments; i++) {
                    Part part = mp.getBodyPart(i);
                    String s = getMailContent(part);
                    result.append(s);
                    // System.out.println(s);
                }
            }
        } else if (o instanceof Part) {
            /*Part part = (Part) o;
            rePart(part);*/
        } else {
            /*System.out.println("类型" + msg.getContentType());
            System.out.println("内容" + msg.getContent());*/
        }

        return result;
    }

    /**
     * @param part
     *            解析内容
     * @throws Exception
     */
    private static void rePart(Part part) throws Exception {

        if (part.getDisposition() != null) {

            String strFileNmae = part.getFileName();
            if(!StringUtils.isEmpty(strFileNmae))
            {	// MimeUtility.decodeText解决附件名乱码问题
                strFileNmae=MimeUtility.decodeText(strFileNmae);
                System.out.println("发现附件: "+ strFileNmae);

                // 打开附件的输入流
                InputStream in = part.getInputStream();
                // 读取附件字节并存储到文件中
                FileOutputStream out = new FileOutputStream(strFileNmae);
                int data;
                while ((data = in.read()) != -1) {
                    out.write(data);
                }
                in.close();
                out.close();

            }

            System.out.println("内容类型: "+ MimeUtility.decodeText(part.getContentType()));
            System.out.println("附件内容:" + part.getContent());


        } else {

            if (part.getContentType().startsWith(ReadEmailConstant.MIME_TYPE_TEXT_PLAIN)) {
                System.out.println("文本内容：" + part.getContent());
            } else {
                // System.out.println("HTML内容：" + part.getContent());
            }
        }
    }

    /**
     * @param multipart
     *            // 接卸包裹（含所有邮件内容(包裹+正文+附件)）
     * @throws Exception
     */
    private static void reMultipart(Multipart multipart) throws Exception {
        // System.out.println("邮件共有" + multipart.getCount() + "部分组成");
        // 依次处理各个部分
        for (int j = 0, n = multipart.getCount(); j < n; j++) {
            // System.out.println("处理第" + j + "部分");
            // 解包, 取出 MultiPart的各个部分,
            Part part = multipart.getBodyPart(j);
            // 每部分可能是邮件内容,
            // 也可能是另一个小包裹(MultipPart)
            // 判断此包裹内容是不是一个小包裹, 一般这一部分是 正文 Content-Type: multipart/alternative
            if (part.getContent() instanceof Multipart) {
                // 转成小包裹
                Multipart p = (Multipart) part.getContent();
                // 递归迭代
                reMultipart(p);
            } else {
                rePart(part);
            }
        }
    }

    public static String getMailContent(Part part) throws Exception {
        String contenttype = part.getContentType();
        int nameindex = contenttype.indexOf("name");
        boolean conname = false;
        if (nameindex != -1) {
            conname = true;
        }
        StringBuilder bodytext = new StringBuilder();
        if (part.isMimeType(ReadEmailConstant.MIME_TYPE_TEXT_PLAIN) && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_TEXT_HTML) && !conname) {
            bodytext.append((String) part.getContent());
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MULTIPART)) {
            Multipart multipart = (Multipart) part.getContent();
            int counts = multipart.getCount();
            for (int i = 0; i < counts; i++) {
                getMailContent(multipart.getBodyPart(i));
            }
        } else if (part.isMimeType(ReadEmailConstant.MIME_TYPE_MESSAGE_RFC822)) {
            getMailContent((Part) part.getContent());
        } else {
        }
        return bodytext.toString();
    }

    /**
     * 读取zip文件带指定后缀的文件
     * @param zipFilePath
     * @param fileExt
     * @param consumer
     */
    public static void readZipFileList(String zipFilePath, String fileExt, Consumer<InputStream> consumer) {
        try {
            ZipFile zipFile = new ZipFile(zipFilePath, Charset.forName("GBK"));
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String entryName = entry.getName();
                if (!entry.isDirectory() && entryName.endsWith(fileExt)) {
                    InputStream inputStream = zipFile.getInputStream(entry);
                    try {
                        consumer.accept(inputStream);
                        // 从io流中读取到字符串
                        /*String s = IoUtil.read(inputStream, "UTF-8");
                        System.out.println(s);
                        inputStream.close();*/
                    } finally {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    }
                }
            }
            zipFile.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

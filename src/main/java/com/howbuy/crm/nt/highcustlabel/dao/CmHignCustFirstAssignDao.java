package com.howbuy.crm.nt.highcustlabel.dao;

import org.springframework.stereotype.Component;

@Component
public interface CmHignCustFirstAssignDao {

    /**
     *  删除高端客户是否分配表
     */
    void deleteCmHighCustFirstAssign();

    /**
     * 更新对应数据
     */
    int insertHighCustFirstAssign();

    /**
     * 将客户的分配过程中只有一个非虚拟投顾的客户标上首次分配客户
     */
    void mergeHighCustFirstAssign();

    /**
     * 处理除CS待处理库以外的客户首次分配日期
     */
    void mergeHighCustFirstAssign1();



}

package com.howbuy.crm.nt.highcustlabel.buss;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao;
import com.howbuy.crm.nt.highcustlabel.domain.CmHighCustinfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class UpdateHighCustInfoLabelBuss {

	@Autowired
	private CmHighCustinfoDao cmHighCustinfoDao;

	public int getMaxPage(){
	    Map<String,Object> params = new HashMap<String,Object>();
		return cmHighCustinfoDao.getMaxNum(params);
	}
	
	public List<CmHighCustinfo> queryCmHighCustinfoByPage(Map<String,Object> params){
		return cmHighCustinfoDao.queryCmHighCustinfo(params);
	}
	
	public void batchUpdateCmHighCustinfo(List<CmHighCustinfo> listhisghcustinfo) {
		cmHighCustinfoDao.batchUpdateCmHighCustinfo(listhisghcustinfo);
	}
}

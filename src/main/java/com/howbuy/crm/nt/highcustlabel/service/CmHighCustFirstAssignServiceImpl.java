/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.highcustlabel.service;

import com.howbuy.crm.nt.highcustlabel.dao.CmHignCustFirstAssignDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description: (高端客户一手分配 ---存储过程改造service 实现层)
 * @since JDK 1.8
 */

@Service("cmHignCustFirstAssignService")
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class CmHighCustFirstAssignServiceImpl implements CmHignCustFirstAssignService {

    @Autowired
    private CmHignCustFirstAssignDao hignCustFirstAssignDao;


    /**
     * @description:()
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:34
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void proDealHighCustFirstassign() {
        try {
            // 1 删除高端客户是否首次分配表
            hignCustFirstAssignDao.deleteCmHighCustFirstAssign();
            // 2 新增客户号数据--高端客户是否一手分配表
            hignCustFirstAssignDao.insertHighCustFirstAssign();
            // 3 将客户的分配过程中只有一个非虚拟投顾的客户标上首次分配客户
            hignCustFirstAssignDao.mergeHighCustFirstAssign();
            // 4 处理除CS待处理库以外的客户首次分配日期
            hignCustFirstAssignDao.mergeHighCustFirstAssign1();
        } catch (Exception e) {
            log.error("高端客户一手分配存储过程执行失败", e);
        }
    }
}
package com.howbuy.crm.nt.highcustlabel.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CmHighCustinfo implements Serializable {

    private static final long serialVersionUID = -1640141246061205619L;

    private String conscustno;

    private String hboneno;

    private String firtrdt;

    private String latesttradedt;

    private Integer page;

    private String zjlabel;

    private String gdlcllabel;
    
    private String gdlcl3mlabel;

    private String gdlcl9mlabel;

    private String smcjlabel;

    private String gdcjlabel;

    private String gdqzlabel;
    
    private String hasgphwlabel;
    private String hasgqhwlabel;
    private String hasdchwlabel;
    private String haszqhwlabel;
    private String hasgshwlabel;
    private String hasfdchwlabel;
    private String hasxjhwlabel;
    private String hasqthwlabel;
    private String hasgplabel;
    private String hasgqlabel;
    private String hasdclabel;
    private String haszqlabel;
    private String hasgslabel;
    private String hasfdclabel;
    private String hasxjlabel;
    private String hasqtlabel;

    private String gdlclnewlabel;
    private String gdcllabel;
    private String lscllabel;

    private String gdkdt;

    private String gdwkdt;

    private String gddtjxz;

    private String gddtytz;
    
    private String firassignlabel;
    
    private String notfirassignlabel;

    private Date creddt;

    private Date upddt;
}

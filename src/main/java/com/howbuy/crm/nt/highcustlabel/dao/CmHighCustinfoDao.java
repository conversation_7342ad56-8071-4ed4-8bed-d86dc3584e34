package com.howbuy.crm.nt.highcustlabel.dao;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.highcustlabel.domain.CmHighCustinfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Component
public interface CmHighCustinfoDao {

    List<CmHighCustinfo> queryCmHighCustinfo(Map<String, Object> params);

    void batchUpdateCmHighCustinfo(List<CmHighCustinfo> listhisghcustinfo);

    int getMaxNum(Map<String, Object> params);

    /**
     * 根据客户号查询
     *
     * @param conscustno
     * @return
     */
    CmHighCustinfo selectByCustNo(@Param("conscustno") String conscustno);

    /**
     * @param
     * @return void
     * @description:(清空资产中心客户持仓情况表表的数据)
     * @author: xufanchao
     * @date: 2023/05/31 09:49
     * @since JDK 1.8
     */
    void cleanCmHighCustFund();

    /**
     * @param
     * @return void
     * @description:(根据产品类型更新持仓情况表)
     * @author: xufanchao
     * @date: 2023/05/31 09:49
     * @since JDK 1.8
     */
    void insertCustHighCustFundByFundType();

    /**
     * @param
     * @return void
     * @description:(根据中台业务码更新持仓情况表)
     * @author: xufanchao
     * @date: 2023/05/31 09:49
     * @since JDK 1.8
     */
    void insertCustHighCustFundByBusCode();

    /**
     * @param
     * @return void
     * @description:(清空 高端成交标签表的数据)
     * @author: xufanchao
     * @date: 2023/6/5 15:19
     * @since JDK 1.8
     */
    void cleanCmHighCustInfo();

    /**
     * @param
     * @return int
     * @description:(高端成交标签表的数据插入最新数据)
     * @author: xufanchao
     * @date: 2023/6/6 15:20
     * @since JDK 1.8
     */
    int insertCmHighCustInfo();

    /**
     * @param
     * @return void
     * @description:(更新高端成交标签表 表中的page字段 按1000/分页)
     * @author: xufanchao
     * @date: 2023/6/6 15:21
     * @since JDK 1.8
     */
    void updateCmHighCustInfo();

    /**
     * @param
     * @return void
     * @description:(插入高端客户首次拜访时间)
     * @author: xufanchao
     * @date: 2023/6/6 15:24
     * @since JDK 1.8
     */
    void mergeintoCmHighCustByFirstDt();

    /**
     * @param
     * @return void
     * @description:(插入高端客户首次分配时间)
     * @author: xufanchao
     * @date: 2023/6/6 15:24
     * @since JDK 1.8
     */
    void mergeintoCmHighCustByFirstAssignDt();

    /**
     * @description:(清空 高端客户公募持仓情况 数据)
     * @author: xufanchao
     * @date: 2023/6/6 15:25
     * @since JDK 1.8
     */
    void cleanCmHighCustPubFund();

    /**
     * @description:(插入高端客户持有公募产品情况)
     * @param
     * @return int
     * @author: xufanchao
     * @date: 2023/6/6 15:26
     * @since JDK 1.8
     */
    int insertCmHighCustPubFund();
}

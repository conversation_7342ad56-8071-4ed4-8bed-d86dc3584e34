/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.highcustlabel.service;

import com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao;
import crm.howbuy.base.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;


/**
 * @description: ( 处理高端客户的首次访问日期，首次分配日期，持有公募情况)
 * <AUTHOR>
 * @date 2023/6/6 15:29
 * @since JDK 1.8
 */
@Slf4j
@Service("cmDealhighCustInfoParamService")
public class CmDealHighCustInfoServiceImpl implements CmDealhighCustInfoParamService{

    @Autowired
    private CmHighCustinfoDao cmHighCustinfoDao;

    /**
     * @description:(高端客户存储任务--业务逻辑处理)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:26
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealHighCustInfoParam() {
        // 1.清空 高端成交标签表表数据
        cmHighCustinfoDao.cleanCmHighCustInfo();
        // 2.往高端成交标签表里插入数据
        cmHighCustinfoDao.insertCmHighCustInfo();
        // 3.更新高端成交标签表里面的page字段
        cmHighCustinfoDao.updateCmHighCustInfo();
        // 4.插入高端客户首次拜访时间
        cmHighCustinfoDao.mergeintoCmHighCustByFirstDt();
        // 5.插入高端客户首次分配时间
        cmHighCustinfoDao.mergeintoCmHighCustByFirstAssignDt();
        // 6.清空 高端客户公募持仓情况 表数据
        cmHighCustinfoDao.cleanCmHighCustPubFund();
        // 7.往高端客户公募持仓情况 插入数据
        cmHighCustinfoDao.insertCmHighCustPubFund();

    }
}
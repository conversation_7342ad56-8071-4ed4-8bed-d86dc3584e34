package com.howbuy.crm.nt.highcustlabel.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.highcustlabel.buss.UpdateHighCustInfoLabelBuss;
import com.howbuy.crm.nt.highcustlabel.domain.CmHighCustinfo;
import com.howbuy.crm.util.CrmNtConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.cc.center.member.tag.domain.UserTagsDomain;
import com.howbuy.cc.center.member.tag.request.QueryUserTagsListBatchRequest;
import com.howbuy.cc.center.member.tag.response.QueryUserTagsListBatchResponse;
import com.howbuy.cc.center.member.tag.service.QueryUserTagsListBatchService;

@Service("highCustLabelService")
@Slf4j
public class HighCustLabelServiceImpl implements HighCustLabelService {

	@Autowired
	private UpdateHighCustInfoLabelBuss updateHighCustInfoLabelBuss;
	
	@Autowired
	private QueryUserTagsListBatchService queryUserTagsListBatchService;
	
	@Override
	public String syncHighCustLabel(String arg) {
		log.info("开始执行标签同步调用！");
		int maxpage = updateHighCustInfoLabelBuss.getMaxPage();
		log.info("一共要执行"+maxpage+"页");
		for(int i = 1; i <= maxpage;i++){
			Map<String,Object> param = new HashMap<String,Object>();
			param.put("page", i);
			List<CmHighCustinfo> list = updateHighCustInfoLabelBuss.queryCmHighCustinfoByPage(param);
			List<CmHighCustinfo> listaddlab = new ArrayList<CmHighCustinfo>();
			List<String> liststr = new ArrayList<String>();
			for(CmHighCustinfo info : list){
				liststr.add(info.getHboneno());
			}
			List<String> listlabels = new ArrayList<String>();
			listlabels.add(CrmNtConstant.ZJLABLES);
			listlabels.add(CrmNtConstant.GDLCLLABLES);
			listlabels.add(CrmNtConstant.GDLCL3MLABLES);
			listlabels.add(CrmNtConstant.GDLCLN9MLABLES);
			listlabels.add(CrmNtConstant.SMCJLABLES);
			listlabels.add(CrmNtConstant.GDCJLABLES);
			listlabels.add(CrmNtConstant.GDQZLABLES);
			
			listlabels.add(CrmNtConstant.GDKDTLABLES);
			listlabels.add(CrmNtConstant.GDWKDTLABLES);
			listlabels.add(CrmNtConstant.GDDTJXZLABLES);
			listlabels.add(CrmNtConstant.GDDTYTZLABLES);
			
			//持有产品类型标签
			listlabels.add(CrmNtConstant.HASGPHWLABLES);
			listlabels.add(CrmNtConstant.HASGQHWLABLES);
			listlabels.add(CrmNtConstant.HASDCHWLABLES);
			listlabels.add(CrmNtConstant.HASZQHWLABLES);
			listlabels.add(CrmNtConstant.HASGSHWLABLES);
			listlabels.add(CrmNtConstant.HASFDCHWLABLES);
			listlabels.add(CrmNtConstant.HASXJHWLABLES);
			listlabels.add(CrmNtConstant.HASQTHWLABLES);
			listlabels.add(CrmNtConstant.HASGPLABLES);
			listlabels.add(CrmNtConstant.HASGQLABLES);
			listlabels.add(CrmNtConstant.HASDCLABLES);
			listlabels.add(CrmNtConstant.HASZQLABLES);
			listlabels.add(CrmNtConstant.HASGSLABLES);
			listlabels.add(CrmNtConstant.HASFDCLABLES);
			listlabels.add(CrmNtConstant.HASXJLABLES);
			listlabels.add(CrmNtConstant.HASQTLABLES);
			
			//是否一手分配客户
			listlabels.add(CrmNtConstant.FIRASSIGNLABEL);
			listlabels.add(CrmNtConstant.NOTFIRASSIGNLABEL);

			listlabels.add(CrmNtConstant.GDLCLNEWLABEL);
			listlabels.add(CrmNtConstant.GDCLLABEL);
			listlabels.add(CrmNtConstant.LSCLLABEL);

			QueryUserTagsListBatchRequest req = new QueryUserTagsListBatchRequest();
			req.setHboneNoList(liststr);
			req.setTagNoList(listlabels);
			QueryUserTagsListBatchResponse res = queryUserTagsListBatchService.execute(req);
			log.info("返回码："+JSON.toJSONString(res));
			if(CrmNtConstant.RMISucc.equals(res.getReturnCode())){
				List<UserTagsDomain> listresult = res.getUserTagsList();
				log.info("获取条数："+listresult.size());
				for(UserTagsDomain usertages : listresult){
					if(usertages.getTagNoList() != null ){
						CmHighCustinfo addinfo = new CmHighCustinfo();
						addinfo.setHboneno(usertages.getHboneNo());
						boolean flag = false;
						if(usertages.getTagNoList().contains(CrmNtConstant.ZJLABLES)){
							addinfo.setZjlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setZjlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.GDLCLLABLES)){
							addinfo.setGdlcllabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdlcllabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.GDLCL3MLABLES)){
							addinfo.setGdlcl3mlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdlcl3mlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.GDLCLN9MLABLES)){
							addinfo.setGdlcl9mlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdlcl9mlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.SMCJLABLES)){
							addinfo.setSmcjlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setSmcjlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDCJLABLES)){
							addinfo.setGdcjlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdcjlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDQZLABLES)){
							addinfo.setGdqzlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdqzlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDKDTLABLES)){
							addinfo.setGdkdt(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdkdt(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDWKDTLABLES)){
							addinfo.setGdwkdt(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGdwkdt(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDDTJXZLABLES)){
							addinfo.setGddtjxz(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGddtjxz(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.GDDTYTZLABLES)){
							addinfo.setGddtytz(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setGddtytz(CrmNtConstant.CRM_LABEL_NO);
						}
						//人持有产品类型海外标签
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGPHWLABLES)){
							addinfo.setHasgphwlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasgphwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGQHWLABLES)){
							addinfo.setHasgqhwlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasgqhwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASDCHWLABLES)){
							addinfo.setHasdchwlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasdchwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASZQHWLABLES)){
							addinfo.setHaszqhwlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHaszqhwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGSHWLABLES)){
							addinfo.setHasgshwlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasgshwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASFDCHWLABLES)){
							addinfo.setHasfdchwlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasfdchwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASXJHWLABLES)){
							addinfo.setHasxjhwlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasxjhwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASQTHWLABLES)){
							addinfo.setHasqthwlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasqthwlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						//人持有产品类型标签
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGPLABLES)){
							addinfo.setHasgplabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasgplabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGQLABLES)){
							addinfo.setHasgqlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasgqlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASDCLABLES)){
							addinfo.setHasdclabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasdclabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASZQLABLES)){
							addinfo.setHaszqlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHaszqlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASGSLABLES)){
							addinfo.setHasgslabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasgslabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASFDCLABLES)){
							addinfo.setHasfdclabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasfdclabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASXJLABLES)){
							addinfo.setHasxjlabel(CrmNtConstant.CRM_LABEL_YES);
							flag = true;
						}else{
							addinfo.setHasxjlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.HASQTLABLES)){
							addinfo.setHasqtlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setHasqtlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.FIRASSIGNLABEL)){
							addinfo.setFirassignlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setFirassignlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						
						if(usertages.getTagNoList().contains(CrmNtConstant.NOTFIRASSIGNLABEL)){
							addinfo.setNotfirassignlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setNotfirassignlabel(CrmNtConstant.CRM_LABEL_NO);
						}

						if(usertages.getTagNoList().contains(CrmNtConstant.GDLCLNEWLABEL)){
							addinfo.setGdlclnewlabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setGdlclnewlabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.GDCLLABEL)){
							addinfo.setGdcllabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setGdcllabel(CrmNtConstant.CRM_LABEL_NO);
						}
						if(usertages.getTagNoList().contains(CrmNtConstant.LSCLLABEL)){
							addinfo.setLscllabel(CrmNtConstant.CRM_LABEL_YES);
						}else{
							addinfo.setLscllabel(CrmNtConstant.CRM_LABEL_NO);
						}

						if(flag){
							listaddlab.add(addinfo);
						}
					}
				}
				log.info("有标签条数："+listaddlab.size());
			}
			if(listaddlab.size()>0){
				updateHighCustInfoLabelBuss.batchUpdateCmHighCustinfo(listaddlab);
			}
		}
		log.info("标签同步执行完毕");
		return "执行成功！";
	}

}

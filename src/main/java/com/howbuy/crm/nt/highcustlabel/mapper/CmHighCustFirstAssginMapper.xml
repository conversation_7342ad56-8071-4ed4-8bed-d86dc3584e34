<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.highcustlabel.dao.CmHignCustFirstAssignDao">

	<update id="deleteCmHighCustFirstAssign">
		truncate table CM_HIGHCUST_FIRSTASSIGN
	</update>

	<insert id="insertHighCustFirstAssign" parameterType="java.util.Map">
		INSERT INTO CM_HIGHCUST_FIRSTASSIGN (CONSCUSTNO)
		SELECT T.CONSCUSTNO FROM CM_HIGH_CUSTINFO T
	</insert>

	<update id="mergeHighCustFirstAssign">
		MERGE INTO CM_HIGHCUST_FIRSTASSIGN A
		USING (SELECT T.CONSCUSTNO, COUNT(T1.CONSCODE) ISFIRSTASSIGN
			   FROM CM_HIGH_CUSTINFO T
						INNER JOIN (select '999999999' as custconshisid, custno, conscode, startdt, enddt
									from CM_CustConstant
									union all
									select custconshisid, custno, conscode, startdt, enddt
									from CM_CustConstantHis) T1 ON T.CONSCUSTNO = T1.CUSTNO
						INNER JOIN CM_CONSULTANT T2 ON T1.CONSCODE = T2.CONSCODE AND T2.ISVIRTUAL = '0'
			   GROUP BY T.CONSCUSTNO
			   HAVING COUNT(T1.CONSCODE) = 1) B
		ON (A.CONSCUSTNO = B.CONSCUSTNO)
		WHEN MATCHED THEN
			UPDATE
			SET A.ISFIRSTASSIGN = '1',
				A.UPDDT         = SYSDATE
	</update>

	<update id="mergeHighCustFirstAssign1">
		MERGE INTO CM_HIGHCUST_FIRSTASSIGN A
			USING (
				SELECT T.CONSCUSTNO, MIN(T1.STARTDT) FIRSTDT
				FROM CM_HIGH_CUSTINFO T
						 INNER JOIN (select '999999999' as custconshisid, custno, conscode, startdt, enddt
									 from CM_CustConstant
									 union all
									 select custconshisid, custno, conscode, startdt, enddt
									 from CM_CustConstantHis) T1 ON T.CONSCUSTNO = T1.CUSTNO
				WHERE T1.CONSCODE != 'DFPXNK'
				GROUP BY T.CONSCUSTNO
			) B
			ON (A.CONSCUSTNO = B.CONSCUSTNO)
			WHEN MATCHED THEN
				UPDATE SET A.FIRSTASSIGNDT = B.FIRSTDT, A.UPDDT = SYSDATE
	</update>




</mapper>
/**
 * Copyright (c) 2023, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.highcustlabel.service;

import com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (处理单个客户产品的预约是否需要进入黑名单)
 * <AUTHOR>
 * @date 2023/05/31 10:28
 * @since JDK 1.8
 */
@Service("cmProDealHighCustFundService")
@Transactional(rollbackFor = Exception.class)
public class CmProDealHighCustFundServiceImpl implements CmProDealHighCustFundService{

    @Autowired
    private CmHighCustinfoDao cmHighCustinfoDao;


    /**
     * @description:(处理单个客户是否进入黑名单存储过程改造--业务逻辑处理)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:20
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealHighCustFund() {
        // 1. 清除高端持仓情况的表
        cmHighCustinfoDao.cleanCmHighCustFund();
        // 2. 根据基金的类型更新高端持仓交易记录的表
        cmHighCustinfoDao.insertCustHighCustFundByFundType();
        // 3. 根据中台的业务码更新高端持仓交易记录表
        cmHighCustinfoDao.insertCustHighCustFundByBusCode();
    }
}
package com.howbuy.crm.nt.highcustlabel.buss;


import com.howbuy.crm.nt.highcustlabel.service.CmHignCustFirstAssignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:(高端客户一手分配 实现方法)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmHighCustFirstAssignBuss {

	@Autowired
	private CmHignCustFirstAssignService cmHignCustFirstAssignService;

	public void proDealHighCustFirstassign() {
		try {
			// 业务逻辑处理
			cmHignCustFirstAssignService.proDealHighCustFirstassign();
		} catch (Exception e) {
			log.error("error in CmHighCustFirstAssignBuss", e);
		}
	}
}

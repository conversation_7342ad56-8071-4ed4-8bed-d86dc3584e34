<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.highcustlabel.domain.CmHighCustinfo">
    	<result column="conscustno" property="conscustno" />
        <result column="hboneno" property="hboneno" />
        <result column="firtrdt" property="firtrdt" />
        <result column="latesttradedt" property="latesttradedt" />
        <result column="page" property="page" jdbcType="INTEGER"/>
        <result column="zjlabel" property="zjlabel" />
        <result column="gdlcllabel" property="gdlcllabel" />
		<result column="gdlcl3mlabel" property="gdlcl3mlabel" />
		<result column="gdlcl9mlabel" property="gdlcl9mlabel" />
        <result column="smcjlabel" property="smcjlabel" />
        <result column="gdcjlabel" property="gdcjlabel" />
        <result column="gdqzlabel" property="gdqzlabel" />
        <result column="hasgphwlabel" property="hasgphwlabel" />
		<result column="hasgqhwlabel" property="hasgqhwlabel" />
		<result column="hasdchwlabel" property="hasdchwlabel" />
		<result column="haszqhwlabel" property="haszqhwlabel" />
		<result column="hasgshwlabel" property="hasgshwlabel" />
		<result column="hasfdchwlabel" property="hasfdchwlabel" />
		<result column="hasxjhwlabel" property="hasxjhwlabel" />
		<result column="hasqthwlabel" property="hasqthwlabel" />
		<result column="hasgplabel" property="hasgplabel" />
		<result column="hasgqlabel" property="hasgqlabel" />
		<result column="hasdclabel" property="hasdclabel" />
		<result column="haszqlabel" property="haszqlabel" />
		<result column="hasgslabel" property="hasgslabel" />
		<result column="hasfdclabel" property="hasfdclabel" />
		<result column="hasxjlabel" property="hasxjlabel" />
		<result column="hasqtlabel" property="hasqtlabel" />
		<result column="firassignlabel" property="firassignlabel" />
		<result column="notfirassignlabel" property="notfirassignlabel" />

		<result column="gdlclnewlabel" property="gdlclnewlabel" />
		<result column="gdcllabel" property="gdcllabel" />
		<result column="lscllabel" property="lscllabel" />

        <result column="gdkdt" property="gdkdt" />
        <result column="gdwkdt" property="gdwkdt" />
        <result column="gddtjxz" property="gddtjxz" />
        <result column="gddtytz" property="gddtytz" />
        <result column="creddt" property="creddt" jdbcType="DATE"/>
        <result column="upddt" property="upddt" jdbcType="DATE"/>
    </resultMap>


	<select id="selectByCustNo" parameterType="string" resultMap="BaseResultMap" >
            SELECT  *
              FROM CM_HIGH_CUSTINFO
             WHERE CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
    </select>

    <select id="queryCmHighCustinfo" resultMap="BaseResultMap" parameterType="map">
		SELECT T.CONSCUSTNO,
		       T.HBONENO,
		       T.FIRTRDT,
		       T.LATESTTRADEDT,
		       T.ZJLABEL,
		       T.GDLCLLABEL,
		       T.GDLCL3MLABEL,
			   T.GDLCL9MLABEL,
		       T.SMCJLABEL,
		       T.GDCJLABEL,
		       T.GDQZLABEL,
		       T.HASGPHWLABEL,
			   T.HASGQHWLABEL,
				T.HASDCHWLABEL,
				T.HASZQHWLABEL,
				T.HASGSHWLABEL,
				T.HASFDCHWLABEL,
				T.HASXJHWLABEL,
				T.HASQTHWLABEL,
				T.HASGPLABEL,
				T.HASGQLABEL,
				T.HASDCLABEL,
				T.HASZQLABEL,
				T.HASGSLABEL,
				T.HASFDCLABEL,
				T.HASXJLABEL,
				T.HASQTLABEL,
				T.FIRASSIGNLABEL,
				T.NOTFIRASSIGNLABEL,
				T.GDLCLNEWLABEL,
				T.GDCLLABEL,
				T.LSCLLABEL,
		       T.GDKDT,
		       T.GDWKDT,
		       T.GDDTJXZ,
		       T.GDDTYTZ,
		       T.PAGE,
		       T.CREDDT,
		       T.UPDDT
		  FROM CM_HIGH_CUSTINFO T
		 WHERE T.PAGE = #{page}
    </select>
    
    <update id="batchUpdateCmHighCustinfo" parameterType="java.util.List">
	      <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	          update CM_HIGH_CUSTINFO 
	          <set>
		            ZJLABEL = #{item.zjlabel},
		            GDLCLLABEL = #{item.gdlcllabel},
		            GDLCL3MLABEL = #{item.gdlcl3mlabel},
				    GDLCL9MLABEL = #{item.gdlcl9mlabel},
		            SMCJLABEL = #{item.smcjlabel},
		            GDCJLABEL = #{item.gdcjlabel},
		            GDQZLABEL = #{item.gdqzlabel},
		            HASGPHWLABEL = #{item.hasgphwlabel},
					HASGQHWLABEL = #{item.hasgqhwlabel},
					HASDCHWLABEL = #{item.hasdchwlabel},
					HASZQHWLABEL = #{item.haszqhwlabel},
					HASGSHWLABEL = #{item.hasgshwlabel},
					HASFDCHWLABEL = #{item.hasfdchwlabel},
					HASXJHWLABEL = #{item.hasxjhwlabel},
					HASQTHWLABEL = #{item.hasqthwlabel},
					HASGPLABEL = #{item.hasgplabel},
					HASGQLABEL = #{item.hasgqlabel},
					HASDCLABEL = #{item.hasdclabel},
					HASZQLABEL = #{item.haszqlabel},
					HASGSLABEL = #{item.hasgslabel},
					HASFDCLABEL = #{item.hasfdclabel},
					HASXJLABEL = #{item.hasxjlabel},
					HASQTLABEL = #{item.hasqtlabel},
					FIRASSIGNLABEL = #{item.firassignlabel},
					NOTFIRASSIGNLABEL = #{item.notfirassignlabel},

				    GDLCLNEWLABEL = #{item.gdlclnewlabel},
				    GDCLLABEL = #{item.gdcllabel},
				    LSCLLABEL = #{item.lscllabel},

		            GDKDT = #{item.gdkdt},
		            GDWKDT = #{item.gdwkdt},
		            GDDTJXZ = #{item.gddtjxz},
		            GDDTYTZ = #{item.gddtytz},
		            UPDDT = sysdate
	          </set>
	          where hboneno = #{item.hboneno}
       	  </foreach>
    </update>   
    
    <select id="getMaxNum" parameterType="Map" resultType="int" useCache="false">
	 	SELECT MAX(T.PAGE) FROM CM_HIGH_CUSTINFO T
	 </select>

	<update id="cleanCmHighCustFund">
		truncate table CM_HIGH_CUSTFUND
	</update>

	<insert id="insertCustHighCustFundByFundType">
		INSERT INTO CM_HIGH_CUSTFUND
		(HBONENO,
		 CUSTNO,
		 FUNDCODE,
		 FUNDNAME,
		 BALANCEVOL,
		 NAV,
		 NAVDT,
		 MARKETVAL,
		 MARKETVALRMB,
		 CURRENCYUNIT,
		 FUNDTYPE,
		 BALANCECOST,
		 BALANCEINCOME,
		 FIRSTACKDT,
		 ACKDT,
		 VERSION,
		 HOLD_FLAG)
		SELECT T.HBONENO,
			   T1.CONSCUSTNO CUSTNO,
			   T.FUNDCODE,
			   T.FUNDNAME,
			   T.BALANCEVOL,
			   T.NAV,
			   T.NAVDT,
			   T.MARKETVAL,
			   T.MARKETVALRMB,
			   T.CURRENCYUNIT,
			   T.FUNDTYPE,
			   T.BALANCECOST,
			   T.BALANCEINCOME,
			   T.FIRSTACKDT,
			   T.ACKDT,
			   T.VERSION,
			   <!--如果是高端产品，判断逻辑是 持仓>1   BALANCEVOL > 1
               如果是香港产品，判断逻辑是 持仓>0   BALANCEVOL > 0-->
				(CASE
				WHEN (J.SFXG='1' AND T.BALANCEVOL >0 ) THEN '1'
				WHEN ( (j.sfxg is null or J.SFXG!='1') AND T.BALANCEVOL >1 ) THEN '1'
				ELSE '0'
				END
				) HOLD_FLAG
		FROM CM_ZC_TRADECUSTFUND T
				 INNER JOIN CM_CONSCUST T1
							ON T.HBONENO = T1.HBONE_NO
			 LEFT JOIN JJXX1 J ON J.JJDM = T.FUNDCODE
		WHERE T.FUNDTYPE IN ('2', '3')
	</insert>

	<insert id="insertCustHighCustFundByBusCode">
		INSERT INTO CM_HIGH_CUSTFUND
			(HBONENO, CUSTNO, FUNDCODE, BALANCEVOL, ACKDT,HOLD_FLAG)
		SELECT Y.HBONENO, Y.CONSCUSTNO CUSTNO, Y.FUNDCODE, 0, Y.LASTTRADEDT,'0'
		FROM (SELECT X.HBONENO,
					 X.CONSCUSTNO,
					 X.FUNDCODE,
					 MAX(X.TRADEDT) LASTTRADEDT
			  FROM (SELECT T1.HBONE_NO   HBONENO,
						   T2.CONSCUSTNO,
						   T.PRODUCTCODE FUNDCODE,
						   T.TATRADEDT   TRADEDT
					FROM CM_ZT_ORDERINFO T
					LEFT JOIN AC_TX_HBONE T1   ON T.TXACCTNO = T1.CUST_NO
					LEFT JOIN CM_CONSCUST T2   ON T1.HBONE_NO = T2.HBONE_NO
					WHERE T.MBUSICODE IN ('1120', '1122')
					  AND T.ORDERSTATUS IN ('2', '3')
					UNION ALL
					SELECT t1.HBONE_NO HBONENO,T1.CONSCUSTNO,T.FUNDCODE,T.TRADEDT
					FROM CM_CUSTPRIVATEFUNDTRADE T
					LEFT JOIN CM_CONSCUST t1   on t.custno = t1.conscustno
					where T.RECSTAT = 1
					  AND T.CHECKFLAG = 4
					  AND (T.BUSICODE = 120 OR T.BUSICODE = 122)
					  AND T.ISDXFLAG = '2'
				      AND T1.HBONE_NO IS NOT NULL
					UNION ALL
				    SELECT T1.HBONE_NO HBONENO,T1.CONSCUSTNO,T.PRODUCT_CODE,T.TA_TRADE_DT
				    FROM CM_HW_ORDER_INFO T
					LEFT JOIN CM_HK_CONSCUST S ON S.HK_TX_ACCT_NO =T.HK_CUST_NO
				    LEFT JOIN CM_CONSCUST T1   ON S.CONSCUSTNO=T1.CONSCUSTNO
				    WHERE T.REC_STAT='0'
				    AND T.MIDDLE_BUSI_CODE IN ('1120', '1122')
		            AND T.ORDER_STATUS IN ('2', '3')
				    AND T1.HBONE_NO IS NOT NULL
				    AND T.TA_TRADE_DT IS NOT NULL <!--海外订单 交易确认才会有值-->
					) X
			  GROUP BY X.HBONENO, X.CONSCUSTNO, X.FUNDCODE) Y
		WHERE Y.HBONENO IS NOT NULL
		  AND NOT EXISTS (SELECT 1
						  FROM CM_ZC_TRADECUSTFUND Q
						  WHERE Q.HBONENO = Y.HBONENO
							AND Q.FUNDCODE = Y.FUNDCODE)
	</insert>

	<update id="cleanCmHighCustInfo">
		TRUNCATE TABLE CM_HIGH_CUSTINFO
	</update>

	<insert id="insertCmHighCustInfo">
		INSERT INTO CM_HIGH_CUSTINFO
			(CONSCUSTNO, HBONENO, FIRTRDT, LATESTTRADEDT)
		SELECT T.CONSCUSTNO, T.HBONE_NO, CD.FIRTRDT, CD.LATESTTRADEDT
		FROM CM_CONSCUST T
				 LEFT JOIN CM_CUSTCONSTANT T1   ON T.CONSCUSTNO = T1.CUSTNO
				 LEFT JOIN (SELECT A.HBONENO,
								   MIN(A.TRADEDT) FIRTRDT,
								   MAX(A.TRADEDT) LATESTTRADEDT
							FROM (
		                          <!--高端中台：认申购 -->
		                          SELECT T2.HBONE_NO       HBONENO,
										 T.EXPECTEDTRADEDT TRADEDT
								  FROM CM_ZT_ORDERINFO T
										   LEFT JOIN AC_TX_HBONE T2 ON T.TXACCTNO = T2.TX_ACCT_NO AND T2.STAT = '0'
								  WHERE T.ORDERSTATUS IN ('2', '3')
									AND T.MBUSICODE IN ('1120', '1122')
								  UNION ALL
		                          <!--crm直销交易记录：认申购 -->
								  SELECT t1.hbone_no HBONENO,T.TRADEDT
								  FROM CM_CUSTPRIVATEFUNDTRADE T
									left join cm_conscust t1 on t.custno = t1.conscustno
								  where T.RECSTAT = 1
									AND T.CHECKFLAG = 4
									AND (T.BUSICODE = 120 OR T.BUSICODE = 122)
									AND T.ISDXFLAG = '2'
								  UNION ALL
		                          <!--高端中台 业务 1134-非交易过户转入 -->
								  SELECT T2.HBONE_NO HBONENO, TN.SUBMITTADT TRADEDT
								  FROM CM_ZT_NOTRADETRANSFER TN
										   LEFT JOIN AC_TX_HBONE T2	 ON TN.TXACCTNO = T2.TX_ACCT_NO AND T2.STAT = '0'
								  WHERE TN.ORDERSTATUS = '3'
									AND TN.MBUSICODE = '1134'
		                          UNION ALL
		                         <!--海外中台：认申购 -->
								SELECT T1.HBONE_NO HBONENO,T.TA_TRADE_DT TRADEDT
								FROM CM_HW_ORDER_INFO T
								   LEFT JOIN CM_HK_CONSCUST S ON S.HK_TX_ACCT_NO =T.HK_CUST_NO
								   LEFT JOIN CM_CONSCUST T1   ON S.CONSCUSTNO=T1.CONSCUSTNO
								WHERE T.REC_STAT='0'
								AND T.MIDDLE_BUSI_CODE IN ('1120', '1122')
								AND T.ORDER_STATUS IN ('2', '3')
								AND T1.HBONE_NO IS NOT NULL
								AND T.TA_TRADE_DT IS NOT NULL <!--海外订单 交易确认才会有值-->
								 ) A
							GROUP BY A.HBONENO) CD
						   ON T.HBONE_NO = CD.HBONENO
		WHERE T.CONSCUSTSTATUS = '0'
		  AND T1.CONSCODE IN
			  (SELECT CC.CONSCODE
			   FROM CM_CONSULTANT CC
			   WHERE CC.CONSSTATUS = '1'
				 AND CC.OUTLETCODE IN
					 (SELECT T.ORGCODE
					  FROM HB_ORGANIZATION T
					  WHERE T.SHOWFLAG = '0'
						AND T.STATUS = '0'
		START WITH ORGCODE IN ('1', '10', '14','1000005627')
		CONNECT BY PRIOR ORGCODE = PARENTORGCODE))
	</insert>


	<update id="updateCmHighCustInfo">
		UPDATE CM_HIGH_CUSTINFO T
		SET T.PAGE = CEIL(ROWNUM / 1000)
		WHERE T.HBONENO IS NOT NULL
	</update>


	<update id="mergeintoCmHighCustByFirstDt">
		MERGE INTO CM_HIGH_CUSTINFO A
			USING (SELECT T.CONSCUSTNO, TO_CHAR(MIN(T1.CREDT), 'yyyymmdd') FIRVISITDT
				   FROM CM_HIGH_CUSTINFO T
							INNER JOIN CS_COMMUNICATE_VISIT T1
									   ON T.CONSCUSTNO = T1.CONSCUSTNO
				   WHERE T.CONSCUSTNO IS NOT NULL
				   GROUP BY T.CONSCUSTNO) B
			ON (A.CONSCUSTNO = B.CONSCUSTNO)
			WHEN MATCHED THEN
				UPDATE SET A.FIRVISITDT = B.FIRVISITDT
	</update>

	<update id="mergeintoCmHighCustByFirstAssignDt">
		MERGE INTO CM_HIGH_CUSTINFO A
			USING (SELECT T.CONSCUSTNO, MIN(T1.STARTDT) FIRASSIGNDT
				   FROM CM_HIGH_CUSTINFO T
							LEFT JOIN (select '999999999' as custconshisid, custno, conscode, startdt, enddt
									   from CM_CustConstant
									   union all
									   select custconshisid, custno, conscode, startdt, enddt
									   from CM_CustConstantHis) T1
									  ON T.CONSCUSTNO = T1.CUSTNO
				   GROUP BY T.CONSCUSTNO) B
			ON (A.CONSCUSTNO = B.CONSCUSTNO)
			WHEN MATCHED THEN
				UPDATE SET A.FIRASSIGNDT = B.FIRASSIGNDT
	</update>

	<update id="cleanCmHighCustPubFund">
		TRUNCATE TABLE CM_HIGHCUST_PUBFUND
	</update>

	<insert id="insertCmHighCustPubFund">
		INSERT INTO CM_HIGHCUST_PUBFUND
			(CONSCUSTNO, HBONENO, FUNDCODE, BALANCEVOL, GLRM)
		SELECT CHC.CONSCUSTNO,
			   CHC.HBONENO,
			   BAL.FUND_CODE   FUNDCODE,
			   BAL.BALANCE_VOL BALANCEVOL,
			   JJXX1.GLRM
		FROM CM_HIGH_CUSTINFO      CHC,
			 AC_TX_HBONE           ATH,
			 SYNC_AC_FUND_ACCT_BAL BAL,
			 SYNC_AC_FUND_TX_ACCT  TXACCT,
			 JJXX1                 JJXX1
		WHERE CHC.HBONENO = ATH.HBONE_NO
		  AND TXACCT.CUST_NO = ATH.CUST_NO
		  AND TXACCT.FUND_TX_ACCT_NO = BAL.FUND_TX_ACCT_NO
		  AND BAL.FUND_CODE = JJXX1.JJDM
	</insert>

</mapper>
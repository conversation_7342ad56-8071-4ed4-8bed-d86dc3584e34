package com.howbuy.crm.nt.secretkey.service;

import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cache.CacheConstants;
import com.howbuy.crm.nt.secretkey.request.SecretKeyRequest;
import com.howbuy.crm.nt.secretkey.response.SecretKeyServiceResponse;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投顾小秘检查用户名和验证码是否正确
 * 根据投顾小秘传过来的验证码和投顾号，查询该投顾号在缓存魔方中的值比较，如果相同并且没有超过12小时，返回成功
 */
@Service("secretKeyService")
public class SecretKeyServiceImpl implements SecretKeyService {
	static Log log = LogFactory.getLog(SecretKeyServiceImpl.class);
	@Override
	public BaseResponse judgeSecretKey(SecretKeyRequest request) {
		BaseResponse response = new BaseResponse();
		String conscode = request.getUserId();
		String key = request.getUserCode();
		Object keyobj = CacheServiceImpl.getInstance().get(CacheConstants.SECRET_KEY_CACHE_PREFIX + conscode);
		if(StringUtil.isNotNullStr(key) && StringUtil.isNotNullStr(keyobj)){
			if(StringUtil.replaceNull(key).equals(StringUtil.replaceNullStr(keyobj))){
				String trandkey = key.substring(0, 14);
				String curtime = DateTimeUtil.PatternDate(new Date(),"yyyyMMddHHmmss");
				BigDecimal diff = DateTimeUtil.getBetweenHours(trandkey, curtime);
				//如果当前的时间戳和传过来的时间戳查过12小时，也自动失效
				if(diff.compareTo(new BigDecimal(12)) > 0){
					response.processedFail();
				}else{
					response.success();
				}
			}else{
				response.processedFail();
			}
		}else{
			response.processedFail();
		}
		
		return response;
	}
	
	@Override
	public SecretKeyServiceResponse getSecretKey(SecretKeyRequest request){
		SecretKeyServiceResponse response = new SecretKeyServiceResponse();
		String conscode = request.getUserId();
		Object keyobj = CacheServiceImpl.getInstance().get(CacheConstants.SECRET_KEY_CACHE_PREFIX + conscode);
		if(StringUtil.isNotNullStr(keyobj)){
			response.success();
			response.setSecretKey(StringUtil.replaceNullStr(keyobj));
		}else{
			response.setReturnCode("0002");
		}
		return response;
	}
	
	@Override
	public BaseResponse creatSecretKeyByUserId(SecretKeyRequest request){
		BaseResponse response = new BaseResponse();
		String conscode = request.getUserId();
		String secretkeyval = DateTimeUtil.PatternDate(new Date(),"yyyyMMddHHmmssSSS");
		// 改造原有缓存魔方[SECRET_KEY_]的逻辑，改为使用EC缓存
		CacheServiceImpl.getInstance().remove(CacheConstants.SECRET_KEY_CACHE_PREFIX + conscode);
		CacheServiceImpl.getInstance().put(CacheConstants.SECRET_KEY_CACHE_PREFIX + conscode, secretkeyval);
		response.success();
		return response;
	}

}

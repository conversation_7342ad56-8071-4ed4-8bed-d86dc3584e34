package com.howbuy.crm.nt.message.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.transaction.annotation.Transactional;
import com.howbuy.crm.nt.message.buss.CmPushMsgDaoBuss;
import com.howbuy.crm.nt.message.dao.CmJjjzMsgLatestDao;
import com.howbuy.crm.nt.message.dao.CmJjjzMsgRecDao;
import com.howbuy.crm.nt.message.domain.CmJjjzMsgRec;
import com.howbuy.simu.dto.business.product.SmjzAndHbAndLastDto;
import com.howbuy.simu.service.business.product.SmjzAndHbService;

@Service("pushJzMsgCollectService")
public class PushJzMsgCollectServiceImpl implements PushJzMsgCollectService {
	static Log log = LogFactory.getLog(PushJzMsgCollectServiceImpl.class);
	
	@Autowired
	private CmJjjzMsgRecDao cmJjjzMsgRecDao;
	
	@Autowired
	private CmJjjzMsgLatestDao cmJjjzMsgLatestDao;
	
	@Autowired
	private CmPushMsgDaoBuss cmPushMsgDaoBuss;
	
	@Autowired
	private SmjzAndHbService smjzAndHbService;

	public static final String ARG_AM9 = "AM9";
	public static final String ARG_START = "START";
	public static final String ARG_PM17 = "PM17";

	/**
	 * 同步净值通知消息数据
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void syncJztzMsgData(String arg) {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();

		// 执行净值跑批方法（早上9点）
		if (arg != null && arg.contains(ARG_AM9)) {
			param.put("timeFlag", ARG_AM9);

			// 同步DB净值数据
			log.info("执行同步DB净值数据跑批方法（早上9点）开始。。。");
			syncDBJzMsgRec(param);
			log.info("执行同步DB净值数据跑批方法（早上9点）结束。。。");
			
			// 生成推送消息数据
			if (arg.contains(ARG_START)) {
				cmPushMsgDaoBuss.syncFundJzMsg(param);
			} else {
				log.info("执行生成消息任务（早上9点）已停用！");
			}
		} else {
			log.info("执行同步DB数据跑批方法（早上9点）已停用！");
		}

		// 执行净值跑批方法（下午17点）
		if (arg != null && arg.contains(ARG_PM17)) {
			param.put("timeFlag", ARG_PM17);
			
			// 同步DB净值数据
			log.info("执行同步DB净值数据跑批方法（下午17点）开始。。。");
			syncDBJzMsgRec(param);
			log.info("执行同步DB净值数据跑批方法（下午17点）结束。。。");
			
			// 生成推送消息数据
			if (arg.contains(ARG_START)) {
				cmPushMsgDaoBuss.syncFundJzMsg(param);
			} else {
				log.info("执行生成消息任务（下午17点）已停用！");
			}
		} else {
			log.info("执行同步DB数据跑批方法（下午17点）已停用！");
		}

	}
	
	/**
	 * 同步DB净值消息数据方法
	 */
	public void syncDBJzMsgRec(Map<String, String> param) {
		try {
			List<SmjzAndHbAndLastDto> syncDbList = smjzAndHbService.getSmjzAndHbAndLastList();
			if (syncDbList != null && syncDbList.size() > 0) {
				log.info("共获取到" + syncDbList.size() + "条DB净值数据。。。");

				List<CmJjjzMsgRec> insertList = new ArrayList<CmJjjzMsgRec>();
				for (SmjzAndHbAndLastDto dbJzBean : syncDbList) {
					// 对DB数据进行同步
					CmJjjzMsgRec cmJjjzMsgRec = new CmJjjzMsgRec();
					BeanUtils.copyProperties(dbJzBean, cmJjjzMsgRec);
					insertList.add(cmJjjzMsgRec);
				}
				
				// 同步接口数据到消息流水表
				if (insertList != null && insertList.size() > 0) {
					this.batchInsertCmJjjzMsgRec(insertList);

					// 同步接口数据到消息最新表
					log.info("生成最新净值数据方法开始。。。");
					cmJjjzMsgLatestDao.batchInsertCmJjjzMsgLatest(param);
					log.info("生成最新净值数据方法结束。。。");
				}
			} else {
				log.info("未获取到DB净值数据！");
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}

	}
	
	
	/**
	 * 将数据分割成多个批次，按批次插入表中
	 */
	public void batchInsertCmJjjzMsgRec(List<CmJjjzMsgRec> insertList) {
		// 每批次限制条数
		int batchLimit = 500;
		// 消息总条数
		int totalSize = insertList.size();

		// 消息总数大于每批次限制的，进行分割成多个批次进行插入
		if (totalSize > batchLimit) {
			// 总批次数
			int totalBatch = totalSize / batchLimit;
			for (int i = 0; i < totalBatch; i++) {
				List<CmJjjzMsgRec> newRecList = insertList.subList(0, batchLimit);
				cmJjjzMsgRecDao.batchInsertCmJjjzMsgRec(newRecList);
				insertList.subList(0, batchLimit).clear();
			}

			// 表示最后剩下的数据
			if (!insertList.isEmpty()) {
				cmJjjzMsgRecDao.batchInsertCmJjjzMsgRec(insertList);
			}
		} else {
			cmJjjzMsgRecDao.batchInsertCmJjjzMsgRec(insertList);
		}

	}
	
}
/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.message.bizservice;

import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 基金信息服务
 * <AUTHOR>
 * @date 2024/6/5 10:30
 * @since JDK 1.8
 */
@Service
public class MessageFundInfoOuterService {

    @Resource
    private JjxxInfoService jjxxInfoService;


    /**
     * @description:(请在此添加描述)
     * @param hbMsgId 好买分销渠道的消息ID
     * @param hzMsgId 好甄消息渠道的消息ID
     * @param hkMsgId 香港销售渠道的消息ID
     * @param fundCode 基金Code
     * @return
     * @author: jinqing.rao
     * @date: 2024/6/5 13:27
     * @since JDK 1.8
     */
    public String getFundMessageIdByFundCode(String hbMsgId,
                                             String hzMsgId,
                                             String hkMsgId,
                                             String fundCode) {
        if(StringUtil.isEmpty(fundCode)){
            return null;
        }
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(fundCode);
        boolean sfxg= jjxxInfo!=null && YesOrNoEnum.YES.getCode().equals(jjxxInfo.getSfxg());
        if(sfxg){
            return hkMsgId;
        }
        // 判断好买，好甄渠道
        DisChannelCodeEnum channelCodeEnum = jjxxInfoService.getDisCodeEnumByJjdm(fundCode);
        // 好买渠道
        if(DisChannelCodeEnum.HOWBUY==channelCodeEnum){
            return hbMsgId;
        }
        // 好甄渠道
        if(DisChannelCodeEnum.HZ ==channelCodeEnum){
            return hzMsgId;
        }
        return null;
   }
}

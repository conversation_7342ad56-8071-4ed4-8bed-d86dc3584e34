<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmMessageDao">
    <select id="listPayMessage" parameterType="Map" resultType="Map" useCache="false">
        SELECT T.CONSCUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.PCODE,
		       T2.JJJC,
		       T.EXPECTTRADEDT,
		       TO_CHAR(T.REALPAYAMT, 'fm999999990.00') REALPAYAMT,
		       HBC.CONSTDESC CURRENCY,
		       TO_CHAR(NVL(CZO.FEE, 0), 'fm999999990.00') FEE,
		       CBD.HBONENO BLACKHBONENO,
		       T2.<PERSON><PERSON><PERSON><PERSON>,
		       CZO.FEECALMODE
		  FROM CM_PREBOOKPRODUCTINFO T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CONSCUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.PCODE = T2.JJDM
		  LEFT JOIN HB_CONSTANT HBC
		    ON HBC.TYPECODE = 'currencys'
		   AND T.CURRENCY = HBC.CONSTCODE
		  LEFT JOIN CM_BLACKLIST_DIRECT CBD
		    ON T1.HBONE_NO = CBD.HBONENO
		   AND T.PCODE = CBD.FUNDCODE
		  LEFT JOIN CM_ZT_ORDERINFO CZO
		    ON T.ID = CZO.APPOINTMENTDEALNO
		 WHERE
		   <!--只处理 高端中台、直销 的 到账确认 消息 -->
		   T.ARCH_TYPE IN ('1','2')
		   AND T.PREBOOKSTATE = '2'
		   AND T.PAYSTATE = '3'
		   AND T.SPECTRADETYPE = '0'
		   AND T.TRADE_TYPE IN ('1', '2')
		   AND T.PAYCHECKDT = TO_CHAR(SYSDATE, 'yyyymmdd')
		   AND ABS(TO_DATE(T.EXPECTTRADEDT, 'yyyymmdd') -
		           TO_DATE(T.PAYCHECKDT, 'yyyymmdd')) &lt;= 30
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '1'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CONSCUSTNO || T.PCODE)
    </select>
    
    <insert id="insertCmMessage" parameterType="com.howbuy.crm.nt.message.domain.CmMessage" >
	    INSERT INTO CM_MESSAGE_DAILY (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="id != null"> id, </if> 
	      	      <if test="type != null"> type, </if> 
	      	      <if test="conscustno != null"> conscustno, </if> 
	      	      <if test="modeid != null"> modeid, </if> 
	      	      <if test="msg != null"> msg, </if> 
	      	      <if test="sendflag != null"> sendflag, </if> 
	      	      <if test="des != null"> des, </if> 
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="id != null"> #{id}, </if> 
	      	      <if test="type != null"> #{type}, </if> 
	      	      <if test="conscustno != null"> #{conscustno}, </if> 
	      	      <if test="modeid != null"> #{modeid}, </if> 
	      	      <if test="msg != null"> #{msg}, </if> 
	      	      <if test="sendflag != null"> #{sendflag}, </if> 
	      	      <if test="des != null"> #{des}, </if> 
	               </trim>	
         )      
	  </insert>
	  
	  <select id="listTradeMessage" parameterType="Map" resultType="Map" useCache="false">
		  SELECT T.APPSERIALNO,
				 T.CUSTNO,
				 T1.HBONE_NO,
				 T1.CUSTNAME,
				 T.FUNDCODE,
				 T2.JJJC,
				 T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 WHERE T.CHECKFLAG = '4'
			 -- 该定时调度是 晚上7点15跑，如果 交易记录的生成时间晚于这个时间，将会漏消息。因此，这里 查询的credt要包含今天和昨天
		   AND (T.CREDT = TO_CHAR(SYSDATE, 'yyyymmdd') or T.CREDT = TO_CHAR(SYSDATE - 1, 'yyyymmdd'))
		   AND T2.SFMSJG = '2'
		   AND T.BUSICODE IN ('120', '122')
		   AND (T.ISMESSAGE IS NULL OR T.ISMESSAGE='1')
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '2'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
		UNION ALL
		  SELECT T.APPSERIALNO,
				 T.CUSTNO,
				 T1.HBONE_NO,
				 T1.CUSTNAME,
				 T.FUNDCODE,
				 T2.JJJC,
				 T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T.CHECKFLAG = '4'
		   -- 该定时调度是 晚上7点15跑，如果 交易记录的生成时间晚于这个时间，将会漏消息。因此，这里 查询的credt要包含今天和昨天
		   AND (T.CREDT = TO_CHAR(SYSDATE, 'yyyymmdd') or T.CREDT = TO_CHAR(SYSDATE - 1, 'yyyymmdd'))
		   AND T2.SFMSJG = '3'
		   AND T.BUSICODE IN ('120', '122')
		   AND (T.ISMESSAGE IS NULL OR T.ISMESSAGE='1')
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '2'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listSaleMessage" parameterType="Map" resultType="Map" useCache="false">
		SELECT T.APPSERIALNO,
			   T.CUSTNO,
			   T1.HBONE_NO,
			   T1.CUSTNAME,
			   T.FUNDCODE,
			   T2.JJJC,
			   T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 WHERE T.CHECKFLAG = '4'
		   -- 该定时调度是 晚上7点15跑，如果 交易记录的生成时间晚于这个时间，将会漏消息。因此，这里 查询的credt要包含今天和昨天
		   AND (T.CREDT = TO_CHAR(SYSDATE, 'yyyymmdd') or T.CREDT = TO_CHAR(SYSDATE - 1, 'yyyymmdd'))
		   AND T2.SFMSJG = '2'
		   AND T.BUSICODE = '124'
		   AND (T.ISMESSAGE IS NULL OR T.ISMESSAGE='1')
		   AND TO_DATE(T.CREDT, 'yyyymmdd') - TO_DATE(T.TRADEDT, 'yyyymmdd') <![CDATA[<= ]]> 30
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '3'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
		UNION ALL
		SELECT T.APPSERIALNO,
			   T.CUSTNO,
			   T1.HBONE_NO,
			   T1.CUSTNAME,
			   T.FUNDCODE,
			   T2.JJJC,
			   T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T.CHECKFLAG = '4'
		   -- 该定时调度是 晚上7点15跑，如果 交易记录的生成时间晚于这个时间，将会漏消息。因此，这里 查询的credt要包含今天和昨天
		   AND (T.CREDT = TO_CHAR(SYSDATE, 'yyyymmdd') or T.CREDT = TO_CHAR(SYSDATE - 1, 'yyyymmdd'))
		   AND T2.SFMSJG = '3'
		   AND T.BUSICODE = '124'
		   AND (T.ISMESSAGE IS NULL OR T.ISMESSAGE='1')
		   AND TO_DATE(T.CREDT, 'yyyymmdd') - TO_DATE(T.TRADEDT, 'yyyymmdd') <![CDATA[<=]]> 30
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '3'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listZxProductDueMessage" parameterType="Map" resultType="Map" useCache="false">
        SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       T2.ZZRQ TRADEDT
		 FROM CM_CUSTPRIVATEFUNDTRADE T
		 LEFT JOIN CM_CUSTPRIVATEFUND P ON P.CUSTNO=T.CUSTNO AND P.FUNDCODE=T.FUNDCODE
		 INNER JOIN CM_CONSCUST T1  ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2   ON T.FUNDCODE = T2.JJDM
		 WHERE T2.SFMSJG = '2' <!--直销产品-->
		   AND T2.HMCPX = '2' <!--固定收益-->
		   AND T.BUSICODE='142'<!--强赎交易-->
		   AND T.TRADEDT = #{sendTradeDt,jdbcType=VARCHAR}
		   AND T.CREATOR = 'sys'
		   AND P.BALANCEVOL <![CDATA[>]]> 1
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '4'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listDxProductDueMessage" parameterType="Map" resultType="Map" useCache="false">
        SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       T2.ZZRQ TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 WHERE T2.SFMSJG = '1'
		   AND T2.HMCPX = '2'
		   AND T2.ZZRQ = TO_CHAR(SYSDATE+7, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '5'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
		UNION ALL
		SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       T2.ZZRQ TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 LEFT JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T2.SFMSJG = '3'
		   AND T2.HMCPX = '2'
		   AND T2.ZZRQ = TO_CHAR(SYSDATE+7, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND T3.HBONENO IS NULL
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '5'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listZxGsDividendMessage" parameterType="Map" resultType="Map" useCache="false">
    	SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       TO_CHAR(T5.FPRQ, 'yyyymmdd') TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN XTSYFPRL T5
		    ON T2.JJDM = T5.JJDM
		 WHERE T2.SFMSJG = '2'
		   AND T2.HMCPX = '2'
		   AND T5.XTFPLX = '1'
		   AND TO_CHAR(T5.FPRQ, 'yyyymmdd') = TO_CHAR(SYSDATE, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '6'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
		UNION ALL
		SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       TO_CHAR(T5.FPRQ, 'yyyymmdd') TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN XTSYFPRL T5
		    ON T2.JJDM = T5.JJDM
		 INNER JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T2.SFMSJG = '3'
		   AND T2.HMCPX = '2'
		   AND T5.XTFPLX = '1'
		   AND TO_CHAR(T5.FPRQ, 'yyyymmdd') = TO_CHAR(SYSDATE, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '6'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listDxGsDividendMessage" parameterType="Map" resultType="Map" useCache="false">
    	SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       TO_CHAR(T5.FPRQ, 'yyyymmdd') TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN XTSYFPRL T5
		    ON T2.JJDM = T5.JJDM
		 WHERE T2.SFMSJG = '1'
		   AND T2.HMCPX = '2'
		   AND TO_CHAR(T5.FPRQ, 'yyyymmdd') = TO_CHAR(SYSDATE + 7, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '8'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
		UNION ALL
		SELECT T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       TO_CHAR(T5.FPRQ, 'yyyymmdd') TRADEDT
		  FROM cm_high_custfund T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN XTSYFPRL T5
		    ON T2.JJDM = T5.JJDM
		  LEFT JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T2.SFMSJG = '3'
		   AND T2.HMCPX = '2'
		   AND TO_CHAR(T5.FPRQ, 'yyyymmdd') = TO_CHAR(SYSDATE + 7, 'yyyymmdd')
		   and t.fundtype = '3'
		   AND T.BALANCEVOL <![CDATA[>]]> 1
		   AND T3.HBONENO IS NULL
		   AND NOT EXISTS (SELECT 1
		          FROM CM_MESSAGE_DAILY A
		         WHERE A.TYPE = '8'
		           AND A.SENDFLAG = '1'
		           AND A.DES = T.CUSTNO || T.FUNDCODE)
    </select>
    
    <select id="listZxNotgsDividendMessage" parameterType="Map" resultType="Map" useCache="false">
    	SELECT T.APPSERIALNO,
		       T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 WHERE T.CHECKFLAG = '4'
		   AND T.ISMESSAGE = '0'
		   AND T2.SFMSJG = '2'
		   AND T2.HMCPX IN ('1', '3', '4', '5', '7')
		   AND T.BUSICODE IN ('143', '999')
		UNION ALL
		SELECT T.APPSERIALNO,
		       T.CUSTNO,
		       T1.HBONE_NO,
		       T1.CUSTNAME,
		       T.FUNDCODE,
		       T2.JJJC,
		       T.TRADEDT
		  FROM CM_CUSTPRIVATEFUNDTRADE T
		 INNER JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		 INNER JOIN JJXX1 T2
		    ON T.FUNDCODE = T2.JJDM
		 INNER JOIN CM_BLACKLIST_DIRECT T3
		    ON T1.HBONE_NO = T3.HBONENO
		   AND T.FUNDCODE = T3.FUNDCODE
		 WHERE T.CHECKFLAG = '4'
		   AND T.ISMESSAGE = '0'
		   AND T2.SFMSJG = '3'
		   AND T2.HMCPX IN ('1', '3', '4', '5', '7')
		   AND T.BUSICODE IN ('143', '999')
    </select>
    
    <update id="updateTradeIsmessageFlag" parameterType="Map">
		update cm_custprivatefundtrade t set t.ismessage = '1' where t.APPSERIALNO = #{appserialno}
	</update>

</mapper>
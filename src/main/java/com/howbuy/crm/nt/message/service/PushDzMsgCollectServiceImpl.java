package com.howbuy.crm.nt.message.service;

import lombok.extern.slf4j.Slf4j;
import com.howbuy.crm.nt.message.buss.CmPushMsgDaoBuss;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("pushDzMsgCollectService")
@Slf4j
public class PushDzMsgCollectServiceImpl implements PushDzMsgCollectService {

    @Autowired
    private CmPushMsgDaoBuss cmPushMsgDaoBuss;

    /** 到账通知 */
    public static final String ARG_DZTZ = "dztz";
	/** 客户预约 */
    public static final String ARG_YYTZ = "yytz";

    /**
     * 同步到账通知消息数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
	public void syncCustDzMsgData(String arg) {
		// 加载到账通知任务
		if (arg != null && arg.contains(ARG_DZTZ)) {
			cmPushMsgDaoBuss.syncCustDzMsg();
		} else {// 同步到账通知数据方法
			log.info("到账通知任务已停用！");
		}

		// 加载客户预约任务
		if (arg != null && arg.contains(ARG_YYTZ)) {
			cmPushMsgDaoBuss.syncCustYyMsg();
		} else {// 同步客户预约数据方法
			log.info("客户预约任务已停用！");
		}
	}

}
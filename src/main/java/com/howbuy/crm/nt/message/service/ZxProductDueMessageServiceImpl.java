package com.howbuy.crm.nt.message.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.nt.message.bizservice.MessageFundInfoOuterService;
import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("zxProductDueMessageServiceImpl")
public class ZxProductDueMessageServiceImpl implements ZxProductDueMessageService {
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private MessageCommonBuss messageCommonBuss;

	@Resource
	private MessageFundInfoOuterService messageFundInfoOuterService;
	
	private Logger logger= LoggerFactory.getLogger(ZxProductDueMessageServiceImpl.class);

	@Override
	public String dealZxProductDueMessage(String arg) {
		Map<String,Object> map = new HashMap<String,Object>(1);
		//推送两天前的交易数据
		String  sendTradeDt= DateUtil.date2String(DateUtil.getAfterOrBeforeDay(-1),DateUtil.SHORT_DATE_PATTERN);
		map.put("sendTradeDt",sendTradeDt);
		//查询符合条件的数据
		List<Map<String,String>> list = cmMessageDao.listZxProductDueMessage(map);
		logger.info("直销产品到期短信发送查询，sendTradeDt：{} ,查询条数：{}",sendTradeDt,( null==list? "空":list.size()));
		if(list != null && list.size()>0){
			logger.info(JSON.toJSONString(list));
			for(Map<String,String> obj : list){
				String hboneno = StringUtil.replaceNull(obj.get("HBONE_NO"));
				String custno = StringUtil.replaceNull(obj.get("CUSTNO"));
				String custname = StringUtil.replaceNull(obj.get("CUSTNAME"));
				String jjjc = StringUtil.replaceNull(obj.get("JJJC"));
				String tradedt = StringUtil.replaceNull(obj.get("TRADEDT"));
				String pcode = StringUtil.replaceNull(obj.get("FUNDCODE"));
				//一账通不为空
				if(StringUtil.isNotNullStr(hboneno)){
					Map<String, String> parmsg = new HashMap<String,String>(3); 
					parmsg.put("custName", custname);
					parmsg.put("tradedt", tradedt);
					parmsg.put("fund_name", jjjc);
					String msg = JSON.toJSONString(parmsg);
					// 默认赋值
					String msgId = CrmNtConstant.MSG_BID_ZXDQ;
					// 通过基金Code获取产品分销渠道的消息ID
					msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_ZXDQ,CrmNtConstant.MSG_BID_ZXDQ_HZ,
							CrmNtConstant.MSG_BID_ZXDQ_HW,pcode);
					if (StringUtils.isBlank(msgId)){
						logger.info("ZxProductDueMessageServiceImpl>>>dealZxProductDueMessage 获取分销渠道消息模版ID是空,不发送消息，pCode:{},custno:{}",pcode,custno);
						continue;
					}
					//发送短信
					int result = messageCommonBuss.sendMessageByHboneNo(msgId, msg, hboneno);
					//发送成功，记录发送成功表
					if(result == 0){
						messageCommonBuss.insertMessageDailly(CrmNtConstant.MSG_TYPE_ZXDQ,custno, msg, custno+pcode);
					}
				}
				
			}
		}
		return null;
	}
}

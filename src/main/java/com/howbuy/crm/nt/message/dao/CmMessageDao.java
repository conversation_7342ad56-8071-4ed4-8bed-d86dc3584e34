package com.howbuy.crm.nt.message.dao;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.message.domain.CmMessage;

/**
 * 短信
 * <AUTHOR>
 *
 */
public interface CmMessageDao {

    /**
     * 查询符合打款条件的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listPayMessage(final Map<String, Object> map);

    /**
     * 查询符合购买交易的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listTradeMessage(final Map<String, Object> map);

    /**
     * 查询符合赎回交易的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listSaleMessage(final Map<String, Object> map);

    /**
     * 查询符合直销产品到期短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listZxProductDueMessage(final Map<String, Object> map);

    /**
     * 查询符合代销产品到期短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listDxProductDueMessage(final Map<String, Object> map);

    /**
     * 查询符合直销固收分红的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listZxGsDividendMessage(final Map<String, Object> map);

    /**
     * 查询符合代销固收分红的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listDxGsDividendMessage(final Map<String, Object> map);

    /**
     * 查询符合直销非固收分红的短信
     *
     * @param map
     * @return
     */
    List<Map<String, String>> listZxNotgsDividendMessage(final Map<String, Object> map);

    /**
     * 插入消息
     *
     * @param mess
     */
    void insertCmMessage(CmMessage mess);

    /**
     * 更新发送过的分红和股权回款的记录
     *
     * @param map
     */
    void updateTradeIsmessageFlag(Map<String, Object> map);
}

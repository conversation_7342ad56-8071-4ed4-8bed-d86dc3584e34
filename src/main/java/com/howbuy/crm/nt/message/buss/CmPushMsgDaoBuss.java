package com.howbuy.crm.nt.message.buss;

import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cache.CacheConstants;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.message.dao.CmCompanyReportDao;
import com.howbuy.crm.nt.message.dao.CmPushMsgDayDao;
import com.howbuy.crm.nt.message.domain.CmCompanyReport;
import com.howbuy.crm.util.DateTimeUtil;
import com.howbuy.persistence.content.interview.CompanyReport;
import crm.howbuy.base.domain.CmPushMsgDay;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class CmPushMsgDaoBuss {

	@Autowired
	private CommonDao commondao;

	@Autowired
    private CmPushMsgDayDao cmPushMsgDayDao;

	@Autowired
	private CmCompanyReportDao cmCompanyReportDao;
	
	/** 消息类型：产品分红 */
    public static final String MSG_TYPE_CPFH = "1";
	/** 消息类型：产品到期 */
    public static final String MSG_TYPE_CPDQ = "2";
	/** 消息类型：团队产品到期 */
    public static final String MSG_TYPE_TDDQ = "3";
	/** 消息类型：客户生日 */
    public static final String MSG_TYPE_KHSR = "4";
	/** 消息类型：到账通知 */
    public static final String MSG_TYPE_DZTZ = "5";
	/** 消息类型：净值通知 */
    public static final String MSG_TYPE_JZTZ = "6";
	/** 消息类型：预约通知 */
    public static final String MSG_TYPE_YYTZ = "7";
	/** 消息类型：资料更新 */
    public static final String MSG_TYPE_ZLGX = "8";
	/** 消息类型：交易类型 = 强赎 */
    public static final String MSG_TYPE_QS = "9";
	/** 消息类型：交易类型 = 红利发放 */
    public static final String MSG_TYPE_HLFF = "10";
	/** 消息类型：缴费提醒-投顾消息中心 */
    public static final String MSG_PAYMENT_REMINDER = "11";
	
	
	/**
	 * 同步基金分红数据方法
	 */
	public void syncFundFhMsg() {
		log.info("同步基金分红数据方法开始执行。。。");
		List<CmPushMsgDay> fhList = cmPushMsgDayDao.queryFundFhMsgList();
        if (fhList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < fhList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = fhList.get(i);            	
            	tempCmPushMsgDay.setId(msgId);
				// 1:基金分红
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_CPFH);
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("基金分红数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的基金分红数据！");
        }
		log.info("同步基金分红数据方法执行结束。。。");
	}
	
	/**
	 * 同步基金到期数据方法
	 */
	public void syncFundDqMsg() {
		log.info("同步基金到期数据方法开始执行。。。");
		List<CmPushMsgDay> dqList = cmPushMsgDayDao.queryFundDqMsgList();
        if (dqList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < dqList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = dqList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 2:基金到期
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_CPDQ);
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("基金到期数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的基金到期数据！");
        }
		log.info("同步基金到期数据方法执行结束。。。");
	}
	
	
	/**
	 * 同步团队产品到期数据方法
	 */
	public void syncFundTddqMsg() {
		log.info("同步团队产品到期数据方法开始执行。。。");
		List<CmPushMsgDay> tddqList = cmPushMsgDayDao.queryFundTddqMsgList();
        if (tddqList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < tddqList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = tddqList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 3:团队产品到期
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_TDDQ);
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("团队基金到期数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的团队基金到期数据！");
        }
		log.info("同步团队产品到期数据方法执行结束。。。");
	}
	
	/**
	 * 同步团队产品到期数据方法
	 */
	public void syncCustBirthDayMsg() {
		log.info("同步投顾客户生日数据方法开始执行。。。");
		List<CmPushMsgDay> khsrList = cmPushMsgDayDao.queryBirthDayMsgList();
        if (khsrList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < khsrList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = khsrList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 4:客户生日
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_KHSR);
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("客户生日数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的投顾客户生日数据！");
        }
		log.info("同步投顾客户生日数据方法执行结束。。。");
	}
	
	/**
	 * 同步客户到账通知数据方法
	 */
	public void syncCustDzMsg() {
		log.info("同步到账通知数据方法开始执行。。。");
		List<CmPushMsgDay> dzList = cmPushMsgDayDao.queryCustDzMsgList();
        if (dzList.size() > 0) {
        	List<CmPushMsgDay> consMsgList = null;
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            Map<String, List<CmPushMsgDay>> consMsgMap = new HashMap<String, List<CmPushMsgDay>>();
            for (int i = 0; i < dzList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = dzList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 5:到账通知
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_DZTZ);
				// 已读标识：0:未读取，1：已读取
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
            	tempCmPushMsgDay.setPushFlag("0");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            	
            	// 存放投顾消息
            	consMsgList = consMsgMap.get(tempCmPushMsgDay.getConsCode());
            	if (consMsgList == null) {
            		consMsgList = new ArrayList<CmPushMsgDay>();
				}
            	consMsgList.add(tempCmPushMsgDay);
            	consMsgMap.put(tempCmPushMsgDay.getConsCode(), consMsgList);
            }

            // 存放新消息到缓存
         	addMsgCache(consMsgMap);
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("到账通知数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的到账通知数据！");
        }
		log.info("同步到账通知数据方法执行结束。。。");
	}

	public void batchInsertCmCompanyReport(List<CompanyReport> reportlist){

		log.info("同步资料更新数据方法开始执行。。。reportlistcount:"+reportlist.size());
		List<CmCompanyReport> cmreportlist = new ArrayList<CmCompanyReport>();
		//转换数据，落表
		for(CompanyReport report:reportlist){
			CmCompanyReport cmreport = new CmCompanyReport();
			BeanUtils.copyProperties(report,cmreport);

			cmreportlist.add(cmreport);
		}

		//落表
		if(cmreportlist.size() > 0){
			cmCompanyReportDao.batchInsertCmCompanyReport(cmreportlist);
		}
	}
	/**
	 * 同步资料更新通知数据方法
	 */
	public void syncCustZlgxMsg() {

		log.info("同步资料更新数据方法开始执行");

		//查询表数据
		List<CmPushMsgDay> dzList = cmPushMsgDayDao.queryCustZlgxMsgList();

		//放缓存
		if (dzList.size() > 0) {
			List<CmPushMsgDay> consMsgList = null;
			List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
			Map<String, List<CmPushMsgDay>> consMsgMap = new HashMap<String, List<CmPushMsgDay>>();
			for (int i = 0; i < dzList.size(); i++) {
				String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
				CmPushMsgDay tempCmPushMsgDay = dzList.get(i);
				tempCmPushMsgDay.setId(msgId);
				// 8:资料更新通知
				tempCmPushMsgDay.setMsgType(MSG_TYPE_ZLGX);
				// 已读标识：0:未读取，1：已读取
				tempCmPushMsgDay.setReadFlag("0");
				tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
				tempCmPushMsgDay.setPushFlag("0");
				tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
				newMsgList.add(tempCmPushMsgDay);

				// 存放投顾消息
				consMsgList = consMsgMap.get(tempCmPushMsgDay.getConsCode());
				if (consMsgList == null) {
					consMsgList = new ArrayList<CmPushMsgDay>();
				}
				consMsgList.add(tempCmPushMsgDay);
				consMsgMap.put(tempCmPushMsgDay.getConsCode(), consMsgList);
			}

			// 存放新消息到缓存
			addMsgCache(consMsgMap);
			if (newMsgList.size() > 0) {
				this.batchInsertCmPushMsgDay(newMsgList);
				log.info("资料更新数据同步成功！");
			}
		} else {
			log.info("未找到需同步的资料更新通知数据！");
		}
		log.info("同步资料更新数据方法执行结束。。。");
	}

	/**
	 * 缴费提醒数据方法
	 */
	public void syncCustPaymentMsg() {

		log.info("同步资料更新数据方法开始执行");

		//查询表数据
		List<CmPushMsgDay> dzList = cmPushMsgDayDao.queryCustPaymentMsgList();

		//放缓存
		if (dzList.size() > 0) {
			List<CmPushMsgDay> consMsgList = null;
			List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
			Map<String, List<CmPushMsgDay>> consMsgMap = new HashMap<String, List<CmPushMsgDay>>();
			for (int i = 0; i < dzList.size(); i++) {
				String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
				CmPushMsgDay tempCmPushMsgDay = dzList.get(i);
				tempCmPushMsgDay.setId(msgId);
				// 11:缴费提醒
				tempCmPushMsgDay.setMsgType(MSG_PAYMENT_REMINDER);
				// 已读标识：0:未读取，1：已读取
				tempCmPushMsgDay.setReadFlag("0");
				tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
				tempCmPushMsgDay.setPushFlag("0");
				tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
				newMsgList.add(tempCmPushMsgDay);

				// 存放投顾消息
				consMsgList = consMsgMap.get(tempCmPushMsgDay.getConsCode());
				if (consMsgList == null) {
					consMsgList = new ArrayList<CmPushMsgDay>();
				}
				consMsgList.add(tempCmPushMsgDay);
				consMsgMap.put(tempCmPushMsgDay.getConsCode(), consMsgList);
			}

			// 存放新消息到缓存
			addMsgCache(consMsgMap);
			if (newMsgList.size() > 0) {
				this.batchInsertCmPushMsgDay(newMsgList);
				log.info("创新产品缴费通知数据同步成功！");
			}
		} else {
			log.info("未找到需同步的创新产品缴费通知数据！");
		}
		log.info("创新产品缴费通知数据方法执行结束。。。");
	}

	/**
	 * 同步客户净值通知数据方法
	 */
	public void syncFundJzMsg(Map<String, String> param) {
		log.info("生成净值消息数据方法开始执行。。。");
		List<CmPushMsgDay> jzList = cmPushMsgDayDao.queryFundJzMsgList(param);
        if (jzList.size() > 0) {
        	List<CmPushMsgDay> consMsgList = null;
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            Map<String, List<CmPushMsgDay>> consMsgMap = new HashMap<String, List<CmPushMsgDay>>();
            for (int i = 0; i < jzList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = jzList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 6:净值通知
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_JZTZ);
				// 已读标识：0:未读取，1：已读取
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
            	tempCmPushMsgDay.setPushFlag("0");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.fmtDate(tempCmPushMsgDay.getPushDt(), DateTimeUtil.DATE_TIME_PATTERN));
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            	
            	// 存放投顾消息
            	consMsgList = consMsgMap.get(tempCmPushMsgDay.getConsCode());
            	if (consMsgList == null) {
            		consMsgList = new ArrayList<CmPushMsgDay>();
				}
            	consMsgList.add(tempCmPushMsgDay);
            	consMsgMap.put(tempCmPushMsgDay.getConsCode(), consMsgList);
            }

            // 存放新消息到缓存
         	addMsgCache(consMsgMap);
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("净值通知数据同步成功！");
            }
        } else {
        	log.info("未找到需要同步的净值消息数据！");
        }
		log.info("生成净值消息数据方法执行结束。。。");
	}
	
	/**
	 * 同步预约客户通知数据方法
	 */
	public void syncCustYyMsg() {
		log.info("同步预约客户数据方法开始执行。。。");
		List<CmPushMsgDay> yyList = cmPushMsgDayDao.queryCustYyMsgList();
        if (yyList.size() > 0) {
            List<CmPushMsgDay> consMsgList = null;
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            Map<String, List<CmPushMsgDay>> consMsgMap = new HashMap<String, List<CmPushMsgDay>>();
            for (int i = 0; i < yyList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = yyList.get(i);
            	tempCmPushMsgDay.setId(msgId);
				// 7:预约通知
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_YYTZ);
				// 已读标识：0:未读取，1：已读取
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
            	tempCmPushMsgDay.setPushFlag("0");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            	
            	// 存放投顾消息
            	consMsgList = consMsgMap.get(tempCmPushMsgDay.getConsCode());
            	if (consMsgList == null) {
            		consMsgList = new ArrayList<CmPushMsgDay>();
				}
            	consMsgList.add(tempCmPushMsgDay);
            	consMsgMap.put(tempCmPushMsgDay.getConsCode(), consMsgList);
            }

            // 存放新消息到缓存
         	addMsgCache(consMsgMap);
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("预约客户数据同步成功！");
            }
        } else {
        	log.info("未找到需同步的预约客户数据！");
        }
		log.info("同步预约客户数据方法执行结束。。。");
	}
	
	public void syncJylxqsMsg() {
		log.info("同步交易类型等于强赎的数据方法开始执行。。。");
		List<CmPushMsgDay> fhList = cmPushMsgDayDao.queryJylxqsMsgList();
        if (fhList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < fhList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = fhList.get(i);            	
            	tempCmPushMsgDay.setId(msgId);
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_QS);	
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("交易类型等于强赎的数据同步成功！");
            }
        } else {
        	log.info("未找到交易类型等于强赎的数据！"); 
        }
		log.info("同步交易类型等于强赎的数据方法结束。");
	}
	
	public void syncJylxhlffMsg() {
		log.info("同步交易类型等于红利发放的数据方法开始执行。。。");
		List<CmPushMsgDay> fhList = cmPushMsgDayDao.queryJylxhlffMsgList();
        if (fhList.size() > 0) {
            List<CmPushMsgDay> newMsgList = new ArrayList<CmPushMsgDay>();
            for (int i = 0; i < fhList.size(); i++) {
            	String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
            	CmPushMsgDay tempCmPushMsgDay = fhList.get(i);            	
            	tempCmPushMsgDay.setId(msgId);
            	tempCmPushMsgDay.setMsgType(MSG_TYPE_HLFF);	
            	tempCmPushMsgDay.setReadFlag("0");
            	tempCmPushMsgDay.setCreator("nt-sys");
            	tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
            	tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
            	newMsgList.add(tempCmPushMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
            	this.batchInsertCmPushMsgDay(newMsgList);
            	log.info("交易类型等于红利发放数据同步成功！");
            }
        } else {
        	log.info("未找到交易类型等于红利发放的数据！"); 
        }
		log.info("同步交易类型等于红利发放的数据方法开始结束。");
	}
	
	/**
	 * 清除消息历史表当日数据
	 */
	public void clearCurHisMsg() {
		log.info("清除当日历史消息表开始执行。。。");

        // 删除当天消息表中数据
    	cmPushMsgDayDao.deleteCurCmPushMsgHis();
        log.info("当日历史数据删除已完成");

		log.info("清除当日历史消息表执行结束。。。");
	}
	
	/**
	 * 回收当天消息数据到历史表，同时删除当天表数据
	 */
	public void recycleDayMsgToHis() {
		log.info("消息回收方法开始执行。。。");

        // 回收当天表消息到历史表
        boolean updateOne = cmPushMsgDayDao.batchInsertCmPushMsgHis() >= 0;
        if (updateOne) {
            log.info("消息回收完成");
        } else {
            log.info("消息回收失败");
        }

        // 回收成功后，删除当天表中数据
        if (updateOne) {
        	cmPushMsgDayDao.deleteCmPushMsgDay();
            log.info("当天消息表删除已完成");
        } else {
        	log.info("当天消息表处理失败");
        }

		log.info("消息回收方法执行结束。。。");
	}
	
	/**
	 * 恢复当天表中字段值
	 */
	public void updateDayMsg() {
		log.info("恢复当天消息表开始执行。。。");

        // 恢复当天消息表中数据
    	cmPushMsgDayDao.updateCmPushMsgDay();
        log.info("数据恢复已完成");

		log.info("恢复当天消息表执行结束。。。");
	}

	/**
	 * @description: 将投顾消息数据加入缓存
	 * @param newMsgMap 新消息数据
	 * @author: jin.wang03
	 * @date: 2023/12/4 19:56
	 * @since JDK 1.8
	 */
	@SuppressWarnings("unchecked")
	public void addMsgCache(Map<String, List<CmPushMsgDay>> newMsgMap) {
		if (MapUtils.isEmpty(newMsgMap)) {
			return;
		}
		for (Map.Entry<String, List<CmPushMsgDay>> entry : newMsgMap.entrySet()) {
			String consCode = entry.getKey();
			List<CmPushMsgDay> newMsgList = entry.getValue();

			// 获取投顾缓存消息
			List<CmPushMsgDay> cacheMsgList = CacheServiceImpl.getInstance().get(CacheConstants.DAYMSG_NO_CACHE_PREFIX + consCode);

			// 追加新消息数据
			if (CollectionUtils.isNotEmpty(cacheMsgList)) {
				cacheMsgList.addAll(newMsgList);
			} else {
				cacheMsgList = newMsgList;
			}

			// 将消息加入缓存
			CacheServiceImpl.getInstance().put(CacheConstants.DAYMSG_NO_CACHE_PREFIX + consCode, cacheMsgList);
		}
	}
	
	/**
	 * 将数据分割成多个批次，按批次插入表中
	 */
	public void batchInsertCmPushMsgDay(List<CmPushMsgDay> msgList) {
		// 每批次限制条数
		int batchLimit = 500;
		// 消息总条数
		int totalSize = msgList.size();

		// 消息总数大于每批次限制的，进行分割成多个批次进行插入
		if (totalSize > batchLimit) {
			// 总批次数
			int totalBatch = totalSize / batchLimit;
			for (int i = 0; i < totalBatch; i++) {
				List<CmPushMsgDay> newMsgList = msgList.subList(0, batchLimit);
				cmPushMsgDayDao.batchInsertCmPushMsgDay(newMsgList);
				msgList.subList(0, batchLimit).clear();
			}

			// 表示最后剩下的数据
			if (!msgList.isEmpty()) {
				cmPushMsgDayDao.batchInsertCmPushMsgDay(msgList);
			}
		} else {
			cmPushMsgDayDao.batchInsertCmPushMsgDay(msgList);
		}

	}

}

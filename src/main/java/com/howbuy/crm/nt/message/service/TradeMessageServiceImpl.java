package com.howbuy.crm.nt.message.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.message.bizservice.MessageFundInfoOuterService;
import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import javax.annotation.Resource;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("tradeMessageServiceImpl")
public class TradeMessageServiceImpl implements TradeMessageService {
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private MessageCommonBuss messageCommonBuss;

	@Resource
	private MessageFundInfoOuterService messageFundInfoOuterService;
	
	private Logger logger= LoggerFactory.getLogger(TradeMessageServiceImpl.class);

	@Override
	public void dealTradeMessage(String arg) {
		Map<String, Object> map = new HashMap<>(1);
		//查询符合条件的数据
		List<Map<String, String>> list = cmMessageDao.listTradeMessage(map);
		logger.info("SendTradeAckMessageJob| 查询出的符合条件的认申购交易记录" + JSON.toJSONString(list));
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (Map<String, String> obj : list) {
			String appserialNo = StringUtil.replaceNull(obj.get("APPSERIALNO"));
			String hboneno = StringUtil.replaceNull(obj.get("HBONE_NO"));
			String custno = StringUtil.replaceNull(obj.get("CUSTNO"));
			String custname = StringUtil.replaceNull(obj.get("CUSTNAME"));
			String jjjc = StringUtil.replaceNull(obj.get("JJJC"));
			String tradedt = StringUtil.replaceNull(obj.get("TRADEDT"));
			String pcode = StringUtil.replaceNull(obj.get("FUNDCODE"));
			//一账通不为空
			if (StringUtil.isNullStr(hboneno)) {
				continue;
			}

			Map<String, String> parmsg = new HashMap<>(3);
			parmsg.put("custName", custname);
			parmsg.put("tradedt", tradedt);
			parmsg.put("fund_name", jjjc);
			String msg = JSON.toJSONString(parmsg);
			// 不同的产品分销渠道,获取不同的消息模版ID
			String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_TRADE, CrmNtConstant.MSG_BID_TRADE_HZ,
					CrmNtConstant.MSG_BID_TRADE_HW, pcode);
			if (StringUtil.isEmpty(msgId)) {
				logger.info("SendTradeAckMessageJob| 没有获取到分销渠道信息," +
						"消息模版ID为空,不发送消息,appserialNo: {}，pCode :{},custno:{}", appserialNo, pcode, custno);
				continue;
			}
			//发送短信
			int result = messageCommonBuss.sendMessageByHboneNo(msgId, msg, hboneno);
			//发送成功，记录发送成功表
			if (result == 0) {
				messageCommonBuss.insertMessageDailly(CrmNtConstant.MSG_TYPE_TRADE, custno, msg, custno + pcode);
			}

		}
	}
}

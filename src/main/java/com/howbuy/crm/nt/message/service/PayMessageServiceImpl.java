package com.howbuy.crm.nt.message.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.message.bizservice.MessageFundInfoOuterService;
import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 打款消息推送
 * <AUTHOR>
 *
 */
@Service("payMessageServiceImpl")
public class PayMessageServiceImpl implements PayMessageService {
	
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private MessageCommonBuss messageCommonBuss;

	@Resource
	private MessageFundInfoOuterService messageFundInfoOuterService;
	
	private Logger log= LoggerFactory.getLogger(PayMessageServiceImpl.class);

	@Override
	public String dealPayMessage(String arg) {
		Map<String,Object> map = new HashMap<String,Object>(1);
		//查询符合条件的数据
		List<Map<String,String>> list = cmMessageDao.listPayMessage(map);
		if(list != null && list.size()>0){
			log.info(JSON.toJSONString(list));
			for(Map<String,String> obj : list){
				String hboneno = StringUtil.replaceNull(obj.get("HBONE_NO"));
				String custno = StringUtil.replaceNull(obj.get("CONSCUSTNO"));
				String custname = StringUtil.replaceNull(obj.get("CUSTNAME"));
				String jjjc = StringUtil.replaceNull(obj.get("JJJC"));
				String tradedt = StringUtil.replaceNull(obj.get("EXPECTTRADEDT"));
				String pcode = StringUtil.replaceNull(obj.get("PCODE"));
				String amount = StringUtil.replaceNull(obj.get("REALPAYAMT"));
				String currency = StringUtil.replaceNull(obj.get("CURRENCY"));
				String blackhboneno = StringUtil.replaceNull(obj.get("BLACKHBONENO"));
				String sfmsjg = StringUtil.replaceNull(obj.get("SFMSJG"));
				String feecalmode = StringUtil.replaceNull(obj.get("FEECALMODE"));
				String fee = StringUtil.replaceNull(obj.get("FEE"));
				String msgid;
				//一账通不为空
				if(StringUtil.isNotNullStr(hboneno)){
					Map<String, String> parmsg = new HashMap<String,String>(6); 
					parmsg.put("custName", custname);
					parmsg.put("tradedt", tradedt);
					parmsg.put("fund_name", jjjc);
					parmsg.put("amount", amount);
					parmsg.put("currency", currency);

					//针对销售类型 = 代销 或 直转代不在黑名单 且 是外扣（取订单） 的预约消息模板用另一个
					boolean sepcflag = (StaticVar.SFMSJG_DX.equals(sfmsjg) || StaticVar.SFMSJG_ZZD.equals(sfmsjg)) && StaticVar.FEECALMODE_OUT.equals(feecalmode) && StringUtil.isNull(blackhboneno);
					if(sepcflag){
						parmsg.put("charge", fee);
//						msgid =messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_PAY_DX,CrmNtConstant.MSG_BID_PAY_DX_HZ,CrmNtConstant.MSG_BID_PAY_DX_HX,pcode);
						msgid =messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_PAY_DX,CrmNtConstant.MSG_BID_PAY_DX_HZ,null,pcode);
					}else {
//						msgid =messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_PAY_NEW,CrmNtConstant.MSG_BID_PAY_NEW_HZ,CrmNtConstant.MSG_BID_PAY_NEW_HW,pcode);
						msgid =messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_PAY_NEW,CrmNtConstant.MSG_BID_PAY_NEW_HZ,null,pcode);
					}
					if(msgid==null){
						msgid = CrmNtConstant.MSG_BID_PAY_NEW;
					}
					String msg = JSON.toJSONString(parmsg);
					//发送短信
					int result = messageCommonBuss.sendMessageByHboneNo(msgid, msg, hboneno);
					//发送成功，记录发送成功表
					if(result ==0){
						messageCommonBuss.insertMessageDailly(CrmNtConstant.MSG_TYPE_PAY,custno, msg, custno+pcode);
					}
				}
				
			}
		}
		return null;
	}



}

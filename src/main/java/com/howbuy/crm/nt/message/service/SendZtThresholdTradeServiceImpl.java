package com.howbuy.crm.nt.message.service;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.cache.cacheService.CacheKeyPrefix;
import com.howbuy.crm.cache.cacheService.lock.LockService;
import com.howbuy.crm.constant.MsgBusinessIdConstants;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.message.dao.JobSendZtThresholdTradeMapper;
import com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade;
import com.howbuy.crm.nt.outerservice.tms.dto.ZtThresholdTradeDTO;
import com.howbuy.crm.nt.outerservice.tms.service.RobotOrdersOuterService;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.tms.robot.orders.facade.enums.QueryCustAmtResStatusEnum;
import com.howbuy.tms.robot.orders.facade.enums.QueryCustAmtTradeTypeEnum;
import crm.howbuy.base.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service("sendZtThresholdTradeService")
public class SendZtThresholdTradeServiceImpl implements SendZtThresholdTradeService {

    @Autowired
    private RobotOrdersOuterService robotOrdersOuterService;


    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Autowired
    private JobSendZtThresholdTradeMapper jobSendZtThresholdTradeMapper;

    @Autowired
    private ConscustMapper conscustMapper;

    /**
     * 自动触发
     */
    private static final String AUTO_JOB_TYPE = "1";

    /**
     * 手动触发
     */
    private static final String MANUAL_JOB_TYPE = "2";

    @Autowired
    protected LockService lockService;

    private static final int DEFAULT_EXPIRE = 43200;


    @Override
    public void deal(String arg) {
        JSONObject paramJson = JSONObject.parseObject(arg);
        log.info("SendZtThresholdTradeService arg:{}", paramJson);

        // 触发方式：自动触发（每日自动触发多次，有一次成功后，后续的触发 跳过执行）、手动触发（手动触发一次，每次都生效）
        String jobType = paramJson.getString("jobType");

        if (AUTO_JOB_TYPE.equals(jobType)) {
            // 自动触发
            // 获取系统当前日期
            String currentDate = DateUtil.getDateYYYYMMDD();
            JobSendZtThresholdTrade dbSendJobPo = jobSendZtThresholdTradeMapper.selectByTradeDateAndJobType(currentDate, AUTO_JOB_TYPE);
            if (Objects.nonNull(dbSendJobPo) &&
                    (QueryCustAmtResStatusEnum.SUCCESS.getCode().equals(dbSendJobPo.getZtResponseStatus()) ||
                            QueryCustAmtResStatusEnum.NOT_TRADE_DAY.getCode().equals(dbSendJobPo.getZtResponseStatus()))) {
                log.info("SendZtThresholdTradeService already deal, tradeDate:{}", currentDate);
                return;
            }
        }
        // 指定日期、指定金额门槛，都可以为空，为空则默认为当天，金额默认为30万
        String inputDate = paramJson.getString("date");
        String buyAmtThreshold = paramJson.getString("buyAmtThreshold");
        String sellAmtThreshold = paramJson.getString("sellAmtThreshold");

        ZtThresholdTradeDTO thresholdTradeDTO = robotOrdersOuterService.queryThresholdTrade(inputDate, buyAmtThreshold, sellAmtThreshold);
        // 消息推送
        int pushFailCount = pushMsg(thresholdTradeDTO, jobType);
        //
        updateJobPo(jobType, inputDate, thresholdTradeDTO, pushFailCount);
    }


    /**
     * @param thresholdTradeDTO
     * @return int
     * @description: 推送消息
     * @author: jin.wang03
     * @date: 2024/6/20 13:17
     * @since JDK 1.8
     */
    private int pushMsg(ZtThresholdTradeDTO thresholdTradeDTO, String jobType) {
        int pushFailCount = 0;
        List<ZtThresholdTradeDTO.TradeAmtDetail> tradeAmtDetailList = thresholdTradeDTO.getTradeList();
        if (CollectionUtils.isNotEmpty(tradeAmtDetailList)) {
            for (ZtThresholdTradeDTO.TradeAmtDetail tradeAmtDetail : tradeAmtDetailList) {
                String hboneNo = tradeAmtDetail.getHboneNo();
                log.info("sendZtThresholdTradeService|hboneNo:{}", hboneNo);
                if (StringUtils.isBlank(hboneNo)) {
                    continue;
                }
                try {
                    ConscustInfo conscustInfo = getCustInfoByHboneNo(hboneNo);
                    log.info("sendZtThresholdTradeService|get conscustInfo by hboneNo:{}", conscustInfo);
                    if (Objects.isNull(conscustInfo)) {
                        continue;
                    }

                    // 幂等性校验：对于自动触发的任务，同一客户、同一日期、同一交易类型，只发一次消息
                    if (AUTO_JOB_TYPE.equals(jobType)) {
                        String uniqKey = CacheKeyPrefix.CRM_THRE_TRADE_SUFFIX + hboneNo + tradeAmtDetail.getTradeType() + tradeAmtDetail.getTradeDt();
                        log.info("processMessage|get lock success,uniqKey:{}", uniqKey);
                        boolean lockFlag = lockService.getLock(uniqKey, DEFAULT_EXPIRE);
                        log.info("processMessage|uniqKey:{} lockFlag: {}", uniqKey, lockFlag);
                        // 运行锁，只有在获取了锁之后才准许执行
                        if (!lockFlag) {
                            log.info("processMessage|get lock fail,uniqKey:{}", uniqKey);
                            continue;
                        }

                    }

                    String tradeType = tradeAmtDetail.getTradeType();
                    String tradeDt = DateUtil.dateFormat(tradeAmtDetail.getTradeDt());
                    BigDecimal tradeAmt = tradeAmtDetail.getTradeAmt();
                    BigDecimal tradeVol = tradeAmtDetail.getTradeVol();

                    String businessId = QueryCustAmtTradeTypeEnum.BUY.getCode().equals(tradeType) ?
                            MsgBusinessIdConstants.BUSI_ID_ZT_THRESHOLD_BUY : MsgBusinessIdConstants.BUSI_ID_ZT_THRESHOLD_SELL;
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("custNo", conscustInfo.getConscustno());
                    paramMap.put("custName", conscustInfo.getCustname());
                    paramMap.put("tradeDt", tradeDt);

                    if (QueryCustAmtTradeTypeEnum.BUY.getCode().equals(tradeType)) {
                        paramMap.put("tradeAmt", tradeAmt.toPlainString());
                    } else {
                        paramMap.put("tradeVol", tradeVol.toPlainString());
                    }

                    log.info("sendZtThresholdTradeService|hboneNo:{}, paramMap:{}", hboneNo, paramMap);
                    cmPushMsgService.pushMsgByHboneNo(businessId, hboneNo, paramMap);
                } catch (Exception e) {
                    pushFailCount++;
                    log.error("公募交易(默认交易日累积超过30万)消息推送 出现异常！一账通号：{}", hboneNo, e);
                }
            }
        }
        return pushFailCount;
    }

    /**
     * @param jobType
     * @param thresholdTradeDTO
     * @param inputDate
     * @description: 更新job po
     * @author: jin.wang03
     * @date: 2024/6/18 14:59
     * @since JDK 1.8
     */
    private void updateJobPo(String jobType, String inputDate, ZtThresholdTradeDTO thresholdTradeDTO, int pushFailCount) {
        if (AUTO_JOB_TYPE.equals(jobType)) {
            // 获取系统当前日期
            String currentDate = DateUtil.getDateYYYYMMDD();
            JobSendZtThresholdTrade dbSendJobPo = jobSendZtThresholdTradeMapper.selectByTradeDateAndJobType(currentDate, AUTO_JOB_TYPE);

            if (Objects.nonNull(dbSendJobPo)) {
                JobSendZtThresholdTrade jobSendZtThresholdTrade = new JobSendZtThresholdTrade();
                jobSendZtThresholdTrade.setId(dbSendJobPo.getId());
                jobSendZtThresholdTrade.setZtResponseStatus(thresholdTradeDTO.getStatus());
                jobSendZtThresholdTrade.setModifyTime(new Date());
                jobSendZtThresholdTrade.setPushFailCount(pushFailCount);
                jobSendZtThresholdTradeMapper.updateByPrimaryKeySelective(jobSendZtThresholdTrade);
            } else {
                JobSendZtThresholdTrade jobSendZtThresholdTrade = new JobSendZtThresholdTrade();
                jobSendZtThresholdTrade.setTradeDate(currentDate);
                jobSendZtThresholdTrade.setJobType(AUTO_JOB_TYPE);
                jobSendZtThresholdTrade.setZtResponseStatus(thresholdTradeDTO.getStatus());
                jobSendZtThresholdTrade.setPushFailCount(pushFailCount);
                jobSendZtThresholdTrade.setCreateTime(new Date());
                jobSendZtThresholdTradeMapper.insert(jobSendZtThresholdTrade);
            }
        } else {
            JobSendZtThresholdTrade jobSendZtThresholdTrade = new JobSendZtThresholdTrade();
            jobSendZtThresholdTrade.setTradeDate(inputDate);
            jobSendZtThresholdTrade.setJobType(MANUAL_JOB_TYPE);
            jobSendZtThresholdTrade.setZtResponseStatus(thresholdTradeDTO.getStatus());
            jobSendZtThresholdTrade.setPushFailCount(pushFailCount);
            jobSendZtThresholdTrade.setCreateTime(new Date());
            jobSendZtThresholdTradeMapper.insert(jobSendZtThresholdTrade);
        }
    }


    /**
     * 根据一账通账号 查找客户信息
     *
     * @param hboneNo
     * @return
     */
    private ConscustInfo getCustInfoByHboneNo(String hboneNo) {
        Map<String, String> params = new HashMap<>();
        params.put("hboneno", hboneNo);
        List<ConscustInfo> listCustInfo = conscustMapper.listConsCustInfoByHboneNo(params);
        if (CollectionUtils.isNotEmpty(listCustInfo)) {
            return listCustInfo.get(0);
        }
        return null;
    }

}

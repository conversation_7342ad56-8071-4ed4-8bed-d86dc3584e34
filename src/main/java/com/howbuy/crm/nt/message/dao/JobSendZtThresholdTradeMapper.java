package com.howbuy.crm.nt.message.dao;


import com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade;
import org.apache.ibatis.annotations.Param;

public interface JobSendZtThresholdTradeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(JobSendZtThresholdTrade record);

    int insertSelective(JobSendZtThresholdTrade record);

    JobSendZtThresholdTrade selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(JobSendZtThresholdTrade record);

    int updateByPrimaryKey(JobSendZtThresholdTrade record);

    /**
     * @description: 根据交易日期、触发类型查询
     * @param tradeDate	交易日期
     * @return com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade
     * @author: jin.wang03
     * @date: 2024/6/18 13:45
     * @since JDK 1.8
     */
    JobSendZtThresholdTrade selectByTradeDateAndJobType(@Param("tradeDate") String tradeDate, @Param("jobType") String jobType);

}
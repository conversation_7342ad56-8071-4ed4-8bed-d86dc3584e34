package com.howbuy.crm.nt.message.domain;

import java.io.Serializable;
import java.util.Date;

public class CmCompanyReport implements Serializable {

    private static final long serialVersionUID = 1L;
    /** id */
    private long id;
    /** 公司id */
    private String companyId;
    /** 产品代码 */
    private String fundCode;
    /** 报告日期 */
    private String reportDate;
    /** 报告标题 */
    private String title;
    /** 创建时间 */
    private Date createTime;
    /** 修改时间 */
    private Date updateTime;
    /** 文件名称 */
    private String fileName;
    /** 报告分类 */
    private String pType;
    /** 是否已经推送 1-已推送 */
    private String sendstatus;
    /** 产品报告类型 */
    private String reportType;
    /** 展示状态 0-前段不展示 1-前端展示 */
    private int status = 0;
    /** 季度 */
    private String quarter;
    /** 跳转地址 */
    private String redirectUrl;
    /** 报告审核状态 0-待审核 1-审核通过 2-驳回 */
    private String checkStatus;
    /** 批注 */
    private String remark;
    /** 创建人 */
    private String creater;
    /** 修改人 */
    private String editor;
    /** 是否发送过自选动态消息 */
    private String sendSmMsg;
    /** 同步时间 */
    private Date syncDate;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getReportDate() {
        return reportDate;
    }

    public void setReportDate(String reportDate) {
        this.reportDate = reportDate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getpType() {
        return pType;
    }

    public void setpType(String pType) {
        this.pType = pType;
    }

    public String getSendstatus() {
        return sendstatus;
    }

    public void setSendstatus(String sendstatus) {
        this.sendstatus = sendstatus;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getQuarter() {
        return quarter;
    }

    public void setQuarter(String quarter) {
        this.quarter = quarter;
    }

    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreater() {
        return creater;
    }

    public void setCreater(String creater) {
        this.creater = creater;
    }

    public String getEditor() {
        return editor;
    }

    public void setEditor(String editor) {
        this.editor = editor;
    }

    public String getSendSmMsg() {
        return sendSmMsg;
    }

    public void setSendSmMsg(String sendSmMsg) {
        this.sendSmMsg = sendSmMsg;
    }

    public Date getSyncDate() {
        return syncDate;
    }

    public void setSyncDate(Date syncDate) {
        this.syncDate = syncDate;
    }
}

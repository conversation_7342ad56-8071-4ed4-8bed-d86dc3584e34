package com.howbuy.crm.nt.message.dao;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.message.domain.CmJjjzMsgRec;

/**
 * @Description: 接口
 * @version 1.0
 * @created 
 */
public interface CmJjjzMsgRecDao {

     /**
     * @Description:得到单个数据对象
     * @param Map
     * @return CmJjjzMsgRec
     */
    CmJjjzMsgRec getCmJjjzMsgRec(Map<String, String> param);
    
	/**
     * @Description:查询列表数据对象
     * @param Map
     * @return List<CmJjjzMsgRec>
     */
	List<CmJjjzMsgRec> listCmJjjzMsgRec(Map<String, Object> param);
	
	/**
     * 插入同步数据
     *
     * @param cmJjjzMsgRec
     */
    void insertCmJjjzMsgRec(CmJjjzMsgRec cmJjjzMsgRec);
	
	/**
     * 批量插入同步数据
     *
     * @param listCmJjjzMsgRec
     */
    void batchInsertCmJjjzMsgRec(List<CmJjjzMsgRec> listCmJjjzMsgRec);
    
}

package com.howbuy.crm.nt.message.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.message.bizservice.MessageFundInfoOuterService;
import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("saleMessageServiceImpl")
public class SaleMessageServiceImpl implements SaleMessageService {
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private MessageCommonBuss messageCommonBuss;

	@Resource
	private MessageFundInfoOuterService messageFundInfoOuterService;
	
	private Logger logger= LoggerFactory.getLogger(SaleMessageServiceImpl.class);

	@Override
	public void dealSaleMessage(String arg) {
		Map<String, Object> map = new HashMap<>(1);
		//查询符合条件的数据
		List<Map<String, String>> list = cmMessageDao.listSaleMessage(map);
		logger.info("SendPrivateFundSaleMessageJob| 查询出的符合条件的赎回交易记录" + JSON.toJSONString(list));
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (Map<String, String> obj : list) {
			String appserialNo = StringUtil.replaceNull(obj.get("APPSERIALNO"));
			String hboneno = StringUtil.replaceNull(obj.get("HBONE_NO"));
			String custno = StringUtil.replaceNull(obj.get("CUSTNO"));
			String custname = StringUtil.replaceNull(obj.get("CUSTNAME"));
			String jjjc = StringUtil.replaceNull(obj.get("JJJC"));
			String tradedt = StringUtil.replaceNull(obj.get("TRADEDT"));
			String pcode = StringUtil.replaceNull(obj.get("FUNDCODE"));
			// 一账通不能为空
			if (StringUtil.isNullStr(hboneno)) {
				continue;
			}
			Map<String, String> parmsg = new HashMap<>(3);
			parmsg.put("custName", custname);
			parmsg.put("tradedt", tradedt);
			parmsg.put("fund_name", jjjc);
			String msg = JSON.toJSONString(parmsg);
			// 通过基金Code获取产品分销渠道的消息ID
			String msgId = messageFundInfoOuterService.getFundMessageIdByFundCode(CrmNtConstant.MSG_BID_SALE, CrmNtConstant.MSG_BID_SALE_HZ,
					CrmNtConstant.MSG_BID_SALE_HW, pcode);
			if (StringUtils.isBlank(msgId)) {
				logger.info("SendPrivateFundSaleMessageJob|" +
						"获取分销渠道消息模版ID是空,不发送消息，appserialNo:{}，pCode:{},custno:{}", appserialNo, pcode, custno);
				continue;
			}
			//发送短信
			int result = messageCommonBuss.sendMessageByHboneNo(msgId, msg, hboneno);
			//发送成功，记录发送成功表
			if (result == 0) {
				messageCommonBuss.insertMessageDailly(CrmNtConstant.MSG_TYPE_SALE, custno, msg, custno + pcode);
			}
		}
	}

	

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmJjjzMsgLatestDao">
	  <select id="listCmJjjzMsgLatest" parameterType="Map" resultType="com.howbuy.crm.nt.message.domain.CmJjjzMsgLatest" useCache="false">
	    SELECT * FROM CM_JJJZ_MSG_LATEST
	     where 1=1  
	      <if test="id != null"> AND id = #{id} </if>
          <if test="jjdm != null"> AND jjdm = #{jjdm} </if>
          <if test="jjrq != null"> AND jjrq = #{jjrq} </if>             
          <if test="jjjz != null"> AND jjjz = #{jjjz} </if>             
          <if test="moddt != null"> AND moddt = #{moddt} </if>             
          <if test="syncdt != null"> AND syncdt = #{syncdt} </if>
   	  </select>


   	  <update id="batchInsertCmJjjzMsgLatest" parameterType="Map" useGeneratedKeys="false">
        INSERT INTO CM_JJJZ_MSG_LATEST (ID, JJDM, JSRQ, JJJZ)
        SELECT T1.ID, T1.JJDM, T1.JSRQ, T1.JJJZ
		  FROM CM_JJJZ_MSG_REC T1
		 WHERE 1 = 1
		   AND T1.JJDM IS NOT NULL
		   AND T1.JSRQ IS NOT NULL
		   AND T1.JJJZ IS NOT NULL
		   AND TO_CHAR(T1.SYNCDT, 'YYYYMMDD') = TO_CHAR(SYSDATE, 'YYYYMMDD')
		   AND NOT EXISTS 
		       (SELECT 1 FROM CM_JJJZ_MSG_LATEST L
		         WHERE TO_CHAR(L.SYNCDT, 'YYYYMMDD') <![CDATA[>=]]> TO_CHAR(SYSDATE - 31, 'YYYYMMDD')
		           AND L.JJDM = T1.JJDM
		           AND L.JSRQ = T1.JSRQ
		           AND L.JJJZ = T1.JJJZ)
    </update>

	  
</mapper>




package com.howbuy.crm.nt.message.dao;

import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.message.domain.CmJjjzMsgLatest;

/**
 * @Description: 接口
 * @version 1.0
 * @created 
 */
public interface CmJjjzMsgLatestDao {

	/**
     * @Description:查询列表数据对象
     * @param Map
     * @return List<CmJjjzMsgLatest>
     */
	List<CmJjjzMsgLatest> listCmJjjzMsgLatest(Map<String, String> param);
	
	/**
     * 批量插入最新消息表
     * @param void
     */
    void batchInsertCmJjjzMsgLatest(Map<String, String> param);
    
}

package com.howbuy.crm.nt.message.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类CmJjjzMsgRec
 */
public class CmJjjzMsgRec implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	private String jjdm;

	private String jjjc;

	private String jsrq;

	private Double jjjz;

	private String lastJzrq;

	private Double hbdr;

	private Date jjjzModdt;

	private Date moddt;
	
	private Date syncdt;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getJjdm() {
		return jjdm;
	}

	public void setJjdm(String jjdm) {
		this.jjdm = jjdm;
	}

	public String getJjjc() {
		return jjjc;
	}

	public void setJjjc(String jjjc) {
		this.jjjc = jjjc;
	}

	public String getJsrq() {
		return jsrq;
	}

	public void setJsrq(String jsrq) {
		this.jsrq = jsrq;
	}

	public Double getJjjz() {
		return jjjz;
	}

	public void setJjjz(Double jjjz) {
		this.jjjz = jjjz;
	}

	public String getLastJzrq() {
		return lastJzrq;
	}

	public void setLastJzrq(String lastJzrq) {
		this.lastJzrq = lastJzrq;
	}

	public Double getHbdr() {
		return hbdr;
	}

	public void setHbdr(Double hbdr) {
		this.hbdr = hbdr;
	}

	public Date getJjjzModdt() {
		return jjjzModdt;
	}

	public void setJjjzModdt(Date jjjzModdt) {
		this.jjjzModdt = jjjzModdt;
	}

	public Date getModdt() {
		return moddt;
	}

	public void setModdt(Date moddt) {
		this.moddt = moddt;
	}

	public Date getSyncdt() {
		return syncdt;
	}

	public void setSyncdt(Date syncdt) {
		this.syncdt = syncdt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
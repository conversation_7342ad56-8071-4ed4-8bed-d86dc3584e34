package com.howbuy.crm.nt.message.dao;


import crm.howbuy.base.domain.CmPushMsgDay;
import java.util.List;
import java.util.Map;


public interface CmPushMsgDayDao {

    /**
     * 获取基金分红消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryFundFhMsgList();

    /**
     * 获取基金到期消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryFundDqMsgList();

    /**
     * 获取基金团队到期消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryFundTddqMsgList();

    /**
     * 获取投顾客户生日提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryBirthDayMsgList();

    /**
     * 获取到账通知提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryCustDzMsgList();

    /**
     * 获取资料更新通知提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryCustZlgxMsgList();
    
    /**
	 * 获取净值通知提醒消息数据
	 * @return List<CmPushMsgDay>
	 */
	List<CmPushMsgDay> queryFundJzMsgList(Map<String, String> param);

    /**
     * 获取缴费提醒消息数据
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryCustPaymentMsgList();

	/**
     * 获取预约客户提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryCustYyMsgList();
	
    /**
     * 批量插入当天消息表
     *
     * @param listCmPushMsgDay
     */
    void batchInsertCmPushMsgDay(List<CmPushMsgDay> listCmPushMsgDay);

    /**
     * 批量回收当天表数据到历史表
     */
    Integer batchInsertCmPushMsgHis();

    /**
     * 清空当天表数据
     */
    Integer deleteCmPushMsgDay();

    /**
     * 清空当天历史表数据
     */
    Integer deleteCurCmPushMsgHis();

    /**
     * 恢复当天表数据
     */
    Integer updateCmPushMsgDay();

    /**
     * 获取当天消息表中所有数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryCurAllMsgList();
    
    /**
     * 获取交易类型 消息数据:交易类型 = 强赎
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryJylxqsMsgList();
    
    /**
     * 获取交易类型 消息数据:交易类型 = 红利发放
     *
     * @return List<CmPushMsgDay>
     */
    List<CmPushMsgDay> queryJylxhlffMsgList();
}

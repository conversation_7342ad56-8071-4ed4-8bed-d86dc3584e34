package com.howbuy.crm.nt.message.domain;

import java.util.Date;

public class JobSendZtThresholdTrade {
    private Long id;

    /**
    * 交易日期
    */
    private String tradeDate;

    /**
    * 中台返回值状态(1成功、2交易申请日终未完成，请稍后查询、3非交易日，无法查询)
    */
    private String ztResponseStatus;

    /**
     * 任务触发方式 1自动触发 2手动触发
     */
    private String jobType;

    /**
    * 创建日期时间
    */
    private Date createTime;

    /**
    * 修改日期时间
    */
    private Date modifyTime;

    /**
     * 消息推送失败数量
     */
    private int pushFailCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(String tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getZtResponseStatus() {
        return ztResponseStatus;
    }

    public void setZtResponseStatus(String ztResponseStatus) {
        this.ztResponseStatus = ztResponseStatus;
    }


    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public int getPushFailCount() {
        return pushFailCount;
    }

    public void setPushFailCount(int pushFailCount) {
        this.pushFailCount = pushFailCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
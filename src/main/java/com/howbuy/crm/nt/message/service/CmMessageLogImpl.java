/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.message.service;

import com.howbuy.crm.nt.message.dao.CmMessageLogDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Service("cmMessageLogService")
@Transactional(rollbackFor = Exception.class)
public class CmMessageLogImpl implements CmMessageLogService{

    @Autowired
    private CmMessageLogDao cmMessageLogDao;


    /**
     * @description:(批量当日短信日志归档操作)
     * @author: fanchao.xu
     * @date: 2023/5/4 17:43
     * @since JDK 1.8
     */
    @Override
    public void dailyDealMessage() {
        // 查询当日数据并且进行归档操作
        cmMessageLogDao.insertIntoMessageLog();
        // 删除短信记录
        cmMessageLogDao.deleteCmMessageDaily();
    }
}
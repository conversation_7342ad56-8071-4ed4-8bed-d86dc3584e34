package com.howbuy.crm.nt.message.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.nt.message.buss.CmPushMsgDaoBuss;
import com.howbuy.crm.util.DateTimeUtil;
import com.howbuy.crm.util.MainLogUtils;
import crm.howbuy.base.utils.HttpUtils;
import com.howbuy.persistence.content.interview.CompanyReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;

@Service("pushZlgxMsgCollectService")
@Slf4j
public class PushZlgxMsgCollectServiceImpl implements PushZlgxMsgCollectService{

    @Autowired
    private CmPushMsgDaoBuss cmPushMsgDaoBuss;

    /*@Autowired
    private CompanyReportService companyReportService;*/

    @Value("${dbhttpurl}")
    private String dbhttpurl;

    /** 资料更新通知任务 */
    public static final String ARG_ZLGXTZ = "zlgxtz";

    @Override
    public void syncCustZlgxMsgData(String arg) {
        /*this.syncZlgxMsgData("zlgxtz");*/
        if (arg != null && arg.contains(ARG_ZLGXTZ)) {
            String beforeday = DateTimeUtil.getBeforeDateFmt(DateTimeUtil.DATE_PATTERN)+" 17:30:00";
            String nowday = DateTimeUtil.getCurDate(DateTimeUtil.DATE_PATTERN)+" 17:30:00";
            log.info("syncCustZlgxMsgData传参beforeday:"+beforeday+",nowday:"+nowday);

            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("startTime",beforeday);
            paramMap.put("endTime",nowday);

            List<CompanyReport> reportlist= new ArrayList<CompanyReport>();
            try {
                long startTime = System.currentTimeMillis();
                String responsejson = HttpUtils.post(dbhttpurl,paramMap);
                long endTime = System.currentTimeMillis();
                MainLogUtils.httpCallOut(dbhttpurl, String.valueOf(HttpStatus.OK.value()), endTime - startTime);
                JSONArray jsonArray = JSONArray.parseArray(responsejson);
                if(jsonArray != null && jsonArray.size() > 0){
                    for(Object obj :jsonArray){
                        JSONObject entity = (JSONObject) obj;
                        CompanyReport bean = JSON.parseObject(entity.toJSONString(), CompanyReport.class);
                        reportlist.add(bean);
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

            log.info("syncCustZlgxMsgData.reportlist:"+ JSON.toJSONString(reportlist));
            if(reportlist != null && reportlist.size() > 0){
                cmPushMsgDaoBuss.batchInsertCmCompanyReport(reportlist);
                this.syncZlgxMsgData(ARG_ZLGXTZ);
            }
        }else{
            log.info("资料更新通知任务已停用！");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncZlgxMsgData(String str){
        // 加载到账通知任务
        if (str != null && str.contains(ARG_ZLGXTZ)) {
            cmPushMsgDaoBuss.syncCustZlgxMsg();
        } else {// 同步到账通知数据方法
            log.info("资料更新通知任务已停用！");
        }
    }
}

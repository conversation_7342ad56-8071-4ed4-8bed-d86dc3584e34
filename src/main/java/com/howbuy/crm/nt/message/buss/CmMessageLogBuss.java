/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.message.buss;


import com.howbuy.crm.nt.message.service.CmMessageLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (当日交易短信日志归档--存储过程改造)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmMessageLogBuss {

    @Autowired
    private CmMessageLogService cmMessageLogService;

    /**
     * @description:(批量当日短信日志归档操作)
     * @author: fanchao.xu
     * @date: 2023/5/4 17:22
     * @since JDK 1.8
     */
    public void batchInsertCmMessageLog() {
        try {
            cmMessageLogService.dailyDealMessage();
        } catch (Exception e) {
            log.error("error in cmMessageLogBuss", e);
        }
    }

}
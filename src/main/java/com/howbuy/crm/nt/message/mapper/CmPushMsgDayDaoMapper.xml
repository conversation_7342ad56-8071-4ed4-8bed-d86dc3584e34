<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmPushMsgDayDao">

    <resultMap id="BaseResultMap" type="crm.howbuy.base.domain.CmPushMsgDay">
    	<result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="MSGTYPE" property="msgType" jdbcType="VARCHAR"/>
        <result column="CONSCODE" property="consCode" jdbcType="VARCHAR"/>
        <result column="MSGCONTENT" property="msgContent" jdbcType="VARCHAR"/>
        <result column="READFLAG" property="readFlag" jdbcType="VARCHAR"/>
        <result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
        <result column="PUSHDT" property="pushDt" jdbcType="VARCHAR"/>
        <result column="CREDT" property="creDt" jdbcType="VARCHAR"/>
        <result column="MODIFIER" property="modifier" jdbcType="VARCHAR"/>
        <result column="MODDT" property="modDt" jdbcType="VARCHAR"/>
        <result column="STIMESTAMP" property="stimeStamp" jdbcType="TIMESTAMP"/>
        <result column="MSGCONTENTDT" property="msgContentDt" jdbcType="VARCHAR"/>
        <result column="EXPIREDT" property="expireDt" jdbcType="VARCHAR"/>
        <result column="MSGCODE" property="msgCode" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryFundFhMsgList" resultMap="BaseResultMap">
		SELECT T.CONSCODE,
		       T.MSGCONTENT,
		       T.MSGCONTENTDT,
		       TO_CHAR(TO_DATE(T.MSGCONTENTDT,'YYYYMMDD') + 7,'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
				SELECT N.CONSCODE AS CONSCODE,
					   '产品' || M.JJJC || ',于' || M.FPRQ || '分红，您持有该产品的客户' || N.CNT || '人，具体持有客户信息请通过固收产品分红提醒菜单中查询。' AS MSGCONTENT,
		               M.FPRQ AS MSGCONTENTDT
				  FROM (
				        SELECT DISTINCT T.FUNDCODE AS JJDM,
				               T2.JJJC,
				               TO_CHAR(T5.FPRQ, 'YYYYMMDD') AS FPRQ
				          FROM CM_HIGH_CUSTFUND T
				         INNER JOIN JJXX1 T2
				            ON T.FUNDCODE = T2.JJDM
				         INNER JOIN XTSYFPRL T5
				            ON T2.JJDM = T5.JJDM
				         WHERE T2.HMCPX = '2'
				           AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[>=]]> TO_CHAR(SYSDATE - 7, 'YYYYMMDD')
				           AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
				           AND T.FUNDTYPE = '3'
				           AND T5.XTFPLX = '1'
				           AND T.BALANCEVOL > 1
				        ) M
				 INNER JOIN (
				             SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
				               FROM CM_HIGH_CUSTFUND T
				              INNER JOIN CM_CONSCUST T1
				                 ON T.CUSTNO = T1.CONSCUSTNO
				              INNER JOIN CM_CUSTCONSTANT B
				                 ON T.CUSTNO = B.CUSTNO
				              INNER JOIN JJXX1 T2
				                 ON T.FUNDCODE = T2.JJDM
				              INNER JOIN XTSYFPRL T5
				                 ON T2.JJDM = T5.JJDM
				              WHERE T2.HMCPX = '2'
				                AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[>=]]> TO_CHAR(SYSDATE - 7, 'YYYYMMDD')
				                AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
				                AND T.FUNDTYPE = '3'
				                AND T5.XTFPLX = '1'
				                AND T.BALANCEVOL > 1
				              GROUP BY T.FUNDCODE, B.CONSCODE
				             ) N
				    ON M.JJDM = N.FUNDCODE
				 WHERE 1 = 1
				   AND N.CONSCODE IS NOT NULL
				 ORDER BY CNT DESC NULLS LAST, FPRQ
		 	) T
        	WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '1' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryFundDqMsgList" resultMap="BaseResultMap">
		SELECT T.CONSCODE,
		       T.MSGCONTENT,
		       T.MSGCONTENTDT,
		       TO_CHAR(TO_DATE(T.MSGCONTENTDT,'YYYYMMDD') + 7,'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
				SELECT N.CONSCODE AS CONSCODE,
					   '产品' || M.JJJC || ',于' || M.ZZRQ || '到期，您持有该产品的客户' || N.CNT || '人，具体持有客户信息请通过固收产品到期提醒菜单中查询。' AS MSGCONTENT,
	               	   M.ZZRQ AS MSGCONTENTDT
				  FROM (SELECT DISTINCT T.FUNDCODE AS JJDM,
				               T2.JJJC,
				               FT.YJDQR AS ZZRQ
				          FROM CM_HIGH_CUSTFUND T
				         INNER JOIN JJXX1 T2
				            ON T.FUNDCODE = T2.JJDM
				             INNER JOIN (SELECT MAX(FX.YJDQR) AS YJDQR, FX.JJDM
							FROM FDB_XTSYL FX
							WHERE FX.JJDM IS NOT NULL
							  AND FX.YJDQR <![CDATA[>=]]> TO_CHAR(SYSDATE - 7, 'YYYYMMDD')
                              AND FX.YJDQR <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
							GROUP BY FX.JJDM) FT
							ON T2.JJDM = FT.JJDM
				         WHERE T.BALANCEVOL > 1
				           AND T.FUNDTYPE = '3'
				           AND T2.HMCPX = '2'
                   AND T2.ZZRQ <![CDATA[>=]]> TO_CHAR(SYSDATE - 7, 'YYYYMMDD')
                   AND T2.ZZRQ <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
                ) M
		        INNER JOIN (SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
		                       FROM CM_HIGH_CUSTFUND T
		                      INNER JOIN CM_CONSCUST T1
		                         ON T.CUSTNO = T1.CONSCUSTNO
		                      INNER JOIN CM_CUSTCONSTANT B
		                         ON T.CUSTNO = B.CUSTNO
		                      INNER JOIN JJXX1 T2
		                         ON T.FUNDCODE = T2.JJDM
		                      WHERE T.BALANCEVOL > 1
		                        AND T.FUNDTYPE = '3'
		                        AND T2.HMCPX = '2'
		                        AND T2.ZZRQ <![CDATA[>=]]> TO_CHAR(SYSDATE - 7, 'YYYYMMDD')
		                        AND T2.ZZRQ <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
		                      GROUP BY T.FUNDCODE, B.CONSCODE) N
		            ON M.JJDM = N.FUNDCODE
		         WHERE 1 = 1
		           AND N.CONSCODE IS NOT NULL
		         ORDER BY CNT DESC NULLS LAST, ZZRQ
          ) T
 		  WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '2' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryBirthDayMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
		       T.MSGCONTENT,
		       TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5) AS MSGCONTENTDT,
		       TO_CHAR(TO_DATE(TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5), 'YYYYMMDD') + 3, 'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
		  		SELECT T1.CONSCODE AS CONSCODE,
		               '客户' || T3.CUSTNAME || '，生日时间：' || SUBSTR(T3.BIRTHDAY, 5, 2)||'月'|| SUBSTR(T3.BIRTHDAY, 7, 2) || '日' AS MSGCONTENT,
		               T3.BIRTHDAY AS MSGCONTENTDT
		          FROM CM_CUSTCONSTANT T1
		          JOIN CM_CONSULTANT T2
		            ON T2.CONSCODE = T1.CONSCODE
		          JOIN CM_CONSCUST T3
		            ON T3.CONSCUSTNO = T1.CUSTNO
		         WHERE T2.ISVIRTUAL = '0'
		           AND T2.CONSLEVEL IS NOT NULL
		           AND T3.BIRTHDAY IS NOT NULL
		           AND LENGTH(T3.BIRTHDAY) = 8
		           AND SUBSTR(T3.BIRTHDAY, 5) <![CDATA[>=]]> TO_CHAR(SYSDATE, 'MMDD')
		           AND SUBSTR(T3.BIRTHDAY, 5) <![CDATA[<=]]> TO_CHAR(SYSDATE + 3, 'MMDD')
		 		) T
		  WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '4' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryCustDzMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
		       T.MSGCONTENT,
		       NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
		       TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
				SELECT T2.CONSCODE AS CONSCODE,
				       '客户' || T4.CUSTNAME || '，认购产品' || T3.JJJC || '，已到账，到账金额' || TRUNC(T.REALPAYAMT/10000, 2)|| '万，到账手续费' || NVL(CZO.FEE, T.FEE) ||'元（请以CRM实际到账为准）。' AS MSGCONTENT,
		               T.REALPAYAMTDT AS MSGCONTENTDT
				  FROM CM_PREBOOKPRODUCTINFO T
				  LEFT JOIN CM_ZT_ORDERINFO CZO
		        	ON T.ID = CZO.APPOINTMENTDEALNO
				  LEFT JOIN CM_CUSTCONSTANT T2
				    ON T2.CUSTNO = T.CONSCUSTNO
				  LEFT JOIN JJXX1 T3
				    ON T3.JJDM = T.PCODE
				  LEFT JOIN CM_CUST_TRANSFERVOL CCT
            		ON T.ID = CCT.FCCLPREID 
            	  LEFT JOIN CM_CONSCUST T4
            		ON T.CONSCUSTNO = T4.CONSCUSTNO
				 WHERE T.PREBOOKSTATE = '2'
				   AND T.PAYSTATE = '3'
				   AND T.TRADE_TYPE IN ('1', '2')
				   AND (CCT.FCCLTYPE IS NULL OR CCT.FCCLTYPE = '2')
				   AND T.PAYCHECKDT = TO_CHAR(SYSDATE, 'YYYYMMDD')
			   ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '5' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

	<select id="queryCustPaymentMsgList" resultMap="BaseResultMap">
		SELECT TT.CONSCODE,
		TT.MSGCONTENT,
		TT.MSGCONTENTDT,
		TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
		F_MD5(TT.CONSCODE || TT.MSGCONTENT) AS MSGCODE
		FROM (SELECT CT.CONSCODE,
		'您名下的客户' || CC.CUSTNAME || '持有的创新产品' || A.FUNDNAME || '，将于' ||
		C.PAYDT || '缴款，请通知客户及时缴款' AS MSGCONTENT,
		TO_CHAR(SYSDATE, 'yyyyMMdd') AS MSGCONTENTDT
		FROM CM_BX_PREBOOKINFO T
		LEFT JOIN CM_BX_PREBOOK_BUYINFO B
		ON T.ID = B.PREID
		LEFT JOIN CM_BX_PRODUCT A
		ON B.FUNDCODE = A.FUNDCODE
		LEFT JOIN CM_BX_PREBOOK_ENDPAY_LIST C
		ON C.BUYID = B.ID
		LEFT JOIN CM_CONSCUST CC
		ON T.CONSCUSTNO = CC.CONSCUSTNO
		LEFT JOIN CM_CUSTCONSTANT CT
		ON T.CONSCUSTNO = CT.CUSTNO
		WHERE C.PAYSTATE = '3'
		AND C.ISDEL = '1'
		AND B.ISDEL = '1'
		AND T.PRESTATE = '2'
		AND T.CHECKSTATE = '2'
		AND T.INSURSTATE = '3'
		AND ((A.BUSITYPE = '1' AND
		((TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 15, 'YYYYMMDD')) OR
		(TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 5, 'YYYYMMDD')))) OR
		(A.BUSITYPE <![CDATA[<>]]> '1' AND
		((TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 15, 'YYYYMMDD')) OR
		(TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 25, 'YYYYMMDD')))))) TT
		WHERE NOT EXISTS
		(SELECT 1
		FROM CM_PUSH_MSG_DAY D
		WHERE D.MSGTYPE = '11'
		AND D.MSGCODE = F_MD5(TT.CONSCODE || TT.MSGCONTENT))
    </select>

	<select id="queryCustZlgxMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
           T.MSGCONTENTDT,
           TO_CHAR(SYSDATE+3, 'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
        SELECT TIT.CONSCODE,
       case
         WHEN TIT.TYPE = 1 then
          '【资料类更新】产品：' || TIT.STR || ','
         else
          '【资料类更新】机构：' || TIT.STR || ','
       end || DECODE(TIT.REPORTTYPE,
       				 '4',
                     '通知公告',
                     '5',
                     '运作报告',
                     '6',
                     '调研报告',
                     '7',
                     '会议纪要',
                     '8',
                     '信息披露',
                     '9',
                     '分配公告',
                     '13',
                     '项目进展',
                     '') || '已更新' AS MSGCONTENT,
       TO_CHAR(SYSDATE, 'yyyyMMdd') AS MSGCONTENTDT
  FROM (SELECT 1 AS TYPE,
               TTT.CONSCODE,
               TTT.REPORTTYPE,
               to_char(wm_concat(TTT.FUNDCODE)) AS STR,
               TTT.TITLE
          FROM (SELECT CAL.CONSCODE, CAL.REPORTTYPE, CAL.FUNDCODE, CAL.TITLE
                  FROM (SELECT (SELECT JX.JJJC
                                  FROM JJXX1 JX
                                 WHERE JX.JJDM = CR.FUNDCODE) as FUNDCODE,
                               CR.REPORTTYPE,
                               CH.CUSTNO,
                               CT.CONSCODE,
                               CR.TITLE
                          FROM (SELECT T.FUNDCODE, T.REPORTTYPE, T.TITLE
                                  FROM CM_company_report T
                                 where T.FUNDCODE IS NOT NULL
                                   AND T.STATUS = 0
                                   AND T.SENDSTATUS = '1'
                                   AND T.REPORTTYPE IN
                                       ('4', '5', '6', '7', '8', '9', '13')
                                   AND TO_CHAR(T.SYNCDATE, 'yyyyMMdd hh:MM:ss') >= to_char(sysdate - 1,'yyyymmdd hh:MM:ss')) CR
                          LEFT JOIN CM_HIGH_CUSTFUND CH
                            ON CR.FUNDCODE = CH.FUNDCODE
                          LEFT JOIN CM_CUSTCONSTANT CT
                            ON CH.CUSTNO = CT.CUSTNO
                         WHERE CH.CUSTNO IS NOT NULL) CAL
                 GROUP BY CAL.CONSCODE,
                          CAL.REPORTTYPE,
                          CAL.FUNDCODE,
                          CAL.TITLE) TTT
         GROUP BY TTT.CONSCODE, TTT.REPORTTYPE, TTT.TITLE
        UNION ALL
        SELECT 0 AS TYPE,
               CAL.CONSCODE,
               CAL.REPORTTYPE,
               (SELECT A.JGMC
                  FROM jgxx A
                 WHERE A.JGDM = CAL.COMPANYID ) AS STR,
               CAL.TITLE
          FROM (SELECT CR.COMPANYID,
                       CR.REPORTTYPE,
                       CH.CUSTNO,
                       CT.CONSCODE,
                       CR.TITLE
                  FROM (SELECT T.JJDM AS FUNDCODE,
                               R.COMPANYID,
                               R.REPORTTYPE,
                               R.TITLE
                          FROM JJXX1 T, CM_company_report R
                         where T.GLRM = R.COMPANYID
                           AND R.FUNDCODE IS NULL
                           AND R.STATUS = 0
                           AND R.SENDSTATUS = '1'
                           AND R.REPORTTYPE IN ('4', '5', '6', '7', '8', '9', '13')
                           AND TO_CHAR(R.SYNCDATE, 'yyyyMMdd hh:MM:ss') >= to_char(sysdate - 1,'yyyymmdd hh:MM:ss')) CR
                  LEFT JOIN CM_HIGH_CUSTFUND CH
                    ON CR.FUNDCODE = CH.FUNDCODE
                  LEFT JOIN CM_CUSTCONSTANT CT
                    ON CH.CUSTNO = CT.CUSTNO
                 WHERE CH.CUSTNO IS NOT NULL) CAL
         GROUP BY CAL.CONSCODE, CAL.REPORTTYPE, CAL.COMPANYID, CAL.TITLE) TIT
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '8' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryFundJzMsgList" parameterType="Map" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
	           T.MSGCONTENT,
	           T.MSGCONTENTDT,
	           T.SYNCDT AS PUSHDT,
	           TO_CHAR(T.SYNCDT + 5, 'YYYYMMDD') AS EXPIREDT,
	           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
	      FROM (
	        SELECT N.CONSCODE AS CONSCODE,
                   '【净值通知】' || J.JJDM || J.JJJC || '，净值日期：' || J.JSRQ || '，净值：' || TO_CHAR(J.JJJZ, 'FM9999999990.0000') || '。与' || J.LASTJZRQ || '相比涨跌幅'|| (CASE WHEN J.DFLAG='1' THEN J.TFLAG || DECODE(J.HBDR, NULL, '--', J.HBDR, TO_CHAR((J.HBDR * 100), 'FM9999999990.00')||'%') ELSE '计算中' END) || '。您名下共有' || N.CNT || '个客户持有该产品。温馨提醒：净值可能存在误差，如对净值有疑议，请及时联系后台同事处理。涨跌幅请以官网公布数据为准。'
                   AS MSGCONTENT,
                   J.SYNCDT,
                   TO_CHAR(J.SYNCDT, 'YYYYMMDD') AS MSGCONTENTDT
            FROM (
              SELECT T1.JJDM, T1.JJJC, T1.JSRQ, T1.JJJZ, T1.HBDR, T1.LASTJZRQ, T1.JJJZMODDT, T1.SYNCDT, (CASE WHEN T1.MODDT IS NOT NULL AND T1.JJJZMODDT <![CDATA[<]]> T1.MODDT THEN 1 WHEN T1.MODDT IS NOT NULL AND T1.JJJZMODDT <![CDATA[>=]]> T1.MODDT THEN 2 ELSE 3 END) DFLAG, (CASE WHEN T1.HBDR IS NOT NULL AND T1.HBDR <![CDATA[>]]> 0 THEN '+' ELSE '' END) TFLAG
                FROM CM_JJJZ_MSG_REC T1
               WHERE 1=1
               <if test="timeFlag != null and timeFlag == 'AM9'">
                	AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE - 1, 'YYYYMMDD') || '1600'
                	AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE,     'YYYYMMDD') || '0000'
        		</if>
        		<if test="timeFlag != null and timeFlag == 'PM17'">
        			AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE, 'YYYYMMDD') || '0000'
                	AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD') || '1600'
        		</if>
        		AND T1.ID IN (SELECT ID FROM CM_JJJZ_MSG_LATEST)
            ) J
            LEFT JOIN (SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
                         FROM CM_HIGH_CUSTFUND T
                        INNER JOIN CM_CONSCUST T1
                           ON T.CUSTNO = T1.CONSCUSTNO
                        INNER JOIN CM_CUSTCONSTANT B
                           ON T.CUSTNO = B.CUSTNO
                        INNER JOIN JJXX1 T2
                           ON T.FUNDCODE = T2.JJDM
                        WHERE T.BALANCEVOL > 1
                          AND T.FUNDTYPE IN ('2', '3')
                        GROUP BY T.FUNDCODE, B.CONSCODE) N
              ON J.JJDM = N.FUNDCODE
            LEFT JOIN JJXX1 T3
              ON T3.JJDM = J.JJDM
           WHERE 1 = 1
             AND N.CONSCODE IS NOT NULL
          ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '6' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryCustYyMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
		       T.MSGCONTENT,
		       T.MSGCONTENTDT,
		       TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
				SELECT CONSCODE, MSGCONTENT, MSGCONTENTDT
				  FROM (SELECT T.BOOKINGCONS AS CONSCODE,
				               (CASE
				                   WHEN T.BOOKINGSTARTTIME = TO_CHAR(SYSDATE, 'HH24:MI') THEN
				                    '【预约开始提醒】'|| CUST.CUSTNAME || '，拜访方式：' || DECODE(T.VISITTYPE, 1, '电话', 2, '见面', 3, '参会', 4, '短信', 5, '邮件') || '，预约日期：' || T.BOOKINGDT || ' ' || T.BOOKINGSTARTTIME || ' 至 ' || NVL(T.BOOKINGENDTIME, '--') || '，预约事项：' || NVL(T.CONTENT, '--')
				                   ELSE
				                    '【预约前15分钟提醒】'|| CUST.CUSTNAME || '，拜访方式：' || DECODE(T.VISITTYPE, 1, '电话', 2, '见面', 3, '参会', 4, '短信', 5, '邮件') || '，预约日期：' || T.BOOKINGDT || ' ' || T.BOOKINGSTARTTIME || ' 至 ' || NVL(T.BOOKINGENDTIME, '--') || '，预约事项：' || NVL(T.CONTENT, '--')
				               END) AS MSGCONTENT,
				               T.BOOKINGDT AS MSGCONTENTDT
				          FROM CM_CONSBOOKINGCUST T
				          LEFT JOIN CM_CONSCUST CUST
				            ON CUST.CONSCUSTNO = T.CONSCUSTNO
		               AND CUST.CONSCUSTSTATUS = '0'
		             WHERE T.BOOKINGDT = TO_CHAR(SYSDATE, 'YYYYMMDD')
		               AND (T.BOOKINGSTARTTIME = TO_CHAR(SYSDATE, 'HH24:MI') OR T.BOOKINGSTARTTIME = TO_CHAR(SYSDATE + 15 / 24 / 60, 'HH24:MI'))
		        	)
		      	) T
        	WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '7' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryJylxqsMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
           TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5) AS MSGCONTENTDT,
           TO_CHAR(TO_DATE(T.MSGCONTENTDT,'YYYYMMDD') + 5,'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
          SELECT * FROM (  
             SELECT ROW_NUMBER() OVER(PARTITION BY tem.CONSCODE,tem.productcode ORDER BY tem.CONSCODE DESC) rn, tem.*         
	         FROM (SELECT T4.CONSCODE AS CONSCODE,
	                   '【到期通知】产品' || T1.productname || '，已于' || TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD') || '到期。相关款项预计在产品结束后10个工作日内回款至客户受益账户，具体请以合同为准。' MSGCONTENT,
	                   TO_CHAR(sysdate , 'YYYYMMDD') AS MSGCONTENTDT,T1.productcode
	              FROM CM_ZT_NOTRADETRANSFER T1
	              INNER JOIN AC_TX_HBONE T2
	                ON T1.TXACCTNO = T2.TX_ACCT_NO 
	              INNER JOIN CM_CONSCUST T3
	                ON T2.hbone_no = T3.hbone_no
	              INNER JOIN CM_CUSTCONSTANT T4
	                ON T3.conscustno = T4.custno 
	              INNER JOIN JJXX1 T5
	                ON T5.JJDM = T1.productcode    
	             WHERE T1.tatradedt = TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD')
	             and T5.HMCPX = '2' and T1.Mbusicode = '1142' ) tem  
	          ) WHERE rn = 1
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '9' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryJylxhlffMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
           TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5) AS MSGCONTENTDT,
           TO_CHAR(TO_DATE(T.MSGCONTENTDT,'YYYYMMDD') + 5,'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
          SELECT * FROM (  
             SELECT ROW_NUMBER() OVER(PARTITION BY tem.CONSCODE,tem.productcode ORDER BY tem.CONSCODE DESC) rn, tem.*         
	         FROM (
	              SELECT T4.CONSCODE AS CONSCODE,
	                   '【分红通知】产品' || T1.productname || '，已于' || TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD') || '分红。相关款项预计在该日期后10个工作日内回款至客户受益账户，具体请以合同为准。' MSGCONTENT,
	                   TO_CHAR(sysdate , 'YYYYMMDD') AS MSGCONTENTDT,T1.productcode
	              FROM CM_ZT_NOTRADETRANSFER T1
	              INNER JOIN AC_TX_HBONE T2
	                ON T1.TXACCTNO = T2.TX_ACCT_NO 
	              INNER JOIN CM_CONSCUST T3
	                ON T2.hbone_no = T3.hbone_no
	              INNER JOIN CM_CUSTCONSTANT T4
	                ON T3.conscustno = T4.custno   
	             WHERE T1.tatradedt = TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD')
	             and (T1.ackvol is null or  T1.ackvol = 0)     and T1.Mbusicode = '1143') tem  
	           ) WHERE rn = 1
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_PUSH_MSG_DAY D WHERE D.MSGTYPE = '10' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>
    
    <select id="queryFundTddqMsgList" resultMap="BaseResultMap">
		SELECT '' AS MSGCONTENT, '' AS CONSCODE FROM JJXX1 M WHERE 1 = 2
    </select>
    
    <update id="batchInsertCmPushMsgDay" parameterType="list" useGeneratedKeys="false">
        INSERT INTO CM_PUSH_MSG_DAY (ID, MSGTYPE, CONSCODE, MSGCONTENT, READFLAG, CREATOR, PUSHDT, CREDT, STIMESTAMP, MSGCONTENTDT, EXPIREDT, MSGCODE)
        SELECT A.* FROM (
        <foreach item="item" index="index" collection="list" separator="union all">
            ( SELECT
            	#{item.id, jdbcType=VARCHAR} as a0,
            	#{item.msgType, jdbcType=VARCHAR} as a1,
            	#{item.consCode, jdbcType=VARCHAR} as a2,
            	#{item.msgContent, jdbcType=VARCHAR } as a3,
            	#{item.readFlag, jdbcType=VARCHAR} as a4,
            	#{item.creator, jdbcType=VARCHAR} as a5,
            	TO_DATE(#{item.pushDt, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') as a6,
            	TO_DATE(#{item.creDt, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') as a7,
            	#{item.stimeStamp, jdbcType=TIMESTAMP} as a8,
            	#{item.msgContentDt, jdbcType=VARCHAR } as a9,
            	#{item.expireDt, jdbcType=VARCHAR } as a10,
            	#{item.msgCode, jdbcType=VARCHAR } as a11
              FROM DUAL
            )
        </foreach>
        )A
    </update>   
    
    <delete id="deleteCurCmPushMsgHis">
        DELETE FROM CM_PUSH_MSG_HIS H
 		 WHERE TO_CHAR(H.CREDT, 'YYYYMMDD') = TO_CHAR(SYSDATE, 'YYYYMMDD')
    </delete>
    
    <update id="batchInsertCmPushMsgHis" useGeneratedKeys="false">
        INSERT INTO CM_PUSH_MSG_HIS
		  (ID,
		   MSGTYPE,
		   CONSCODE,
		   MSGCONTENT,
		   READFLAG,
		   CREATOR,
		   PUSHDT,
		   CREDT,
		   MODDT,
		   MODIFIER,
		   STIMESTAMP,
		   MSGCONTENTDT,
		   EXPIREDT,
		   MSGCODE)
		  (SELECT ID,
		          MSGTYPE,
		          CONSCODE,
		          MSGCONTENT,
		          READFLAG,
		          CREATOR,
		          PUSHDT,
		          SYSDATE,
		          MODDT,
		          MODIFIER,
		          STIMESTAMP,
		          MSGCONTENTDT,
		          EXPIREDT,
		          MSGCODE
		     FROM CM_PUSH_MSG_DAY
		    WHERE TO_CHAR(CREDT, 'YYYYMMDD') != TO_CHAR(SYSDATE, 'YYYYMMDD')
   			  AND EXPIREDT <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
   		   )
    </update>

    <delete id="deleteCmPushMsgDay">
        DELETE FROM CM_PUSH_MSG_DAY
		 WHERE TO_CHAR(CREDT, 'YYYYMMDD') != TO_CHAR(SYSDATE, 'YYYYMMDD')
		   AND EXPIREDT <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
    </delete>
    
    <update id="updateCmPushMsgDay" useGeneratedKeys="false">
        UPDATE CM_PUSH_MSG_DAY T1
		   SET (T1.READFLAG, T1.PUSHDT, T1.CREDT, T1.MODDT, T1.MODIFIER, T1.STIMESTAMP) =
		       (SELECT T2.READFLAG, T2.PUSHDT, T2.PUSHDT, T2.MODDT, T2.MODIFIER, T2.STIMESTAMP
		          FROM CM_PUSH_MSG_HIS T2
		         WHERE TO_CHAR(T2.CREDT, 'YYYYMMDD') = TO_CHAR((SELECT MAX(T2.CREDT) FROM CM_PUSH_MSG_HIS T2), 'YYYYMMDD')
		           AND T1.MSGCODE = T2.MSGCODE
		           AND T1.CONSCODE = T2.CONSCODE
		           AND T1.MSGTYPE = T2.MSGTYPE
		           AND T2.READFLAG = '1'
		           AND ROWNUM = 1)
		 WHERE EXISTS (SELECT 1	FROM CM_PUSH_MSG_HIS T2
         				WHERE TO_CHAR(T2.CREDT, 'YYYYMMDD') = TO_CHAR((SELECT MAX(T2.CREDT) FROM CM_PUSH_MSG_HIS T2), 'YYYYMMDD')
           				  AND T1.MSGCODE = T2.MSGCODE
           				  AND T1.CONSCODE = T2.CONSCODE
           				  AND T1.MSGTYPE = T2.MSGTYPE
           				  AND T2.READFLAG = '1'
           			  )
    </update>

	<select id="queryCurAllMsgList" resultMap="BaseResultMap">
		SELECT * FROM CM_PUSH_MSG_DAY ORDER BY ID DESC
    </select>
</mapper>
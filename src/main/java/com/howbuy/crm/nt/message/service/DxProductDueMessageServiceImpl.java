package com.howbuy.crm.nt.message.service;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.message.buss.MessageCommonBuss;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("dxProductDueMessageServiceImpl")
public class DxProductDueMessageServiceImpl implements DxProductDueMessageService {
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private MessageCommonBuss messageCommonBuss;
	
	private Logger logger= LoggerFactory.getLogger(DxProductDueMessageServiceImpl.class);

	@Override
	public String dealDxProductDueMessage(String arg) {
		Map<String,Object> map = new HashMap<String,Object>(1);
		//查询符合条件的数据
		List<Map<String,String>> list = cmMessageDao.listDxProductDueMessage(map);
		if(list != null && list.size()>0){
			logger.info(JSON.toJSONString(list));
			for(Map<String,String> obj : list){
				String hboneno = StringUtil.replaceNull(obj.get("HBONE_NO"));
				String custno = StringUtil.replaceNull(obj.get("CUSTNO"));
				String custname = StringUtil.replaceNull(obj.get("CUSTNAME"));
				String jjjc = StringUtil.replaceNull(obj.get("JJJC"));
				String tradedt = StringUtil.replaceNull(obj.get("TRADEDT"));
				String pcode = StringUtil.replaceNull(obj.get("FUNDCODE"));
				String hkfs = "";
				//一账通不为空
				if(StringUtil.isNotNullStr(hboneno)){
					//获取回款方式
					hkfs = messageCommonBuss.getHkfs(hboneno);
					logger.info("一账通："+hboneno+"的回款方式："+hkfs);
					Map<String, String> parmsg = new HashMap<String,String>(4); 
					parmsg.put("custName", custname);
					parmsg.put("tradedt", tradedt);
					parmsg.put("fund_name", jjjc);
					parmsg.put("hkfs", hkfs);
					String msg = JSON.toJSONString(parmsg);
					//发送短信
					int result = messageCommonBuss.sendMessageByHboneNo(CrmNtConstant.MSG_BID_DXDQ, msg, hboneno);
					//发送成功，记录发送成功表
					if(result==0){
						messageCommonBuss.insertMessageDailly(CrmNtConstant.MSG_TYPE_ZXDQ,custno, msg, custno+pcode);
					}
				}
				
			}
		}
		return null;
	}

	

}

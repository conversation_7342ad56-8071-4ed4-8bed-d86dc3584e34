package com.howbuy.crm.nt.message.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.constant.MsgBusinessIdConstants;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import com.howbuy.crm.consultant.service.ConsultantInfoService;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustDO;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import crm.howbuy.base.constants.Constants;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 划转客户消息提醒 实现类
 * @Date 2024/3/13 14:36
 */
@Slf4j
@Service("transferCustMessageServiceImpl")
public class TransferCustMessageServiceImpl implements TransferCustMessageService {

    @Autowired
    private ConsultantInfoService consultantInfoService;
    @Autowired
    private ConscustMapper conscustMapper;
    @Autowired
    private CmConsultantMapper cmConsultantMapper;
    @Autowired
    private HbOrganizationService hbOrganizationService;
    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Override
    public String dealTransferReplyMessage(String arg) {
        Map<String, Object> param = new HashMap<>();
        // state 1:待审核
        param.put("state", "1");
        // 查询 HBC 中心下的所有带划转客户数据
        param.put("orgCodes", Lists.newArrayList(CenterOrgEnum.OVERSEAS_BRANCH.getCode()));
        List<CmWaitTransferCustDO> waitTransferCustList = this.getCmWaitTransferCustListAll(param);
        if (CollectionUtils.isEmpty(waitTransferCustList)) {
            log.info("TransferCustMessageServiceImpl dealTransferReplyMessage list size is null !");
            return null;
        }

        // 推送数据数量 根据投顾客户号去重
        long count = waitTransferCustList.stream()
                .filter(Objects::nonNull)
                .map(CmWaitTransferCustDO::getConsCustNo)
                .distinct()
                .count();

        // 查询所有 销售总部-系统运营 角色下的投顾信息Map<投顾编号,投顾信息>
        Map<String, ConsultantSimpleInfoDto> consultantMap = this.getStringConsultantSimpleInfoDtoMap();
        consultantMap = getCenterConsultantMap(CenterOrgEnum.OVERSEAS_BRANCH, consultantMap);
        if (consultantMap == null) {
            return null;
        }

        // 待发送消息投顾编号列表
        List<String> consCodeList = consultantMap.entrySet().stream()
                .filter(Objects::nonNull)
                .map(Map.Entry::getValue)
                .map(ConsultantSimpleInfoDto::getConsCode)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("number", String.valueOf(count));
        BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.BUSI_ID_HZPF, consCodeList, paramMap);
        log.info("TransferCustMessageServiceImpl dealTransferReplyMessage pushMsgByConsCodeList params=[{}], baseResponse:{}", new Object[]{MsgBusinessIdConstants.BUSI_ID_HZPF, consCodeList, paramMap}, baseResponse);

        return null;
    }

    @Override
    public String dealTransferTwiceReplyMessage(String arg) {
        Map<String, Object> param = new HashMap<>();
        // state 1:待复核
        param.put("state", "2");
        // 查询 IC、HBC 中心下的所有带划转客户数据
        List<String> centerCodeList = Lists.newArrayList(CenterOrgEnum.IC.getCode(), CenterOrgEnum.OVERSEAS_BRANCH.getCode());
        param.put("orgCodes", centerCodeList);
        List<CmWaitTransferCustDO> waitTransferCustList = this.getCmWaitTransferCustListAll(param);
        if (CollectionUtils.isEmpty(waitTransferCustList)) {
            log.info("TransferCustMessageServiceImpl dealTransferTwiceReplyMessage list size is null !");
            return null;
        }

        // 待划转客户按中心分组
        Map<String, List<CmWaitTransferCustDO>> centerDataMap = waitTransferCustList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(CmWaitTransferCustDO::getCenterOrgCode));

        // 查询所有 销售总部-系统运营 角色下的投顾信息Map<投顾编号,投顾信息>
        Map<String, ConsultantSimpleInfoDto> consultantMap = this.getStringConsultantSimpleInfoDtoMap();
        if (consultantMap == null) {
            return null;
        }


        for (String centerCode : centerCodeList) {
            CenterOrgEnum centerOrgEnum = CenterOrgEnum.getEnum(centerCode);
            if (null == centerOrgEnum) {
                continue;
            }

            // 查询对应中心下的所有投顾组织架构信息map
            Map<String, OrgLayerInfo> orgLayerMap = this.getOrgLayerInfoMap(centerOrgEnum);
            if (MapUtils.isEmpty(orgLayerMap)) {
                continue;
            }

            // 获取对应中心下 销售总部-系统运营 角色下的投顾信息Map<投顾编号,投顾信息>
            Map<String, ConsultantSimpleInfoDto> centerConsultantMap = this.getCenterConsultantMap(centerOrgEnum, consultantMap);
            if (MapUtils.isEmpty(centerConsultantMap)) {
                continue;
            }

            // 获取投顾所属区域名称，无区域时取对应中心名称
            String districtNameStr = centerConsultantMap.entrySet().stream()
                    .filter(Objects::nonNull)
                    .map(Map.Entry::getValue)
                    .filter(Objects::nonNull)
                    .map(v -> orgLayerMap.get(v.getOutletCode()))
                    .filter(Objects::nonNull)
                    .map(v -> StringUtil.isBlank(v.getDistrictName()) ? v.getCenterOrgName() : v.getDistrictName())
                    .distinct()
                    .collect(Collectors.joining("、"));

            // 按中心获取待发送消息投顾编号列表
            List<String> consCodeList = centerConsultantMap.entrySet().stream()
                    .filter(Objects::nonNull)
                    .map(Map.Entry::getValue)
                    .map(ConsultantSimpleInfoDto::getConsCode)
                    .distinct()
                    .collect(Collectors.toList());

            // 按中心获取待发送客户数量
            List<CmWaitTransferCustDO> cmWaitTransferCustDOS = centerDataMap.get(centerCode);
            long count = cmWaitTransferCustDOS.stream()
                    .filter(Objects::nonNull)
                    .map(CmWaitTransferCustDO::getConsCustNo)
                    .distinct()
                    .count();

            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("number", String.valueOf(count));
            paramMap.put("district", districtNameStr);
            BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.BUSI_ID_ECPF, consCodeList, paramMap);
            log.info("TransferCustMessageServiceImpl dealTransferReplyMessage pushMsgByConsCodeList centerOrgCode={}, params=[{}], baseResponse:{}", centerCode, new Object[]{MsgBusinessIdConstants.BUSI_ID_ECPF, consCodeList, paramMap}, baseResponse);
        }

        return null;
    }

    /**
     * 根据中心枚举查询对应中心下的所有拥有 销售总部-系统运营 角色的投顾组织架构信息map
     *
     * @param centerOrgEnum 中心枚举
     * @return Map
     */

    private Map<String, OrgLayerInfo> getOrgLayerInfoMap(CenterOrgEnum centerOrgEnum) {
        if (null == centerOrgEnum) {
            return null;
        }

        Map<String, ConsultantSimpleInfoDto> consultantMap = getStringConsultantSimpleInfoDtoMap();
        if (consultantMap == null) {
            return null;
        }

        List<String> orgCodes = consultantMap.entrySet().stream()
                .filter(Objects::nonNull)
                .map(Map.Entry::getValue)
                .filter(Objects::nonNull)
                .filter(v -> centerOrgEnum.getCode().equals(v.getCenterOrgCode()))
                .map(ConsultantSimpleInfoDto::getOutletCode)
                .distinct()
                .collect(Collectors.toList());

        return hbOrganizationService.getOrgLayerInfoByOrgCodeList(orgCodes);
    }


    /**
     * 查询所有 销售总部-系统运营 角色下的投顾信息
     *
     * @return Map<投顾编号, 投顾信息></>
     */
    private Map<String, ConsultantSimpleInfoDto> getStringConsultantSimpleInfoDtoMap() {
        // 查询所有 销售总部-系统运营 角色下的投顾编码
        List<String> consCodeList = cmConsultantMapper.getConsCodeListByRoleList(Lists.newArrayList(Constants.ROLE_IC_OPDB));
        if (CollectionUtils.isEmpty(consCodeList)) {
            return null;
        }

        Map<String, ConsultantSimpleInfoDto> consultantMap = consultantInfoService.getConsultantMapByCodeList(consCodeList);
        if (MapUtils.isEmpty(consultantMap)) {
            return null;
        }

        return consultantMap;
    }

    /**
     * 根据中心枚举筛选 投顾信息
     *
     * @return Map<投顾编号, 投顾信息></>
     */
    private Map<String, ConsultantSimpleInfoDto> getCenterConsultantMap(CenterOrgEnum centerOrgEnum, Map<String, ConsultantSimpleInfoDto> consultantMap) {
        if (null == centerOrgEnum || MapUtils.isEmpty(consultantMap)) {
            return null;
        }

        Map<String, ConsultantSimpleInfoDto> map = new HashMap<>();
        consultantMap.forEach((k, v) -> {
            if (v != null && centerOrgEnum.getCode().equals(v.getCenterOrgCode())) {
                map.put(k, v);
            }
        });
        return map;
    }

    /**
     * 根据参数分页递归查询所有符合条件数据
     *
     * @param param DB请求参数
     * @return List
     */
    private List<CmWaitTransferCustDO> getCmWaitTransferCustListAll(Map<String, Object> param) {
        if (MapUtils.isEmpty(param)) {
            return null;
        }

        // 初始分页参数
        int page = 1;
        int size = 100;
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(new HashMap<>(), page, size);
        return recursivePageQuery(pageBean, param, (p, map) -> conscustMapper.listCmWaitTransferCustListByPage(map, p));
    }

    /**
     * 递归分页查询所有符合条件数据集
     *
     * @param pageBean 分页对象
     * @param param    查询条件
     * @param biFnc    function
     * @return <T>      返回结果
     */
    private <T> List<T> recursivePageQuery(CommPageBean pageBean, Map<String, Object> param, BiFunction<CommPageBean, Map<String, Object>, List<T>> biFnc) {
        if (MapUtils.isEmpty(param)) {
            return null;
        }

        List<T> list = biFnc.apply(pageBean, param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        CommPageBean newPageBean = CommPageBean.getPageBeanByParam(new HashMap<>(), pageBean.getCurPage() + 1, pageBean.getPageSize());
        List<T> nextList = this.recursivePageQuery(newPageBean, param, biFnc);
        if (CollectionUtils.isNotEmpty(nextList)) {
            list.addAll(nextList);
        }
        return list;
    }
}

package com.howbuy.crm.nt.message.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.constant.MsgBusinessIdConstants;
import com.howbuy.crm.nt.prebookinfo.dao.CmPrebookinfoDao;
import com.howbuy.crm.nt.prebookinfo.dto.CmPrebookLegalDocDto;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 划转客户消息提醒 实现类
 * @Date 2024/4/2 17:27
 */
@Slf4j
@Service("preBookMessageServiceImpl")
public class PreBookMessageServiceImpl implements PreBookMessageService {

    @Autowired
    private CmPushMsgService cmPushMsgService;
    @Autowired
    private CmPrebookinfoDao cmPrebookinfoDao;
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Override
    public String dealLegalDocMessage(String arg) {
        List<CmPrebookLegalDocDto> cmPrebookLegalDocDtos = cmPrebookinfoDao.selectCmPrebookLegaldocList();
        if (CollectionUtils.isEmpty(cmPrebookLegalDocDtos)) {
            log.info("PreBookMessageServiceImpl dealLegalDocMessage cmPrebookLegalDocDtos is empty!");
            return null;
        }

        Map<String, List<CmPrebookLegalDocDto>> map = cmPrebookLegalDocDtos.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(CmPrebookLegalDocDto::getIsdxflag));

        // 代销list
        List<CmPrebookLegalDocDto> dxLegalDocDtos = map.get("1");
        if (CollectionUtils.isNotEmpty(dxLegalDocDtos)) {
            // 上报申请标识：submitAppFlag=2 上报完成
            dxLegalDocDtos.stream()
                    .filter(v -> "2".equals(v.getSubmitAppFlag()))
                    .forEach(this::sendLegalMessage);
        }

        // 直销list
        List<CmPrebookLegalDocDto> zxLegalDocDtos = map.get("2");
        Date now = new Date();
        if (CollectionUtils.isNotEmpty(zxLegalDocDtos)) {
            zxLegalDocDtos.stream()
                    .filter(v -> StringUtils.isNotBlank(v.getExpecttradedt()))
                    .filter(v -> {
                        Date expecttradedt = DateUtil.string2Date(v.getExpecttradedt(), DateUtil.SHORT_DATE_PATTERN);
                        return now.after(expecttradedt);
                    })
                    .forEach(this::sendLegalMessage);
        }

        return null;
    }

    /**
     * 消息推送通知投顾
     * @param dto 预约信息
     */
    private void sendLegalMessage(CmPrebookLegalDocDto dto) {
        if (null == dto || StringUtil.isEmpty(dto.getConscustno())) {
            return;
        }

        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("custname", dto.getConscustname());
        paramMap.put("custno", dto.getConscustno());
        paramMap.put("id", dto.getId());

        QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
        queryConscustInfoRequest.setConscustno(dto.getConscustno());
        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
        if (null == queryConscustInfoResponse || null == queryConscustInfoResponse.getConscustinfo()) {
            log.info("PreBookMessageServiceImpl sendLegalMessage queryConscustInfoResponse is null! param={}", JSON.toJSONString(dto));
        }

        ConscustInfoDomain conscustinfo = queryConscustInfoResponse.getConscustinfo();
        List<String> conscodeList = Lists.newArrayList(conscustinfo.getConscode());

        if (DisChannelCodeEnum.HOWBUY.getCode().equals(dto.getPreDisCode())) {
            paramMap.put("service", "好买-售前留痕材料");
            BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.HB_BUSI_ID_HGYJ, conscodeList, paramMap);
            log.info("PreBookMessageServiceImpl dealLegalDocMessage pushMsgByConsCodeList params=[{}], baseResponse:{}",
                    new Object[]{MsgBusinessIdConstants.HB_BUSI_ID_HGYJ, conscodeList, paramMap}, baseResponse);

        } else if (DisChannelCodeEnum.HZ.getCode().equals(dto.getPreDisCode())) {
            paramMap.put("service", "好臻-售前留痕材料");
            BaseResponse baseResponse = cmPushMsgService.pushMsgByConsCodeList(MsgBusinessIdConstants.HZ_BUSI_ID_HGYJ, conscodeList, paramMap);
            log.info("PreBookMessageServiceImpl dealLegalDocMessage pushMsgByConsCodeList params=[{}], baseResponse:{}",
                    new Object[]{MsgBusinessIdConstants.HZ_BUSI_ID_HGYJ, conscodeList, paramMap}, baseResponse);

        }

    }

}

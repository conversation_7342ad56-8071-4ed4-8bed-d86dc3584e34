<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmMessageLogDao">


	<update id="deleteCmMessageDaily">
		truncate table CM_MESSAGE_DAILY
	</update>

	<insert id="insertIntoMessageLog">
		INSERT INTO CM_MESSAGE_LOG
			(ID, TYPE, CONSCUSTNO, MOBILE, MODEID, MSG, SENDFLAG, DES, CREDDT)
		SELECT T.ID,
			   T.TYPE,
			   T.CONSCUSTNO,
			   T.MOBILE,
			   T.MODEID,
			   T.MSG,
			   T.SENDFLAG,
			   T.DES,
			   T.CREDDT
		FROM CM_MESSAGE_DAILY T
	</insert>



</mapper>
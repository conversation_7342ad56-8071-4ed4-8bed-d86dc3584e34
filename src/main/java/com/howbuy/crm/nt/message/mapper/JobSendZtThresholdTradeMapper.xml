<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.message.dao.JobSendZtThresholdTradeMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade">
    <!--@mbg.generated-->
    <!--@Table JOB_SEND_ZT_THRESHOLD_TRADE-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="TRADE_DATE" jdbcType="VARCHAR" property="tradeDate" />
    <result column="ZT_RESPONSE_STATUS" jdbcType="VARCHAR" property="ztResponseStatus" />
    <result column="JOB_TYPE" jdbcType="VARCHAR" property="jobType" />
    <result column="PUSH_FAIL_COUNT" jdbcType="INTEGER" property="pushFailCount" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, TRADE_DATE, ZT_RESPONSE_STATUS, JOB_TYPE,PUSH_FAIL_COUNT, CREATE_TIME, MODIFY_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from JOB_SEND_ZT_THRESHOLD_TRADE
    where ID = #{id,jdbcType=DECIMAL}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from JOB_SEND_ZT_THRESHOLD_TRADE
    where ID = #{id,jdbcType=DECIMAL}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade">
      <!--@mbg.generated-->
      <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
          select SEQ_SEND_ZT_THRESHOLD_TRADE.nextval
          from dual
      </selectKey>
      insert into JOB_SEND_ZT_THRESHOLD_TRADE (ID, TRADE_DATE, ZT_RESPONSE_STATUS, JOB_TYPE,PUSH_FAIL_COUNT,
                                               CREATE_TIME, MODIFY_TIME)
      values (#{id,jdbcType=DECIMAL}, #{tradeDate,jdbcType=VARCHAR}, #{ztResponseStatus,jdbcType=VARCHAR},
              #{jobType,jdbcType=VARCHAR}, #{pushFailCount,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade">
    <!--@mbg.generated-->
    <selectKey keyProperty="id" order="BEFORE" resultType="java.lang.Long">
      select SEQ_SEND_ZT_THRESHOLD_TRADE.nextval from dual
    </selectKey>
    insert into JOB_SEND_ZT_THRESHOLD_TRADE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      ID,
      <if test="tradeDate != null">
        TRADE_DATE,
      </if>
      <if test="ztResponseStatus != null">
        ZT_RESPONSE_STATUS,
      </if>
      <if test="jobType != null">
        JOB_TYPE,
      </if>
      <if test="pushFailCount != null">
        PUSH_FAIL_COUNT,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id,jdbcType=DECIMAL},
      <if test="tradeDate != null">
        #{tradeDate,jdbcType=VARCHAR},
      </if>
      <if test="ztResponseStatus != null">
        #{ztResponseStatus,jdbcType=VARCHAR},
      </if>
      <if test="jobType != null">
        #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="pushFailCount != null">
        #{pushFailCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade">
    <!--@mbg.generated-->
    update JOB_SEND_ZT_THRESHOLD_TRADE
    <set>
      <if test="tradeDate != null">
        TRADE_DATE = #{tradeDate,jdbcType=VARCHAR},
      </if>
      <if test="ztResponseStatus != null">
        ZT_RESPONSE_STATUS = #{ztResponseStatus,jdbcType=VARCHAR},
      </if>
      <if test="jobType != null">
        JOB_TYPE = #{jobType,jdbcType=VARCHAR},
      </if>
      <if test="pushFailCount != null">
        PUSH_FAIL_COUNT = #{pushFailCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=DECIMAL}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.message.domain.JobSendZtThresholdTrade">
    <!--@mbg.generated-->
    update JOB_SEND_ZT_THRESHOLD_TRADE
    set TRADE_DATE = #{tradeDate,jdbcType=VARCHAR},
      ZT_RESPONSE_STATUS = #{ztResponseStatus,jdbcType=VARCHAR},
    JOB_TYPE = #{jobType,jdbcType=VARCHAR},
    PUSH_FAIL_COUNT = #{pushFailCount,jdbcType=INTEGER},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=DECIMAL}
  </update>

  <select id="selectByTradeDateAndJobType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from JOB_SEND_ZT_THRESHOLD_TRADE
    where TRADE_DATE = #{tradeDate,jdbcType=VARCHAR} AND JOB_TYPE = #{jobType,jdbcType=VARCHAR}
  </select>
</mapper>
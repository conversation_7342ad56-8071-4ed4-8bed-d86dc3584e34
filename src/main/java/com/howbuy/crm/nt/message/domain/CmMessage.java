package com.howbuy.crm.nt.message.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CmMessage implements Serializable {
	private static final long serialVersionUID = 1L;
	private BigDecimal id;
	private String type;
	private String conscustno;
	private String mobile;
	private String modeid;
	private String msg;
	private String sendflag;
	private String des;
	private Date creddt;
	public BigDecimal getId() {
		return id;
	}
	public void setId(BigDecimal id) {
		this.id = id;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getConscustno() {
		return conscustno;
	}
	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getModeid() {
		return modeid;
	}
	public void setModeid(String modeid) {
		this.modeid = modeid;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public String getSendflag() {
		return sendflag;
	}
	public void setSendflag(String sendflag) {
		this.sendflag = sendflag;
	}
	public String getDes() {
		return des;
	}
	public void setDes(String des) {
		this.des = des;
	}
	public Date getCreddt() {
		return creddt;
	}
	public void setCreddt(Date creddt) {
		this.creddt = creddt;
	}
	public Date getUpddt() {
		return upddt;
	}
	public void setUpddt(Date upddt) {
		this.upddt = upddt;
	}
	private Date upddt;
}

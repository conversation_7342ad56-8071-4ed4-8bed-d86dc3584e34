package com.howbuy.crm.nt.message.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类CmJjjzMsgLatest
 */
public class CmJjjzMsgLatest implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	private String jjdm;

	private String jsrq;

	private Double jjjz;

	private Date syncdt;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getJjdm() {
		return jjdm;
	}

	public void setJjdm(String jjdm) {
		this.jjdm = jjdm;
	}

	public String getJsrq() {
		return jsrq;
	}

	public void setJsrq(String jsrq) {
		this.jsrq = jsrq;
	}

	public Double getJjjz() {
		return jjjz;
	}

	public void setJjjz(Double jjjz) {
		this.jjjz = jjjz;
	}

	public Date getSyncdt() {
		return syncdt;
	}

	public void setSyncdt(Date syncdt) {
		this.syncdt = syncdt;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
}
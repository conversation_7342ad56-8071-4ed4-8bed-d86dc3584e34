<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmCompanyReportDao">

    <update id="batchInsertCmCompanyReport" parameterType="list" useGeneratedKeys="false">
		INSERT INTO CM_company_report
		(ID,
		COMPANYID,
		FUNDCODE,
		REPORTDATE,
		TITLE,
		FILENAME,
		PTYPE,
		SENDSTATUS,
		REPORTTYPE,
		STATUS,
		QUARTER,
		REDIRECTURL,
		<PERSON><PERSON><PERSON>TATUS,
		REMARK,
		SENDSMMSG,
		CREATER,
		CREATETIME,
		EDITOR,
		UPDATETIME,
		SYNCDATE)
		SELECT A.* FROM (
        <foreach item="item" index="index" collection="list" separator="union all">
            ( SELECT
            	#{item.id, jdbcType=NUMERIC} as ID,
            	#{item.companyId, jdbcType=VARCHAR} as COMPANYID,
            	#{item.fundCode, jdbcType=VARCHAR} as FUNDCODE,
            	#{item.reportDate, jdbcType=VARCHAR } as REPORTDATE,
            	#{item.title, jdbcType=VARCHAR} as TITLE,
            	#{item.fileName, jdbcType=VARCHAR} as FILENAME,
            	#{item.pType, jdbcType=VARCHAR} as PTYPE,
            	#{item.sendstatus, jdbcType=VARCHAR} as SENDSTATUS,
            	#{item.reportType, jdbcType=VARCHAR} as REPORTTYPE,
				#{item.status, jdbcType=INTEGER} as STATUS,
				#{item.quarter, jdbcType=VARCHAR} as QUARTER,
				#{item.redirectUrl, jdbcType=VARCHAR} as REDIRECTURL,
				#{item.checkStatus, jdbcType=VARCHAR} as CHECKSTATUS,
				#{item.remark, jdbcType=VARCHAR} as REMARK,
				#{item.sendSmMsg, jdbcType=VARCHAR} as SENDSMMSG,
				#{item.creater, jdbcType=VARCHAR} as CREATER,
				#{item.createTime, jdbcType=TIMESTAMP} as CREATETIME,
				#{item.editor, jdbcType=VARCHAR} as EDITOR,
				#{item.updateTime, jdbcType=TIMESTAMP} as UPDATETIME,
				#{item.syncDate, jdbcType=TIMESTAMP} as SYNCDATE
			FROM DUAL
            )
        </foreach>
        )A
    </update>   

</mapper>
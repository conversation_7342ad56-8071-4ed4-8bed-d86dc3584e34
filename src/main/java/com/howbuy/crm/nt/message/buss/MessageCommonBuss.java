package com.howbuy.crm.nt.message.buss;


import java.math.BigDecimal;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.highcustlabel.dao.CmHighCustinfoDao;
import com.howbuy.crm.nt.message.dao.CmMessageDao;
import com.howbuy.crm.nt.message.domain.CmMessage;
import com.howbuy.crm.util.CrmNtConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoFacade;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoRequest;
import com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoResponse;
import com.howbuy.cc.message.send.auto.request.SendMsgByHboneNoRequest;
import com.howbuy.cc.message.send.auto.response.BaseResponse;
import com.howbuy.cc.message.send.auto.service.AutoSendMessageService;

@Component
public class MessageCommonBuss {
	private Logger log= LoggerFactory.getLogger(MessageCommonBuss.class);

	@Autowired
	private CmHighCustinfoDao cmHighCustinfoDao;
	
	@Autowired
	private QueryCustInfoFacade queryCustInfoFacade;
	
	@Autowired
	private CommonDao commonDao;
	
	@Autowired
	private CmMessageDao cmMessageDao;
	
	@Autowired
	private AutoSendMessageService autoSendMessageService;

	/**
	 * 发送短信和微信接口
	 * @param businessId
	 * @param msg
	 * @param hboneno
	 * @return
	 */
	public int sendMessageByHboneNo(String businessId,String msg,String hboneno) {
		SendMsgByHboneNoRequest req = new SendMsgByHboneNoRequest();
		req.setBusinessId(businessId);
		req.setHboneNo(hboneno);
		String msgstr = "{\"params\":"+msg+",\"wechat_url\":\"\"}";
		req.setTemplateVar(msgstr);
		log.info("发送交易消息通知：{}",JSON.toJSONString(req));
		BaseResponse res = autoSendMessageService.sendMsgByHboneNo(req);
		log.info("发送交易消息通知，返回结果信息：{}",JSON.toJSONString(res));
		return res.getCode();
	}
	
	/**
	 * 获取一账通客户回款方式
	 * @param hboneno
	 * @return
	 */
	public String getHkfs(String hboneno){
		String collectprotocolmethod = "";
		try {
			//查回款协议
	        QueryCustInfoRequest custInfoReq = new QueryCustInfoRequest();
	        custInfoReq.setHboneNo(hboneno);
	        custInfoReq.setDisCode("HB000A001");
	        custInfoReq.setTxCode("520043");
	        log.info("QueryCustInfoRequest:"+JSON.toJSONString(custInfoReq));
	        QueryCustInfoResponse custInfoResponse = queryCustInfoFacade.execute(custInfoReq);
	        log.info("QueryCustInfoResponse:"+JSON.toJSONString(custInfoResponse));
	        if (custInfoResponse != null) {
	            if (custInfoResponse.getCustInfoBean() != null) {
	                collectprotocolmethod = custInfoResponse.getCustInfoBean().getCollectProtocolMethod();
	                log.info("获取回款方式collectprotocolmethod:" + collectprotocolmethod);
	            }
	        }
	    }catch(Exception e) {
	    	log.error("查询接口queryCustInfoFacade错误："+e.getMessage());
	    	log.error(e.getMessage(),e);
	    }
		if("1".equals(collectprotocolmethod) || "2".equals(collectprotocolmethod)){
			collectprotocolmethod = "回款至银行卡";
		}
		if("3".equals(collectprotocolmethod) || "4".equals(collectprotocolmethod)){
			collectprotocolmethod = "回款至储蓄罐";
		}
		return collectprotocolmethod;
	}
	
	
	public void insertMessageDailly(String type,String custno,String msg,String des){
		String id = commonDao.getSeqValue("SEQ_CM_MESSAGE");
		CmMessage mess = new CmMessage();
		mess.setId(new BigDecimal(id));
		mess.setConscustno(custno);
		mess.setType(type);
		mess.setMsg(msg);
		mess.setSendflag(CrmNtConstant.MSG_SEND_SUCCESS);
		mess.setModeid(CrmNtConstant.MSG_BID_DXGSFH);
		mess.setDes(des);
		cmMessageDao.insertCmMessage(mess);
		log.info("插入 CM_MESSAGE_DAILY 表数据:{} ",JSON.toJSONString(mess));
	}
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.message.dao.CmJjjzMsgRecDao">
	  <select id="getCmJjjzMsgRec" parameterType="Map" resultType="com.howbuy.crm.nt.message.domain.CmJjjzMsgRec" useCache="false">
	    SELECT * FROM CM_JJJZ_MSG_REC
 		 WHERE ID = (SELECT MAX(ID) FROM CM_JJJZ_MSG_REC)
   	  </select>


	  <select id="listCmJjjzMsgRec" parameterType="Map" resultType="com.howbuy.crm.nt.message.domain.CmJjjzMsgRec" useCache="false">
	    SELECT * FROM CM_JJJ<PERSON>_MSG_REC
	     where 1=1 
	      <if test="id != null"> AND id = #{id} </if>
          <if test="jjdm != null"> AND jjdm = #{jjdm} </if>
          <if test="jjjc != null"> AND jjjc = #{jjjc} </if>             
          <if test="jsrq != null"> AND jsrq = #{jsrq} </if>             
          <if test="jjjz != null"> AND jjjz = #{jjjz} </if>             
          <if test="lastJzrq != null"> AND lastJzrq = #{lastJzrq} </if>             
          <if test="hbdr != null"> AND hbdr = #{hbdr} </if>             
          <if test="jjjzModdt != null"> AND jjjzModdt = #{jjjzModdt} </if>             
          <if test="moddt != null"> AND moddt = #{moddt} </if>
          AND TO_CHAR(SYNCDT, 'YYYYMMDD') = TO_CHAR(SYSDATE, 'YYYYMMDD')
   	  </select>


	  <insert id="insertCmJjjzMsgRec" parameterType="com.howbuy.crm.nt.message.domain.CmJjjzMsgRec" >
	    INSERT INTO CM_JJJZ_MSG_REC (
	     <trim suffix="" suffixOverrides=",">	
      	      id,
      	      <if test="jjdm != null"> jjdm, </if> 
      	      <if test="jjjc != null"> jjjc, </if> 
      	      <if test="jsrq != null"> jsrq, </if> 
      	      <if test="jjjz != null"> jjjz, </if> 
      	      <if test="lastJzrq != null"> lastJzrq, </if> 
      	      <if test="hbdr != null"> hbdr, </if> 
      	      <if test="jjjzModdt != null"> jjjzModdt, </if> 
      	      <if test="moddt != null"> moddt, </if> 
         </trim>
         ) values (
         <trim suffix="" suffixOverrides=",">
        	  SEQ_CM_JJJZ_MSG_ID.NEXTVAL,
     	      <if test="jjdm != null"> #{jjdm, jdbcType=VARCHAR}, </if> 
     	      <if test="jjjc != null"> #{jjjc, jdbcType=VARCHAR}, </if> 
     	      <if test="jsrq != null"> #{jsrq, jdbcType=VARCHAR}, </if> 
     	      <if test="jjjz != null"> #{jjjz, jdbcType=DOUBLE}, </if> 
     	      <if test="lastJzrq != null"> #{lastJzrq, jdbcType=VARCHAR}, </if> 
     	      <if test="hbdr != null"> #{hbdr, jdbcType=DOUBLE}, </if> 
     	      <if test="jjjzModdt != null"> #{jjjzModdt}, </if> 
     	      <if test="moddt != null"> #{moddt, jdbcType=VARCHAR}, </if> 
	     </trim>	
         )      
	  </insert>


   	  <update id="batchInsertCmJjjzMsgRec" parameterType="list" useGeneratedKeys="false">
        INSERT INTO CM_JJJZ_MSG_REC (ID, JJDM, JJJC, JSRQ, JJJZ, LASTJZRQ, HBDR, JJJZMODDT, MODDT)
        SELECT SEQ_CM_JJJZ_MSG_ID.NEXTVAL, A.* FROM (
        <foreach item="item" index="index" collection="list" separator="union all">
            ( SELECT
            	 #{item.jjdm, jdbcType=VARCHAR} as a1,
            	 #{item.jjjc, jdbcType=VARCHAR} as a2,
            	 #{item.jsrq, jdbcType=VARCHAR } as a3,
            	 #{item.jjjz, jdbcType=DOUBLE} as a4,
            	 #{item.lastJzrq, jdbcType=VARCHAR} as a5,
            	 #{item.hbdr, jdbcType=DOUBLE} as a6,
            	 #{item.jjjzModdt} as a7,
            	 #{item.moddt, jdbcType=VARCHAR} as a8
               FROM DUAL
            )
        </foreach>
        ) A
    </update>


</mapper>




/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.realconsultant.buss;

import com.howbuy.crm.nt.realconsultant.response.CmRealConsultantResponse;
import com.howbuy.crm.nt.realconsultant.service.CmRealConsultantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (CmWechatCustInfoBuss)
 * <AUTHOR>
 * @date 2023/3/17 9:10
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmRealConsultantBuss {

    @Autowired
    private CmRealConsultantService cmRealConsultantService;


    public CmRealConsultantResponse getCmRealConsultant(String hboneno) {
        return cmRealConsultantService.getCmRealConsultant(hboneno);
    }


}
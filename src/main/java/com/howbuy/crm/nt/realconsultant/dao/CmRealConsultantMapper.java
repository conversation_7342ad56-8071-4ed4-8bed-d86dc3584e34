package com.howbuy.crm.nt.realconsultant.dao;

import com.howbuy.crm.nt.realconsultant.dto.CmRealConsultant;
import com.howbuy.crm.nt.realconsultant.dto.HbOrganization;
import org.mybatis.spring.annotation.MapperScan;

import java.util.Map;

@MapperScan
public interface CmRealConsultantMapper {

    /**
     * 获取单条记录
     * @param param
     * @return
     */
    CmRealConsultant getCmRealConsultant(Map<String, Object> param);

    /**
     * 获取组织架构
     * @param param
     * @return
     */
    HbOrganization getHbOrganization(Map<String, String> param);

    Map<String, Object> selectoutletcode(String hboneNo);

    Map<String, Object> selectoutletnocode(String hboneNo);

}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.realconsultant.service;


import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.nt.realconsultant.dao.CmRealConsultantMapper;
import com.howbuy.crm.nt.realconsultant.dto.CmRealConsultant;
import com.howbuy.crm.nt.realconsultant.dto.HbOrganization;
import com.howbuy.crm.nt.realconsultant.response.CmRealConsultantResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/4/19 11:32
 * @since JDK 1.8
 */
@Service("cmRealConsultantService")
public class CmRealConsultantServiceImpl implements CmRealConsultantService {
    @Autowired
    private CmRealConsultantMapper cmRealConsultantMapper;

    @Override
    public CmRealConsultantResponse getCmRealConsultant(String hboneno) {
        CmRealConsultantResponse response = new CmRealConsultantResponse();
        response.paramError("查询数据为空");
        Map<String, Object> params = new HashMap<>();
        params.put("hboneno", hboneno);

        // 查询是否维护实际投关系
        CmRealConsultant cmRealConsultant = cmRealConsultantMapper.getCmRealConsultant(params);
        // 实际维护投顾、实际维护投顾-中心、实际维护投顾-区域、实际维护投顾-部门；
        if (null != cmRealConsultant) {
            String realConscode = cmRealConsultant.getReal_conscode();
            Map<String, Object> selectoutletcodeMap = cmRealConsultantMapper.selectoutletcode(realConscode);
            if (null != selectoutletcodeMap.get("OUTLETCODE")) {
                getCmRealResponse(response, realConscode, selectoutletcodeMap.get("OUTLETCODE").toString());
            }
        } else {
            Map<String, Object> selectoutletcodeMap = cmRealConsultantMapper.selectoutletnocode(hboneno);
            if (null!= selectoutletcodeMap && selectoutletcodeMap.size() == 2) {
                String outletcode = selectoutletcodeMap.get("OUTLETCODE").toString();
                String conscode = selectoutletcodeMap.get("CONSCODE").toString();
                getCmRealResponse(response, conscode,outletcode);
            }
        }

        return response;
    }

    private CmRealConsultantResponse getCmRealResponse(CmRealConsultantResponse response, String realConscode, String selectoutletcode) {
        Map<String, String> param = new HashMap<>();
        param.put("orgcode", selectoutletcode);
        // 获取部门名称
        HbOrganization hbOrganization = cmRealConsultantMapper.getHbOrganization(param);
        String deptName = hbOrganization.getOrgname();
        String deptCode = hbOrganization.getParentorgcode();
        param.put("orgcode", deptCode);
        // 获取区域名称
        HbOrganization areaorg = cmRealConsultantMapper.getHbOrganization(param);
        String areacode = areaorg.getParentorgcode();
        String areaname = areaorg.getOrgname();
        param.put("orgcode", areacode);
        // 中心名称
        HbOrganization centerorg = cmRealConsultantMapper.getHbOrganization(param);
        String centername = centerorg.getOrgname();
        response.setConscode(realConscode);
        response.setAreaname(areaname);
        response.setCentername(centername);
        response.setDeptname(deptName);
        response.success();
        return response;
    }


}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.realconsultant.dao.CmRealConsultantMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.realconsultant.dto.CmRealConsultant">
        <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
        <result column="HBONE_NO" jdbcType="VARCHAR" property="hboneNo" />
        <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
        <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
        <result column="REAL_CONSCODE" jdbcType="VARCHAR" property="realConscode" />
        <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
        <result column="MODIFERUSER" jdbcType="VARCHAR" property="modiferuser" />
        <result column="MODIFER_TIME" jdbcType="TIMESTAMP" property="modiferTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        CONSCUSTNO,
        HBONE_NO,
        CUSTNAME,
        CONSCODE,
        REAL_CONSCODE,
        REMARKS,
        MODIFERUSER,
        MODIFER_TIME
    </sql>

    <select id="getCmRealConsultant" resultType="com.howbuy.crm.nt.realconsultant.dto.CmRealConsultant">
        select *
        from CM_REAL_CONSULTANT
        where  1 =1
        <if test="conscustno != null">
            AND CONSCUSTNO = #{conscustno}
        </if>
        <if test="hboneno != null">
            AND HBONE_NO = #{hboneno}
        </if>
    </select>

    <select id="selectoutletcode" parameterType="java.lang.String" resultType="map">
        select
        c.OUTLETCODE as outletcode,
        c.conscode as conscode
        from
        CM_CONSULTANT c
        where
        c.CONSCODE  = #{hboneNo,jdbcType=VARCHAR}
    </select>

    <select id="selectoutletnocode" parameterType="java.lang.String" resultType="map">
        select c.OUTLETCODE as outletcode,c.conscode as conscode
        from CM_CONSCUST A
        left join CM_CUSTCONSTANT b ON A.CONSCUSTNO = B.CUSTNO
        left join CM_CONSULTANT c on c.CONSCODE = b.CONSCODE
        where A.HBONE_NO  = #{hboneNo,jdbcType=VARCHAR}
    </select>

    <select id="getHbOrganization" parameterType="Map" resultType="com.howbuy.crm.nt.realconsultant.dto.HbOrganization">
        SELECT
        *
        FROM HB_ORGANIZATION
        where 1=1
        <if test="orgcode != null"> AND orgcode = #{orgcode} </if>
    </select>

</mapper>
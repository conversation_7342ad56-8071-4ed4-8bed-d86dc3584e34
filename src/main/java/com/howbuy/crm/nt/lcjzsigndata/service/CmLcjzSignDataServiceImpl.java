/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.lcjzsigndata.service;

import com.howbuy.crm.nt.lcjzsigndata.dao.CmLcjzSignDataMapper;
import com.howbuy.crm.nt.lcjzsigndata.dto.CmLcjzsigndata;
import com.howbuy.crm.nt.lcjzsigndata.servcie.CmLcjzSignDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/7/28 17:33
 * @since JDK 1.8
 */
@Service(value = "cmLcjzSignDataService")
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmLcjzSignDataServiceImpl implements CmLcjzSignDataService {

    @Autowired
    private CmLcjzSignDataMapper cmLcjzSignDataMapper;

    @Override
    public void mergeCmLcjzSignData(CmLcjzsigndata cmLcjzsigndata) {
        cmLcjzSignDataMapper.mergeCmLcjzsigndata(cmLcjzsigndata);
    }

}
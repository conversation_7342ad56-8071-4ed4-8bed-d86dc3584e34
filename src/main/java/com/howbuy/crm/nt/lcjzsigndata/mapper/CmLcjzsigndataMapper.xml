<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.lcjzsigndata.dao.CmLcjzSignDataMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.lcjzsigndata.dto.CmLcjzsigndata">
        <!--@mbg.generated-->
        <!--@Table CM_LCJZSIGNDATA-->
        <id column="COURSEID" jdbcType="VARCHAR" property="courseid"/>
        <id column="JOINCOURSENAME" jdbcType="VARCHAR" property="joincoursename"/>
        <id column="CONFERENCEID" jdbcType="VARCHAR" property="conferenceid"/>
        <id column="HOLDORG" jdbcType="VARCHAR" property="holdorg"/>
        <id column="CONFERENCETYPE" jdbcType="VARCHAR" property="conferencetype"/>
        <id column="HBONENO" jdbcType="VARCHAR" property="hboneno"/>
        <id column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno"/>
        <id column="REALNAME" jdbcType="VARCHAR" property="realname"/>
        <id column="CONSCODE" jdbcType="VARCHAR" property="conscode"/>
        <result column="COURSENAME" jdbcType="VARCHAR" property="coursename"/>
        <result column="SPONSOR" jdbcType="VARCHAR" property="sponsor"/>
        <result column="CITYNAME" jdbcType="VARCHAR" property="cityname"/>
        <result column="STARTDATE" jdbcType="TIMESTAMP" property="startdate"/>
        <result column="LECTURER" jdbcType="VARCHAR" property="lecturer"/>
        <result column="ACTIVITYAREA" jdbcType="VARCHAR" property="activityarea"/>
        <result column="ACTIVITYTIME" jdbcType="VARCHAR" property="activitytime"/>
        <result column="ONLINEFLAG" jdbcType="VARCHAR" property="onlineflag"/>
        <result column="CONFERENCENAME" jdbcType="VARCHAR" property="conferencename"/>
        <result column="ASSIGNSOURCE" jdbcType="VARCHAR" property="assignsource"/>
        <result column="APPOINTSTATUS" jdbcType="VARCHAR" property="appointstatus"/>
        <result column="APPOINTTIME" jdbcType="TIMESTAMP" property="appointtime"/>
        <result column="IFCHECKIN" jdbcType="VARCHAR" property="ifcheckin"/>
        <result column="CHECKINTIME" jdbcType="TIMESTAMP" property="checkintime"/>
        <result column="IFCONS" jdbcType="VARCHAR" property="ifcons"/>
        <result column="CUSTPROPERTY" jdbcType="VARCHAR" property="custproperty"/>
        <result column="CUSTCLASSIFYASSIGN" jdbcType="VARCHAR" property="custclassifyassign"/>
        <result column="CUSTCLASSIFYNOW" jdbcType="VARCHAR" property="custclassifynow"/>
        <result column="FIRSTLEVELNAME" jdbcType="VARCHAR" property="firstlevelname"/>
        <result column="SECONDLEVELNAME" jdbcType="VARCHAR" property="secondlevelname"/>
        <result column="THIRDLEVELNAME" jdbcType="VARCHAR" property="thirdlevelname"/>
        <result column="FOURTHLEVELNAME" jdbcType="VARCHAR" property="fourthlevelname"/>
        <result column="GDHOLDFLAG" jdbcType="VARCHAR" property="gdholdflag"/>
        <result column="LSHOLDFLAG" jdbcType="VARCHAR" property="lsholdflag"/>
        <result column="HWFLAG" jdbcType="VARCHAR" property="hwflag"/>
        <result column="GDCAP" jdbcType="DECIMAL" property="gdcap"/>
        <result column="LSCAP" jdbcType="DECIMAL" property="lscap"/>
        <result column="GDFSTACKDT" jdbcType="TIMESTAMP" property="gdfstackdt"/>
        <result column="IFWITHCONS" jdbcType="VARCHAR" property="ifwithcons"/>
        <result column="CONSNAME" jdbcType="VARCHAR" property="consname"/>
        <result column="CONSNAMENOW" jdbcType="VARCHAR" property="consnamenow"/>
        <result column="U1NAME" jdbcType="VARCHAR" property="u1name"/>
        <result column="U2NAME" jdbcType="VARCHAR" property="u2name"/>
        <result column="U3NAME" jdbcType="VARCHAR" property="u3name"/>
        <result column="IFPRDRESERV" jdbcType="VARCHAR" property="ifprdreserv"/>
        <result column="IFCALL" jdbcType="VARCHAR" property="ifcall"/>
        <result column="CALLHANDLEDATE" jdbcType="TIMESTAMP" property="callhandledate"/>
        <result column="IFCALLSUCCESS" jdbcType="VARCHAR" property="ifcallsuccess"/>
        <result column="IFCALLLEADS" jdbcType="VARCHAR" property="ifcallleads"/>
        <result column="IFCALL30" jdbcType="VARCHAR" property="ifcall30"/>
        <result property="callhandledate30" column="callhandledate30" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        <result column="IFCALLSUCCESS30" jdbcType="VARCHAR" property="ifcallsuccess30"/>
        <result column="IFCALLLEADS30" jdbcType="VARCHAR" property="ifcallleads30"/>
        <result column="IFCALLSAMETIME" jdbcType="VARCHAR" property="ifcallsametime"/>
        <result column="CREDT" jdbcType="TIMESTAMP" property="credt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        COURSEID,
        JOINCOURSENAME,
        CONFERENCEID,
        HOLDORG,
        CONFERENCETYPE,
        HBONENO,
        CONSCUSTNO,
        REALNAME,
        CONSCODE,
        COURSENAME,
        SPONSOR,
        CITYNAME,
        STARTDATE,
        LECTURER,
        ACTIVITYAREA,
        ACTIVITYTIME,
        ONLINEFLAG,
        CONFERENCENAME,
        ASSIGNSOURCE,
        APPOINTSTATUS,
        APPOINTTIME,
        IFCHECKIN,
        CHECKINTIME,
        IFCONS,
        CUSTPROPERTY,
        CUSTCLASSIFYASSIGN,
        CUSTCLASSIFYNOW,
        FIRSTLEVELNAME,
        SECONDLEVELNAME,
        THIRDLEVELNAME,
        FOURTHLEVELNAME,
        GDHOLDFLAG,
        LSHOLDFLAG,
        HWFLAG,
        GDCAP,
        LSCAP,
        GDFSTACKDT,
        IFWITHCONS,
        CONSNAME,
        CONSNAMENOW,
        U1NAME,
        U2NAME,
        U3NAME,
        IFPRDRESERV,
        IFCALL,
        CALLHANDLEDATE,
        IFCALLSUCCESS,
        IFCALLLEADS,
        IFCALL30,
        CALLHANDLEDATE30,
        IFCALLSUCCESS30,
        IFCALLLEADS30,
        IFCALLSAMETIME,
        CREDT
    </sql>

    <update id="mergeCmLcjzsigndata" parameterType="com.howbuy.crm.nt.lcjzsigndata.dto.CmLcjzsigndata">
        MERGE INTO CM_LCJZSIGNDATA T
        using (select #{courseid}       as courseid,
                      #{joincoursename} as joincoursename,
                      #{conferenceid}   as conferenceid,
                      #{holdorg}        as holdorg,
                      #{conferencetype} as conferencetype,
                      #{hboneno}        as hboneno,
                      #{conscustno}     as conscustno,
                      #{realname}       as realname,
                      #{conscode}       as conscode
               from dual) t1
        ON (T.COURSEID = #{courseid}
            AND T.JOINCOURSENAME = #{joincoursename}
            AND T.CONFERENCEID = #{conferenceid}
            AND T.HOLDORG = #{holdorg}
            AND T.CONFERENCETYPE = #{conferencetype}
            AND T.HBONENO = #{hboneno}
            AND T.CONSCUSTNO = #{conscustno}
            AND T.REALNAME = #{realname}
            AND T.CONSCODE = #{conscode})
        WHEN MATCHED THEN
            UPDATE
            SET
                COURSENAME         = #{coursename},
                SPONSOR            = #{sponsor},
                CITYNAME           = #{cityname},
                STARTDATE          = #{startdate},
                LECTURER           = #{lecturer},
                ACTIVITYAREA       = #{activityarea},
                ACTIVITYTIME       = #{activitytime},
                ONLINEFLAG         = #{onlineflag},
                CONFERENCENAME     = #{conferencename},
                ASSIGNSOURCE       = #{assignsource},
                APPOINTSTATUS      = #{appointstatus},
                APPOINTTIME        = #{appointtime},
                IFCHECKIN          = #{ifcheckin},
                CHECKINTIME        = #{checkintime},
                IFCONS             = #{ifcons},
                CUSTPROPERTY       = #{custproperty},
                CUSTCLASSIFYASSIGN = #{custclassifyassign},
                CUSTCLASSIFYNOW    = #{custclassifynow},
                FIRSTLEVELNAME     = #{firstlevelname},
                SECONDLEVELNAME    = #{secondlevelname},
                THIRDLEVELNAME     = #{thirdlevelname},
                FOURTHLEVELNAME    = #{fourthlevelname},
                GDHOLDFLAG         = #{gdholdflag},
                LSHOLDFLAG         = #{lsholdflag},
                HWFLAG             = #{hwflag},
                GDCAP              = #{gdcap},
                LSCAP              = #{lscap},
                GDFSTACKDT         = #{gdfstackdt},
                IFWITHCONS         = #{ifwithcons},
                CONSNAME           = #{consname},
                CONSNAMENOW        = #{consnamenow},
                U1NAME             = #{u1name},
                U2NAME             = #{u2name},
                U3NAME             = #{u3name},
                IFPRDRESERV        = #{ifprdreserv},
                IFCALL             = #{ifcall},
                CALLHANDLEDATE     = #{callhandledate},
                IFCALLSUCCESS      = #{ifcallsuccess},
                IFCALLLEADS        = #{ifcallleads},
                IFCALL30           = #{ifcall30},
                CALLHANDLEDATE30   = #{callhandledate30},
                IFCALLSUCCESS30    = #{ifcallsuccess30},
                IFCALLLEADS30      = #{ifcallleads30},
                IFCALLSAMETIME     = #{ifcallsametime},
                CREDT              = #{credt},
                CONFERENCETIME     = #{conferencetime}
        WHEN NOT MATCHED THEN
            insert (COURSEID, JOINCOURSENAME, CONFERENCEID,
                    HOLDORG, CONFERENCETYPE, HBONENO,
                    CONSCUSTNO, REALNAME, CONSCODE,
                    COURSENAME, SPONSOR, CITYNAME,
                    STARTDATE, LECTURER, ACTIVITYAREA,
                    ACTIVITYTIME, ONLINEFLAG, CONFERENCENAME,
                    ASSIGNSOURCE, APPOINTSTATUS, APPOINTTIME,
                    IFCHECKIN, CHECKINTIME, IFCONS,
                    CUSTPROPERTY, CUSTCLASSIFYASSIGN, CUSTCLASSIFYNOW,
                    FIRSTLEVELNAME, SECONDLEVELNAME, THIRDLEVELNAME,
                    FOURTHLEVELNAME, GDHOLDFLAG, LSHOLDFLAG,
                    HWFLAG, GDCAP, LSCAP,
                    GDFSTACKDT, IFWITHCONS, CONSNAME,
                    CONSNAMENOW, U1NAME, U2NAME,
                    U3NAME, IFPRDRESERV, IFCALL,
                    CALLHANDLEDATE, IFCALLSUCCESS, IFCALLLEADS,
                    IFCALL30, CALLHANDLEDATE30, IFCALLSUCCESS30,
                    IFCALLLEADS30, IFCALLSAMETIME,
                    CREDT, CONFERENCETIME)
            values (#{courseid,jdbcType=VARCHAR}, #{joincoursename,jdbcType=VARCHAR}, #{conferenceid,jdbcType=VARCHAR},
                    #{holdorg,jdbcType=VARCHAR}, #{conferencetype,jdbcType=VARCHAR}, #{hboneno,jdbcType=VARCHAR},
                    #{conscustno,jdbcType=VARCHAR}, #{realname,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR},
                    #{coursename,jdbcType=VARCHAR}, #{sponsor,jdbcType=VARCHAR}, #{cityname,jdbcType=VARCHAR},
                    #{startdate,jdbcType=TIMESTAMP},
                    #{lecturer,jdbcType=VARCHAR}, #{activityarea,jdbcType=VARCHAR}, #{activitytime,jdbcType=VARCHAR},
                    #{onlineflag,jdbcType=VARCHAR}, #{conferencename,jdbcType=VARCHAR},
                    #{assignsource,jdbcType=VARCHAR},
                    #{appointstatus,jdbcType=VARCHAR}, #{appointtime,jdbcType=TIMESTAMP}, #{ifcheckin,jdbcType=VARCHAR},
                    #{checkintime,jdbcType=TIMESTAMP}, #{ifcons,jdbcType=VARCHAR}, #{custproperty,jdbcType=VARCHAR},
                    #{custclassifyassign,jdbcType=VARCHAR}, #{custclassifynow,jdbcType=VARCHAR},
                    #{firstlevelname,jdbcType=VARCHAR}, #{secondlevelname,jdbcType=VARCHAR},
                    #{thirdlevelname,jdbcType=VARCHAR},
                    #{fourthlevelname,jdbcType=VARCHAR}, #{gdholdflag,jdbcType=VARCHAR}, #{lsholdflag,jdbcType=VARCHAR},
                    #{hwflag,jdbcType=VARCHAR}, #{gdcap,jdbcType=DECIMAL}, #{lscap,jdbcType=DECIMAL},
                    #{gdfstackdt,jdbcType=TIMESTAMP},
                    #{ifwithcons,jdbcType=VARCHAR}, #{consname,jdbcType=VARCHAR}, #{consnamenow,jdbcType=VARCHAR},
                    #{u1name,jdbcType=VARCHAR}, #{u2name,jdbcType=VARCHAR}, #{u3name,jdbcType=VARCHAR},
                    #{ifprdreserv,jdbcType=VARCHAR},
                    #{ifcall,jdbcType=VARCHAR}, #{callhandledate,jdbcType=TIMESTAMP}, #{ifcallsuccess,jdbcType=VARCHAR},
                    #{ifcallleads,jdbcType=VARCHAR}, #{ifcall30,jdbcType=VARCHAR},
                    #{callhandledate30,jdbcType=TIMESTAMP},
                    #{ifcallsuccess30,jdbcType=VARCHAR}, #{ifcallleads30,jdbcType=VARCHAR},
                    #{ifcallsametime,jdbcType=VARCHAR},
                    #{credt,jdbcType=TIMESTAMP},#{conferencetime,jdbcType=TIMESTAMP})
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conscust.dao.QueryFamilyCustInfoMapper">
	<resultMap type="com.howbuy.crm.nt.conscust.domain.QueryFamilyCustInfo" id="familyCustInfoResult">
		<result property="hboneNo" column="HBONENO" />
		<result property="conscustNo" column="CONSCUSTNO" />
		<result property="custName" column="CUSTNAME" />
		<result property="userName" column="USERNAME" />
		<result property="fcFlag" column="FCFLAG" />
	</resultMap>

	<select id="queryFamilyCustInfo" resultMap="familyCustInfoResult" parameterType="map" useCache="false">
		 SELECT TC.HBONE_NO AS HBONENO, TC.CONSCUSTNO, TC.CUSTNAME, TC.VIPUSERNAME AS USERNAME, FCFLAG
      FROM (SELECT T1.HBONENO, '1' AS FCFLAG
              FROM CM_RELATION_ACCOUNT T1
              LEFT JOIN CM_RELATION_ACCOUNT_SUB T2
                ON T2.RELATIONID = T1.RELATIONID
             WHERE T1.RELATIONSTATE = '0'
               AND T2.RELATIONSTATE = '0'
               AND (
                       T1.Hboneno = #{hboneno} OR T2.Hboneno =  #{hboneno}
                   )
            UNION
            SELECT T2.Hboneno, '2' AS FCFLAG
              FROM CM_RELATION_ACCOUNT T1
              LEFT JOIN CM_RELATION_ACCOUNT_SUB T2
                ON T2.RELATIONID = T1.RELATIONID
             WHERE T1.RELATIONSTATE = '0'
               AND T2.RELATIONSTATE = '0'
               AND (
                        T1.Hboneno = #{hboneno} OR T2.Hboneno = #{hboneno}
                   )
               ) TF
      LEFT JOIN CM_CONSCUST TC
        ON TC.Hbone_No = TF.HBONENO
     ORDER BY FCFLAG
	</select>

</mapper>
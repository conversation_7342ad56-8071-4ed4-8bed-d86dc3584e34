package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2021/8/24
 */
@Slf4j
@Service("updateCustRgSourceLogService")
public class UpdateCustRgSourceLogServiceImpl implements UpdateCustRgSourceLogService {

    @Autowired
    private ConscustMapper conscustMapper;

    @Override
    public String execute(String arg) {
        Map<String, Object> param = new HashMap<>(1);
        conscustMapper.batchUpdateCustRgSourceLog(param);
        return "success";
    }
}

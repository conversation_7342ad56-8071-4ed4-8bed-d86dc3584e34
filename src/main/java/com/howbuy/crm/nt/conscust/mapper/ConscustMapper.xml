<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conscust.dao.ConscustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conscust.domain.ConscustInfo">
        <result column="CONSCUSTNO" property="conscustno" jdbcType="VARCHAR"/>
        <result column="CONSCUSTLVL" property="conscustlvl" jdbcType="VARCHAR"/>
        <result column="CONSCUSTGRADE" property="conscustgrade" jdbcType="INTEGER"/>
        <result column="CONSCUSTSTATUS" property="conscuststatus" jdbcType="VARCHAR"/>
        <result column="IDTYPE" property="idtype" jdbcType="VARCHAR"/>
        <result column="IDNO" property="idno" jdbcType="VARCHAR"/>
        <result column="CUSTNAME" property="custname" jdbcType="VARCHAR"/>
        <result column="PROVCODE" property="provcode" jdbcType="VARCHAR"/>
        <result column="CITYCODE" property="citycode" jdbcType="VARCHAR"/>
        <result column="EDULEVEL" property="edulevel" jdbcType="VARCHAR"/>
        <result column="VOCATION" property="vocation" jdbcType="VARCHAR"/>
        <result column="INCLEVEL" property="inclevel" jdbcType="VARCHAR"/>
        <result column="BIRTHDAY" property="birthday" jdbcType="VARCHAR"/>
        <result column="GENDER" property="gender" jdbcType="VARCHAR"/>
        <result column="MARRIED" property="married" jdbcType="VARCHAR"/>
        <result column="PINCOME" property="pincome" jdbcType="VARCHAR"/>
        <result column="FINCOME" property="fincome" jdbcType="VARCHAR"/>
        <result column="DECISIONFLAG" property="decisionflag" jdbcType="VARCHAR"/>
        <result column="INTERESTS" property="interests" jdbcType="VARCHAR"/>
        <result column="CONTACTTIME" property="contacttime" jdbcType="VARCHAR"/>
        <result column="COMPANY" property="company" jdbcType="VARCHAR"/>
        <result column="OFFICETELNO" property="officetelno" jdbcType="VARCHAR"/>
        <result column="RISKLEVEL" property="risklevel" jdbcType="VARCHAR"/>
        <result column="SELFRISKLEVEL" property="selfrisklevel" jdbcType="VARCHAR"/>
        <result column="ADDR" property="addr" jdbcType="VARCHAR"/>
        <result column="POSTCODE" property="postcode" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="TELNO" property="telno" jdbcType="VARCHAR"/>
        <result column="FAX" property="fax" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="SALON" property="salon" jdbcType="VARCHAR"/>
        <result column="BEFOREINVEST" property="beforeinvest" jdbcType="VARCHAR"/>
        <result column="VISITFQCY" property="visitfqcy" jdbcType="VARCHAR"/>
        <result column="ADDR2" property="addr2" jdbcType="VARCHAR"/>
        <result column="POSTCODE2" property="postcode2" jdbcType="VARCHAR"/>
        <result column="MOBILE2" property="mobile2" jdbcType="VARCHAR"/>
        <result column="EMAIL2" property="email2" jdbcType="VARCHAR"/>
        <result column="KNOWHOWBUY" property="knowhowbuy" jdbcType="VARCHAR"/>
        <result column="SUBKNOW" property="subknow" jdbcType="VARCHAR"/>
        <result column="SUBKNOWTYPE" property="subknowtype" jdbcType="VARCHAR"/>
        <result column="SPECIALFLAG" property="specialflag" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="REGDT" property="regdt" jdbcType="VARCHAR"/>
        <result column="LINKMAN" property="linkman" jdbcType="VARCHAR"/>
        <result column="LINKTEL" property="linktel" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE" property="linkmobile" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL" property="linkemail" jdbcType="VARCHAR"/>
        <result column="LINKPOSTCODE" property="linkpostcode" jdbcType="VARCHAR"/>
        <result column="LINKADDR" property="linkaddr" jdbcType="VARCHAR"/>
        <result column="GPSINVESTLEVEL" property="gpsinvestlevel" jdbcType="VARCHAR"/>
        <result column="GPSRISKLEVEL" property="gpsrisklevel" jdbcType="VARCHAR"/>
        <result column="ISJOINCLUB" property="isjoinclub" jdbcType="VARCHAR"/>
        <result column="CUSTSOURCEREMARK" property="custsourceremark" jdbcType="VARCHAR"/>
        <result column="LATESTTRADEDT" property="latesttradedt" jdbcType="VARCHAR"/>
        <result column="RSTOHIGHREASON" property="rstohighreason" jdbcType="VARCHAR"/>
        <result column="ISPUBTRADE" property="ispubtrade" jdbcType="VARCHAR"/>
        <result column="INVSTTYPE" property="invsttype" jdbcType="VARCHAR"/>
        <result column="VIPUSERNAME" property="vipusername" jdbcType="VARCHAR"/>
        <result column="NEWSOURCENO" property="newsourceno" jdbcType="VARCHAR"/>
        <result column="HBONE_NO" property="hboneno" jdbcType="VARCHAR"/>
        <result column="VALIDITY" property="validity" jdbcType="VARCHAR"/>
        <result column="VALIDITYDT" property="validitydt" jdbcType="VARCHAR"/>
        <result column="WECHATCODE" property="wechatcode" jdbcType="VARCHAR"/>
        <result column="ISVIRTUAL" property="isvirtual" jdbcType="VARCHAR"/>
        <result column="ISHIGHORG" property="ishighorg" jdbcType="VARCHAR"/>
        <result column="NATURE" property="nature" jdbcType="VARCHAR"/>
        <result column="APTITUDE" property="aptitude" jdbcType="VARCHAR"/>
        <result column="SCOPEBUSINESS" property="scopebusiness" jdbcType="VARCHAR"/>
        <result column="ADDTIME" property="addtime" jdbcType="DATE"/>
        <result column="UPDATETIME" property="updatetime" jdbcType="DATE"/>
        <result column="CONSCODE" property="conscode" jdbcType="DATE"/>
        <result column="RESTYPE" property="restype" jdbcType="VARCHAR"/>
        <result column="IDNO_DIGEST" property="idnoDigest" jdbcType="VARCHAR"/>
        <result column="ADDR_DIGEST" property="addrDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="TELNO_DIGEST" property="telnoDigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emailDigest" jdbcType="VARCHAR"/>
        <result column="ADDR2_DIGEST" property="addr2Digest" jdbcType="VARCHAR"/>
        <result column="MOBILE2_DIGEST" property="mobile2Digest" jdbcType="VARCHAR"/>
        <result column="EMAIL2_DIGEST" property="email2Digest" jdbcType="VARCHAR"/>
        <result column="LINKTEL_DIGEST" property="linktelDigest" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_DIGEST" property="linkmobileDigest" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_DIGEST" property="linkemailDigest" jdbcType="VARCHAR"/>
        <result column="LINKADDR_DIGEST" property="linkaddrDigest" jdbcType="VARCHAR"/>
        
        <result column="IDNO_MASK" property="idnoMask" jdbcType="VARCHAR"/>
        <result column="ADDR_MASK" property="addrMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="TELNO_MASK" property="telnoMask" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailMask" jdbcType="VARCHAR"/>
        <result column="ADDR2_MASK" property="addr2Mask" jdbcType="VARCHAR"/>
        <result column="MOBILE2_MASK" property="mobile2Mask" jdbcType="VARCHAR"/>
        <result column="EMAIL2_MASK" property="email2Mask" jdbcType="VARCHAR"/>
        <result column="LINKTEL_MASK" property="linktelMask" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_MASK" property="linkmobileMask" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_MASK" property="linkemailMask" jdbcType="VARCHAR"/>
        <result column="LINKADDR_MASK" property="linkaddrMask" jdbcType="VARCHAR"/>
       
        <result column="IDNO_CIPHER" property="idnoCipher" jdbcType="VARCHAR"/>
        <result column="ADDR_CIPHER" property="addrCipher" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
        <result column="TELNO_CIPHER" property="telnoCipher" jdbcType="VARCHAR"/>
        <result column="EMAIL_CIPHER" property="emailCipher" jdbcType="VARCHAR"/>
        <result column="ADDR2_CIPHER" property="addr2Cipher" jdbcType="VARCHAR"/>
        <result column="MOBILE2_CIPHER" property="mobile2Cipher" jdbcType="VARCHAR"/>
        <result column="EMAIL2_CIPHER" property="email2Cipher" jdbcType="VARCHAR"/>
        <result column="LINKTEL_CIPHER" property="linktelCipher" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_CIPHER" property="linkmobileCipher" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_CIPHER" property="linkemailCipher" jdbcType="VARCHAR"/>
        <result column="LINKADDR_CIPHER" property="linkaddrCipher" jdbcType="VARCHAR"/>
    </resultMap>
    
    <resultMap id="custInfoMap" type="com.howbuy.crm.nt.conscust.domain.CustInfo">
        <result column="CUSTNAME" property="custName" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emailDigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailMask" jdbcType="VARCHAR"/>
        <result column="EMAIL_CIPHER" property="emailCipher" jdbcType="VARCHAR"/>
        <result column="CONSCUSTNO" property="conscustNo" jdbcType="VARCHAR"/>
        <result column="HBONENO" property="hboneNo" jdbcType="VARCHAR"/>
        <result column="PUBCUSTNO" property="pubcustNo" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listConsCustInfo" resultMap="BaseResultMap" parameterType="map" useCache="false">
        SELECT T.*,A.CONSCODE,C.ISVIRTUAL,
        (SELECT CASE WHEN count(*) > 0 THEN '1' ELSE '0' END
        FROM (SELECT ORGCODE
        FROM HB_ORGANIZATION
        START WITH ORGCODE IN ('1', '10')
        CONNECT BY PARENTORGCODE = PRIOR ORGCODE) A
        where A.ORGCODE = C.OUTLETCODE) AS ISHIGHORG
        FROM CM_CONSCUST  T
        LEFT JOIN CM_CUSTCONSTANT A ON T.CONSCUSTNO = A.CUSTNO
        LEFT JOIN CM_CONSULTANT C ON A.CONSCODE = C.CONSCODE
        WHERE 1=1 AND CONSCUSTSTATUS = '0'
        <if test="idtype != null"> AND IDTYPE = #{idtype} </if>
        <if test="idno != null"> AND IDNO = #{idno} </if>
        <if test="custname != null"> AND CUSTNAME = #{custname} </if>
        <if test="mobile != null"> AND MOBILE = #{mobile} </if> <!--TODO:potential bug :未定义明确表明，-->
        <if test="email != null"> AND EMAIL = #{email} </if>
        <if test="invsttype != null"> AND INVSTTYPE = #{invsttype} </if>
        <if test="hboneno != null"> AND HBONE_NO = #{hboneno} </if>
        <if test="conscustno != null"> AND CONSCUSTNO = #{conscustno} </if>
    </select>

    <select id="listConsCustInfoByMobile" resultMap="BaseResultMap" parameterType="string">
           SELECT T.*,A.CONSCODE
           FROM CM_CONSCUST  T
           LEFT JOIN CM_CUSTCONSTANT A ON T.CONSCUSTNO = A.CUSTNO
           LEFT JOIN CM_CONSULTANT CC ON CC.CONSCODE = A.CONSCODE
           <where>
               AND T.CONSCUSTSTATUS = '0'
               <!--AND CC.CONSSTATUS = '1'-->
               AND T.MOBILE_DIGEST=#{mobileDigest,jdbcType=VARCHAR}
           </where>
    </select>

    <select id="listConsCustInfoCount" resultType="Integer" parameterType="map" useCache="false">
        SELECT count(*) FROM CM_CONSCUST  T
        LEFT JOIN CM_CUSTCONSTANT A ON T.CONSCUSTNO = A.CUSTNO
        LEFT JOIN CM_CONSULTANT CC ON CC.CONSCODE = A.CONSCODE
        WHERE 1=1 AND CC.CONSSTATUS = '1' AND CONSCUSTSTATUS = '0'
        <if test="conscode != null"> AND A.CONSCODE = #{conscode} </if>
        <if test="consmobile != null"> AND CC.MOBILE = #{consmobile} </if>
        <if test="custname != null"> AND T.CUSTNAME like '%'||#{custname}||'%' </if>
    </select>

    <select id="listConsCustInfoShow" resultMap="BaseResultMap" parameterType="map" useCache="false">
        SELECT * FROM (
        SELECT T.*,A.CONSCODE,ROWNUM AS ROWNO FROM CM_CONSCUST  T
        LEFT JOIN CM_CUSTCONSTANT A ON T.CONSCUSTNO = A.CUSTNO
        LEFT JOIN CM_CONSULTANT CC ON CC.CONSCODE = A.CONSCODE
        WHERE 1=1 AND CC.CONSSTATUS = '1' AND CONSCUSTSTATUS = '0'
        <if test="conscode != null"> AND A.CONSCODE = #{conscode} </if>
        <if test="consmobile != null"> AND CC.MOBILE = #{consmobile} </if>
        <if test="custname != null"> AND T.CUSTNAME like '%'||#{custname}||'%' </if>
        ) OT WHERE OT.ROWNO BETWEEN #{pagenumindex} AND #{pagenum}
    </select>

    <select id="listConsCustInfoShowAll" resultMap="BaseResultMap" parameterType="map" useCache="false">

        SELECT T.*,A.CONSCODE,ROWNUM AS ROWNO FROM CM_CONSCUST  T
        LEFT JOIN CM_CUSTCONSTANT A ON T.CONSCUSTNO = A.CUSTNO
        LEFT JOIN CM_CONSULTANT CC ON CC.CONSCODE = A.CONSCODE
        WHERE 1=1 AND CC.CONSSTATUS = '1' AND CONSCUSTSTATUS = '0'
        <if test="conscode != null"> AND A.CONSCODE = #{conscode} </if>
        <if test="consmobile != null"> AND CC.MOBILE = #{consmobile} </if>
        <if test="custname != null"> AND T.CUSTNAME like '%'||#{custname}||'%' </if>
    </select>
    
    <select id="listConsCustInfoByHboneNo" resultMap="BaseResultMap" parameterType="map" useCache="false">
	    SELECT T.* FROM CM_CONSCUST T
		 WHERE 1 = 1
		   AND CONSCUSTSTATUS = '0'
		   AND HBONE_NO = #{hboneno}
    </select>
    
    <select id="listCustInfoByConscustNoAndHboneNoList" resultMap="custInfoMap" parameterType="map" useCache="false">
        SELECT T.*,
        TC.MOBILE_CIPHER,
        TC.EMAIL_CIPHER,
        T.HBONE_NO       AS HBONENO,
        T1.CUST_NO       AS PUBCUSTNO
        FROM CM_CONSCUST T
        LEFT JOIN CM_CONSCUST_CIPHER TC
        ON T.CONSCUSTNO = TC.CONSCUSTNO
        LEFT JOIN AC_TX_HBONE T1
        ON T1.HBONE_NO = T.HBONE_NO
        WHERE T.CONSCUSTSTATUS = '0'
        AND T1.STAT = '0'
		   <if test="conscustNoList != null and conscustNoList.size() > 0 ">
			   AND T.CONSCUSTNO in
			   <foreach collection="conscustNoList" item="conscustNo" index="index" open="(" close=")" separator=",">
				    #{conscustNo}
			   </foreach>
		   </if>
		   <if test="hboneNoList != null and hboneNoList.size() > 0 ">
			   AND T.HBONE_NO in
			   <foreach collection="hboneNoList" item="hboneNo" index="index" open="(" close=")" separator=",">
				    #{hboneNo}
			   </foreach>
		   </if>
    </select>


    <select id="listCustInfoForSync" resultMap="custInfoMap" useCache="false">
        SELECT
        T.HBONE_NO  AS HBONENO,
        A.CUST_NO  AS PUBCUSTNO
        FROM HBONE_OPEN_SOURCE T
        LEFT JOIN AC_TX_HBONE A ON T.HBONE_NO = A.HBONE_NO
        WHERE T.Deal_Status = '1'
        and t.deal_date <![CDATA[>=]]> sysdate - 30 / (24 * 60)
        AND T.deal_date <![CDATA[<]]> sysdate
    </select>

    <select id="listCustNameForPinYin" parameterType="map" resultMap="custInfoMap" useCache="false">
      select * from (
          SELECT T.conscustNo, custName,PINYIN
            FROM CM_CONSCUST T
           inner join cm_high_custinfo t1
              on t.conscustno = t1.conscustno
           where t1.conscustno > #{conscustno}
           ORDER  by conscustno
       )  where rownum <![CDATA[<=]]> #{pageSize}
    </select>

    <update id="updateCustPinYin" parameterType="com.howbuy.crm.nt.conscust.domain.CustInfo">
        declare
        begin
        <foreach collection="custInfos" item="item">
            UPDATE CM_CONSCUST SET
            pinYin = #{item.pinYin}
            where conscustno = #{item.conscustNo};
        </foreach>
        end;
    </update>

    <select id="queryCustForSecret" parameterType="map" resultType="string">
        select t1.conscustno, t1.custname
        from cm_conscust t1
        inner join cm_high_custinfo t2
        on t1.conscustno = t2.conscustno
        left join cm_custconstant t3
        on t1.conscustno = t3.custno
        where t3.conscode = #{consCode}
        <if test="custName != null">
            and (t1.custname = #{custName} or t1.pinyin = #{pinYin})
        </if>
        <if test="mobile != null">
            and t1.mobile_digest = #{mobile}
        </if>
    </select>
    
    <update id="batchUpdateCustRgSourceLog" parameterType="map">
		DELETE FROM CM_CUST_RESOURCE_LOG A
		 WHERE A.ID IN (SELECT T.ID
		                  FROM CM_CUST_RESOURCE_LOG T
		                  LEFT JOIN CM_CUSTCONSTANT T1
		                    ON T.CONSCUSTNO = T1.CUSTNO
		                 WHERE T.CREDDT &lt; T1.BINDDATE)
	</update>

    <select id="listCmConsultRestype" resultType="com.howbuy.crm.nt.consultant.dto.CmConsultRestype">
        SELECT T.CONSCUSTNO as conscutno,
               T1.MODDT as moddt,
               T3.GDCJLABEL as gdcjlabel,
               DECODE(T2.FIRST_LEVEL_CODE,
                      'B',
                      DECODE(T2.SECOND_LEVEL_CODE,
                             '019',
                             '1',
                             '013',
                             '1',
                             '0'),
                      'I',
                      '1',
                      'M',
                      '1',
                      '0') RESTYPE
        FROM CM_CONSCUST T
                 INNER JOIN CM_HIGH_CUSTINFO T3
                            ON T.CONSCUSTNO = T3.CONSCUSTNO
                 LEFT JOIN CM_CUSTCONSTANT T1
                           ON T.CONSCUSTNO = T1.CUSTNO
                 LEFT JOIN CM_SOURCEINFO_NEW T2
                           ON T.NEWSOURCENO = T2.SOURCENO
        WHERE T.RESTYPE IS NOT NULL
          AND T1.STARTDT = TO_CHAR(SYSDATE-1, 'yyyymmdd')
    </select>

    <update id="updateConscustResType" parameterType="map">
        UPDATE CM_CONSCUST
        SET RESTYPE = #{type}
        WHERE CONSCUSTNO IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateLatestTradeDtByConscustNo" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";" open="begin" close=";end;">
            update CM_CONSCUST set LATESTTRADEDT = #{item.latesttradedt} where CONSCUSTNO = #{item.conscustno}
        </foreach>
    </update>

    <select id="listCmConsultRestype1" resultType="com.howbuy.crm.nt.consultant.dto.CmConsultRestype">
        SELECT T.CONSCUSTNO as conscutno,
               T3.MODDT as moddt ,
               T1.GDCJLABEL as gdcjlabel,
               DECODE(T2.FIRST_LEVEL_CODE,
                      'B',
                      DECODE(T2.SECOND_LEVEL_CODE,
                             '019',
                             '1',
                             '013',
                             '1',
                             '0'),
                      'I',
                      '1',
                      'M',
                      '1',
                      '0') RESTYPE
        FROM CM_CONSCUST T
                 INNER JOIN CM_HIGH_CUSTINFO T1
                            ON T.CONSCUSTNO = T1.CONSCUSTNO
                 LEFT JOIN CM_SOURCEINFO_NEW T2
                           ON T.NEWSOURCENO = T2.SOURCENO
                 LEFT JOIN CM_CUSTCONSTANT T3
                           ON T.CONSCUSTNO = T3.CUSTNO
        WHERE T.RESTYPE IS NULL
    </select>

    <select id="selectConscustNoByHboneNo" resultType="string">
        select conscustno from cm_conscust where hbone_no = #{hboneNo}
    </select>

    <select id="selectCustNosFromSurveyRec" resultType="string">
        select CONSCUSTNO from CM_CONSCUSTSURVEYREC
    </select>

    <select id="getCustNameByCustNo" parameterType="string" resultType="string">
        select CUSTNAME from CM_CONSCUST where CONSCUSTNO = #{custNo}
    </select>

    <update id="updateCustNameInSurveyRec" parameterType="list">
        update CM_CONSCUSTSURVEYREC
        set CUSTNAME =
        <foreach collection="params" item="item" index="index"
                 separator=" " open="case" close="end">
            when CONSCUSTNO=#{item.custNo, jdbcType=VARCHAR} then #{item.custName, jdbcType=VARCHAR}
        </foreach>
        where CONSCUSTNO in
        <foreach collection="params" index="index" item="item"
                 separator="," open="(" close=")">
            #{item.custNo, jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getSurveryRecDiffCustName" resultType="com.howbuy.crm.nt.conscust.domain.ConsCustSurveryRecDiffDO">
        select a.conscustno, T.custname
        from CM_CONSCUSTSURVEYREC a
                 left join CM_CONSCUST T
                           on a.conscustno = T.conscustno
        where a.custname
        <![CDATA[ <> ]]>
        T.custname
    </select>

    <update id="updateSurveyRecCustNameByConcCustNo">
        update CM_CONSCUSTSURVEYREC
        set CUSTNAME = #{custName,jdbcType=VARCHAR}
        where CONSCUSTNO = #{consCustNo,jdbcType=VARCHAR}
    </update>

    <update id="zeroingBuyProd">
        UPDATE CM_CONSCUST
        SET BUYINGPROD = '0000000000000000', BUYEDPROD = '0000000000000000'
    </update>

    <update id="updateBuyingProdByCustNoList">
        UPDATE CM_CONSCUST
        SET BUYINGPROD = SUBSTR(BUYINGPROD, 0, #{index,jdbcType=INTEGER}) || 1 ||
                         SUBSTR(BUYINGPROD, 2 + #{index,jdbcType=INTEGER})
        <where>
            CONSCUSTNO in
            <foreach collection="custNoList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </update>

    <update id="updateBuyedProdByCustNoList">
        UPDATE CM_CONSCUST
        SET BUYEDPROD = SUBSTR(BUYEDPROD, 0, #{index,jdbcType=INTEGER}) || 1 ||
                        SUBSTR(BUYEDPROD, 2 + #{index,jdbcType=INTEGER})
        <where>
            CONSCUSTNO in
            <foreach collection="custNoList" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </update>

    <select id="countConscust" resultType="int">
        select count(1) from cm_conscust
    </select>

    <select id="getMinCustNo" resultType="string">
        select min(conscustno) from cm_conscust
    </select>

    <select id="getCustNoByAscIndex" resultType="java.lang.String">
        SELECT conscustno
        FROM (SELECT conscustno, ROW_NUMBER() OVER (ORDER BY conscustno ASC) AS rn
              FROM cm_conscust) t
        WHERE t.rn = #{index,jdbcType=INTEGER}
    </select>

    <update id="zeroingBuyProdBetweenCustNo">
        UPDATE CM_CONSCUST
        SET BUYINGPROD = '0000000000000000',
            BUYEDPROD  = '0000000000000000'
        WHERE CONSCUSTNO BETWEEN #{startCustNo,jdbcType=VARCHAR} AND #{endCustNo,jdbcType=VARCHAR}
    </update>


    <select id="listCmWaitTransferCustListByPage" parameterType="Map" resultType="com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustDO" >
        WITH CENTER_CODE AS (
            SELECT T.ORGCODE,T.ORGNAME,T.PARENTORGCODE,CONNECT_BY_ROOT orgcode AS CENTERORGCODE
            FROM HB_ORGANIZATION T
            START WITH T.PARENTORGCODE = '0'
            CONNECT BY T.PARENTORGCODE = PRIOR T.ORGCODE
        )
        select
            C.CENTERORGCODE as centerOrgCode,
            T.CONSCUSTNO as consCustNo,
            T.STATE as state,
            T1.CONSCODE as conscode,
            t3.CUSTNAME as custName
        from CM_WAIT_TRANSFER_CUST t
        join CM_CUSTCONSTANT t1 on t.CONSCUSTNO = t1.CUSTNO
        join CM_CONSULTANT t2 on t1.CONSCODE = t2.CONSCODE AND t2.CONSSTATUS = '1'
        join CM_CONSCUST t3 on t.CONSCUSTNO = t3.CONSCUSTNO
        join CENTER_CODE C ON T2.OUTLETCODE = C.ORGCODE AND C.CENTERORGCODE in
        <foreach collection="param.orgCodes" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        where t.state = #{param.state,jdbcType=VARCHAR}
    </select>
</mapper>
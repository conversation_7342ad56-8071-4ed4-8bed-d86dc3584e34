package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.nt.conference.domain.CsCommunicateVisit;
import com.howbuy.crm.nt.conscust.buss.CsCommunicateVisitBuss;
import com.howbuy.crm.nt.conscust.dto.CsCommunicateVisitDomain;
import com.howbuy.crm.nt.conscust.request.CsCommunicateVisitRequest;
import com.howbuy.crm.nt.conscust.response.CsCommunicateVisitResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: crm-nt
 * date: 2023/2/1 9:51
 * version: 1.0
 * <AUTHOR>
 */
@Slf4j
@Service("csCommunicateVisitService")
public class CsCommunicateVisitServiceImpl implements CsCommunicateVisitService {


    @Autowired
    private CsCommunicateVisitBuss csCommunicateVisitBuss;

    @Override
    public CsCommunicateVisitResponse insertCsCommunicateVisit(CsCommunicateVisitRequest request) {

        CsCommunicateVisitResponse response = new CsCommunicateVisitResponse();

        if (request == null || request.getCsCommunicateVisit() == null) {
            response.paramError("参数不能为空");
            return response;
        }

        try {
            CsCommunicateVisitDomain domain = request.getCsCommunicateVisit();

            CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();
            csCommunicateVisit.setConscustNo(domain.getConscustno());
            csCommunicateVisit.setVisitType(domain.getVisittype());
            csCommunicateVisit.setVisitClassify(domain.getVisitclassify());
            csCommunicateVisit.setCommContent(domain.getCommcontent());
            csCommunicateVisit.setCreator(domain.getCreator());

            csCommunicateVisitBuss.insertCsCommunicateVisit(csCommunicateVisit, response);
        } catch (Exception e) {
            response.paramError("cmConsbookingService接口异常" + e.getMessage());
            log.error("cmConsbookingService error{}", e);
        }

        response.success();
        return response;
    }
}

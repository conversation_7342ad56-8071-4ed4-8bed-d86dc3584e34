package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.nt.conscust.buss.QueryConscustInfoBuss;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryConscustListRequest;
import com.howbuy.crm.nt.conscust.response.QueryConscustListResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("queryConscustListService")
public class QueryConscustListServiceImpl implements QueryConscustListService {

	private static final Logger log = LoggerFactory.getLogger(QueryConscustListServiceImpl.class);
	
	@Autowired
	private QueryConscustInfoBuss queryConscustInfoBuss;
	
	@Override
	public QueryConscustListResponse queryConscustInfo(QueryConscustListRequest request) {
		QueryConscustListResponse response = new QueryConscustListResponse();
		// 参数验证
		if (this.checkParams(request)) {
			response.invalidReqParams("请输入正确查询参数,客户类别存在时证件号必传！");
			
		}else {
			List<ConscustInfo> conscustinfolist = queryConscustInfoBuss.listConsCustInfo(request.getIdtype(), request.getIdno(), request.getCustname(), request.getMobile(), request.getEmail(), request.getInvsttype(), request.getConscustno(), request.getHboneno());
			
			if(conscustinfolist != null && conscustinfolist.size() > 0) {
				List<ConscustInfoDomain> conscustlist = new ArrayList<ConscustInfoDomain>();
				
				for(ConscustInfo conscustInfo:conscustinfolist) {
					ConscustInfoDomain conscustInfoDomain = new ConscustInfoDomain();
					BeanUtils.copyProperties(conscustInfo,conscustInfoDomain);
					conscustlist.add(conscustInfoDomain);
				}
				log.info("conscustlist:"+conscustlist.size());
				response.setConscustlist(conscustlist);
			}
			response.success();
			
		}
		
		return response;
	}

	private boolean checkParams(QueryConscustListRequest request) {
		boolean flag = false;
		if (StringUtils.isEmpty(request.getCustname()) && StringUtils.isEmpty(request.getEmail())
				&& StringUtils.isEmpty(request.getIdno()) && StringUtils.isEmpty(request.getIdtype())
				&& StringUtils.isEmpty(request.getInvsttype()) && StringUtils.isEmpty(request.getMobile())
				&& StringUtils.isEmpty(request.getConscustno()) && StringUtils.isEmpty(request.getHboneno())) {
			
			flag = true;
		}else if(StringUtils.isNotEmpty(request.getInvsttype()) && (StringUtils.isEmpty(request.getIdno()) || StringUtils.isEmpty(request.getIdtype()))){
			flag = true;
		}
		
		return flag;
	}
}

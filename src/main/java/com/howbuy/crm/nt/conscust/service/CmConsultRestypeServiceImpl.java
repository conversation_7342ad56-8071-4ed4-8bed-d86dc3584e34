/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.constant.CmConsuctType;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.consultant.dto.CmConsultRestype;
import com.howbuy.crm.nt.consultant.service.CmConsultRestypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (批量修改客户资源 - - 存储过程改造)
 * @since JDK 1.8
 */
@Service("cmConsultRestypeService")
public class CmConsultRestypeServiceImpl implements CmConsultRestypeService {

    @Autowired
    private ConscustMapper cmConsultRestypeMapper;

    /**
     * @description:(批量修改客户资源--业务逻辑处理)
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:18
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void proBatchUpdatePrebookRestype() {
        // 查询昨天分配过的高端客户，且资源类型不是空的
        List<CmConsultRestype> cmConsultRestypes = cmConsultRestypeMapper.listCmConsultRestype();
        // 更新昨日分配的高端客户数据
        if (!CollectionUtils.isEmpty(cmConsultRestypes)) {
            batchUpdateCmConsultRestypes(cmConsultRestypes);
        }

        // 查询客户资源字段为空的，正常处理资源类型
        List<CmConsultRestype> cmConsultRestypes1 = cmConsultRestypeMapper.listCmConsultRestype1();
        // 批量更新查询出来的高端客户数据
        if (!CollectionUtils.isEmpty(cmConsultRestypes1)) {
            batchUpdateCmConsultRestypes(cmConsultRestypes1);
        }

    }

    /**
     * @description:(更新查询出来的数据)
     * @params cmConsultRestypes
     * @author: fanchao.xu
     * @date: 2023/5/4 17:36
     * @since JDK 1.8
     */
    private void batchUpdateCmConsultRestypes(List<CmConsultRestype> cmConsultRestypes) {
        // 未划转过的客户号
        List<String> conscustnoList1 = new ArrayList<>();
        // 划转过的客户号
        List<String> conscustnoList2 = new ArrayList<>();
        // 投顾资源划转过的潜在客户
        List<String> conscustnoList3 = new ArrayList<>();
        // 公司资源的客户
        List<String> conscustnoList4 = new ArrayList<>();
        // 查询出来有昨日分配过的高端客户的数据
        if (!CollectionUtils.isEmpty(cmConsultRestypes)) {
            cmConsultRestypes.forEach(it -> {
                String restype = it.getRestype();
                String moddt = it.getModdt();
                // 投顾资源未划转过
                if (CmConsuctType.ADVISORY_RESOURCE_NO_TRANS.equals(restype) && null == moddt) {
                    conscustnoList1.add(it.getConscutno());
                }
                // 投顾资源划转过成交客户
                else if (CmConsuctType.ADVISORY_RESOURCE_NO_TRANS.equals(it.getRestype()) && CmConsuctType.IS_GDCJLABEL.equals(it.getGdcjlabel())) {
                    conscustnoList2.add(it.getConscutno());
                }
                // 投顾资源划转过潜在客户
                else if (CmConsuctType.ADVISORY_RESOURCE_NO_TRANS.equals(it.getRestype())) {
                    conscustnoList3.add(it.getConscutno());
                } else {
                    conscustnoList4.add(it.getConscutno());
                }
            });

            // 对应数据进行批量更新操作
            // 把客户的资源状态改成  投顾资源类型(无划转)
            updateConscustResType(CmConsuctType.ADVISORY_RESOURCE_NO_TRANS, conscustnoList1);
            // 把客户的资源状态改成  投顾资源类型 (划转--成交)
            updateConscustResType(CmConsuctType.ADVISORY_RESOURCE_TRANS_DEAL, conscustnoList2);
            // 把客户的资源状态改成  投顾资源类型 (划转--潜在)
            updateConscustResType(CmConsuctType.ADVISORY_RESOURCE_TRANS_POTENTIAL, conscustnoList3);
            // 把客户的资源状态改成 公司资源
            updateConscustResType(CmConsuctType.COMPANY_RESOURCE, conscustnoList4);
        }

    }


    /**
     * @description:(批量更新数据)
     * @author: fanchao.xu
     * @date: 2023/5/4 15:41
     * @since JDK 1.8
     */
    private void updateConscustResType(String resType, List<String> conscustnoList) {
        if (!CollectionUtils.isEmpty(conscustnoList)) {
            cmConsultRestypeMapper.updateConscustResType(resType, conscustnoList);
        }
    }
}
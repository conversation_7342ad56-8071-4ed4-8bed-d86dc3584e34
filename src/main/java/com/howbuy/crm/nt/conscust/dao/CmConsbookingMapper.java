package com.howbuy.crm.nt.conscust.dao;

import com.howbuy.crm.nt.conscust.domain.CmConsbookingInfo;
import org.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 * @Description: 预约沟通记录
 * @reason:
 * @Date: 2020/8/10 17:38
 */
@MapperScan
public interface CmConsbookingMapper {
    /**
     * @Title: insertSelective 新增预约沟通记录
     * @Author: yu.zhang
     * @DateTime: 2023/2/1 11:15
     * @param: [record]
     * @return int
     * @throws 
     */
    int insertSelective(CmConsbookingInfo record);
}
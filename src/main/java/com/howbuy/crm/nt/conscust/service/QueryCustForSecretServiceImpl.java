package com.howbuy.crm.nt.conscust.service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.cache.CacheConstants;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.request.QueryCustForSecretRequest;
import com.howbuy.crm.nt.conscust.response.QueryCustForSecretResponse;
import com.howbuy.crm.util.PinYinUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/8/10 17:38
 */
@Service("queryCustForSecretService")
public class QueryCustForSecretServiceImpl implements QueryCustForSecretService {

    @Autowired
    private ConscustMapper conscustMapper;

    @Override
    public QueryCustForSecretResponse execute(QueryCustForSecretRequest request){
        QueryCustForSecretResponse response = new QueryCustForSecretResponse();
        if(StringUtil.isNullStr(request.getSearchType()) || StringUtil.isNullStr(request.getSearchText()) || StringUtil.isNullStr(request.getUserId())){
            response.paramError("参数不能为空");
            return response;
        }
        Map param = new HashMap(3);
        if(StaticVar.ONE.equals(request.getSearchType())) {
            param.put("custName", request.getSearchText());
            param.put("pinYin", PinYinUtil.getHanYuPinYin(request.getSearchText()));
        }
        if(StaticVar.TWO.equals(request.getSearchType())) {
            param.put("mobile", DigestUtil.digest(request.getSearchText()));
        }
        param.put("consCode", request.getUserId());
        List<String> list = conscustMapper.queryCustForSecret(param);
        if(CollectionUtils.isNotEmpty(list)){
            if (list.size() > 1) {
                String key = request.getUserId() + DateTimeUtil.getCurDateTime();
                CacheServiceImpl.getInstance().put(CacheConstants.CONSCUST_FOR_SECRET_CACHE_PREFIX + key, list);
                response.setCustListId(key);
            } else {
                response.setConscustno(list.get(0));
            }
            response.setDataSize(list.size());
        }else {
            response.setDataSize(0);
        }
        response.success();
        return response;
    }

    @Override
    public QueryCustForSecretResponse query(QueryCustForSecretRequest queryCustForSecretRequest) {
        QueryCustForSecretResponse response = new QueryCustForSecretResponse();

        if (queryCustForSecretRequest == null || StringUtils.isEmpty(queryCustForSecretRequest.getKey())) {
            response.paramError("查询缓存的key：userId不能为空");
            return response;
        }

        List<String> list = CacheServiceImpl.getInstance().get(CacheConstants.CONSCUST_FOR_SECRET_CACHE_PREFIX + queryCustForSecretRequest.getKey());
        response.setCustNoList(list);

        response.success();
        return response;
    }

}

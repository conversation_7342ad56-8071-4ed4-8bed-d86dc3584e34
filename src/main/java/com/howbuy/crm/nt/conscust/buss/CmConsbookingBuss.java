package com.howbuy.crm.nt.conscust.buss;

import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.conference.domain.CsCommunicateVisit;
import com.howbuy.crm.nt.conscust.dao.CmConsbookingMapper;
import com.howbuy.crm.nt.conscust.domain.CmConsbookingInfo;
import crm.howbuy.base.constants.ProcessStatus;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description: crm-nt 预约沟通记录
 * date: 2023/1/31 17:16
 * luthor: yu.zhang
 * version: 1.0
 */
@Component
@Slf4j
public class CmConsbookingBuss {

    @Autowired
    private CmConsbookingMapper cmConsbookingMapper;

    @Autowired
    private CsCommunicateVisitBuss csCommunicateVisitBuss;

    @Autowired
    private CommonDao commondao;

    /**
     * @return void
     * @throws
     * @Title: insertCmConsbooking 新增预约沟通记录，填充时间戳和创建时间
     * @Author: yu.zhang
     * @DateTime: 2023/1/31 17:24
     * @param: [record,communicateVisitId]
     */
    public void insertCmConsbooking(CmConsbookingInfo record, String communicateVisitId) {
        record.setConsbookingid(commondao.getSeqValue("SEQ_PCUSTREC"));
        record.setCredt(DateTimeUtil.getCurrYMD());
        record.setBookingcons(record.getCreator());
        record.setBookingstatus(ProcessStatus.Unprocess.getValue());
        record.setStimestamp(new java.sql.Timestamp(System.currentTimeMillis()));
        cmConsbookingMapper.insertSelective(record);

        //修改沟通记录
        if (StringUtils.isNotEmpty(communicateVisitId)) {
            CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();

            csCommunicateVisit.setId(communicateVisitId);
            // 预约新增时间设置成下次拜访时间
            csCommunicateVisit.setNextDt(record.getBookingdt());
            csCommunicateVisit.setNextStartTime(record.getBookingstarttime());
            csCommunicateVisit.setNextEndTime(record.getBookingendtime());
            csCommunicateVisit.setNextVisitContent(record.getContent());
            csCommunicateVisit.setNextVisitType(record.getVisittype());
            csCommunicateVisitBuss.updateCsCommunicateVisit(csCommunicateVisit);
        }
    }
}

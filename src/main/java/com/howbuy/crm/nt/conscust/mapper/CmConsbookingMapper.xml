<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conscust.dao.CmConsbookingMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conscust.domain.CmConsbookingInfo">
    <result column="CONSBOOKINGID" jdbcType="VARCHAR" property="consbookingid" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="BOOKINGSTATUS" jdbcType="VARCHAR" property="bookingstatus" />
    <result column="BOOKINGCONS" jdbcType="VARCHAR" property="bookingcons" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="BOOKINGDT" jdbcType="VARCHAR" property="bookingdt" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="BOOKINGSTARTTIME" jdbcType="VARCHAR" property="bookingstarttime" />
    <result column="BOOKINGENDTIME" jdbcType="VARCHAR" property="bookingendtime" />
    <result column="VISITTYPE" jdbcType="VARCHAR" property="visittype" />
    <result column="VISITCLASSIFY" jdbcType="VARCHAR" property="visitclassify" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.conscust.domain.CmConsbookingInfo">
    insert into CM_CONSBOOKINGCUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="consbookingid != null">CONSBOOKINGID,</if>
      <if test="conscustno != null">CONSCUSTNO,</if>
      <if test="bookingstatus != null">BOOKINGSTATUS,</if>
      <if test="bookingcons != null">BOOKINGCONS,</if>
      <if test="content != null">CONTENT,</if>
      <if test="bookingdt != null">BOOKINGDT,</if>
      <if test="creator != null">CREATOR,</if>
      <if test="credt != null">CREDT,</if>
      <if test="moddt != null">MODDT,</if>
      <if test="stimestamp != null">STIMESTAMP,</if>
      <if test="bookingstarttime != null">BOOKINGSTARTTIME,</if>
      <if test="bookingendtime != null">BOOKINGENDTIME,</if>
      <if test="visittype != null">VISITTYPE,</if>
      <if test="visitclassify != null">VISITCLASSIFY,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="consbookingid != null">#{consbookingid,jdbcType=VARCHAR},</if>
      <if test="conscustno != null">#{conscustno,jdbcType=VARCHAR},</if>
      <if test="bookingstatus != null">#{bookingstatus,jdbcType=VARCHAR},</if>
      <if test="bookingcons != null">#{bookingcons,jdbcType=VARCHAR},</if>
      <if test="content != null">#{content,jdbcType=VARCHAR},</if>
      <if test="bookingdt != null">#{bookingdt,jdbcType=VARCHAR},</if>
      <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
      <if test="credt != null">#{credt,jdbcType=VARCHAR},</if>
      <if test="moddt != null">#{moddt,jdbcType=VARCHAR},</if>
      <if test="stimestamp != null">#{stimestamp,jdbcType=TIMESTAMP},</if>
      <if test="bookingstarttime != null">#{bookingstarttime,jdbcType=VARCHAR},</if>
      <if test="bookingendtime != null">#{bookingendtime,jdbcType=VARCHAR},</if>
      <if test="visittype != null">#{visittype,jdbcType=VARCHAR},</if>
      <if test="visitclassify != null">#{visitclassify,jdbcType=VARCHAR},</if>
    </trim>
  </insert>
</mapper>
package com.howbuy.crm.nt.conscust.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * CM_CONSBOOKINGCUST
 * <AUTHOR>
@Data
public class CmConsbookingInfo implements Serializable {
    private String consbookingid;

    private String conscustno;

    /**
     * 预约状态
     */
    private String bookingstatus;

    /**
     * 预约投顾
     */
    private String bookingcons;

    /**
     * 预约内容
     */
    private String content;

    /**
     * 预约日期
     */
    private String bookingdt;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 记录创建日期
     */
    private String credt;

    /**
     * 记录修改日期
     */
    private String moddt;

    /**
     * 时间戳
     */
    private Date stimestamp;

    /**
     * 预约开始时段
     */
    private String bookingstarttime;

    /**
     * 预约结束时段
     */
    private String bookingendtime;

    /**
     * 预约方式{"5":"投顾去电","1":"客户来电","4":"投顾去访","0":"客户来访","2":"电子邮件","3":"短信"}
     */
    private String visittype;

    /**
     * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
     */
    private String visitclassify;

    private static final long serialVersionUID = 1L;
}
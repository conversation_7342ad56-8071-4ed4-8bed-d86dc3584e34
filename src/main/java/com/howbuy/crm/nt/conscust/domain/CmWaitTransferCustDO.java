package com.howbuy.crm.nt.conscust.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description 待划转客户信息
 * @Date 2024/3/13 17:13
 */
@Getter
@Setter
public class CmWaitTransferCustDO implements Serializable {
    private static final long serialVersionUID = -6728111992707491757L;

    /**
     * 投顾客户号
     */
    private String consCustNo;
    /**
     * 所属中心代码
     */
    private String centerOrgCode;
    /**
     * 状态 1:待审核2：待复核
     */
    private String state;
    /**
     * 投顾编码
     */
    private String consCode;
    /**
     * 客户姓名
     */
    private String custName;
}

package com.howbuy.crm.nt.conscust.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * CS_VISIT_NEWEST
 * <AUTHOR>
 */
@Data
public class CsVisitNewestInfo implements Serializable {
    /**
     * 主键编号
     */
    private String id;

    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 沟通内容
     */
    private String commcontent;

    /**
     * 投顾预约编码
     */
    private String consbookingid;

    /**
     * 拜访方式 1:电话, 2:见面, 3:参会, 4:短信, 5:邮件, 6:微信
     */
    private String visittype;

    /**
     * 拜访分类 {0:正常客户,1:失联客户,2:黑名单,3:屏蔽客户}
     */
    private String visitclassify;

    /**
     * 可修改标识 1:内容可编辑, 0:内容不可编辑
     */
    private String modifyflag;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建日期
     */
    private Date credt;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改日期
     */
    private Date moddt;

    /**
     * 时间戳
     */
    private Date stimestamp;

    /**
     * 历史数据标识 1:沟通记录表, 0:拜访表
     */
    private String hisflag;

    /**
     * 历史数据主键编号
     */
    private String hisid;

    private static final long serialVersionUID = 1L;
}
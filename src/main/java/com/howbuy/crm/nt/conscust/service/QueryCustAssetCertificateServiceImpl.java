package com.howbuy.crm.nt.conscust.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusFacade;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusRequest;
import com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusResponse;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusFacade;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusRequest;
import com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusResponse;
import com.howbuy.cc.center.feature.asset.request.QueryCurrentAssetCertificateStatusRequest;
import com.howbuy.cc.center.feature.asset.response.QueryCurrentAssetCertificateStatusResponse;
import com.howbuy.cc.center.feature.asset.service.QueryCurrentAssetCertificateStatusService;
import com.howbuy.crm.nt.conscust.dto.QueryCustAssetCertificateDomain;
import com.howbuy.crm.nt.conscust.request.QueryCustAssetCertificateRequest;
import com.howbuy.crm.nt.conscust.response.QueryCustAssetCertificateResponse;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/4/13 11:18
 */
@Service("queryCustAssetCertificateService")
public class QueryCustAssetCertificateServiceImpl implements QueryCustAssetCertificateService {

    private static final Logger log = LoggerFactory.getLogger(QueryCustAssetCertificateServiceImpl.class);

    @Autowired
    private QueryCurrentAssetCertificateStatusService queryCurrentAssetCertificateStatusService;
    @Autowired
    private QueryAssetManagementCertificateStatusFacade queryAssetManagementCertificateStatusFacade;
    @Autowired
    private QueryCustAssetCertificateStatusFacade queryCustAssetCertificateStatusFacade;

    @Override
    public QueryCustAssetCertificateResponse execute(QueryCustAssetCertificateRequest request) {
        QueryCustAssetCertificateResponse response = new QueryCustAssetCertificateResponse();
        String hboneno = request.getHboneno();
        if(StringUtil.isNullStr(hboneno)) {
            log.error("一账通为空");
            response.paramError("一账通为空");
            return response;
        }
        QueryCustAssetCertificateDomain queryCustAssetCertificateDomain = new QueryCustAssetCertificateDomain();
        //查询资产证明审核状态
        QueryCurrentAssetCertificateStatusRequest queryCurrentAssetReq = new QueryCurrentAssetCertificateStatusRequest();
        queryCurrentAssetReq.setHboneNo(hboneno);
        log.info("QueryCurrentAssetCertificateStatusRequest:"+ JSON.toJSONString(queryCurrentAssetReq));
        QueryCurrentAssetCertificateStatusResponse queryCurrentAssetResponse = queryCurrentAssetCertificateStatusService.execute(queryCurrentAssetReq);
        log.info("QueryCurrentAssetCertificateStatusResponse:"+JSON.toJSONString(queryCurrentAssetResponse));
        if (CrmNtConstant.RMISucc.equals(queryCurrentAssetResponse.getReturnCode())) {
            queryCustAssetCertificateDomain.setAssetCertAuditStatus(queryCurrentAssetResponse.getLatestCertificateStatus());
        }
        //查询资产证明信息(私募)
        QueryCustAssetCertificateStatusRequest queryCustAssetCertificateStatusRequest = new QueryCustAssetCertificateStatusRequest();
        queryCustAssetCertificateStatusRequest.setHboneNo(hboneno);
        log.info("QueryCustAssetCertificateStatusRequest:"+JSON.toJSONString(queryCustAssetCertificateStatusRequest));
        QueryCustAssetCertificateStatusResponse queryCustAssetCertificateStatusResponse = queryCustAssetCertificateStatusFacade.execute(queryCustAssetCertificateStatusRequest);
        log.info("QueryCustAssetCertificateStatusResponse:"+JSON.toJSONString(queryCustAssetCertificateStatusResponse));

        if (CrmNtConstant.RMISuccNew.equals(queryCustAssetCertificateStatusResponse.getReturnCode())) {
            queryCustAssetCertificateDomain.setPrivateStatus(queryCustAssetCertificateStatusResponse.getStatus());
            queryCustAssetCertificateDomain.setPrivateAssetCertStatus(queryCustAssetCertificateStatusResponse.getAssetCertStatus());
            queryCustAssetCertificateDomain.setPrivateIncomeCertStatus(queryCustAssetCertificateStatusResponse.getIncomeCertStatus());
            queryCustAssetCertificateDomain.setPrivateAssetAmount(queryCustAssetCertificateStatusResponse.getAssetAmount());
            queryCustAssetCertificateDomain.setPrivateAssetCertDt(queryCustAssetCertificateStatusResponse.getEndDate());
        }

        //查询资产证明信息(资管)
        QueryAssetManagementCertificateStatusRequest queryCurrentAssetManagementReq = new QueryAssetManagementCertificateStatusRequest();
        queryCurrentAssetManagementReq.setHboneNo(hboneno);

        log.info("QueryAssetManagementCertificateStatusRequest:"+JSON.toJSONString(queryCurrentAssetManagementReq));
        QueryAssetManagementCertificateStatusResponse queryAssetManagementResponse = queryAssetManagementCertificateStatusFacade.execute(queryCurrentAssetManagementReq);
        log.info("QueryAssetManagementCertificateStatusResponse:"+JSON.toJSONString(queryAssetManagementResponse));

        if (CrmNtConstant.RMISuccNew.equals(queryAssetManagementResponse.getReturnCode())) {
            queryCustAssetCertificateDomain.setManageStatus(queryAssetManagementResponse.getStatus());
            queryCustAssetCertificateDomain.setManageIncomeCertStatus(queryAssetManagementResponse.getIncomeCertStatus());
            queryCustAssetCertificateDomain.setManageInvestExpCertStatus(queryAssetManagementResponse.getInvestExpCertStatus());
            queryCustAssetCertificateDomain.setManageAssetCertStatus(queryAssetManagementResponse.getAssetCertStatus());
            queryCustAssetCertificateDomain.setManageAssetAmount(queryAssetManagementResponse.getAssetAmount());
            queryCustAssetCertificateDomain.setManageAssetDt(queryAssetManagementResponse.getEndDate());
        }
        response.setQueryCustAssetCertificateDomain(queryCustAssetCertificateDomain);
        response.success();
        return response;
    }
}

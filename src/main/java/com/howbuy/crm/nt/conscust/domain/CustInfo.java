package com.howbuy.crm.nt.conscust.domain;

import java.io.Serializable;

public class CustInfo implements Serializable {

		private static final long serialVersionUID = 1L;

		private String mobile;
		
		private String mobileDigest;
		
		private String mobileMask;
		
		private String mobileCipher;

		private String email;
		
		private String emailDigest;
		
		private String emailMask;
		
		private String emailCipher;
		
		private String pinYin;

		/**
		 *  投资者名称
		 */
		private String custName;

		/**
		 * 公募客户号
		 */
		private String pubcustNo;

		/**
		 * 投顾客户号
		 */
		private String conscustNo;

		/**
		 * 一账通号
		 */
		private String hboneNo;

		public String getMobile() {
			return mobile;
		}

		public void setMobile(String mobile) {
			this.mobile = mobile;
		}

		public String getEmail() {
			return email;
		}

		public void setEmail(String email) {
			this.email = email;
		}

		public String getCustName() {
			return custName;
		}

		public void setCustName(String custName) {
			this.custName = custName;
		}

		public String getPubcustNo() {
			return pubcustNo;
		}

		public void setPubcustNo(String pubcustNo) {
			this.pubcustNo = pubcustNo;
		}

		public String getConscustNo() {
			return conscustNo;
		}

		public void setConscustNo(String conscustNo) {
			this.conscustNo = conscustNo;
		}
		
		public String getHboneNo() {
			return hboneNo;
		}

		public void setHboneNo(String hboneNo) {
			this.hboneNo = hboneNo;
		}

		public static long getSerialversionuid() {
			return serialVersionUID;
		}

		public String getMobileDigest() {
			return mobileDigest;
		}

		public void setMobileDigest(String mobileDigest) {
			this.mobileDigest = mobileDigest;
		}

		public String getMobileMask() {
			return mobileMask;
		}

		public void setMobileMask(String mobileMask) {
			this.mobileMask = mobileMask;
		}

		public String getMobileCipher() {
			return mobileCipher;
		}

		public void setMobileCipher(String mobileCipher) {
			this.mobileCipher = mobileCipher;
		}

		public String getEmailDigest() {
			return emailDigest;
		}

		public void setEmailDigest(String emailDigest) {
			this.emailDigest = emailDigest;
		}

		public String getEmailMask() {
			return emailMask;
		}

		public void setEmailMask(String emailMask) {
			this.emailMask = emailMask;
		}

		public String getEmailCipher() {
			return emailCipher;
		}

		public void setEmailCipher(String emailCipher) {
			this.emailCipher = emailCipher;
		}

		public String getPinYin() {
			return pinYin;
		}

		public void setPinYin(String pinYin) {
			this.pinYin = pinYin;
		}

}

package com.howbuy.crm.nt.conscust.dao;


import com.howbuy.crm.nt.conscust.domain.CsVisitNewestInfo;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * <AUTHOR>
 * @Description: 最新拜访表
 * @reason:
 * @Date: 2020/8/10 17:38
 */
@MapperScan
public interface CsVisitNewestMapper {
    int deleteByPrimaryKey(String id);

    int deleteByConscustNo(@Param("conscustno") String conscustno);

    int insertSelective(CsVisitNewestInfo record);

    CsVisitNewestInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CsVisitNewestInfo record);
}
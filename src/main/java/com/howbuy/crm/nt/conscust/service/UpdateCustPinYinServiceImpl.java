package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.CustInfo;
import com.howbuy.crm.util.PinYinUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/8/6 9:50
 */
@Slf4j
@Service("updateCustPinYinService")
public class UpdateCustPinYinServiceImpl implements UpdateCustPinYinService {

    @Autowired
    private ConscustMapper conscustMapper;

    private int pageSize = 1000;

    @Override
    public String execute(String arg) {
        Map<String, Object> param = new HashMap<>();
        String startConscustNo = "0";
        while (true) {
            param.put("conscustno", startConscustNo);
            param.put("pageSize", pageSize);
            List<CustInfo> custInfos = null;
            try {
                custInfos = conscustMapper.listCustNameForPinYin(param);
                List<CustInfo> insertCustInfos = new ArrayList<>(custInfos.size());
                if (CollectionUtils.isNotEmpty(custInfos)) {
                    startConscustNo = custInfos.get(custInfos.size() - 1).getConscustNo();
                    for (CustInfo custInfo : custInfos) {
                        if (StringUtil.isNotNullStr(custInfo.getCustName())) {
                            try {
                                String pinyin = PinYinUtil.getHanYuPinYin(custInfo.getCustName());
                                //拼音有变化则更新
                                if(!pinyin.equals(custInfo.getPinYin())){
                                    custInfo.setPinYin(pinyin);
                                    insertCustInfos.add(custInfo);
                                }
                            }catch (Exception e){
                                log.error(e.getMessage(), e);
                            }
                        }
                    }
                    if(insertCustInfos.size() > 0) {
                        conscustMapper.updateCustPinYin(insertCustInfos);
                    }
                }
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
            if(custInfos == null || custInfos.size() < pageSize){
                break;
            }
        }
        return "success";
    }
}

package com.howbuy.crm.nt.conscust.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.crm.nt.conscust.buss.QueryCustInfoListForCCBuss;
import com.howbuy.crm.nt.conscust.request.QueryCustInfoListForCCRequest;
import com.howbuy.crm.nt.conscust.response.QueryCustInfoListForCCResponse;

@Service("queryCustInfoListForCCService")
public class QueryCustInfoListForCCServiceImpl implements QueryCustInfoListForCCService {
	@Autowired
	private QueryCustInfoListForCCBuss queryCustInfoListForCCBuss;

	@Override
	public QueryCustInfoListForCCResponse queryCustInfoList(QueryCustInfoListForCCRequest request) {
		return queryCustInfoListForCCBuss.queryCustInfoListForCC(request);
	}
	

}
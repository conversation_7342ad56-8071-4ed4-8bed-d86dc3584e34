package com.howbuy.crm.nt.conscust.dao;

import com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustDO;
import com.howbuy.crm.nt.conscust.domain.ConsCustSurveryRecDiffDO;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.conscust.domain.CustInfo;

import com.howbuy.crm.nt.consultant.dto.CmConsultRestype;
import crm.howbuy.base.db.CommPageBean;
import org.mybatis.spring.annotation.MapperScan;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@MapperScan
public interface ConscustMapper{

	List<ConscustInfo> listConsCustInfo(Map<String, Object> params);

	List<ConscustInfo> listConsCustInfoShow(Map<String, Object> params);

	List<ConscustInfo> listConsCustInfoShowAll(Map<String, Object> params);

	Integer listConsCustInfoCount(Map<String, Object> params);

	List<ConscustInfo> listConsCustInfoByHboneNo(Map<String, String> params);
	List<CustInfo> listCustInfoByConscustNoAndHboneNoList(@Param("conscustNoList")List<String> conscustNoList, @Param("hboneNoList")List<String> hboneNoList);

	/**
	 * 查询客户以更新拼音:
	 * @param params
	* <AUTHOR>
	* @date 2020/8/6
	*/
	List<CustInfo> listCustNameForPinYin(Map<String, Object> params);

	List<CustInfo> listCustInfoForSync();


	/**
	 * 更新拼音:
	 * @param custInfos
	* <AUTHOR>
	* @date 2020/8/6
	*/
	void updateCustPinYin(@Param("custInfos")List<CustInfo> custInfos);

	/**
	 * 投顾小秘查询客户接口:
	 * @param param
	 * <AUTHOR>
	 * @date 2020/8/7
	 */
	List<String> queryCustForSecret(Map<String, Object> param);


	/**
	 * 根据mobileDigest查询客户列表
	 * @param mobileDigest
	 * @return
	 */
	List<ConscustInfo> listConsCustInfoByMobile(@Param("mobileDigest") String mobileDigest);
	
	/**
	 * 定时清理人工修改的客户来源类型
	 * @param params
	 */
	void batchUpdateCustRgSourceLog(Map<String, Object> params);

	/**
	 * 查询昨天分配过的高端客户，且资源类型不是空的
	 *
	 * @return
	 */
	List<CmConsultRestype> listCmConsultRestype();

	/**
	 * 查询客户资源字段为空的，正常处理资源类型
	 * @return
	 */
	List<CmConsultRestype> listCmConsultRestype1();

	/**
	 * 批量更新
	 *
	 * @param type
	 * @param conscustnoList
	 */
	void updateConscustResType(@Param("type") String type, @Param("list") List<String> conscustnoList);

	/**
	 * @description:(根据一账通号获取投顾客户号)
	 * @param hboneNo
	 * @return java.lang.String
	 * @author: xufanchao
	 * @date: 2023/7/21 22:42
	 * @since JDK 1.8
	 */
	String selectConscustNoByHboneNo(@Param("hboneNo") String hboneNo);

	/**
	 * @description  全量查询CM_CONSCUSTSURVEYREC
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/26 上午10:13
	 * @since JDK 1.8
	 */
	List<String> selectCustNosFromSurveyRec();

	/**
	 * @description 根据custNo查询custName
	 * @param custNo
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/26 上午10:14
	 * @since JDK 1.8
	 */
	String getCustNameByCustNo(@Param("custNo")String custNo);

	/**
	 * @description 批量更新CM_CONSCUSTSURVEYREC.CUSTNAME
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/26 上午10:16
	 * @since JDK 1.8
	 */
	int updateCustNameInSurveyRec(@Param("params") List<Map<String, String>> params);

	List<ConsCustSurveryRecDiffDO> getSurveryRecDiffCustName();

	int updateSurveyRecCustNameByConcCustNo(@Param("consCustNo") String consCustNo, @Param("custName")String custName);

	/**
	 * @description: 购买产品和曾购买产品全部清0
	 * @return int 更新条数
	 * @author: jin.wang03
	 * @date: 2023/11/20 10:27
	 * @since JDK 1.8
	 */
	int zeroingBuyProd();

	/**
	 * @description: 更新购买产品
	 * @param index 购买产品重置为16位 '0000000000000000' 后，将指定下标的位置改为1，表示购买了该类产品
	 * @param custNoList 客户号列表
	 * @return int 更新条数
	 * @author: jin.wang03
	 * @date: 2023/11/20 10:55
	 * @since JDK 1.8
	 */
	int updateBuyingProdByCustNoList(@Param("index") int index, @Param("custNoList") List<String> custNoList);

	/**
	 * @description: 更新曾购买产品
	 * @param index 曾购买产品重置为16位 '0000000000000000' 后，将指定下标的位置改为1，表示曾购买了该类产品
	 * @param partitionList 客户号列表
	 * @return int 更新条数
	 * @author: jin.wang03
	 * @date: 2023/11/20 13:27
	 * @since JDK 1.8
	 */
	int updateBuyedProdByCustNoList(@Param("index") int index, @Param("custNoList") List<String> partitionList);

	/**
	 * @description: 批量更新最近一次高净值交易日期
	 * @param conscustList 客户信息列表
	 * @return void
	 * @author: jin.wang03
	 * @date: 2023/11/21 13:17
	 * @since JDK 1.8
	 */
	void batchUpdateLatestTradeDtByConscustNo(List<ConscustInfo> conscustList);

	/**
	 * @description: 查询CM_CONSCUST表所有的记录数
	 * @return int 记录数
	 * @author: jin.wang03
	 * @date: 2023/11/29 10:40
	 * @since JDK 1.8
	 */
	int countConscust();

	/**
	 * @description: 查询CM_CONSCUST表中最小的custscustNo
	 * @return String 最小的custscustNo
	 * @author: jin.wang03
	 * @date: 2023/11/29 10:46
	 * @since JDK 1.8
	 */
	String getMinCustNo();

	/**
	 * @description: 查询CM_CONSCUST表中正序排序，指定行数的custscustNo
	 * @param index 正序排序下标
	 * @return java.lang.String 指定行数的custscustNo
	 * @author: jin.wang03
	 * @date: 2023/11/29 11:01
	 * @since JDK 1.8
	 */
	String getCustNoByAscIndex(@Param("index") int index);


	/**
	 * @description: 购买产品和曾购买产品全部清0
	 * @return int 更新条数
	 * @author: jin.wang03
	 * @date: 2023/11/20 10:27
	 * @since JDK 1.8
	 */
	int zeroingBuyProdBetweenCustNo(@Param("startCustNo") String startCustNo, @Param("endCustNo") String endCustNo);

	/**
	 * 根据审核状态 & 所属中心查询 待划转客户列表
	 * @return List<CmWaitTransferCustDO> 待划转客户列表
	 */
	List<CmWaitTransferCustDO> listCmWaitTransferCustListByPage(@Param("param") Map<String, Object> param, @Param("page") CommPageBean pageBean);
}
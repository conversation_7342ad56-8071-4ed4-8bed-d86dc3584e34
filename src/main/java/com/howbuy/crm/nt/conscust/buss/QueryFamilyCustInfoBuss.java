package com.howbuy.crm.nt.conscust.buss;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.dao.QueryFamilyCustInfoMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.conscust.domain.QueryFamilyCustInfo;
import com.howbuy.crm.nt.conscust.dto.QueryFamilyCustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryFamilyCustInfoRequset;
import com.howbuy.crm.nt.conscust.response.QueryFamilyCustInfoResponse;

@Component
public class QueryFamilyCustInfoBuss {

	private static final Logger log = LoggerFactory.getLogger(QueryFamilyCustInfoBuss.class);

	@Autowired
	private QueryFamilyCustInfoMapper queryFamilyCustInfoMapper;
	
	@Autowired
    private ConscustMapper conscustMapper;

	public QueryFamilyCustInfoResponse queryFamilyCustInfoByHboneNo(QueryFamilyCustInfoRequset req, boolean queryAllFlag) {
		QueryFamilyCustInfoResponse res = new QueryFamilyCustInfoResponse();
		
		// 获取传入的一账通号
		String hboneNo = req.getHboneNo();
		if (StringUtils.isBlank(hboneNo)) {
			res.invalidReqParams("一账通号不能为空！");
			return res;
		}

		// 将一账通号转换成投顾客户号
		String conscustNo = getConscustNoByHboneNo(hboneNo);
		if (StringUtils.isBlank(conscustNo)) {
			res.noData("该一账通号在CRM中不存在！");
			return res;
		}

		// 获取家庭账户组内成员信息
		Map<String, String> params = new HashMap<String, String>();
		params.put("hboneno", hboneNo);
		List<QueryFamilyCustInfo> listFamilyCustInfo = queryFamilyCustInfoMapper.queryFamilyCustInfo(params);
		if (listFamilyCustInfo != null && listFamilyCustInfo.size() > 0) {
			List<QueryFamilyCustInfoDomain> familyCustList = new ArrayList<QueryFamilyCustInfoDomain>();
			boolean masterCust = false;
			for (QueryFamilyCustInfo queryFamilyCustInfo : listFamilyCustInfo) {
				// 设置当前查询账户是否为家庭主账户
				if (hboneNo.equals(queryFamilyCustInfo.getHboneNo()) && "1".equals(queryFamilyCustInfo.getFcFlag())) {
					masterCust = true;
				}
				
				// 如果不是查询全部的，需要排除入参的那条记录
				if (!queryAllFlag && hboneNo.equals(queryFamilyCustInfo.getHboneNo())) {
					continue;
				}

				QueryFamilyCustInfoDomain familyCustInfoDomain = new QueryFamilyCustInfoDomain();
				BeanUtils.copyProperties(queryFamilyCustInfo, familyCustInfoDomain);
				familyCustList.add(familyCustInfoDomain);
			}
			log.info("familyCustList: " + familyCustList.size());
			res.setMasterCust(masterCust);
			res.setFamilyCustList(familyCustList);
		}
		res.success();
		return res;
	}


	/**
	 * 根据一账通号获取客户号
	 * 
	 * @param hboneNo
	 * @return String
	 */
	public String getConscustNoByHboneNo(String hboneNo) {
		String conscustNo = null;
		try {
			Map<String, String> params = new HashMap<String, String>();
			params.put("hboneno", hboneNo);
			List<ConscustInfo> listCustInfo = conscustMapper.listConsCustInfoByHboneNo(params);
			if (listCustInfo != null && listCustInfo.size() > 0) {
				conscustNo = listCustInfo.get(0).getConscustno();
			}
		} catch (Exception e) {
			log.error(e.getMessage());
		}

		return conscustNo;
	}

}

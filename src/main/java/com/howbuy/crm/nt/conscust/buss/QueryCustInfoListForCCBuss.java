package com.howbuy.crm.nt.conscust.buss;

import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.CustInfo;
import com.howbuy.crm.nt.conscust.dto.CustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryCustInfoListForCCRequest;
import com.howbuy.crm.nt.conscust.response.QueryCustInfoListForCCResponse;

@Component
public class QueryCustInfoListForCCBuss {

	private static final Logger log = LoggerFactory.getLogger(QueryCustInfoListForCCBuss.class);

	@Autowired
    private ConscustMapper conscustMapper;

	/** 投顾客户号查询结果最大限制 */
	public static final int CUSTNO_QUERY_MAX_COUNT = 100;
	/** 一账通查询结果最大限制 */
	public static final int HBONENO_QUERY_MAX_COUNT = 100;

	public QueryCustInfoListForCCResponse queryCustInfoListForCC(QueryCustInfoListForCCRequest req) {
		QueryCustInfoListForCCResponse res = new QueryCustInfoListForCCResponse();
		
		// 获取传入参数
		List<String> listCustNo = req.getListConscustNo();
		List<String> listHboneNo = req.getListHboneNo();
		if ((listCustNo != null && listCustNo.size() > 0) || (listHboneNo != null && listHboneNo.size() > 0)) {
			if(listCustNo != null && listCustNo.size() > CUSTNO_QUERY_MAX_COUNT) {
				res.dealFail("投顾客户号查询参数超过最大限制（每次最多查询100条数据）！");
				return res;
			}
			
			if(listHboneNo != null && listHboneNo.size() > HBONENO_QUERY_MAX_COUNT) {
				res.dealFail("一账通查询参数超过最大限制（每次最多查询100条数据）！");
				return res;
			}
			
			List<CustInfo> listCustInfo = conscustMapper.listCustInfoByConscustNoAndHboneNoList(listCustNo, listHboneNo);
			log.info("-----listCustInfo-----:" + (null==listCustInfo?"空": listCustInfo.toString()) );
			if (listCustInfo != null && listCustInfo.size() > 0) {
				List<CustInfoDomain> custInfoList = new ArrayList<CustInfoDomain>();
				for (CustInfo custInfo : listCustInfo) {
					CustInfoDomain custInfoDomain = new CustInfoDomain();
					BeanUtils.copyProperties(custInfo, custInfoDomain);
					custInfoList.add(custInfoDomain);
				}
				res.setCustInfoList(custInfoList);
			} else {
				res.noData("未找到相关客户信息！");
				return res;
			}
		} else {
			res.invalidReqParams("传入参数不能为空！");
			return res;
		}

		res.success();
		return res;
	}

}

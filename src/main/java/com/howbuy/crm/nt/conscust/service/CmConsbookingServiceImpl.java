package com.howbuy.crm.nt.conscust.service;

import com.howbuy.crm.nt.conscust.buss.CmConsbookingBuss;
import com.howbuy.crm.nt.conscust.domain.CmConsbookingInfo;
import com.howbuy.crm.nt.conscust.request.CmConsbookingRequest;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * description: crm-nt
 * date: 2023/1/31 17:28
 * version: 1.0
 * <AUTHOR>
 */
@Slf4j
@Service("cmConsbookingService")
public class CmConsbookingServiceImpl implements CmConsbookingService {

    @Autowired
    private CmConsbookingBuss cmConsbookingBuss;

    @Override
    public BaseResponse insertCmConsbooking(CmConsbookingRequest request) {

        BaseResponse response = new BaseResponse();

        if (request.getCmConsbooking() == null) {
            response.paramError("参数不能为空");
            return response;
        }

        try {
            CmConsbookingInfo record = new CmConsbookingInfo();
            BeanUtils.copyProperties(request.getCmConsbooking(), record);
            cmConsbookingBuss.insertCmConsbooking(record, request.getCmConsbooking().getCommunicateVisitId());
        } catch (Exception e) {
            response.paramError("cmConsbookingService接口异常" + e.getMessage());
            log.error("cmConsbookingService error{}", e);
        }

        response.success();
        return response;
    }
}

package com.howbuy.crm.nt.conscust.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import crm.howbuy.base.utils.MapRemoveNullUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class QueryConscustInfoBuss {

	private static final Logger log = LoggerFactory.getLogger(QueryConscustInfoBuss.class);
	
	@Autowired
    private ConscustMapper conscustMapper;

	public List<ConscustInfo> listConsCustInfo(String idtype, String idno, String custname, String mobile, String email, String invsttype, String conscustno, String hboneno) {

		List<ConscustInfo> conscustinfolist = null;

		try {

			Map<String, Object> params = new HashMap<String, Object>();
			params.put("hboneno", hboneno);
			params.put("conscustno", conscustno);
			params.put("idtype", idtype);
			params.put("idno", idno);
			params.put("custname", custname);
			params.put("mobile", mobile);
			params.put("email", email);
			params.put("invsttype", invsttype);

			MapRemoveNullUtil.removeNullValue(params);

			conscustinfolist = conscustMapper.listConsCustInfo(params);
			log.info("conscustInfoDao.listConsCustInfo is " + JSON.toJSONString(conscustinfolist));
		}catch(Exception e){
			log.error(e.getMessage());
		}

		return conscustinfolist;
	}

	public List<ConscustInfo> listConsCustInfoByPage(String conscode, String consmobile, String custname,Boolean isfull, Integer page, Integer pagecount) {

		List<ConscustInfo> conscustinfolist = null;

		try {

			Map<String, Object> params = new HashMap<String, Object>();
			params.put("conscode", conscode);
            params.put("consmobile", consmobile);
            params.put("custname", custname);

            if(isfull){
				MapRemoveNullUtil.removeNullValue(params);
				conscustinfolist = conscustMapper.listConsCustInfoShowAll(params);
			}else{
				if(page == null){
					page = 1;
				}

				if(pagecount == null){
					pagecount = 10;
				}

				Integer pagenum = page*pagecount;
				Integer pagenumindex = page*pagecount-pagecount+1;
				params.put("pagenum", pagenum);
				params.put("pagenumindex", pagenumindex);

				MapRemoveNullUtil.removeNullValue(params);
				conscustinfolist = conscustMapper.listConsCustInfoShow(params);
			}

			log.info("conscustInfoDao.listConsCustInfoByPage is " + JSON.toJSONString(conscustinfolist));
		}catch(Exception e){
			log.error(e.getMessage());
		}

		return conscustinfolist;
	}

    public Integer listConsCustInfoCount(String conscode, String consmobile, String custname) {

        Integer custcount = null;

        try {

            Map<String, Object> params = new HashMap<String, Object>();
            params.put("conscode", conscode);
            params.put("consmobile", consmobile);
            params.put("custname", custname);

            MapRemoveNullUtil.removeNullValue(params);

            custcount = conscustMapper.listConsCustInfoCount(params);
            System.out.println(custcount);
        }catch(Exception e){
            log.error(e.getMessage());
        }

        return custcount;
    }

}

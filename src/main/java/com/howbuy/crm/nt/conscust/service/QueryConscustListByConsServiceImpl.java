package com.howbuy.crm.nt.conscust.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.conscust.buss.QueryConscustInfoBuss;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.nt.conscust.request.QueryConscustListByConsRequest;
import com.howbuy.crm.nt.conscust.response.QueryConscustListByConsResponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service("queryConscustListByConsService")
public class QueryConscustListByConsServiceImpl implements QueryConscustListByConsService {

	private static final Logger log = LoggerFactory.getLogger(QueryConscustListByConsServiceImpl.class);
	
	@Autowired
	private QueryConscustInfoBuss queryConscustInfoBuss;

	private boolean checkParams(QueryConscustListByConsRequest request) {
		boolean flag = false;
		if (StringUtils.isEmpty(request.getConscode()) && StringUtils.isEmpty(request.getConsmobile())) {
			flag = true;
		}

		return flag;
	}

	@Override
	public QueryConscustListByConsResponse queryConscustInfoByConsCode(QueryConscustListByConsRequest request) {
		QueryConscustListByConsResponse response = new QueryConscustListByConsResponse();

		log.info("QueryConscustListByConsRequest is " + JSON.toJSONString(request));
		// 参数验证
		if (this.checkParams(request)) {
			response.invalidReqParams("请输入正确查询参数,投顾编码或投顾手机号必传！");

		}else {

            Integer custcount = queryConscustInfoBuss.listConsCustInfoCount(request.getConscode(), request.getConsmobile(), request.getCustname());

            if(custcount > 0){
                response.setCount(custcount);
                List<ConscustInfo> conscustinfolist = queryConscustInfoBuss.listConsCustInfoByPage(request.getConscode(), request.getConsmobile(), request.getCustname(),request.isIsfull(), request.getPage(), request.getPagecount());

                if(conscustinfolist != null && conscustinfolist.size() > 0) {
                    List<ConscustInfoDomain> conscustlist = new ArrayList<ConscustInfoDomain>();

                    for(ConscustInfo conscustInfo:conscustinfolist) {
                        ConscustInfoDomain conscustInfoDomain = new ConscustInfoDomain();
                        BeanUtils.copyProperties(conscustInfo,conscustInfoDomain);
                        conscustlist.add(conscustInfoDomain);
                    }
                    log.info("conscustlist:"+conscustlist.size());
                    response.setConscustlist(conscustlist);
                }

                response.success();
            }else{
                response.putBaseResult(BaseConstantEnum.DATA_NOT_FUND, "未查到数据！");
            }
		}

		return response;
	}
}

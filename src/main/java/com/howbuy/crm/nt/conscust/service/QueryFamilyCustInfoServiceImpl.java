package com.howbuy.crm.nt.conscust.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.crm.nt.conscust.buss.QueryFamilyCustInfoBuss;
import com.howbuy.crm.nt.conscust.request.QueryFamilyCustInfoRequset;
import com.howbuy.crm.nt.conscust.response.QueryFamilyCustInfoResponse;

@Service("queryFamilyCustInfoService")
public class QueryFamilyCustInfoServiceImpl implements QueryFamilyCustInfoService {
	@Autowired
	private QueryFamilyCustInfoBuss queryFamilyCustInfoBuss;

	@Override
	public QueryFamilyCustInfoResponse queryFamilyCustInfoByHboneNo(QueryFamilyCustInfoRequset request) {
		return queryFamilyCustInfoBuss.queryFamilyCustInfoByHboneNo(request, false);
	}

	@Override
	public QueryFamilyCustInfoResponse queryAllFamilyCustInfoByHboneNo(QueryFamilyCustInfoRequset request) {
		return queryFamilyCustInfoBuss.queryFamilyCustInfoByHboneNo(request, true);
	}

}
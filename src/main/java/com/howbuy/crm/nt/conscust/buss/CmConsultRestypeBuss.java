/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conscust.buss;

import com.howbuy.crm.nt.consultant.service.CmConsultRestypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (批量修改客户资源)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmConsultRestypeBuss {
    @Autowired
    private CmConsultRestypeService cmConsultRestypeService;

    public void batchInsertCmMessageLog() {
        // 批量修改客户资源
        try {
            cmConsultRestypeService.proBatchUpdatePrebookRestype();
        } catch (Exception e) {
            log.error("error in cmMessageLogBuss", e);
        }
    }

}
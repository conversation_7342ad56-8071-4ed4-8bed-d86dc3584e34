package com.howbuy.crm.nt.conscust.buss;

import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.conference.dao.CsCommunicateVisitDao;
import com.howbuy.crm.nt.conference.domain.CsCommunicateVisit;
import com.howbuy.crm.nt.conscust.dao.CsVisitNewestMapper;
import com.howbuy.crm.nt.conscust.domain.CsVisitNewestInfo;
import com.howbuy.crm.nt.conscust.response.CsCommunicateVisitResponse;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * description: crm-nt
 * date: 2023/1/31 18:12
 * author: yu.zhang
 * version: 1.0
 */
@Component
@Slf4j
public class CsCommunicateVisitBuss {

    @Autowired
    private CsCommunicateVisitDao csCommunicateVisitDao;

    @Autowired
    private CsVisitNewestMapper csVisitNewestMapper;

    @Autowired
    private CommonDao commondao;

    @Transactional(rollbackFor = Exception.class)
    public void insertCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit, CsCommunicateVisitResponse response){

        // 获取相关id编号
        String id = commondao.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID");
        // 从序列中获取预约编号
        String newConsBookingId = commondao.getSeqValue("SEQ_PCUSTREC");
        // 从序列中获取交易流水号
        String appSerialNo = commondao.getSeqValue("SEQ_PCUSTREC");

        csCommunicateVisit.setId(id);
        csCommunicateVisit.setConsBookingId(newConsBookingId);
        csCommunicateVisit.setHisId(appSerialNo);
        csCommunicateVisit.setHisFlag("0");
        //插入沟通拜访表
        csCommunicateVisitDao.insertCsCommunicateVisit(csCommunicateVisit);

        CsVisitNewestInfo csVisitNewest = new CsVisitNewestInfo();

        csVisitNewest.setId(csCommunicateVisit.getId());
        csVisitNewest.setConscustno(csCommunicateVisit.getConscustNo());
        csVisitNewest.setCommcontent(csCommunicateVisit.getCommContent());
        csVisitNewest.setVisittype(csCommunicateVisit.getVisitType());
        csVisitNewest.setHisid(csCommunicateVisit.getHisId());
        csVisitNewest.setHisflag(csCommunicateVisit.getHisFlag());
        csVisitNewest.setCreator(csCommunicateVisit.getCreator());

        //删除最新拜访表历史记录和新增最新拜访表记录
        csVisitNewestMapper.deleteByConscustNo(csVisitNewest.getConscustno());
        csVisitNewestMapper.insertSelective(csVisitNewest);

        response.setId(id);
    }

    public void updateCsCommunicateVisit(CsCommunicateVisit csCommunicateVisit){
        //插入沟通拜访表
        csCommunicateVisitDao.updateCsCommunicateVisit(csCommunicateVisit);
    }
}

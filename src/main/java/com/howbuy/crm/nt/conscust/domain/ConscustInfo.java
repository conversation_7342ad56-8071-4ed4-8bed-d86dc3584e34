package com.howbuy.crm.nt.conscust.domain;

import java.io.Serializable;
import java.util.Date;

public class ConscustInfo implements Serializable {

	private static final long serialVersionUID = 1L;
	
	/** 投顾客户号 */
    private String conscustno;
	
	/** 投顾客户等级 */
    private String conscustlvl;
	
	/** 投顾客户评分 */
    private Integer conscustgrade;
	
	/** 投顾客户状态 */
    private String conscuststatus;
	
	/** 证件类型 */
    private String idtype;
	
	/** 证件号码 */
    private String idno;
	
	/** 投资者名称 */
    private String custname;
	
	/** 省份代码 */
    private String provcode;
	
	/** 城市代码 */
    private String citycode;
	
	/** 投资者学历 */
    private String edulevel;
	
	/** 投资者职业代码 */
    private String vocation;
	
	/** 投资者年收入 */
    private String inclevel;
	
	/** 投资者生日 */
    private String birthday;
	
	/** 投资者性别 */
    private String gender;
	
	/** 婚否 */
    private String married;
	
	/** 个人年收入 */
    private String pincome;
	
	/** 家庭年收入 */
    private String fincome;
	
	/** 是否投资决策人 */
    private String decisionflag;
	
	/** 兴趣爱好 */
    private String interests;
	
	/** 方便联系时段 */
    private String contacttime;
	
	/** 公司 */
    private String company;
	
	/** 投资者单位电话 */
    private String officetelno;
	
	/** 客户风险承受能力等级 */
    private String risklevel;
	
	/** 自定义风险承受能力等级 */
    private String selfrisklevel;
	
	/** 地址 */
    private String addr;
	
	/** 邮政编码 */
    private String postcode;
	
	/** 投资者手机号码 */
    private String mobile;
	
	/** 联系电话 */
    private String telno;
	
	/** 传真 */
    private String fax;
	
	/** 电子邮箱 */
    private String email;
	
	/** 希望参加的沙龙 */
    private String salon;
	
	/** 之前投资品种 */
    private String beforeinvest;
	
	/** 沟通频率 */
    private String visitfqcy;
	
	/** 地址2 */
    private String addr2;
	
	/** 邮政编码2 */
    private String postcode2;
	
	/** 投资者手机号码2 */
    private String mobile2;
	
	/** 电子邮箱2 */
    private String email2;
	
	/** 知道好买 */
    private String knowhowbuy;

	/** 知道好买细分 */
    private String subknow;

	/** 知道好买具体 */
    private String subknowtype;

	/** 是否特殊客户 */
    private String specialflag;
	
	/** 备注 */
    private String remark;
	
	/** 登记日期 */
    private String regdt;
	
	/** 创建人 */
    private String creator;
	
	/** 联系人姓名 */
    private String linkman;
	
	/** 经办人电话 */
    private String linktel;
	
	/** 经办人手机 */
    private String linkmobile;
	
	/** 经办人电子邮箱 */
    private String linkemail;
	
	/** 经办人邮政编码 */
    private String linkpostcode;
	
	/** 经办人地址 */
    private String linkaddr;
	
	/** GPS投资级别 */
    private String gpsinvestlevel;
	
	/** GPS风险偏好 */
    private String gpsrisklevel;
	
	/** 是否入会 */
    private String isjoinclub;
	
	/** 客户来源备注 */
    private String custsourceremark;
	
	/** 最近一次高净值交易日期 */
    private String latesttradedt;
	
	/** 划转原因(1:RS转RT;2:RS转高端--挖掘;3:RS转高端--客户) */
    private String rstohighreason;
	
	/** 是否公募交易过（0：否；1：是） */
    private String ispubtrade;
	
	/** 机构还是个人（0：购机；1：个人） */
    private String invsttype;
	
	/** 贵宾账号用户名 */
    private String vipusername;
	
	/** 新来源编号 */
    private String newsourceno;
	
	/** 一账通账号 */
    private String hboneno;
	
	/** 证件有限期 */
    private String validity;
	
	/** 证件有限期日期 */
    private String validitydt;
	
	/** 性质 */
    private String nature;
	
	/** 资质 */
    private String aptitude;
	
	/** 经营范围 */
    private String scopebusiness;
	
	/** 新增时间 */
    private Date addtime;
	
	/** 修改时间 */
    private Date updatetime;
	
	/** 投顾编码 */
    private String conscode;

	/** 微信号 */
    private String wechatcode;

	/** 5.2资源类型 */
    private String restype;

	/** 9.3是否是虚拟投顾 */
    private String isvirtual;

	/** 10.0是否在IC和HBC下 */
    private String ishighorg;
	
	/**
	 * 以Digest结尾的是脱敏信息的摘要，以Mask结尾的是掩码，以Cipher结尾的是密文
	 */
	private String idnoDigest;
	private String idnoMask;
	private String idnoCipher;
	
	private String addrDigest;
	private String addrMask;
	private String addrCipher;
	
	private String mobileDigest;
	private String mobileMask;
	private String mobileCipher;
	
	private String telnoDigest;
	private String telnoMask;
	private String telnoCipher;
	
	private String emailDigest;
	private String emailMask;
	private String emailCipher;
	
	private String addr2Digest;
	private String addr2Mask;
	private String addr2Cipher;
	
	private String mobile2Digest;
	private String mobile2Mask;
	private String mobile2Cipher;
	
	private String email2Digest;
	private String email2Mask;
	private String email2Cipher;
	
	private String linkaddrDigest;
	private String linkaddrMask;
	private String linkaddrCipher;
	
	private String linkmobileDigest;
	private String linkmobileMask;
	private String linkmobileCipher;
	
	private String linktelDigest;
	private String linktelMask;
	private String linktelCipher;
	
	private String linkemailDigest;
	private String linkemailMask;
	private String linkemailCipher;

	public String getIdnoDigest() {
		return idnoDigest;
	}

	public void setIdnoDigest(String idnoDigest) {
		this.idnoDigest = idnoDigest;
	}

	public String getIdnoMask() {
		return idnoMask;
	}

	public void setIdnoMask(String idnoMask) {
		this.idnoMask = idnoMask;
	}

	public String getIdnoCipher() {
		return idnoCipher;
	}

	public void setIdnoCipher(String idnoCipher) {
		this.idnoCipher = idnoCipher;
	}

	public String getAddrDigest() {
		return addrDigest;
	}

	public void setAddrDigest(String addrDigest) {
		this.addrDigest = addrDigest;
	}

	public String getAddrMask() {
		return addrMask;
	}

	public void setAddrMask(String addrMask) {
		this.addrMask = addrMask;
	}

	public String getAddrCipher() {
		return addrCipher;
	}

	public void setAddrCipher(String addrCipher) {
		this.addrCipher = addrCipher;
	}

	public String getMobileDigest() {
		return mobileDigest;
	}

	public void setMobileDigest(String mobileDigest) {
		this.mobileDigest = mobileDigest;
	}

	public String getMobileMask() {
		return mobileMask;
	}

	public void setMobileMask(String mobileMask) {
		this.mobileMask = mobileMask;
	}

	public String getMobileCipher() {
		return mobileCipher;
	}

	public void setMobileCipher(String mobileCipher) {
		this.mobileCipher = mobileCipher;
	}

	public String getTelnoDigest() {
		return telnoDigest;
	}

	public void setTelnoDigest(String telnoDigest) {
		this.telnoDigest = telnoDigest;
	}

	public String getTelnoMask() {
		return telnoMask;
	}

	public void setTelnoMask(String telnoMask) {
		this.telnoMask = telnoMask;
	}

	public String getTelnoCipher() {
		return telnoCipher;
	}

	public void setTelnoCipher(String telnoCipher) {
		this.telnoCipher = telnoCipher;
	}

	public String getEmailDigest() {
		return emailDigest;
	}

	public void setEmailDigest(String emailDigest) {
		this.emailDigest = emailDigest;
	}

	public String getEmailMask() {
		return emailMask;
	}

	public void setEmailMask(String emailMask) {
		this.emailMask = emailMask;
	}

	public String getEmailCipher() {
		return emailCipher;
	}

	public void setEmailCipher(String emailCipher) {
		this.emailCipher = emailCipher;
	}

	public String getAddr2Digest() {
		return addr2Digest;
	}

	public void setAddr2Digest(String addr2Digest) {
		this.addr2Digest = addr2Digest;
	}

	public String getAddr2Mask() {
		return addr2Mask;
	}

	public void setAddr2Mask(String addr2Mask) {
		this.addr2Mask = addr2Mask;
	}

	public String getAddr2Cipher() {
		return addr2Cipher;
	}

	public void setAddr2Cipher(String addr2Cipher) {
		this.addr2Cipher = addr2Cipher;
	}

	public String getMobile2Digest() {
		return mobile2Digest;
	}

	public void setMobile2Digest(String mobile2Digest) {
		this.mobile2Digest = mobile2Digest;
	}

	public String getMobile2Mask() {
		return mobile2Mask;
	}

	public void setMobile2Mask(String mobile2Mask) {
		this.mobile2Mask = mobile2Mask;
	}

	public String getMobile2Cipher() {
		return mobile2Cipher;
	}

	public void setMobile2Cipher(String mobile2Cipher) {
		this.mobile2Cipher = mobile2Cipher;
	}

	public String getEmail2Digest() {
		return email2Digest;
	}

	public void setEmail2Digest(String email2Digest) {
		this.email2Digest = email2Digest;
	}

	public String getEmail2Mask() {
		return email2Mask;
	}

	public void setEmail2Mask(String email2Mask) {
		this.email2Mask = email2Mask;
	}

	public String getEmail2Cipher() {
		return email2Cipher;
	}

	public void setEmail2Cipher(String email2Cipher) {
		this.email2Cipher = email2Cipher;
	}

	public String getLinkaddrDigest() {
		return linkaddrDigest;
	}

	public void setLinkaddrDigest(String linkaddrDigest) {
		this.linkaddrDigest = linkaddrDigest;
	}

	public String getLinkaddrMask() {
		return linkaddrMask;
	}

	public void setLinkaddrMask(String linkaddrMask) {
		this.linkaddrMask = linkaddrMask;
	}

	public String getLinkaddrCipher() {
		return linkaddrCipher;
	}

	public void setLinkaddrCipher(String linkaddrCipher) {
		this.linkaddrCipher = linkaddrCipher;
	}

	public String getLinkmobileDigest() {
		return linkmobileDigest;
	}

	public void setLinkmobileDigest(String linkmobileDigest) {
		this.linkmobileDigest = linkmobileDigest;
	}

	public String getLinkmobileMask() {
		return linkmobileMask;
	}

	public void setLinkmobileMask(String linkmobileMask) {
		this.linkmobileMask = linkmobileMask;
	}

	public String getLinkmobileCipher() {
		return linkmobileCipher;
	}

	public void setLinkmobileCipher(String linkmobileCipher) {
		this.linkmobileCipher = linkmobileCipher;
	}

	public String getLinktelDigest() {
		return linktelDigest;
	}

	public void setLinktelDigest(String linktelDigest) {
		this.linktelDigest = linktelDigest;
	}

	public String getLinktelMask() {
		return linktelMask;
	}

	public void setLinktelMask(String linktelMask) {
		this.linktelMask = linktelMask;
	}

	public String getLinktelCipher() {
		return linktelCipher;
	}

	public void setLinktelCipher(String linktelCipher) {
		this.linktelCipher = linktelCipher;
	}

	public String getLinkemailDigest() {
		return linkemailDigest;
	}

	public void setLinkemailDigest(String linkemailDigest) {
		this.linkemailDigest = linkemailDigest;
	}

	public String getLinkemailMask() {
		return linkemailMask;
	}

	public void setLinkemailMask(String linkemailMask) {
		this.linkemailMask = linkemailMask;
	}

	public String getLinkemailCipher() {
		return linkemailCipher;
	}

	public void setLinkemailCipher(String linkemailCipher) {
		this.linkemailCipher = linkemailCipher;
	}

	public String getIsvirtual() {
		return isvirtual;
	}

	public void setIsvirtual(String isvirtual) {
		this.isvirtual = isvirtual;
	}

	public String getOfficetelno() {
		return officetelno;
	}

	public void setOfficetelno(String officetelno) {
		this.officetelno = officetelno;
	}

	public String getConscode() {
		return conscode;
	}

	public void setConscode(String conscode) {
		this.conscode = conscode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getConscustno() {
		return conscustno;
	}

	public void setConscustno(String conscustno) {
		this.conscustno = conscustno;
	}

	public String getConscustlvl() {
		return conscustlvl;
	}

	public void setConscustlvl(String conscustlvl) {
		this.conscustlvl = conscustlvl;
	}

	public Integer getConscustgrade() {
		return conscustgrade;
	}

	public void setConscustgrade(Integer conscustgrade) {
		this.conscustgrade = conscustgrade;
	}

	public String getConscuststatus() {
		return conscuststatus;
	}

	public void setConscuststatus(String conscuststatus) {
		this.conscuststatus = conscuststatus;
	}

	public String getIdtype() {
		return idtype;
	}

	public void setIdtype(String idtype) {
		this.idtype = idtype;
	}

	public String getIdno() {
		return idno;
	}

	public void setIdno(String idno) {
		this.idno = idno;
	}

	public String getCustname() {
		return custname;
	}

	public void setCustname(String custname) {
		this.custname = custname;
	}

	public String getProvcode() {
		return provcode;
	}

	public void setProvcode(String provcode) {
		this.provcode = provcode;
	}

	public String getCitycode() {
		return citycode;
	}

	public void setCitycode(String citycode) {
		this.citycode = citycode;
	}

	public String getEdulevel() {
		return edulevel;
	}

	public void setEdulevel(String edulevel) {
		this.edulevel = edulevel;
	}

	public String getVocation() {
		return vocation;
	}

	public void setVocation(String vocation) {
		this.vocation = vocation;
	}

	public String getInclevel() {
		return inclevel;
	}

	public void setInclevel(String inclevel) {
		this.inclevel = inclevel;
	}

	public String getBirthday() {
		return birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getMarried() {
		return married;
	}

	public void setMarried(String married) {
		this.married = married;
	}

	public String getPincome() {
		return pincome;
	}

	public void setPincome(String pincome) {
		this.pincome = pincome;
	}

	public String getFincome() {
		return fincome;
	}

	public void setFincome(String fincome) {
		this.fincome = fincome;
	}

	public String getDecisionflag() {
		return decisionflag;
	}

	public void setDecisionflag(String decisionflag) {
		this.decisionflag = decisionflag;
	}

	public String getInterests() {
		return interests;
	}

	public void setInterests(String interests) {
		this.interests = interests;
	}

	public String getContacttime() {
		return contacttime;
	}

	public void setContacttime(String contacttime) {
		this.contacttime = contacttime;
	}

	public String getCompany() {
		return company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	public String getRisklevel() {
		return risklevel;
	}

	public void setRisklevel(String risklevel) {
		this.risklevel = risklevel;
	}

	public String getSelfrisklevel() {
		return selfrisklevel;
	}

	public void setSelfrisklevel(String selfrisklevel) {
		this.selfrisklevel = selfrisklevel;
	}

	public String getAddr() {
		return addr;
	}

	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getTelno() {
		return telno;
	}

	public void setTelno(String telno) {
		this.telno = telno;
	}

	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getSalon() {
		return salon;
	}

	public void setSalon(String salon) {
		this.salon = salon;
	}

	public String getBeforeinvest() {
		return beforeinvest;
	}

	public void setBeforeinvest(String beforeinvest) {
		this.beforeinvest = beforeinvest;
	}

	public String getVisitfqcy() {
		return visitfqcy;
	}

	public void setVisitfqcy(String visitfqcy) {
		this.visitfqcy = visitfqcy;
	}

	public String getAddr2() {
		return addr2;
	}

	public void setAddr2(String addr2) {
		this.addr2 = addr2;
	}

	public String getPostcode2() {
		return postcode2;
	}

	public void setPostcode2(String postcode2) {
		this.postcode2 = postcode2;
	}

	public String getMobile2() {
		return mobile2;
	}

	public void setMobile2(String mobile2) {
		this.mobile2 = mobile2;
	}

	public String getEmail2() {
		return email2;
	}

	public void setEmail2(String email2) {
		this.email2 = email2;
	}

	public String getKnowhowbuy() {
		return knowhowbuy;
	}

	public void setKnowhowbuy(String knowhowbuy) {
		this.knowhowbuy = knowhowbuy;
	}

	public String getSpecialflag() {
		return specialflag;
	}

	public void setSpecialflag(String specialflag) {
		this.specialflag = specialflag;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRegdt() {
		return regdt;
	}

	public void setRegdt(String regdt) {
		this.regdt = regdt;
	}

	public String getLinkman() {
		return linkman;
	}

	public void setLinkman(String linkman) {
		this.linkman = linkman;
	}

	public String getLinktel() {
		return linktel;
	}

	public void setLinktel(String linktel) {
		this.linktel = linktel;
	}

	public String getLinkmobile() {
		return linkmobile;
	}

	public void setLinkmobile(String linkmobile) {
		this.linkmobile = linkmobile;
	}

	public String getLinkemail() {
		return linkemail;
	}

	public void setLinkemail(String linkemail) {
		this.linkemail = linkemail;
	}

	public String getLinkpostcode() {
		return linkpostcode;
	}

	public void setLinkpostcode(String linkpostcode) {
		this.linkpostcode = linkpostcode;
	}

	public String getLinkaddr() {
		return linkaddr;
	}

	public void setLinkaddr(String linkaddr) {
		this.linkaddr = linkaddr;
	}

	public String getGpsinvestlevel() {
		return gpsinvestlevel;
	}

	public void setGpsinvestlevel(String gpsinvestlevel) {
		this.gpsinvestlevel = gpsinvestlevel;
	}

	public String getGpsrisklevel() {
		return gpsrisklevel;
	}

	public void setGpsrisklevel(String gpsrisklevel) {
		this.gpsrisklevel = gpsrisklevel;
	}

	public String getIsjoinclub() {
		return isjoinclub;
	}

	public void setIsjoinclub(String isjoinclub) {
		this.isjoinclub = isjoinclub;
	}

	public String getCustsourceremark() {
		return custsourceremark;
	}

	public void setCustsourceremark(String custsourceremark) {
		this.custsourceremark = custsourceremark;
	}

	public String getLatesttradedt() {
		return latesttradedt;
	}

	public void setLatesttradedt(String latesttradedt) {
		this.latesttradedt = latesttradedt;
	}

	public String getRstohighreason() {
		return rstohighreason;
	}

	public void setRstohighreason(String rstohighreason) {
		this.rstohighreason = rstohighreason;
	}

	public String getIspubtrade() {
		return ispubtrade;
	}

	public void setIspubtrade(String ispubtrade) {
		this.ispubtrade = ispubtrade;
	}

	public String getInvsttype() {
		return invsttype;
	}

	public void setInvsttype(String invsttype) {
		this.invsttype = invsttype;
	}

	public String getVipusername() {
		return vipusername;
	}

	public void setVipusername(String vipusername) {
		this.vipusername = vipusername;
	}

	public String getNewsourceno() {
		return newsourceno;
	}

	public void setNewsourceno(String newsourceno) {
		this.newsourceno = newsourceno;
	}

	public String getHboneno() {
		return hboneno;
	}

	public void setHboneno(String hboneno) {
		this.hboneno = hboneno;
	}

	public String getValidity() {
		return validity;
	}

	public void setValidity(String validity) {
		this.validity = validity;
	}

	public String getValiditydt() {
		return validitydt;
	}

	public void setValiditydt(String validitydt) {
		this.validitydt = validitydt;
	}

	public String getNature() {
		return nature;
	}

	public void setNature(String nature) {
		this.nature = nature;
	}

	public String getAptitude() {
		return aptitude;
	}

	public void setAptitude(String aptitude) {
		this.aptitude = aptitude;
	}

	public String getScopebusiness() {
		return scopebusiness;
	}

	public void setScopebusiness(String scopebusiness) {
		this.scopebusiness = scopebusiness;
	}

	public Date getAddtime() {
		return addtime;
	}

	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	public Date getUpdatetime() {
		return updatetime;
	}

	public void setUpdatetime(Date updatetime) {
		this.updatetime = updatetime;
	}

	public String getWechatcode() {
		return wechatcode;
	}

	public void setWechatcode(String wechatcode) {
		this.wechatcode = wechatcode;
	}

	public String getSubknow() {
		return subknow;
	}

	public void setSubknow(String subknow) {
		this.subknow = subknow;
	}

	public String getSubknowtype() {
		return subknowtype;
	}

	public void setSubknowtype(String subknowtype) {
		this.subknowtype = subknowtype;
	}

	public String getRestype() {
		return restype;
	}

	public void setRestype(String restype) {
		this.restype = restype;
	}

	public String getIshighorg() {
		return ishighorg;
	}

	public void setIshighorg(String ishighorg) {
		this.ishighorg = ishighorg;
	}
}

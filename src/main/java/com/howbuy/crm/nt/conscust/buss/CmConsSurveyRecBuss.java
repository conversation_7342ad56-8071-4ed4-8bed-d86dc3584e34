/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.conscust.buss;

import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConsCustSurveryRecDiffDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: (私募客户调查问卷流水表 - 入会情况更新job实现)
 * <AUTHOR>
 * @date 2023/9/27 上午10:11
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmConsSurveyRecBuss {

    private static final String CUST_NO = "custNo";
    private static final String CUST_NAME = "custName";

    @Autowired
    private ConscustMapper conscustMapper;


    /**
     * @return void
     * @description 更新入会信息
     * step1 分页查询CM_CONSCUSTSURVEYREC全量CUSTNO
     * step2 判断是否在CM_CONSCUST表
     * step3 批量更新
     * <AUTHOR>
     * @date 2023/9/26 上午10:03
     * @since JDK 1.8
     */
    public void updateJoinClubInfo() {
        List<ConsCustSurveryRecDiffDO> diffInfos = conscustMapper.getSurveryRecDiffCustName();
        if (CollectionUtils.isNotEmpty(diffInfos)) {
            log.info("更新入会差异条数:{}", diffInfos.size());
            for (ConsCustSurveryRecDiffDO diffInfo : diffInfos) {
                int i = conscustMapper.updateSurveyRecCustNameByConcCustNo(diffInfo.getConscustno(), diffInfo.getCustname());
                log.info("更新入会成功条数={}, conscustno={}", i, diffInfo.getConscustno());
            }
        }
    }


    /**
     * @description 分页执行update
     * @param page	 页数
     * @param custNos
     * @return
     * <AUTHOR>
     * @date 2023/9/27 上午10:23
     * @since JDK 1.8
     */
    private void doUpdateByPage(int page, List<String> custNos) {
        List<Map<String, String>> paramsList = new ArrayList<>();
        for (String custNo : custNos) {
            String custName = conscustMapper.getCustNameByCustNo(custNo);
            if (StringUtils.isNotEmpty(custName)) {
                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put(CUST_NO, custNo);
                paramsMap.put(CUST_NAME, custName);
                paramsList.add(paramsMap);
            }
        }
        log.info("第{}页，需要更新入会信息条数：{}", page, paramsList.size());
        int updateNum = conscustMapper.updateCustNameInSurveyRec(paramsList);
        log.info("第{}页,更新入会信息成功条数:{}", page, updateNum);
    }
}
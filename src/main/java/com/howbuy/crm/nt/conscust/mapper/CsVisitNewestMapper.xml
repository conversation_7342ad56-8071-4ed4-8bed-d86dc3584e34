<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.conscust.dao.CsVisitNewestMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.conscust.domain.CsVisitNewestInfo">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno" />
    <result column="COMMCONTENT" jdbcType="VARCHAR" property="commcontent" />
    <result column="CONSBOOKINGID" jdbcType="VARCHAR" property="consbookingid" />
    <result column="VISITTYPE" jdbcType="VARCHAR" property="visittype" />
    <result column="VISITCLASSIFY" jdbcType="VARCHAR" property="visitclassify" />
    <result column="MODIFYFLAG" jdbcType="VARCHAR" property="modifyflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODDT" jdbcType="TIMESTAMP" property="moddt" />
    <result column="STIMESTAMP" jdbcType="TIMESTAMP" property="stimestamp" />
    <result column="HISFLAG" jdbcType="VARCHAR" property="hisflag" />
    <result column="HISID" jdbcType="VARCHAR" property="hisid" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CONSCUSTNO, COMMCONTENT, CONSBOOKINGID, VISITTYPE, VISITCLASSIFY, MODIFYFLAG,
    CREATOR, CREDT, MODIFIER, MODDT, STIMESTAMP, HISFLAG, HISID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CS_VISIT_NEWEST
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CS_VISIT_NEWEST
    where ID = #{id,jdbcType=VARCHAR}
  </delete>

  <delete id="deleteByConscustNo" parameterType="java.lang.String">
    delete from CS_VISIT_NEWEST
    where CONSCUSTNO = #{conscustno,jdbcType=VARCHAR}
  </delete>

  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.conscust.domain.CsVisitNewestInfo" useGeneratedKeys="true">
    insert into CS_VISIT_NEWEST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">ID,</if>
      <if test="conscustno != null">CONSCUSTNO,</if>
      <if test="commcontent != null">COMMCONTENT,</if>
      <if test="consbookingid != null">CONSBOOKINGID,</if>
      <if test="visittype != null">VISITTYPE,</if>
      <if test="visitclassify != null">VISITCLASSIFY,</if>
      <if test="modifyflag != null">MODIFYFLAG,</if>
      <if test="creator != null">CREATOR,</if>
      <if test="credt != null">CREDT,</if>
      <if test="modifier != null">MODIFIER,</if>
      <if test="moddt != null">MODDT,</if>
      <if test="stimestamp != null">STIMESTAMP,</if>
      <if test="hisflag != null">HISFLAG,</if>
      <if test="hisid != null">HISID,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">#{id,jdbcType=VARCHAR},</if>
      <if test="conscustno != null">#{conscustno,jdbcType=VARCHAR},</if>
      <if test="commcontent != null">#{commcontent,jdbcType=VARCHAR},</if>
      <if test="consbookingid != null">#{consbookingid,jdbcType=VARCHAR},</if>
      <if test="visittype != null">#{visittype,jdbcType=VARCHAR},</if>
      <if test="visitclassify != null">#{visitclassify,jdbcType=VARCHAR},</if>
      <if test="modifyflag != null">#{modifyflag,jdbcType=VARCHAR},</if>
      <if test="creator != null">#{creator,jdbcType=VARCHAR},</if>
      <if test="credt != null">#{credt,jdbcType=TIMESTAMP},</if>
      <if test="modifier != null">#{modifier,jdbcType=VARCHAR},</if>
      <if test="moddt != null">#{moddt,jdbcType=TIMESTAMP},</if>
      <if test="stimestamp != null">#{stimestamp,jdbcType=TIMESTAMP},</if>
      <if test="hisflag != null">#{hisflag,jdbcType=VARCHAR},</if>
      <if test="hisid != null">#{hisid,jdbcType=VARCHAR},</if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.conscust.domain.CsVisitNewestInfo">
    update CS_VISIT_NEWEST
    <set>
      <if test="conscustno != null">CONSCUSTNO = #{conscustno,jdbcType=VARCHAR},</if>
      <if test="commcontent != null">COMMCONTENT = #{commcontent,jdbcType=VARCHAR},</if>
      <if test="consbookingid != null">CONSBOOKINGID = #{consbookingid,jdbcType=VARCHAR},</if>
      <if test="visittype != null">VISITTYPE = #{visittype,jdbcType=VARCHAR},</if>
      <if test="visitclassify != null">VISITCLASSIFY = #{visitclassify,jdbcType=VARCHAR},</if>
      <if test="modifyflag != null">MODIFYFLAG = #{modifyflag,jdbcType=VARCHAR},</if>
      <if test="creator != null">CREATOR = #{creator,jdbcType=VARCHAR},</if>
      <if test="credt != null">CREDT = #{credt,jdbcType=TIMESTAMP},</if>
      <if test="modifier != null">MODIFIER = #{modifier,jdbcType=VARCHAR},</if>
      <if test="moddt != null">MODDT = #{moddt,jdbcType=TIMESTAMP},</if>
      <if test="stimestamp != null">STIMESTAMP = #{stimestamp,jdbcType=TIMESTAMP},</if>
      <if test="hisflag != null">HISFLAG = #{hisflag,jdbcType=VARCHAR},</if>
      <if test="hisid != null">HISID = #{hisid,jdbcType=VARCHAR},</if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

</mapper>
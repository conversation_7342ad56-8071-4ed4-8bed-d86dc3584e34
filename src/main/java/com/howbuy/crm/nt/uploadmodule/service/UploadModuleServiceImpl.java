package com.howbuy.crm.nt.uploadmodule.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.crm.nt.uploadmodule.dao.CmUploadModuleMapper;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleType;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleTypeSuffix;

import crm.howbuy.base.enums.CrmUploadModuleEnum;
import crm.howbuy.base.enums.CrmUploadTypeEnum;
import crm.howbuy.base.utils.StringUtil;
/**
 * 获取各模块上传格式相关
 * <AUTHOR>
 *
 */
@Service("uploadModuleService")
@Slf4j
public class UploadModuleServiceImpl implements UploadModuleService {

	@Autowired
	private CmUploadModuleMapper cmUploadModuleMapper;
	
	/**
	 * 获取传入模块可上传的类型和格式
	 */
	@Override
	public NtCmUploadModule getCmUploadModule(CrmUploadModuleEnum module) {
		if(module != null && StringUtil.isNotNullStr(module.getCode())){
			String modulecode = module.getCode();
			NtCmUploadModule vo = new NtCmUploadModule();
			vo.setMaintype(modulecode.split("_")[0]);
			vo.setSubtype(modulecode.split("_")[1]);
			//已经选中的类型
			List<String> listHasSelectTypes = new ArrayList<String>();
			NtCmUploadModule mod = cmUploadModuleMapper.getCmUploadModule(vo);
			if(mod != null){
				StringBuilder sb = new StringBuilder();
				NtCmUploadModuleType typevo = new NtCmUploadModuleType();
				typevo.setModuleid(mod.getId());
				List<NtCmUploadModuleType> listtype = cmUploadModuleMapper.listCmUploadModuleType(typevo);
				if(listtype != null && listtype.size() > 0){
					for(NtCmUploadModuleType type : listtype){
						listHasSelectTypes.add(type.getUptype());
						NtCmUploadModuleTypeSuffix suffixvo = new NtCmUploadModuleTypeSuffix();
						suffixvo.setTypeid(type.getId());
						List<NtCmUploadModuleTypeSuffix> listsuffix = cmUploadModuleMapper.listCmUploadModuleTypeSuffix(suffixvo);
						//设置上传类型对应的上传格式
						setHasSelectSuffix(mod,listsuffix,type,sb);
					}
				}
				//设置查询模块的上传类型的明细
				mod.setListCmUploadModuleType(listtype);
				//设置查询模块的上传类型
				mod.setListHasSelectTypes(listHasSelectTypes);
				//设置如果因为上传的格式超过了设定的值，返回大小提示
				sb.append("请知悉。");
				mod.setSizeResult(sb.toString());
			}else{
				mod = new NtCmUploadModule();
			}
			
			return mod;
		}else{
			return new NtCmUploadModule();
		}
	}
	
	/**
	 * 设置上传类型对应的上传格式
	 * @param module
	 * @param listsuffix
	 * @param type
	 */
	private void setHasSelectSuffix(NtCmUploadModule mod,List<NtCmUploadModuleTypeSuffix> listsuffix,NtCmUploadModuleType type,StringBuilder sb){
		
		
		if(CrmUploadTypeEnum.DOCUMENT.equals(CrmUploadTypeEnum.getEnum(type.getUptype()))){
			setSuffix(CrmUploadTypeEnum.DOCUMENT,mod,listsuffix,type,sb);
		}
		if(CrmUploadTypeEnum.PICTURE.equals(CrmUploadTypeEnum.getEnum(type.getUptype()))){
			setSuffix(CrmUploadTypeEnum.PICTURE,mod,listsuffix,type,sb);
		}
		if(CrmUploadTypeEnum.AUDIO.equals(CrmUploadTypeEnum.getEnum(type.getUptype()))){
			setSuffix(CrmUploadTypeEnum.AUDIO,mod,listsuffix,type,sb);
		}
		if(CrmUploadTypeEnum.VIDEO.equals(CrmUploadTypeEnum.getEnum(type.getUptype()))){
			setSuffix(CrmUploadTypeEnum.VIDEO,mod,listsuffix,type,sb);
		}
		if(CrmUploadTypeEnum.EMAIL.equals(CrmUploadTypeEnum.getEnum(type.getUptype()))){
			setSuffix(CrmUploadTypeEnum.EMAIL,mod,listsuffix,type,sb);
		}
		
	}
	
	private void setSuffix(CrmUploadTypeEnum typeenum,NtCmUploadModule mod,List<NtCmUploadModuleTypeSuffix> listsuffix,NtCmUploadModuleType type,StringBuilder sb){
		if(listsuffix != null && listsuffix.size() > 0){
			//文档已经选中的格式
			List<String> listHasSelectTypesSuffixs = new ArrayList<String>();
			for(NtCmUploadModuleTypeSuffix suffix : listsuffix){
				listHasSelectTypesSuffixs.add(suffix.getSuffix());
				if(mod.getListHasSelectSuffixs() != null){
					mod.getListHasSelectSuffixs().add(suffix.getSuffix());
				}else{
					List<String> listHasSelectSuffixs = new ArrayList<String>();
					listHasSelectSuffixs.add(suffix.getSuffix());
					mod.setListHasSelectSuffixs(listHasSelectSuffixs);
				}
				if(mod.getSuffixSizeMap() != null){
					mod.getSuffixSizeMap().put(suffix.getSuffix(), type.getMaxsize().toPlainString());
				}else{
					Map<String,String> suffixSizeMap = new HashMap<String, String>();
					suffixSizeMap.put(suffix.getSuffix(), type.getMaxsize().toPlainString());
					mod.setSuffixSizeMap(suffixSizeMap);
				}
				
			}
			//设置类型对应的格式
			if(mod.getTypeSuffixsMap() != null){
				mod.getTypeSuffixsMap().put(typeenum.getDescription(), String.join("、", listHasSelectTypesSuffixs));
			}else{
				Map<String,String> typeSuffixsMap = new HashMap<String, String>();
				typeSuffixsMap.put(typeenum.getDescription(), String.join("、", listHasSelectTypesSuffixs));
				mod.setTypeSuffixsMap(typeSuffixsMap);
			}
			//设置文档类型上传最大值
			sb.append(typeenum.getDescription()+"单个大小限制"+type.getMaxsize().toPlainString()+"M，");
		}
	}

}

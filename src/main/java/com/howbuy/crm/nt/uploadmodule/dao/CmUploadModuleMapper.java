package com.howbuy.crm.nt.uploadmodule.dao;

import java.util.List;

import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleType;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleTypeSuffix;

/**
 * 
 * <AUTHOR>
 *
 */
public interface CmUploadModuleMapper {
	

	/**
	 * 查询模块下的可上传类型
	 * @param vo
	 * @return
	 */
	List<NtCmUploadModuleType> listCmUploadModuleType(NtCmUploadModuleType vo);
	
	/**
	 * 获取单个模块
	 * @param vo
	 * @return
	 */
	NtCmUploadModule getCmUploadModule(NtCmUploadModule vo);
	
	/**
	 * 查询模块下的可上传类型选中的格式
	 * @param vo
	 * @return
	 */
	List<NtCmUploadModuleTypeSuffix> listCmUploadModuleTypeSuffix(NtCmUploadModuleTypeSuffix vo);
	
	
}

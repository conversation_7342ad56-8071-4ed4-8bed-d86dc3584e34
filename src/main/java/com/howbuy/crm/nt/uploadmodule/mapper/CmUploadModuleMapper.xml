<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.uploadmodule.dao.CmUploadModuleMapper">

	<select id="listCmUploadModuleType" parameterType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleType" resultType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleType" useCache="false">
			select t.id, t.moduleid, t.uptype, t.maxsize from CM_UPLOAD_MODULE_TYPE t
			where 1=1
			<if test="moduleid != null">
			and moduleid = #{moduleid}
			</if>
	</select>
	  
	<select id="getCmUploadModule" parameterType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule" resultType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule" useCache="false">
		select t1.id,t1.maintype,t1.subtype from cm_upload_module t1 where t1.maintype = #{maintype} and t1.subtype = #{subtype}
	</select>
	
	<select id="listCmUploadModuleTypeSuffix" parameterType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleTypeSuffix" resultType="com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModuleTypeSuffix" useCache="false">
			select t.id, t.typeid, t.suffix from CM_UPLOAD_MODULE_TYPE_SUFFIX t
			where 1=1
			<if test="typeid != null">
			and typeid = #{typeid}
			</if>
	</select>
		
</mapper>
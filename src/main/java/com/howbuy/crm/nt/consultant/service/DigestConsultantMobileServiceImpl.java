package com.howbuy.crm.nt.consultant.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import com.howbuy.acc.common.utils.DigestUtil;
import crm.howbuy.base.utils.StringUtil;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("DigestConsultantMobileService")
public class DigestConsultantMobileServiceImpl implements DigestConsultantMobileService {

	@Autowired
	private CmConsultantMapper cmConsultantMapper;

	@Override
	public void digestMobileData(String arg) {
		//查询所有投顾的手机号和手机摘要
		List<CmConsultantInfo> list = cmConsultantMapper.listConsInfoAllMoile(null);
		//存放需要更新的手机号摘要
		List<CmConsultantInfo> listupdate = new ArrayList<CmConsultantInfo>();
		
		if(list!=null && list.size() > 0){
			for(CmConsultantInfo info : list){
				boolean changeflag = false;
				String mobileGigest = "";
				if(StringUtil.isNotNullStr(info.getMobile())){
					mobileGigest = DigestUtil.digest(info.getMobile().trim());
					if(StringUtil.isNullStr(info.getMobileDigest())){
						changeflag = true;
					}else{
						if(!mobileGigest.equals(info.getMobileDigest())){
							changeflag = true;
						}
					}
				}else{
					if(StringUtil.isNotNullStr(info.getMobileDigest())){
						changeflag = true;
					}
				}
				if(changeflag){
					CmConsultantInfo upcons = new CmConsultantInfo();
					upcons.setConsCode(info.getConsCode());
					upcons.setMobileDigest(mobileGigest);
					listupdate.add(upcons);
				}
			}
		}
		if(listupdate != null && listupdate.size() > 0){
			cmConsultantMapper.batchUpdateConsMoileDigest(listupdate);
		}
	}

}

package com.howbuy.crm.nt.consultant.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.model.BaseConstantEnum;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.dto.CmConsultantExpDomain;
import com.howbuy.crm.organization.dto.HbUpLevelOrgCodeDTO;
import com.howbuy.crm.organization.response.OrganizationCatchResponse;
import com.howbuy.crm.organization.service.OrganizationCatchService;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
public class QueryConsultantExpBuss {

	@Autowired
	private CmConsultantMapper cmConsultantInfoDao;

	@Autowired
	private OrganizationCatchService organizationCatchService;

	/**
	 * 根据投顾号获取  分总   区域总   区域副总
	 * @param conscode
	 * @return
	 */
	public CmConsultantExpDomain queryConsultantExpLead(String conscode) {
		CmConsultantExpDomain cmConsultantExpDomain = new CmConsultantExpDomain();
		//先查询部门code
		String outletCode = cmConsultantInfoDao.getOutletCodeByConsCode(conscode);
		// 将原有查询缓存[CONSORGCACHE_]的逻辑，改为查询crm-core提供的dubbo接口
		String upOrgCode = getUpOrgCode(outletCode);

		String areaCode = "";
		String zero = "0";
		if(zero.equals(upOrgCode)){
			areaCode = outletCode;
		}else{
			areaCode = upOrgCode;
		}
		//获取领导
		Map<String,String> paramleader = new HashMap<String,String>(2);
		//查询分总
		if(StringUtil.isNotNullStr(outletCode)) {
			String fenzong = "3";
			paramleader.clear();
			paramleader.put("orgcode", outletCode);
			paramleader.put("userlevel", fenzong);
			List<String> listorgleaders = cmConsultantInfoDao.listLeadersByOrgCodeAndLevel(paramleader);
			if (CollectionUtils.isNotEmpty(listorgleaders)) {
				cmConsultantExpDomain.setOutletleaderList(listorgleaders);
			}

			//查询区域执行副总
			paramleader.clear();
			paramleader.put("orgcode", outletCode);
			List<String> listareafleaders = cmConsultantInfoDao.listFLeadersByOrgCode(paramleader);
			if(CollectionUtils.isNotEmpty(listareafleaders)){
				cmConsultantExpDomain.setAreafleaderList(listareafleaders);
			}
		}
		if(StringUtil.isNotNullStr(areaCode)) {
			//查询区域总
			String araelevel = "5";
			paramleader.clear();
			paramleader.put("orgcode", areaCode);
			paramleader.put("userlevel", araelevel);
			List<String> listarealeaders = cmConsultantInfoDao.listLeadersByOrgCodeAndLevel(paramleader);
			if (CollectionUtils.isNotEmpty(listarealeaders)) {
				cmConsultantExpDomain.setArealeaderList(listarealeaders);
			}
		}
		return cmConsultantExpDomain;
	}

	/**
	 * @description: 获取当前节点以上的第3级机构编码,如果当前节点没有超过3级,就返回当前节点的机构编码
	 * @param outletCode 当前节点机构编码
	 * @return java.lang.String 上级节点机构编码
	 * @author: jin.wang03
	 * @date: 2023/11/30 13:50
	 * @since JDK 1.8
	 */
	private String getUpOrgCode(String outletCode) {
		String uporgcode = "";

		OrganizationCatchResponse<List<HbUpLevelOrgCodeDTO>> upOrgResponse = organizationCatchService.getUpOrgMapCache();
		log.info("getUpOrgMapCache response:{}", JSON.toJSONString(upOrgResponse));
		if (ObjectUtils.isNotEmpty(upOrgResponse) && BaseConstantEnum.SUCCESS.getCode().equals(upOrgResponse.getReturnCode())) {
			List<HbUpLevelOrgCodeDTO> upOrgList = upOrgResponse.getData();
			if (CollectionUtils.isNotEmpty(upOrgList)) {
				for (HbUpLevelOrgCodeDTO hbUpLevelOrgCodeDTO : upOrgList) {
					if (hbUpLevelOrgCodeDTO.getOrgCode().equals(outletCode)) {
						uporgcode = hbUpLevelOrgCodeDTO.getUpLevelOrgCode();
						break;
					}
				}
			}
		}
		return uporgcode;
	}
}

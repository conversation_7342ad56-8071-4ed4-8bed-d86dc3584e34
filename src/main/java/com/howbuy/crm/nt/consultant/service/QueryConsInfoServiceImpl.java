package com.howbuy.crm.nt.consultant.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.base.model.NtBaseConstantEnum;
import com.howbuy.crm.nt.consultant.buss.QueryConsInfoBuss;
import com.howbuy.crm.nt.consultant.request.QueryConsInfoRequest;
import com.howbuy.crm.nt.consultant.request.QueryConsListByCustRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsInfoReponse;
import com.howbuy.crm.nt.consultant.response.QueryConsListByCustReponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("queryConsInfoService")
@Slf4j
public class QueryConsInfoServiceImpl implements QueryConsInfoService {
	@Autowired
	private QueryConsInfoBuss queryConsInfoBuss;

	@Override
	public QueryConsInfoReponse queryConsInfoByHboneNo(QueryConsInfoRequest request) {
		return queryConsInfoBuss.queryConsInfoByHboneNo(request);
	}

	@Override
	public QueryConsListByCustReponse queryConsConsListByCust(QueryConsListByCustRequest request) {

		log.info("CmPushMsgByConsCodeRequest is: {}", JSON.toJSONString(request));
		QueryConsListByCustReponse response = new QueryConsListByCustReponse();

		if(this.validate(response, request)){
			response = queryConsInfoBuss.queryConsConsListByCust(request);
		}
		return response;
	}

	public boolean validate(QueryConsListByCustReponse response, QueryConsListByCustRequest request) {
		boolean flag = true;
		// 校验传入数据
		if (request.getConscustnos() == null && !request.getConscustnos().isEmpty()) {
			response.putBaseResult(NtBaseConstantEnum.DATA_NOT_FUND, "没有数据！");
			flag = false;
		}

		return flag;
	}

}
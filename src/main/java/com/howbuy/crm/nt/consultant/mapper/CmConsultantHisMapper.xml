<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.consultant.dao.CmConsultantHisMapper">


    <insert id="insertCmCustConscustantHis">
        INSERT INTO CM_CUSTCONSTANTHIS
        (CUSTCONSHISID,
         CUSTNO,
         CONSCODE,
         STARTDT,
         ENDDT,
         CREATOR,
         CREDT,
         BINDDATE,
         UNBUNDDATE,
         OPERATE_DATE)
        SELECT SEQ_CUSTREC.NEXTVAL,
               T1.CONSCUSTNO                AS CUSTNO,
               T5.CONSCODE                  AS CONSCODE,
               T5.STARTDT                   AS STARTDT,
               TO_CHAR(SYSDATE, 'yyyyMMdd') AS ENDDT,
               'sys'                        AS CREATOR,
               TO_CHAR(SY<PERSON>AT<PERSON>, 'yyyyMMdd') AS CREDT,
               T5.BINDDATE,
               SYSDATE,
               SYSDATE
        FROM CM_CONSCUST T1
                 LEFT JOIN CM_CUSTCONSTANT T5
                           ON T1.CONSCUSTNO = T5.CUSTNO
                 LEFT JOIN CM_CONSULTANT CONS
                           ON T5.CONSCODE = CONS.CONSCODE
                 LEFT JOIN SYNC_CAS_BALANCE_SUMMARY CAS
                           ON CAS.HBONE_NO = T1.HBONE_NO
                 LEFT JOIN AC_TX_HBONE txhbone
                           ON txhbone.HBONE_NO = T1.HBONE_NO
                 LEFT JOIN sync_ac_cust accust
                           ON txhbone.cust_no = accust.cust_no
        WHERE 1 = 1
          AND T1.CONSCUSTSTATUS = '0'
          --来源是“闻石”和“大V”客户不参与20w划转
          AND T1.NEWSOURCENO NOT IN
              ('RO1711F02', 'RO1906W01', 'RO1909W01', 'RO1912W01', 'RO2002W01', 'RO2003W01', 'RO2003W02', 'RO2007W01',
               'RO2005W03', 'RO2008W01', 'RO2004W01')
          --一账通注册渠道与CRM-来源管理（新）大V合作来源编号相同时，不执行划转
          AND accust.hbone_reg_outlet_code not in (select sourceno
                                                   from cm_sourceinfo_new
                                                   where first_level_code = 'K'
                                                     AND second_level_code = '086'
                                                     and rec_stat = '1')
          AND (T1.SPECIALFLAG IS NULL OR T1.SPECIALFLAG = '0')
          --投顾是‘公司同事库’，‘客户服务部-高端--深潜库’，‘零售-禁扰客户库’，‘零售-禁扰客户库’的客户不参与20w划转,
          AND T5.CONSCODE not in ('weijietong', 'shenqianku', 'JR_CSLS', 'wuyixiang')
          --用户处在10250大V合作的标签中时，不参与20w划转
          AND NOT EXISTS (SELECT 1 FROM CM_BIGV_CUST BIGV WHERE T1.CONSCUSTNO = BIGV.CONSCUSTNO)
          --限制客户当前投顾在客服（零售+高端）部门下，且不含20w临时库
          AND CONS.CONSCODE IN
              (SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE 1 = 1
                 AND T2.ORGCODE = '12'
                 AND T1.CONSSTATUS = '1'
               UNION
               SELECT T1.CONSCODE
               FROM CM_CONSULTANT T1
                        LEFT JOIN HB_ORGANIZATION T2
                                  ON T1.OUTLETCODE = T2.ORGCODE
               WHERE T1.CONSCODE != '20wTemporaryLibrary'
                 AND T1.CONSCODE != 'leadsFPK'
                 AND T2.ORGCODE = '11'
                 AND T1.CONSSTATUS = '1')
          AND CAS.RETAIL_AMOUNT >= 200000
          --分销机构是理财通客户不参与划转
          AND NOT EXISTS (SELECT 1
                          FROM AC_TX_HBONE A
                          WHERE A.CUST_NO IN
                                (SELECT AC.CUST_NO
                                 From SYNC_AC_DIS_FUND_TX_ACCT AC
                                 WHERE AC.CUST_NO IN
                                       (SELECT A.CUST_NO
                                        FROM SYNC_AC_DIS_FUND_TX_ACCT A
                                        GROUP BY A.CUST_NO
                                        HAVING COUNT(A.CUST_NO) = 1)
                                   AND AC.DIS_CODE in ('LCT00K001', 'EFUNDS001'))
                            and A.HBONE_NO = T1.HBONE_NO)
    </insert>

</mapper>
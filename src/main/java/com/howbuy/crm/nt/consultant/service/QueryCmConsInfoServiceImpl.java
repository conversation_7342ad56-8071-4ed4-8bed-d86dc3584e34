package com.howbuy.crm.nt.consultant.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.crm.nt.consultant.buss.QueryCmConsInfoBuss;
import com.howbuy.crm.nt.consultant.request.QueryConsInfoRequest;
import com.howbuy.crm.nt.consultant.response.QueryCmConsInfoReponse;
import com.howbuy.crm.nt.consultant.response.QueryCmConsInfoListReponse;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("queryCmConsInfoService")
public class QueryCmConsInfoServiceImpl implements QueryCmConsInfoService {
	@Autowired
	private QueryCmConsInfoBuss queryCmConsInfoBuss;

	@Override
	public QueryCmConsInfoReponse queryConsInfoByHboneno(QueryConsInfoRequest request) {
		return queryCmConsInfoBuss.queryConsInfoByHboneno(request);
	}

	@Override
	public QueryCmConsInfoListReponse ListConsInfoForIc() {
		return queryCmConsInfoBuss.listConsInfoForIc();
	}

}
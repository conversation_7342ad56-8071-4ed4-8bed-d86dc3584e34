package com.howbuy.crm.nt.consultant.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import com.howbuy.crm.nt.consultant.dto.CmConsultantInfoDomain;
import com.howbuy.crm.nt.consultant.request.QueryConsultantInfoRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsultantInfoReponse;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.request.QueryWeChatMemberInfoRequest;
import com.howbuy.crm.wechat.client.producer.wechatmembermanagement.response.QueryWeChatMemberInfoResponse;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.enums.YesOrNoEnum;
import crm.howbuy.base.utils.MapRemoveNullUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("queryConsultantInfoService")
public class QueryConsultantInfoServiceImpl implements QueryConsultantInfoService {

	private static final Logger log = LoggerFactory.getLogger(QueryConsultantInfoServiceImpl.class);

	private static final String SUCCESS_CODE = "0000";

	@Autowired
	private CmConsultantMapper cmConsultantInfoDao;

	@Resource
	private QueryWeChatMemberInfoService queryWeChatMemberInfoService;

	/**
	 * CM_CONSULTANT_EXP表中，jjcardno(基金从业资格编码) 为空或者为“无”时，不返回数据
	 */
	private static final String NO = "无";

	@Override
	public QueryConsultantInfoReponse queryConsultantInfo(QueryConsultantInfoRequest request) {
		log.info("QueryConsultantInfoRequest is: " + JSON.toJSONString(request));
		
		QueryConsultantInfoReponse response = new QueryConsultantInfoReponse();
		response.success();
		if(this.validate(response, request)) {
			Map<String, Object> params = new HashMap<>();
			params.put("conscode", request.getConscode());
			params.put("mobile", request.getMobile());
			params.put("mobileDigest", request.getMobileDigest());
			MapRemoveNullUtil.removeNullValue(params);
			CmConsultantInfo consultantInfo = cmConsultantInfoDao.queryConsultantInfo(params);

			if (consultantInfo == null) {
				log.info("QueryConsultantInfoReponse is: " + JSON.toJSONString(response));
				return response;
			}

			CmConsultantInfoDomain consultantInfoDomain = new CmConsultantInfoDomain();
			BeanUtils.copyProperties(consultantInfo, consultantInfoDomain);

			// 从业编码判断
			//  取值判断：CRM-系统管理-系统配置-用户管理（新）中【是否进花名册】字段，若为【是】取花名册判断，若为【否】取用户管理判断
			//  花名册判断：CRM-绩效管理-人员信息管理-花名册中【基金从业资格编码】判断
			//  用户管理判断：CRM-系统管理-系统配置-用户管理（新）中【基金从业资格编码】判断
			if (StaticVar.YES.equals(consultantInfo.getIsExp())) {
				if (StringUtils.isNotBlank(consultantInfo.getJjCardNo()) && !NO.equals(consultantInfo.getJjCardNo())) {
					consultantInfoDomain.setCardNo(consultantInfo.getJjCardNo());
				}
			} else {
				if (StringUtils.isNotBlank(consultantInfo.getEmpCardNo())) {
					consultantInfoDomain.setCardNo(consultantInfo.getEmpCardNo());
				}
			}
			// 获取企微信息
			queryEnterpriseWechatQrCode(request, consultantInfo, consultantInfoDomain);
			response.setConsultantInfo(consultantInfoDomain);
		}
		log.info("QueryConsultantInfoReponse is: " + JSON.toJSONString(response));
		return response;
	}

	/**
	 * @description: 获取企业微信信息
	 * @param request	
	 * @param consultantInfo	
	 * @param consultantInfoDomain
	 * @return void
	 * @author: jinqing.rao
	 * @date: 2024/6/28 13:54
	 * @since JDK 1.8
	 */
	private void queryEnterpriseWechatQrCode(QueryConsultantInfoRequest request, CmConsultantInfo consultantInfo, CmConsultantInfoDomain consultantInfoDomain) {
		if(StringUtils.isNotBlank(request.getFirmWechat()) && request.getFirmWechat().equals(YesOrNoEnum.YES.getCode())){
			if(StringUtils.isBlank(consultantInfo.getWechatConsCode())){
				log.info("queryEnterpriseWechatQrCode >>> 企业微信的userID 是空 不查询, params :{}",JSON.toJSONString(request));
				return;
			}
			QueryWeChatMemberInfoRequest queryWeChatMemberInfoRequest = new QueryWeChatMemberInfoRequest();
			queryWeChatMemberInfoRequest.setUserId(consultantInfo.getWechatConsCode());
			// 需要 将企微头像放置在企微二维码的中间，并把图片转成base64字符串
			queryWeChatMemberInfoRequest.setNeedQrImageStr(true);
			Response<QueryWeChatMemberInfoResponse> weChatMemberInfo = queryWeChatMemberInfoService.queryWeChatMemberInfo(queryWeChatMemberInfoRequest);
			if(null == weChatMemberInfo){
				log.info("queryEnterpriseWechatQrCode >>> 企业微信没查询到数据, params :{}",JSON.toJSONString(request));
				return;
			}
			if(!SUCCESS_CODE.equals(weChatMemberInfo.getCode())){
				log.info("queryEnterpriseWechatQrCode>>>queryEnterpriseWechatQrCode 查询用户企微信息异常 userId:{},result : {}", consultantInfo.getWechatConsCode(), JSON.toJSONString(weChatMemberInfo));
				return ;
			}
			QueryWeChatMemberInfoResponse weChatInfoResponse = weChatMemberInfo.getReturnObject();
			if (null == weChatInfoResponse) {
				log.info("queryEnterpriseWechatQrCode>>>queryEnterpriseWechatQrCode 未获取到用户的企微信息 userId:{}", consultantInfo.getWechatConsCode());
				return;
			}
			consultantInfoDomain.setEnterpriseWechatQrCode(weChatInfoResponse.getQrCode());
			consultantInfoDomain.setEnterpriseWechatMobile(weChatInfoResponse.getMobile());
			consultantInfoDomain.setEnterpriseWechatThumbAvatar(weChatInfoResponse.getThumbAvatar());
			consultantInfoDomain.setEnterpriseWechatQrImageStr(weChatInfoResponse.getEnterpriseWechatQrImageStr());
		}
	}


	public boolean validate(QueryConsultantInfoReponse response,QueryConsultantInfoRequest request) {
		boolean flag = true;
		// 校验传入数据
		if (StringUtils.isBlank(request.getMobileDigest()) && StringUtils.isBlank(request.getMobile()) && StringUtils.isBlank(request.getConscode())) {
			response.invalidReqParams("传参对象为空！");
			flag = false;
		}
		
		return flag;
	}
}

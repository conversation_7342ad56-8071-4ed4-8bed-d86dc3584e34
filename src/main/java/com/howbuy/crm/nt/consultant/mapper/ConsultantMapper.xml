<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.consultant.dao.CmConsultantMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.consultant.domain.CmConsultantInfo">
        <result column="CONSCODE" property="consCode" jdbcType="VARCHAR"/>
        <result column="CONSNAME" property="consName" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="PICADDR" property="picAddr" jdbcType="VARCHAR"/>
        <result column="CODEPICADDR" property="codePicAddr" jdbcType="VARCHAR"/>
        <result column="CHARACTER" property="character" jdbcType="VARCHAR"/>
        <result column="POSITION" property="position" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="TELNO" property="telNo" jdbcType="VARCHAR"/>
		<result column="ROLECODE" property="rolecode" jdbcType="VARCHAR"/>
        <result column="CUSTNO" property="custNo" jdbcType="VARCHAR"/>
        <result column="ORGNAME" property="orgName" jdbcType="VARCHAR"/>
        <result column="ISVIRTUAL" property="isVirtual" jdbcType="VARCHAR"/>

		<result column="PROVCODE" property="provCode" jdbcType="VARCHAR"/>
		<result column="CITYCODE" property="cityCode" jdbcType="VARCHAR"/>
		<result column="ISEXP" property="isExp" jdbcType="VARCHAR"/>
		<result column="EMPCARDNO" property="empCardNo" jdbcType="VARCHAR"/>
		<result column="JJCARDNO" property="jjCardNo" jdbcType="VARCHAR"/>
		<result column="ADDWECHATAPPLETROUTING" property="addWechatAppletRouting" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryConsultantInfo" resultMap="BaseResultMap" parameterType="map">
		SELECT T.CONSCODE,
		T.CONSNAME,
		T.MOBILE,
		T.CHARACTER1 CHARACTER,
		T.PICADDR1 PICADDR,
		T.CODEPICADDR,
		T.EMAIL,
		T.EMPCARDNO,
		case
		when A.ROLECODE is not null then
		'1'
		else
		'0'
		end as ROLECODE,
		NVL(T1.CONSTDESC, '好买资深投资顾问') POSITION,
		E.PROVCODE,E.CITYCODE,
		T.ISVIRTUAL,
		(CASE
		WHEN E.USERID IS NOT NULL THEN
		'1'
		ELSE
		'0'
		END) ISEXP,
		T.WECHATCONSCODE as wechatConsCode,
		T.ADDWECHATAPPLETROUTING,
		E.JJCARDNO
		FROM CM_CONSULTANT T
		LEFT JOIN HB_CONSTANT T1 ON T.CONSLEVEL = T1.CONSTCODE	AND T1.TYPECODE = 'conslevelout'
		LEFT JOIN HB_USERROLE A ON A.USERCODE = T.CONSCODE	AND A.ROLECODE = 'ROLE_SIC'
		LEFT JOIN CM_CONSULTANT_EXP E ON E.USERID=T.CONSCODE
		WHERE T.CONSSTATUS = '1'
		AND T.CONSLEVEL IS NOT NULL
		AND T.OUTLETCODE NOT IN
		(SELECT ORGCODE
		FROM HB_ORGANIZATION
		START WITH ORGCODE IN ('11', '12')
		CONNECT BY PARENTORGCODE = PRIOR ORGCODE)
				<if test="mobile != null">
			   	AND T.MOBILE = #{mobile}
			   	</if>
			   	<if test="conscode != null">
			   	AND T.CONSCODE = #{conscode}
			   	</if>
				<if test="mobileDigest != null">
				AND T.MOBILE_DIGEST = #{mobileDigest}
				</if>
    </select>
    
    <select id="queryConsInfoByHboneNo" resultMap="BaseResultMap" parameterType="map">
        SELECT T1.CUSTNO,
		       T1.CONSCODE,
		       T2.CONSNAME,
		       T4.ORGNAME,
		       T2.TELNO,
		       T2.EMAIL,
		       T2.MOBILE,
		       T2.PICADDR,
		       T2.ISVIRTUAL
		  FROM CM_CUSTCONSTANT T1, CM_CONSULTANT T2, CM_CONSCUST T3 ,HB_ORGANIZATION T4
		 WHERE T1.CONSCODE = T2.CONSCODE
		   AND T2.CONSSTATUS = '1'
		   AND T3.CONSCUSTSTATUS = '0'
		   AND T2.OUTLETCODE = T4.ORGCODE
		   AND T1.CUSTNO = T3.CONSCUSTNO
		   AND T3.HBONE_NO = #{hboneNo}
    </select>

	<select id="query20WTemporaryLibrary" resultType="com.howbuy.crm.nt.consultant.domain.CmConsultantBO">
		SELECT T1.CONSCUSTNO,
				T5.CONSCODE,
				T1.HBONE_NO hboneNo,
				txhbone.CUST_NO pubCustNo
		   FROM CM_CONSCUST T1
					LEFT JOIN CM_CUSTCONSTANT T5
							  ON T1.CONSCUSTNO = T5.CUSTNO
					LEFT JOIN CM_CONSULTANT CONS
							  ON T5.CONSCODE = CONS.CONSCODE
					LEFT JOIN SYNC_CAS_BALANCE_SUMMARY CAS
							  ON CAS.HBONE_NO = T1.HBONE_NO
					LEFT JOIN AC_TX_HBONE txhbone
							  ON txhbone.HBONE_NO = T1.HBONE_NO
					LEFT JOIN sync_ac_cust accust
							  ON txhbone.cust_no = accust.cust_no
		   WHERE  T1.CONSCUSTSTATUS = '0'
			AND (T1.SPECIALFLAG IS NULL OR T1.SPECIALFLAG = '0')
			--来源是“闻石”和“大V”客户不参与20w划转
			AND T1.NEWSOURCENO NOT IN
			('RO1711F02', 'RO1906W01', 'RO1909W01', 'RO1912W01', 'RO2002W01', 'RO2003W01', 'RO2003W02',
			'RO2007W01', 'RO2005W03', 'RO2008W01', 'RO2004W01')
			--一账通注册渠道与CRM-来源管理（新）大V合作来源编号相同时，不执行划转  （历史逻辑，应该包含在【“闻石”和“大V”客户】条件， 但为防止错误还保留）
			AND accust.hbone_reg_outlet_code not in (select sourceno
														from cm_sourceinfo_new
														where first_level_code = 'K'
														AND second_level_code = '086'
														and rec_stat = '1')
		--投顾是‘公司同事库’，‘客户服务部-高端--深潜库’，‘零售-禁扰客户库’，‘零售-禁扰客户库’的客户不参与20w划转,
		AND T5.CONSCODE not in ('weijietong', 'shenqianku', 'JR_CSLS', 'wuyixiang')
			--限制客户当前投顾在客服（零售+高端）部门下，且不含20w临时库
			 AND CONS.CONSCODE IN
				 (SELECT T1.CONSCODE
					  FROM CM_CONSULTANT T1
						LEFT JOIN HB_ORGANIZATION T2
						ON T1.OUTLETCODE = T2.ORGCODE
					  WHERE  T2.ORGCODE = '12'
						AND T1.CONSSTATUS = '1'
				  UNION
				  SELECT T1.CONSCODE
					  FROM CM_CONSULTANT T1
						LEFT JOIN HB_ORGANIZATION T2
						ON T1.OUTLETCODE = T2.ORGCODE
					  WHERE T1.CONSCODE != #{consCode20w}
							 AND T1.CONSCODE != 'leadsFPK'
							 AND T2.ORGCODE = '11'
							 AND T1.CONSSTATUS = '1')
			AND CAS.RETAIL_AMOUNT >= 200000
	</select>

	<update id="mergeTo20WTemporaryLibrary">
		--将符合条件客户划转到20wTemporaryLibrary
		MERGE INTO CM_CUSTCONSTANT AA
			USING (SELECT T1.CONSCUSTNO, T5.CONSCODE, T5.STARTDT
				   FROM CM_CONSCUST T1
							LEFT JOIN CM_CUSTCONSTANT T5
									  ON T1.CONSCUSTNO = T5.CUSTNO
							LEFT JOIN CM_CONSULTANT CONS
									  ON T5.CONSCODE = CONS.CONSCODE
							LEFT JOIN SYNC_CAS_BALANCE_SUMMARY CAS
									  ON CAS.HBONE_NO = T1.HBONE_NO
							LEFT JOIN AC_TX_HBONE txhbone
									  ON txhbone.HBONE_NO = T1.HBONE_NO
							LEFT JOIN sync_ac_cust accust
									  ON txhbone.cust_no = accust.cust_no
				   WHERE 1 = 1
					 AND T1.CONSCUSTSTATUS = '0'
--一账通注册渠道与CRM-来源管理（新）大V合作来源编号相同时，不执行划转
					 AND accust.hbone_reg_outlet_code not in (select sourceno
															  from cm_sourceinfo_new
															  where first_level_code = 'K'
																AND second_level_code = '086'
																and rec_stat = '1')
					 AND (T1.SPECIALFLAG IS NULL OR T1.SPECIALFLAG = '0')
--投顾是‘公司同事库’，‘客户服务部-高端--深潜库’，‘零售-禁扰客户库’，‘零售-禁扰客户库’的客户不参与20w划转,
					 AND T5.CONSCODE not in ('weijietong', 'shenqianku', 'JR_CSLS', 'wuyixiang')
--用户处在10250大V合作的标签中时，不参与20w划转
					 AND NOT EXISTS (SELECT 1 FROM CM_BIGV_CUST BIGV WHERE T1.CONSCUSTNO = BIGV.CONSCUSTNO)
--限制客户当前投顾在客服（零售+高端）部门下，且不含20w临时库
					 AND CONS.CONSCODE IN
						 (SELECT T1.CONSCODE
						  FROM CM_CONSULTANT T1
								   LEFT JOIN HB_ORGANIZATION T2
											 ON T1.OUTLETCODE = T2.ORGCODE
						  WHERE 1 = 1
							AND T2.ORGCODE = '12'
							AND T1.CONSSTATUS = '1'
						  UNION
						  SELECT T1.CONSCODE
						  FROM CM_CONSULTANT T1
								   LEFT JOIN HB_ORGANIZATION T2
											 ON T1.OUTLETCODE = T2.ORGCODE
						  WHERE T1.CONSCODE
					   != '20wTemporaryLibrary'
					 AND T1.CONSCODE != 'leadsFPK'
					 AND T2.ORGCODE = '11'
					 AND T1.CONSSTATUS = '1')
			AND CAS.RETAIL_AMOUNT >= 200000
--分销机构是理财通客户不参与划转
			AND NOT EXISTS (SELECT 1
			FROM AC_TX_HBONE A
			WHERE A.CUST_NO IN
			(SELECT AC.CUST_NO
			From SYNC_AC_DIS_FUND_TX_ACCT AC
			WHERE AC.CUST_NO IN
			(SELECT A.CUST_NO
			FROM SYNC_AC_DIS_FUND_TX_ACCT A
			GROUP BY A.CUST_NO
			HAVING COUNT (A.CUST_NO) = 1)
			AND AC.DIS_CODE in ('LCT00K001', 'EFUNDS001'))
			and A.HBONE_NO = T1.HBONE_NO)) BB
			ON (AA.CUSTNO = BB.CONSCUSTNO)
			--如果匹配，则将客户划转到20wTemporaryLibrary
			WHEN MATCHED THEN
		UPDATE
			SET AA.CONSCODE = '20wTemporaryLibrary',
			AA.STARTDT = TO_CHAR(SYSDATE, 'yyyyMMdd'),
			AA.MODIFIER = 'sys',
			AA.MODDT = TO_CHAR(SYSDATE, 'yyyyMMdd'),
			AA.BINDDATE = SYSDATE,
			AA.OPERATE_DATE = SYSDATE
	</update>


	<select id="queryCustconstantByHboneNo" resultMap="BaseResultMap" parameterType="map">
		SELECT
		T1.CONSCODE,
		T4.ORGCODE as ORGNAME
		FROM CM_CUSTCONSTANT T1, CM_CONSULTANT T2, CM_CONSCUST T3 ,HB_ORGANIZATION T4
		WHERE T1.CONSCODE = T2.CONSCODE
		AND T2.CONSSTATUS = '1'
		AND T3.CONSCUSTSTATUS = '0'
		AND T2.OUTLETCODE = T4.ORGCODE
		AND T1.CUSTNO = T3.CONSCUSTNO
		AND T3.HBONE_NO = #{hboneNo}
	</select>

	<select id="queryConsCodeByHkTxAcctNo" resultType="string" parameterType="string">
		SELECT
		T1.CONSCODE
		FROM
		CM_HK_CONSCUST T4
		LEFT JOIN CM_CONSCUST T3 ON T4.CONSCUSTNO = T3.CONSCUSTNO
		LEFT JOIN CM_CUSTCONSTANT T1 ON T3.CONSCUSTNO = T1.CUSTNO
		LEFT JOIN CM_CONSULTANT T2 ON T1.CONSCODE = T2.CONSCODE
		WHERE
		T2.CONSSTATUS = '1'
		AND T3.CONSCUSTSTATUS = '0'
		AND T4.HK_TX_ACCT_NO= #{hkTxAcctNo,jdbcType=VARCHAR}
	</select>
    
    <select id="queryCreateCustDt" parameterType="string" resultType="java.lang.String">
		select to_char(min(t.stimestamp),'yyyymmdd') CreateCustDt from cm_conscusthis t where t.conscustno=#{custno}
	</select>

	<select id="queryConsConsListByCust" parameterType="string" resultType="java.lang.String">
		SELECT T2.WECHATCONSCODE
		FROM CM_CUSTCONSTANT T1, CM_CONSULTANT T2
		WHERE T1.CONSCODE = T2.CONSCODE
		  AND T2.CONSSTATUS = '1'
		<if test="conscustnos != null and conscustnos.size()>0 ">
			AND T1.CUSTNO in
			<foreach collection="conscustnos" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="ListConsInfoForIc" resultType="com.howbuy.crm.nt.consultant.dto.CmConsInfoDomain">
        SELECT A.CONSCODE,A.CONSNAME FROM CM_CONSULTANT A WHERE A.OUTLETCODE IN (
	SELECT ORGCODE
	FROM HB_ORGANIZATION
	START WITH ORGCODE IN ('1', '10')
	CONNECT BY PARENTORGCODE = PRIOR ORGCODE) AND A.Consstatus = '1'
    </select>
    
    <select id="queryConsInfoByHboneno" resultType="com.howbuy.crm.nt.consultant.dto.CmConsInfoDomain" parameterType="map">
        SELECT T1.CUSTNO,
		       T1.CONSCODE,
		       T2.CONSNAME,
		       T4.ORGNAME,
		       T2.TELNO,
		       T2.EMAIL,
		       T2.MOBILE,
		       T2.PICADDR PICTUREURL,
		       T2.PICADDR1 PICADDR,
		       T2.CODEPICADDR,
		       T2.ADDWECHATAPPLETROUTING as addWechatAppletRouting,
		       NVL(HC.CONSTDESC, '好买资深投资顾问') POSITION
		  FROM CM_CUSTCONSTANT T1
		  LEFT JOIN CM_CONSULTANT T2
		    ON T1.CONSCODE = T2.CONSCODE
		   AND T2.CONSSTATUS = '1'
		  LEFT JOIN CM_CONSCUST T3
		    ON T1.CUSTNO = T3.CONSCUSTNO
		   AND T3.CONSCUSTSTATUS = '0'
		  LEFT JOIN HB_ORGANIZATION T4
		    ON T2.OUTLETCODE = T4.ORGCODE
		  LEFT JOIN HB_CONSTANT HC
		    ON T2.CONSLEVEL = HC.CONSTCODE
		   AND HC.TYPECODE = 'conslevelout'
		 WHERE T3.HBONE_NO = #{hboneNo}
    </select>

	<select id="queryTierCount" parameterType="String" resultType="int">
		SELECT nvl(max(level), 0) as tierCount
		FROM HB_ORGANIZATION t
		where t.status = '0'
		START WITH ORGCODE = (select cc.outletcode from CM_CONSULTANT cc where cc.conscode = #{consCode})
		CONNECT BY PRIOR PARENTORGCODE = ORGCODE
	</select>

	<select id="queryDeptNameWithGivenTier" parameterType="map" resultType="String">
		select orgName from
		(select rownum as rn, tt.* from (SELECT level as h, t.*
			FROM HB_ORGANIZATION t
			where t.status = '0'
			START WITH ORGCODE = (select cc.outletcode from CM_CONSULTANT cc where cc.conscode = #{consCode})
			CONNECT BY PRIOR PARENTORGCODE = ORGCODE
		order by level desc) tt) where rn = #{tier}
	</select>
	
	<select id="listConsInfoAllMoile" resultMap="BaseResultMap" parameterType="map">
        SELECT T.CONSCODE, T.MOBILE, T.MOBILE_DIGEST FROM CM_CONSULTANT T
    </select>
    
    <update id="batchUpdateConsMoileDigest" parameterType="java.util.List">
	      <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
	          update CM_CONSULTANT 
	          <set>
		            MOBILE_DIGEST = #{item.mobileDigest}
	          </set>
	          where CONSCODE = #{item.consCode}
       	  </foreach>
       </update>

	<update id="batchUpdateCodepicaddr" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">
			update CM_CONSULTANT
			<set>
				CODEPICADDR = #{item.codePicAddr}
			</set>
			where CONSCODE = #{item.consCode}
		</foreach>
	</update>

	<select id="selectMobileCount" parameterType="String" resultType="Integer">
		SELECT COUNT(*)
		FROM CM_CONSULTANT CCS
		WHERE CCS.CONSSTATUS = '1'
		  AND CCS.MOBILE_DIGEST IN
			  (SELECT CUST.MOBILE_DIGEST
			   FROM CM_CONSCUST CUST
			   WHERE CUST.CONSCUSTNO = #{custno})
	</select>

	<select id="getConsCodeListByRoleList" resultType="string" parameterType="list">
        SELECT C.CONSCODE
        FROM CM_CONSULTANT C
        LEFT JOIN HB_USERROLE HU  ON C.CONSCODE = HU.USERCODE
        <where>
			AND C.CONSSTATUS = '1'
			AND HU.ROLECODE IN
			<foreach collection="roleCodeList" open="(" close=")" separator="," item="item">
				#{item}
			</foreach>
		</where>
	</select>
	<select id="getOutletCodeByConsCode" resultType="java.lang.String">
    	select OUTLETCODE from CM_CONSULTANT_EXP where  userid = #{userid}
	</select>
	<select id="listLeadersByOrgCodeAndLevel" parameterType="Map" resultType="String" useCache="false">
		SELECT DISTINCT T1.userid
		FROM  CM_CONSULTANT_EXP T1
		WHERE T1.CHECKFLAG = '2'
		AND T1.WORKTYPE != '2'
		AND T1.OUTLETCODE = #{orgcode}
		<if test="userlevel != null and userlevel =='3'.toString() ">

			and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
			from dual
			connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
			intersect
			(select regexp_substr( '18|19|20|21|22|23|35|36|37', '[^|]+', 1, level)
			from dual
			connect by regexp_substr('18|19|20|21|22|23|35|36|37', '[^|]+', 1, level) is not null ) )

		</if>
		<if test="userlevel != null and userlevel =='5'.toString() ">
			and exists( (select regexp_substr( T1.CURMONTHLEVEL, '[^,]+', 1, level)
			from dual
			connect by regexp_substr(T1.CURMONTHLEVEL, '[^,]+', 1, level) is not null )
			intersect
			(select regexp_substr( '25|26|27|28', '[^|]+', 1, level)
			from dual
			connect by regexp_substr('25|26|27|28', '[^|]+', 1, level) is not null ) )
		</if>
	</select>

	<select id="listFLeadersByOrgCode" parameterType="Map" resultType="java.lang.String" useCache="false">
		select b.userid from  CM_CONSULTANT_EXP_ORG a,CM_CONSULTANT_EXP b
		where a.userid = b.userid
			and b.CHECKFLAG = '2'
		   AND b.WORKTYPE != '2'
		   and  a.orgcode =#{orgcode}
	</select>

	<select id="selectDelCustNo" resultType="string">
		SELECT T1.CONSCUSTNO
        FROM CM_CONSCUST T1
        WHERE T1.CONSCUSTSTATUS = '1'
	</select>

	<insert id="transferDelCustToHis" useGeneratedKeys="false">
		INSERT INTO CM_CUSTCONSTANTHIS
            (CUSTCONSHISID,
             CUSTNO,
             CONSCODE,
             STARTDT,
             ENDDT,
             MEMO,
             RECSTAT,
             CHECKFLAG,
             CREATOR,
             MODIFIER,
             CHECKER,
             CREDT,
             MODDT,
             BINDDATE,
             UNBUNDDATE)
            SELECT SEQ_CUSTREC.NEXTVAL,
                   F.CUSTNO,
                   F.CONSCODE,
                   F.STARTDT,
                   TO_CHAR(SYSDATE, 'yyyymmdd'),
                   F.MEMO,
                   F.RECSTAT,
                   F.CHECKFLAG,
                   F.CREATOR,
                   F.MODIFIER,
                   F.CHECKER,
                   F.CREDT,
                   F.MODDT,
                   F.BINDDATE,
                   SYSDATE
              FROM CM_CUSTCONSTANT F
             WHERE F.CONSCODE  <![CDATA[ <> ]]> 'yishanchukehu'
				AND F.CUSTNO IN (SELECT T1.CONSCUSTNO
								FROM CM_CONSCUST T1
								WHERE T1.CONSCUSTSTATUS = '1')
	</insert>

	<update id="updateDeletedConsCode" parameterType="list">
		UPDATE CM_CUSTCONSTANT T
     	SET T.CONSCODE = 'yishanchukehu'
   		WHERE T.CONSCODE <![CDATA[ <> ]]> 'yishanchukehu'
		AND T.CUSTNO IN
			(SELECT T1.CONSCUSTNO
			 FROM CM_CONSCUST T1
			 WHERE T1.CONSCUSTSTATUS = '1')
	</update>

	<select id="getDelCustWithNoConstant" resultType="string">
		SELECT T1.CONSCUSTNO
        FROM CM_CONSCUST T1
        WHERE T1.CONSCUSTSTATUS = '1'
            AND NOT EXISTS
                (SELECT 1 FROM CM_CUSTCONSTANT T2
                 WHERE T2.CUSTNO = T1.CONSCUSTNO)
	</select>

	<insert id="bindDelConstant">
		INSERT INTO CM_CUSTCONSTANT
      		(CUSTNO, CONSCODE, CREATOR, RECSTAT, STARTDT, CREDT, BINDDATE)
    	VALUES
      		(#{custNo},
		   'yishanchukehu',
		   'sys',
		   1,
		   TO_CHAR(#{date}, 'yyyymmdd'),
		   TO_CHAR(#{date}, 'yyyymmdd'),
		   #{date,jdbcType=DATE})
	</insert>

    <select id="listConsCode" resultType="java.lang.String">
		SELECT T.CONSCODE FROM CM_CONSULTANT T WHERE T.CONSSTATUS = '1'
	</select>

    <select id="countNormal" resultType="long">
		select count(1)
		from CM_CONSULTANT T WHERE T.CONSSTATUS = '1'
	</select>
</mapper>
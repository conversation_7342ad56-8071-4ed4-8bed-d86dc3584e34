/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.consultant.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.bean.DisCustInfoBean;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.UpdateCustconstantInfoRequest;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.domain.CmConsultantBO;
import com.howbuy.crm.util.MainLogUtils;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.HttpUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;

/**
 * @description: (20w客户分配serivce实现)
 * <AUTHOR>
 * @date 2023/8/8 20:11
 * @since JDK 1.8
 */
@Service("cmCustConstantService")
public class CmCustConstantServiceImpl implements CmCustConstantService{

    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    @Value("${QUERY_COOPCUSTINFO_API}")
    private String queryCoopCustInfoApi;
    @Autowired
    private QueryAllCustInfoAndDisCustInfoFacade queryAllCustInfoAndDisCustInfoFacade;

    @Autowired
    private UpdateCustconstantInfoService updateCustconstantInfoService;
    /**
     * 20w临时库
     */
    private static final String CONS_CODE_20W = "20wTemporaryLibrary";
    /**
     * 自动划转人
     */
    private static final String OPT_MAN = "sys";
    /**
     * 线程池
     */
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10,
            10,
            1,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<Runnable>());

    /**
     * @description:(零售市值达到20万客户转分配到CS的20万临时库 --存储过程业务处理方法实现)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/17 09:21
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeTo20WTemporaryLibrary() {
        /**
         * 初步筛选出需要划转的客户
         * 条件 1.客户状态正常
         *      2.来源是“闻石”和“大V”客户不参与20w划转
         *      3.一账通注册渠道与CRM-来源管理（新）大V合作来源编号相同时，不执行划转  （历史逻辑，应该包含在条件2（张培远说的）， 但为防止错误还保留）
         *      4.限制客户当前投顾在客服（零售+高端）部门下，且不含20w临时库
         *      5.零售市值超过20万
         */
        List<CmConsultantBO> list = cmConsultantMapper.query20WTemporaryLibrary(CONS_CODE_20W);
        if(CollectionUtils.isNotEmpty(list)){
            List<CustconstantInfoDomain> updateList = new CopyOnWriteArrayList<>();
            CompletableFuture[] futures = new CompletableFuture[list.size()];
            int i = 0;
            //开线程校验是否添加到划转列表
            for(CmConsultantBO cmConsultantBO : list){
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> checkAddToList(cmConsultantBO, updateList), threadPoolExecutor);
                futures[i] = future;
                i++;
            }
            //线程聚合
            CompletableFuture.allOf(futures).join();
            //调core划转接口划转
            UpdateCustconstantInfoRequest updateCustconstantInfoRequest = new UpdateCustconstantInfoRequest();
            updateCustconstantInfoRequest.setListCustconstantInfoDomain(updateList);
            updateCustconstantInfoService.batchUpdateCustConstant(updateCustconstantInfoRequest);
        }
    }

    /**
     * @description 校验是否添加到划转列表
     * @param cmConsultantBO
     * @param updateList
     * @return void
     * @author: jianjian.yang
     * @date: 2023/9/13 18:22
     * @since JDK 1.8
     */
    private void checkAddToList(CmConsultantBO cmConsultantBO, List<CustconstantInfoDomain> updateList){
        //客户不是【大V客户】
        if(isVip(cmConsultantBO.getHboneNo())){
            return;
        }
        //客户存在分销机构为“好买分销”的开户信息
        if(!existHowbuyDisCode(cmConsultantBO.getPubCustNo())){
            return;
        }
        CustconstantInfoDomain custconstantInfoDomain = new CustconstantInfoDomain();
        custconstantInfoDomain.setCustno(cmConsultantBO.getConscustNo());
        custconstantInfoDomain.setStartdt(DateUtil.getDateYYYYMMDD());
        custconstantInfoDomain.setConscode(CONS_CODE_20W);
        custconstantInfoDomain.setNextcons(CONS_CODE_20W);
        custconstantInfoDomain.setCreator(OPT_MAN);
        custconstantInfoDomain.setModifier(OPT_MAN);
        custconstantInfoDomain.setModdt(DateUtil.getDateYYYYMMDD());
        updateList.add(custconstantInfoDomain);
    }

    /**
     * @description 是否存在好买分销
     * @param pubCustNo
     * @return boolean
     * @author: jianjian.yang
     * @date: 2023/9/13 13:53
     * @since JDK 1.8
     */
    private boolean existHowbuyDisCode(String pubCustNo){
        QueryAllCustInfoAndDisCustInfoRequest dubboREQ = new QueryAllCustInfoAndDisCustInfoRequest();
        dubboREQ.setTxAcctNo(pubCustNo);
        QueryAllCustInfoAndDisCustInfoResponse dubboRSP = queryAllCustInfoAndDisCustInfoFacade.execute(dubboREQ);
        if(StaticVar.ZT_SUCCESS_FLAG.equals(dubboRSP.getReturnCode())){
            List<DisCustInfoBean> disList = dubboRSP.getDisCustInfoList();
            if(CollectionUtils.isNotEmpty(disList)){
                for(DisCustInfoBean disCustInfoBean : disList) {
                    if (DisCodeEnum.HOWBUY.getCode().equals(disCustInfoBean.getDisCode())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * @description 判断是否大V客户
     * @param hboneNo
     * @return boolean
     * @author: jianjian.yang
     * @date: 2023/9/13 13:29
     * @since JDK 1.8
     */
    private boolean isVip(String hboneNo){
        JSONObject requestObject = new JSONObject();
        requestObject.put("hboneNo", hboneNo);
        long startTime = System.currentTimeMillis();
        String result = HttpUtils.jsonPost(queryCoopCustInfoApi, requestObject);
        long endTime = System.currentTimeMillis();
        MainLogUtils.httpCallOut(queryCoopCustInfoApi, String.valueOf(HttpStatus.OK.value()), endTime - startTime);
        List<String> types = Arrays.asList("1", "2", "3", "4", "7", "8");
        JSONObject response = JSON.parseObject(result);
        String custType = (String)response.get("custType");
        return custType != null && types.contains(custType);
    }

}
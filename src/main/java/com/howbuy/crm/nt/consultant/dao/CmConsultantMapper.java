package com.howbuy.crm.nt.consultant.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.consultant.domain.CmConsultantBO;
import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import com.howbuy.crm.nt.consultant.dto.CmConsInfoDomain;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 
 * <AUTHOR>
 *
 */
@MapperScan
public interface CmConsultantMapper{

	/**
	 * 查询
	 * @param params
	 * @return
	 */
	CmConsultantInfo queryConsultantInfo(Map<String, Object> params);
	
	/**
	 * 根据一账通查询
	 * @param params
	 * @return
	 */
	CmConsultantInfo queryConsInfoByHboneNo(Map<String, Object> params);


	/**
	 * @description:(将符合条件的客户划转到投顾客户表中并且更新conscode = '20wTemporaryLibrary')
	 * @param
	 * @return void
	 * @author: xufanchao
	 * @date: 2023/8/17 09:52
	 * @since JDK 1.8
	 */
	void mergeTo20WTemporaryLibrary();

	/**
	 *  根据一账通查询对应的投顾以及所属部门号
	 * @param params
	 * @return
	 */
	CmConsultantInfo queryCustconstantByHboneNo(Map<String, Object> params);


	/**
	 *  根据 香港交易账号 查询对应的投顾
	 * @param hkTxAcctNo
	 * @return
	 */
	String queryConsCodeByHkTxAcctNo(@Param("hkTxAcctNo") String hkTxAcctNo);

	List<String> queryConsConsListByCust(@Param("conscustnos") List<String> conscustnos);

	List<CmConsInfoDomain> ListConsInfoForIc();

	/**
	 * 根据客户一账通查询客户对应的投顾的信息
	 * @param params
	 * @return
	 */
	CmConsInfoDomain queryConsInfoByHboneno(Map<String, Object> params);

	/**
	 * 获取指定投顾所在部门所处的层级
	 * @param consCode 投顾号
	 * @return
	 */
	int queryTierCount(String consCode);

	/**
	 * 获取指定投顾，指定层级的所属部门名称
	 * @param params
	 * @return
	 */
	String queryDeptNameWithGivenTier(Map<String, Object> params);
	
	/**
	 * 查询投顾的手机号和手机摘要
	 * @param params
	 * @return
	 */
	List<CmConsultantInfo> listConsInfoAllMoile(Map<String, Object> params);
	
	/**
	 * 批量更新手机号的摘要
	 * @param listCmConsultantInfo
	 */
	void batchUpdateConsMoileDigest(List<CmConsultantInfo> listCmConsultantInfo);


	/**
	 * @description: 查询所有[正常]员工的consCode
	 * @param
	 * @return java.util.List<java.lang.String>
	 * @author: jin.wang03
	 * @date: 2024/8/19 14:35
	 * @since JDK 1.8
	 */
	List<String> listConsCode();

	/**
	 * @description: 查询所有[正常]员工的数量
	 * @param
	 * @return long
	 * @author: jin.wang03
	 * @date: 2024/8/19 14:41
	 * @since JDK 1.8
	 */
	long countNormal();

	/**
	 * @description: 批量更新投顾的 [添加企微地址] 字段
	 * @param listCmConsultantInfo
	 * @return void
	 * @author: jin.wang03
	 * @date: 2024/8/19 14:07
	 * @since JDK 1.8
	 */
	void batchUpdateCodepicaddr(List<CmConsultantInfo> listCmConsultantInfo);


	/**
	 * @description:(查询当前客户手机号是否投顾手机号)
	 * @param custno
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/6/6 18:08
	 * @since JDK 1.8
	 */
	int selectMobileCount(@Param("custno") String custno);


	/**
	 * 根据角色列表 返回 持有角色的 consCode 列表
	 * @param roleCodeList NOT NULL
	 * @return List<String>
	 */
	List<String> getConsCodeListByRoleList(@Param("roleCodeList") List<String> roleCodeList);
	
	/**
	 * 根据客户号查询客户落表时间
	 * @param custno
	 * @return
	 */
	String queryCreateCustDt(String custno);

	/**
	 * 根据投顾代码去花名册表查询部门code
	 * @param conscode
	 * @return
	 */
    String getOutletCodeByConsCode(String conscode);

	/**
	 * 花名册查询领导
	 * @param paramleader
	 * @return
	 */
	List<String> listLeadersByOrgCodeAndLevel(Map<String, String> paramleader);
	/**
	 * 花名册查询区域副总
	 * @param paramleader
	 * @return
	 */
	List<String> listFLeadersByOrgCode(Map<String, String> paramleader);

	/**
	 * 查询需要划转到20W临时库的数据
	 * @param consCode20w
	 * @return java.util.List<com.howbuy.crm.nt.consultant.domain.CmConsultantBO>
	 * @author: jianjian.yang
	 * @date: 2023/9/13 13:08
	 * @since JDK 1.8
	 */
	List<CmConsultantBO> query20WTemporaryLibrary(@Param("consCode20w") String consCode20w);

	/** @description 投顾客户状态为1的数据
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/27 下午1:06
	 * @since JDK 1.8
	 */
	List<String> selectDelCustNo();

	/**
	 * @description 批量新增进his表
	 * @param custNos
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/27 下午1:07
	 * @since JDK 1.8
	 */
	int transferDelCustToHis();

	/**
	 * @description 更新cm_custconstant.conscode已删除客户
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/27 下午1:10
	 * @since JDK 1.8
	 */
	int updateDeletedConsCode();

	/**
	 * @description 查询没有投顾的已删除客户
	 * @return custNo
	 * <AUTHOR>
	 * @date 2023/9/27 下午1:14
	 * @since JDK 1.8
	 */
	List<String> getDelCustWithNoConstant();

	/**
	 * @description 给没有投顾的已删除客户添加yishanchukehu投顾信息
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/27 下午1:17
	 * @since JDK 1.8
	 */
	int bindDelConstant(@Param("custNo")String custNo, @Param("date") Date date);
}

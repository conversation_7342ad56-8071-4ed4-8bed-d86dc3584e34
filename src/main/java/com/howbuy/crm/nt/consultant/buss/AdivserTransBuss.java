/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.consultant.buss;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @description: (自动划转已删除客户投顾)
 * <AUTHOR>
 * @date 2023/9/27 上午11:10
 * @since JDK 1.8
 */
@Component
@Slf4j
public class AdivserTransBuss {

    @Autowired
    private CmConsultantMapper cmConsultantMapper;
    @Autowired
    private AdivserTransBuss adivserTransBuss;

    /**
     * @description 划转投顾
     * @return
     * <AUTHOR>
     * @date 2023/10/20 2:20 PM
     * @since JDK 1.8
     */
    public void transAdviser() {
        adivserTransBuss.doCopyHisAndUpdateConsCode();
        doAddConstant();
    }

    /**
     * @description 备份his表+更新投顾表conscode='yishanchukehu'
     * @return
     * <AUTHOR>
     * @date 2023/9/27 下午1:45
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class)
    public void doCopyHisAndUpdateConsCode() {
        int transHisNum = cmConsultantMapper.transferDelCustToHis();
        log.info("[自动划转已删除客户投顾job]备份进his表记录条数:{}", transHisNum);
        int updateNum = cmConsultantMapper.updateDeletedConsCode();
        log.info("[自动划转已删除客户投顾job]更新已删除客户状态记录条数:{}", updateNum);
    }

    /**
     * @description 新增投顾（没有投顾的已删除客户）
     * @return
     * <AUTHOR>
     * @date 2023/9/27 下午1:55
     * @since JDK 1.8
     */
    private void doAddConstant() {
        List<String> consCustNos = cmConsultantMapper.getDelCustWithNoConstant();
        log.info("[自动划转已删除客户投顾job]查询没有投顾的已删除客户：{}", consCustNos.size());
        if (CollectionUtils.isNotEmpty(consCustNos)) {
            consCustNos.forEach(consCustNo -> {
                try {
                    cmConsultantMapper.bindDelConstant(consCustNo, new Date());
                } catch (Exception e) {
                    log.error("[自动划转已删除客户投顾job]投顾客户关系表中增加已删除记录error:{}", Throwables.getStackTraceAsString(e));
                }
            });
        }
    }
}
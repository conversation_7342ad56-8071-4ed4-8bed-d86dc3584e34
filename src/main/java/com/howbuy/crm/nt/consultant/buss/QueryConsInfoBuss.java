package com.howbuy.crm.nt.consultant.buss;

import java.util.*;
import java.util.stream.Collectors;

import com.howbuy.crm.nt.base.model.NtBaseConstantEnum;
import com.howbuy.crm.nt.consultant.request.QueryConsListByCustRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsListByCustReponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import com.howbuy.crm.nt.consultant.dto.ConsInfoDomain;
import com.howbuy.crm.nt.consultant.request.QueryConsInfoRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsInfoReponse;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class QueryConsInfoBuss {

	private static final Logger log = LoggerFactory.getLogger(QueryConsInfoBuss.class);

	public static final int TIE_COUNT_THREE = 3;
	
	@Autowired
	private CmConsultantMapper cmConsultantMapper;
	
	public QueryConsInfoReponse queryConsInfoByHboneNo(QueryConsInfoRequest req) {
		log.info("QueryConsInfoRequest: " + JSON.toJSONString(req));
		QueryConsInfoReponse res = new QueryConsInfoReponse();
		// 获取传入的一账通号
		String hboneNo = req.getHboneNo();
		if (StringUtils.isBlank(hboneNo)) {
			res.invalidReqParams("一账通号不能为空！");
			return res;
		}
		
		// 设置查询参数
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("hboneNo", hboneNo);
		CmConsultantInfo cmConsultantInfo = cmConsultantMapper.queryConsInfoByHboneNo(params);

		if (cmConsultantInfo != null && StringUtils.isNotBlank(cmConsultantInfo.getConsCode())) {

			ConsInfoDomain consInfoDomain = new ConsInfoDomain();
			consInfoDomain.setConsCode(cmConsultantInfo.getConsCode());
			consInfoDomain.setConsName(cmConsultantInfo.getConsName());
			consInfoDomain.setTelNo(cmConsultantInfo.getTelNo());
			consInfoDomain.setEmail(cmConsultantInfo.getEmail());
			consInfoDomain.setMobile(cmConsultantInfo.getMobile());
			consInfoDomain.setPictureUrl(cmConsultantInfo.getPicAddr());
			consInfoDomain.setConscustNo(cmConsultantInfo.getCustNo());
			String orgName = cmConsultantInfo.getOrgName();
			consInfoDomain.setOrgName(orgName);
			consInfoDomain.setBelongArea(getBelongRegion(cmConsultantInfo.getConsCode(), orgName));
			consInfoDomain.setIsVirtual(cmConsultantInfo.getIsVirtual());
			//根据客户号查询客户落表CRM时间
			consInfoDomain.setCreateCustDt(cmConsultantMapper.queryCreateCustDt(cmConsultantInfo.getCustNo()));
			res.setConsInfoDomain(consInfoDomain);
			res.success();
		} else {
			res.noData("未找到相关信息！");
		}
		log.info("QueryConsInfoReponse: " + JSON.toJSONString(res));
		return res;
	}

	// 获取所属区域
	private String getBelongRegion(String consCode, String orgName) {
		int tierCount = cmConsultantMapper.queryTierCount(consCode);
		// 判断该投顾所在组织层级是否 >= 3
		if (tierCount >= TIE_COUNT_THREE) {
			// 是，则取第三级部门
			Map<String, Object> params = new HashMap<>();
			params.put("consCode", consCode);
			params.put("tier", 3);
			return cmConsultantMapper.queryDeptNameWithGivenTier(params);
		} else {
			// 否，则取其直属部门
			return orgName;
		}
	}

	/**
	 * @description 根据传参投顾客户号查询所属投顾
	 * <AUTHOR>
	 * @updateTime 2022/3/1 11:15
	 * @throws 
	 */
	public QueryConsListByCustReponse queryConsConsListByCust(QueryConsListByCustRequest req) {
		log.info("QueryConsListByCustRequest: {}",JSON.toJSONString(req));
		QueryConsListByCustReponse res = new QueryConsListByCustReponse();
		List<String> conscustnos = req.getConscustnos();
		List<String> conscodes = new ArrayList<>();

		//限制条数,【修改这里】
		int pointsDataLimit = 500;
		Integer size = conscustnos.size();

		//判断是否有必要分批
		if (pointsDataLimit < size) {
			//分批数
			int part = size / pointsDataLimit;
			log.info("共有: {}条,分为:{},批",size,part);

			for (int i = 0; i < part; i++) {
				List<String> listPage = conscustnos.subList(0, pointsDataLimit);
				log.info("第{}次,执行处理:{}",i,listPage);
				conscodes.addAll(cmConsultantMapper.queryConsConsListByCust(listPage));
				conscustnos.subList(0, pointsDataLimit).clear();
			}

			if (!conscustnos.isEmpty()) {
				//表示最后剩下的数据
				log.info("最后一批数据,执行处理:",conscustnos);
				conscodes.addAll(cmConsultantMapper.queryConsConsListByCust(conscustnos));
			}
		} else {
			log.info("不需要分批,执行处理:{}",conscustnos);
			conscodes.addAll(cmConsultantMapper.queryConsConsListByCust(conscustnos));
		}
		
		conscodes = conscodes.stream().distinct().collect(Collectors.toList());
		res.setConscodes(conscodes);
		res.success();

		log.info("QueryConsListByCustReponse: {}", JSON.toJSONString(res));
		return res;
	}

}

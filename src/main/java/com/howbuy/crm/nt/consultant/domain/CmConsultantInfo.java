package com.howbuy.crm.nt.consultant.domain;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class CmConsultantInfo implements Serializable {

	private static final long serialVersionUID = 7195195222582678680L;

	private String consCode;
	private String consName;
	private String character;
	private String mobile;
	private String mobileDigest;
	private String picAddr;
	private String codePicAddr;
	private String position;
	private String email;
	private String telNo;
	private String custNo;
	private String orgName;
	private String rolecode;
	private String isVirtual;
	private String createCustDt;

	/**
	 * 从业资格证书号码
	 */
	private String empCardNo;

	/**
	 * 是否进入花名册
	 */
	private String isExp;

	/**
	 * ！！！花名册中的-从业资格证书号码
	 */
	private String jjCardNo;

	/**
	 * 省 代码
	 */
	private String provCode;

	/**
	 * 市代码
	 */
	private String cityCode;

	/**
	 * 企业微信userId
	 */
	private String wechatConsCode;

	/**
	 * 添加微信小程序路由
	 */
	private String addWechatAppletRouting;

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getConsCode() {
		return consCode;
	}

	public void setConsCode(String consCode) {
		this.consCode = consCode;
	}

	public String getConsName() {
		return consName;
	}

	public void setConsName(String consName) {
		this.consName = consName;
	}

	public String getCharacter() {
		return character;
	}

	public void setCharacter(String character) {
		this.character = character;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobileDigest() {
		return mobileDigest;
	}

	public void setMobileDigest(String mobileDigest) {
		this.mobileDigest = mobileDigest;
	}

	public String getPicAddr() {
		return picAddr;
	}

	public void setPicAddr(String picAddr) {
		this.picAddr = picAddr;
	}

	public String getCodePicAddr() {
		return codePicAddr;
	}

	public void setCodePicAddr(String codePicAddr) {
		this.codePicAddr = codePicAddr;
	}

	public String getTelNo() {
		return telNo;
	}

	public void setTelNo(String telNo) {
		this.telNo = telNo;
	}

	public String getCustNo() {
		return custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getRolecode() {
		return rolecode;
	}

	public void setRolecode(String rolecode) {
		this.rolecode = rolecode;
	}

	public String getIsVirtual() {
		return isVirtual;
	}

	public void setIsVirtual(String isVirtual) {
		this.isVirtual = isVirtual;
	}

	public String getCreateCustDt() {
		return createCustDt;
	}

	public void setCreateCustDt(String createCustDt) {
		this.createCustDt = createCustDt;
	}

	public String getProvCode() {
		return provCode;
	}

	public void setProvCode(String provCode) {
		this.provCode = provCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getEmpCardNo() {
		return empCardNo;
	}

	public void setEmpCardNo(String empCardNo) {
		this.empCardNo = empCardNo;
	}

	public String getIsExp() {
		return isExp;
	}

	public void setIsExp(String isExp) {
		this.isExp = isExp;
	}

	public String getJjCardNo() {
		return jjCardNo;
	}

	public void setJjCardNo(String jjCardNo) {
		this.jjCardNo = jjCardNo;
	}

	public String getWechatConsCode() {
		return wechatConsCode;
	}

	public void setWechatConsCode(String wechatConsCode) {
		this.wechatConsCode = wechatConsCode;
	}


	public String getAddWechatAppletRouting() {
		return addWechatAppletRouting;
	}

	public void setAddWechatAppletRouting(String addWechatAppletRouting) {
		this.addWechatAppletRouting = addWechatAppletRouting;
	}
}

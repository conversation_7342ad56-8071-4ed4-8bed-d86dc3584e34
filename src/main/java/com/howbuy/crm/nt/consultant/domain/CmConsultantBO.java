package com.howbuy.crm.nt.consultant.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 查询划转20W库数据
 * @date 2023/9/13 13:03
 * @since JDK 1.8
 */
@Setter
@Getter
@ToString
public class CmConsultantBO implements Serializable {

    private static final long serialVersionUID = 7195195222582678680L;

    /**
     * 客户号
     */
    private String conscustNo;
    /**
     * 投顾编号
     */
    private String consCode;
    /**
     * 一账通号
     */
    private String hboneNo;
    /**
     * 公募客户号
     */
    private String pubCustNo;
}

package com.howbuy.crm.nt.consultant.buss;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.dto.CmConsInfoDomain;
import com.howbuy.crm.nt.consultant.request.QueryConsInfoRequest;
import com.howbuy.crm.nt.consultant.response.QueryCmConsInfoReponse;
import com.howbuy.crm.nt.consultant.response.QueryCmConsInfoListReponse;

/**
 * 
 * <AUTHOR>
 *
 */
@Component
public class QueryCmConsInfoBuss {

	private static final Logger log = LoggerFactory.getLogger(QueryCmConsInfoBuss.class);
	
	@Autowired
	private CmConsultantMapper cmConsultantMapper;
	
	public QueryCmConsInfoReponse queryConsInfoByHboneno(QueryConsInfoRequest req) {
		log.info("QueryConsInfoRequest: " + JSON.toJSONString(req));
		QueryCmConsInfoReponse res = new QueryCmConsInfoReponse();
		// 获取传入的一账通号
		String hboneNo = req.getHboneNo();
		if (StringUtils.isBlank(hboneNo)) {
			res.invalidReqParams("一账通号不能为空！");
			return res;
		}
		
		// 设置查询参数
		Map<String, Object> params = new HashMap<String, Object>(1);
		params.put("hboneNo", hboneNo);
		CmConsInfoDomain cmConsultantInfo = cmConsultantMapper.queryConsInfoByHboneno(params);
		if (cmConsultantInfo != null && StringUtils.isNotBlank(cmConsultantInfo.getConsCode())) {
			res.setCmConsInfoDomain(cmConsultantInfo);
			res.success();
		} else {
			res.noData("未找到相关信息！");
		}
		log.info("QueryCmConsInfoReponse: " + JSON.toJSONString(res));
		return res;
	}

	public QueryCmConsInfoListReponse listConsInfoForIc() {
		QueryCmConsInfoListReponse res = new QueryCmConsInfoListReponse();

		List<CmConsInfoDomain> cmConsultants = cmConsultantMapper.ListConsInfoForIc();
		if (cmConsultants != null && cmConsultants.size() > 0 ) {
			res.setCmConsInfoDomainlist(cmConsultants);
			res.success();
		} else {
			res.noData("未找到相关信息！");
		}
		log.info("QueryCmConsInfoReponse: " + JSON.toJSONString(res));
		return res;
	}

}

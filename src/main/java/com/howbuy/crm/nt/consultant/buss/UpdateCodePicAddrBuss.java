/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.consultant.buss;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.howbuy.crm.nt.qywechat.service.WebChatService;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.domain.CmConsultantInfo;
import com.howbuy.crm.nt.qywechat.dto.CmQueryWeChatShortChainRequest;
import com.howbuy.crm.nt.qywechat.dto.CmQueryWeChatShortChainResponse;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @description: 更新投顾的 [添加企微地址] 字段
 * <AUTHOR>
 * @date 2024/8/19 14:22
 * @since JDK 1.8
 */
@Slf4j
@Component
public class UpdateCodePicAddrBuss {


    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    @Autowired
    private WebChatService webChatService;


    @Value("${hz.applet.wx.path}")
    private String hzAppletWxPath;

    /**
     * 每次查询500条
     */
    private static final int PAGE_SIZE = 500;

    /**
     * @description: 更新投顾的 [添加企微地址] 字段
     * @param message
     * @return void
     * @author: jin.wang03
     * @date: 2024/8/19 13:09
     * @since JDK 1.8
     */
    public void updateCodePicAddr(SimpleMessage message) {
        List<String> consCodeList = getConsCodeList(message);
        if (CollectionUtils.isEmpty(consCodeList)) {
            log.info("UpdateCodePicAddrJob| 投顾号List为空，不更新");
            return;
        }


        List<CmConsultantInfo> updateList = new ArrayList<>();
        for (String consCode : consCodeList) {
            // 生成添加企微地址短链 , 原有的“企业微信二维码地址换名字添加企微地址短链"
            try {
                CmQueryWeChatShortChainRequest shortChainRequest = new CmQueryWeChatShortChainRequest();
                shortChainRequest.setAppletPath(hzAppletWxPath);
                Map<String, String> paramsMap = new HashMap<>();
                paramsMap.put("tgNo", consCode);
                paramsMap.put("userType", "tg.");
                paramsMap.put("consCode", consCode);
                paramsMap.put("from", "share");
                shortChainRequest.setParamsMap(paramsMap);
                CmQueryWeChatShortChainResponse chainResponse = queryWeChatShortChain(shortChainRequest);
                log.info("UpdateCodePicAddrJob| consCode:{} CmQueryWeChatShortChainResponse >>>>> result :{}", consCode, JSON.toJSONString(chainResponse));
                if (null == chainResponse || StringUtils.isBlank(chainResponse.getShortChain())) {
                    log.error("UpdateCodePicAddrJob| consCode:{}生成添加企微地址短链错误", consCode);
                    continue;
                }

                CmConsultantInfo cmConsultant = new CmConsultantInfo();
                cmConsultant.setConsCode(consCode);
                cmConsultant.setCodePicAddr(chainResponse.getShortChain());

                updateList.add(cmConsultant);
            } catch (Exception e) {
                log.error("UpdateCodePicAddrJob| queryWeChatShortChain>>>生成添加企微地址短链错误", e);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            log.info("UpdateCodePicAddrJob| 需要更新的投顾号数量为：{}", updateList.size());
            List<List<CmConsultantInfo>> partition = Lists.partition(updateList, PAGE_SIZE);
            for (List<CmConsultantInfo> cmConsultantInfos : partition) {
                cmConsultantMapper.batchUpdateCodepicaddr(cmConsultantInfos);
            }
        }

    }

    /**
     * @description: 获取需要更新的投顾code
     * @param message
     * @return java.util.List<java.lang.String>
     * @author: jin.wang03
     * @date: 2024/8/19 14:46
     * @since JDK 1.8
     */
    private List<String> getConsCodeList(SimpleMessage message) {
        List<String> consCodeList = new ArrayList<>();

        if (Objects.nonNull(message) && Objects.nonNull(message.getContent())) {
            // 如果调度的入参中指定了 投顾号，则直接使用
            Map<String, Object> requestMap = JSON.parseObject(message.getContent().toString());
            if (requestMap.containsKey("consCodeList")) {
                return (List<String>) requestMap.get("consCodeList");
            }

        }

        // 默认情况下，查询正常员工的 投顾号
        long count = cmConsultantMapper.countNormal();
        log.info("CM_CONSULTANT表 正常员工 的记录总数为：{}", count);
        if (count <= 0L) {
            return consCodeList;
        }

        // 分批处理，每次500条
        long cycNum = count / PAGE_SIZE + 1;

        for (int i = 1; i <= cycNum; i++) {
            PageHelper.startPage(i, PAGE_SIZE);
            List<String> consCode1List = cmConsultantMapper.listConsCode();

            consCodeList.addAll(consCode1List);
        }

        return consCodeList;
    }

    /**
     * @description: 获取 企微地址短链
     * @param request
     * @return com.howbuy.crm.http.model.CmQueryWeChatShortChainResponse
     * @author: jin.wang03
     * @date: 2024/8/19 13:48
     * @since JDK 1.8
     */
    public CmQueryWeChatShortChainResponse queryWeChatShortChain(CmQueryWeChatShortChainRequest request) {
        CmQueryWeChatShortChainResponse chainResponse = new CmQueryWeChatShortChainResponse();
        String generatedUrl = webChatService.getGeneratedUrl(request.getAppletPath(), request.getParamsMap());
        chainResponse.setShortChain(generatedUrl);
        return chainResponse;
    }

}
package com.howbuy.crm.nt.consultant.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.consultant.buss.QueryConsultantExpBuss;
import com.howbuy.crm.nt.consultant.dto.CmConsultantExpDomain;
import com.howbuy.crm.nt.consultant.request.QueryConsultantExpRequest;
import com.howbuy.crm.nt.consultant.response.QueryConsultantExpReponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 
 * <AUTHOR>
 *
 */
@Service("queryConsultantExpService")
public class QueryConsultantExpServiceImpl implements QueryConsultantExpService {

	private static final Logger log = LoggerFactory.getLogger(QueryConsultantExpServiceImpl.class);
	
	@Autowired
	private QueryConsultantExpBuss queryConsultantExpBuss;
	
	@Override
	public QueryConsultantExpReponse queryConsultantExpLead(QueryConsultantExpRequest request) {
		log.info("queryConsultantExpLead_request is: " + JSON.toJSONString(request));

		QueryConsultantExpReponse response = new QueryConsultantExpReponse();
		if(this.validate(response, request)) {
			CmConsultantExpDomain consultantInfo = queryConsultantExpBuss.queryConsultantExpLead(request.getConscode());
			
			if(consultantInfo != null) {
				response.setCmConsultantExpDomain(consultantInfo);
			}
			log.info("queryConsultantExpLead_reponse is: " + JSON.toJSONString(response));
		}
		response.success();
		return response;
	}

	public boolean validate(QueryConsultantExpReponse response, QueryConsultantExpRequest request) {
		boolean flag = true;
		// 校验传入数据
		if (StringUtils.isBlank(request.getConscode())) {
			response.invalidReqParams("传参对象为空！");
			flag = false;
		}
		
		return flag;
	}
}

package com.howbuy.crm.nt.consultant.dao;

import org.mybatis.spring.annotation.MapperScan;

/**
 * @description:(客户信息历史表mapper层)
 * @return
 * @author: xufanchao
 * @date: 2023/8/17 09:14
 * @since JDK 1.8
 */
@MapperScan
public interface CmConsultantHisMapper {

    /**
     * @description:(把分配后的数据插入到客户信息的历史表中)
     * @param
     * @return int
     * @author: xufancha<PERSON>
     * @date: 2023/8/17 09:14
     * @since JDK 1.8
     */
    int insertCmCustConscustantHis();



}
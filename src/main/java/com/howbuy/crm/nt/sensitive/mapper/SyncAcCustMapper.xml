<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.SyncAcCustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.SyncAcCust">
        <result column="CUST_NO" property="custno" jdbcType="VARCHAR"/>
        <result column="CORP_ID_NO" property="corpidno" jdbcType="VARCHAR"/>
        <result column="ID_NO" property="idno" jdbcType="VARCHAR"/>
        <result column="CORP_ID_NO_DIGEST" property="corpidnodigest" jdbcType="VARCHAR"/>
        <result column="CORP_ID_NO_MASK" property="corpidnomask" jdbcType="VARCHAR"/>
        <result column="ID_NO_DIGEST" property="idnodigest" jdbcType="VARCHAR"/>
        <result column="ID_NO_MASK" property="idnomask" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listSyncAcCustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT CUST_NO,
		       CORP_ID_NO,
		       ID_NO
		 FROM SYNC_AC_CUST 
		 WHERE (  (CORP_ID_NO is not null AND (CORP_ID_NO_DIGEST is null or CORP_ID_NO_MASK is null) ) 
		         OR  (ID_NO is not null AND (ID_NO_DIGEST is null or ID_NO_MASK is null) )
		        )
		 <if test="startId !=null"> AND to_number(CUST_NO) &gt;= #{startId} </if>
		 <if test="endId !=null"> AND to_number(CUST_NO) &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY CUST_NO
    </select>
    
    
    <insert id="batchUpdateSyncAcCust" parameterType="com.howbuy.crm.nt.sensitive.dto.SyncAcCust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update SYNC_AC_CUST 
        set 
           CORP_ID_NO_DIGEST = #{item.corpidnodigest,jdbcType=VARCHAR},CORP_ID_NO_MASK = #{item.corpidnomask,jdbcType=VARCHAR},
           ID_NO_DIGEST = #{item.idnodigest,jdbcType=VARCHAR},ID_NO_MASK = #{item.idnomask,jdbcType=VARCHAR}
        where CUST_NO = #{item.custno};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
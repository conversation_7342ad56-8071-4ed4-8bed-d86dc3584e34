<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsLeavemsgCustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsLeavemsgCust">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="CALLER" property="caller" jdbcType="VARCHAR"/>
        <result column="CALLER_DIGEST" property="callerDigest" jdbcType="VARCHAR"/>
        <result column="CALLER_MASK" property="callerMask" jdbcType="VARCHAR"/>
        <result column="CALLER_CIPHER" property="callerCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsLeavemsgCustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT ID,
		       CALLER
		 FROM CS_LEAVEMSG_CUST 
		 WHERE ( CALLER is not null and (CALLER_DIGEST is null or CALLER_MASK is null or CALLER_CIPHER is null) )
		 <if test="startId !=null"> AND ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY dumpdate
    </select>
    
    
    <insert id="batchUpdateCsLeavemsgCust" parameterType="com.howbuy.crm.nt.sensitive.dto.CsLeavemsgCust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_LEAVEMSG_CUST 
        set 
           CALLER_DIGEST = #{item.callerDigest,jdbcType=VARCHAR},CALLER_MASK = #{item.callerMask,jdbcType=VARCHAR},CALLER_CIPHER = #{item.callerCipher,jdbcType=VARCHAR}
        where ID = #{item.id};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
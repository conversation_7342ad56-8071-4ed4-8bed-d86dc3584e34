<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsCallinMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsCallin">
        <result column="CALLINID" property="callinid" jdbcType="DECIMAL"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsCallinLimit200" parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT CALLINID,
		       MOBILE
		 FROM CS_CALLIN 
		 WHERE ( MOBILE is not null and (MOBILE_DIGEST is null or MOBILE_MASK is null or MOBILE_CIPHER is null) )
		 <if test="startId !=null"> AND CALLINID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND CALLINID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY RECORD_DT
    </select>
    
    
    <insert id="batchUpdateCsCallin" parameterType="com.howbuy.crm.nt.sensitive.dto.CsCallin">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_CALLIN 
        set 
           MOBILE_DIGEST = #{item.mobileDigest,jdbcType=VARCHAR},MOBILE_MASK = #{item.mobileMask,jdbcType=VARCHAR},MOBILE_CIPHER = #{item.mobileCipher,jdbcType=VARCHAR}
        where CALLINID = #{item.callinid};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
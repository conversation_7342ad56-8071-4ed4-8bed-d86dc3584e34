package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsCalloutWaitdistributeMapper;
import com.howbuy.crm.nt.sensitive.dto.CsCalloutWaitdistribute;

/**
 * 刷待分配脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsCalloutWaitdistributeService")
public class EncyCsCalloutWaitdistributeServiceImpl implements EncyCsCalloutWaitdistributeService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsCalloutWaitdistributeServiceImpl.class);

	@Autowired
	private CsCalloutWaitdistributeMapper csCalloutWaitdistributeMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyCsCalloutWaitdistributeData(String arg) {
		logger.info("处理待分配脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");
		
		List<CsCalloutWaitdistribute> listCsCalloutWaitdistribute = null;
        // 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
        // 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询待分配数据：每次查询两百条
				listCsCalloutWaitdistribute = csCalloutWaitdistributeMapper.listCsCalloutWaitdistributeLimit200(param);
				
				//处理脱敏三要素
				handleCsCalloutWaitdistributeList(listCsCalloutWaitdistribute);
				
				//更新待分配表
				int count = this.updateCsCalloutWaitdistributeBatch(listCsCalloutWaitdistribute);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理待分配脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理待分配脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsCalloutWaitdistributeList(List<CsCalloutWaitdistribute> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			List<String> emailList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsCalloutWaitdistribute csCalloutWaitdistribute : list){
				String mobile = csCalloutWaitdistribute.getMobile();
				String email = csCalloutWaitdistribute.getEmail();
				if(StringUtils.isNotBlank(mobile)){
					csCalloutWaitdistribute.setMobileDigest(DigestUtil.digest(mobile.trim()));
					csCalloutWaitdistribute.setMobileMask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
				
				if(StringUtils.isNotBlank(email)){
					csCalloutWaitdistribute.setEmailDigest(DigestUtil.digest(email.trim().toLowerCase()));
					csCalloutWaitdistribute.setEmailMask(MaskUtil.maskEmail(email.trim().toLowerCase()));
					emailList.add(email.trim().toLowerCase());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("待分配mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求待分配手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求待分配手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CsCalloutWaitdistribute csCalloutWaitdistribute : list){
	    				if(StringUtils.isNotBlank(csCalloutWaitdistribute.getMobile())){
	    					csCalloutWaitdistribute.setMobileCipher(mobilemap.get(csCalloutWaitdistribute.getMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("待分配手机mobileList加密结束时间"+System.currentTimeMillis());
			}
			
			
			if(emailList != null && emailList.size() > 0){
    			logger.info("待分配邮件emailList条数:"+emailList.size()+";加密开始时间"+System.currentTimeMillis());
    			logger.info("请求待分配邮件加密参数："+JSON.toJSONString(emailList));
	    		CodecBatchResponse resemail = encryptBatchFacade.encryptBatch(emailList);
	    		logger.info("请求待分配邮件加密返回："+JSON.toJSONString(resemail));
	    		if(resemail != null && resemail.getCodecMap() != null){
	    			Map<String,String> emailmap = resemail.getCodecMap();
	    			for(CsCalloutWaitdistribute csCalloutWaitdistribute : list){
	    				if(StringUtils.isNotBlank(csCalloutWaitdistribute.getEmail())){
	    					csCalloutWaitdistribute.setEmailCipher(emailmap.get(csCalloutWaitdistribute.getEmail().trim().toLowerCase()));
	    				}
	    			}
	    		}
	    		logger.info("待分配邮件emailList加密结束时间"+System.currentTimeMillis());
    		}
			
			logger.info("保存待分配数据："+JSON.toJSONString(list));
		}

	}
	
	/**
	 * 插入待分配数据
	 * @param list
	 * @return
	 */
	private int updateCsCalloutWaitdistributeBatch(List<CsCalloutWaitdistribute> list){
		if(CollectionUtils.isNotEmpty(list)){
			csCalloutWaitdistributeMapper.batchUpdateCsCalloutWaitdistribute(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsCalloutRecMapper;
import com.howbuy.crm.nt.sensitive.dto.CsCalloutRec;

/**
 * 刷外呼记录流水脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsCalloutRecService")
public class EncyCsCalloutRecServiceImpl implements EncyCsCalloutRecService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsCalloutRecServiceImpl.class);
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;

	@Autowired
	private CsCalloutRecMapper csCalloutRecMapper; 
	
	@Override
	public void encyCsCalloutRecData(String arg) {
		logger.info("处理外呼记录流水脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<CsCalloutRec> listCsCalloutRec = null;
    	// 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
    	// 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询外呼记录流水信息：每次查询两百条
				listCsCalloutRec = csCalloutRecMapper.listCsCalloutRecLimit200(param);
				
				//处理脱敏三要素
				handleCsCalloutRecList(listCsCalloutRec);
				
				//更新外呼记录流水表
				int count = this.batchUpdateCsCalloutRec(listCsCalloutRec);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理外呼记录流水脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理外呼记录流水脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsCalloutRecList(List<CsCalloutRec> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsCalloutRec csCalloutRec : list){
				String mobile = csCalloutRec.getMobile();
				if(StringUtils.isNotBlank(mobile)){
					csCalloutRec.setMobileDigest(DigestUtil.digest(mobile.trim()));
					csCalloutRec.setMobileMask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("外呼记录流水mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求外呼记录流水手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求外呼记录流水手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CsCalloutRec csCalloutRec : list){
	    				if(StringUtils.isNotBlank(csCalloutRec.getMobile())){
	    					csCalloutRec.setMobileCipher(mobilemap.get(csCalloutRec.getMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("外呼记录流水mobileList加密结束时间"+System.currentTimeMillis());
			}
		}

		logger.info("保存外呼记录流水脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入外呼记录流水信息
	 * @param list
	 * @return
	 */
	private int batchUpdateCsCalloutRec(List<CsCalloutRec> list){
		if(CollectionUtils.isNotEmpty(list)){
			csCalloutRecMapper.batchUpdateCsCalloutRec(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CmPotentialcustMapper;
import com.howbuy.crm.nt.sensitive.dto.CmPotentialcust;

/**
 * 刷潜在客户信息脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCmPotentialcustService")
public class EncyCmPotentialcustServiceImpl implements EncyCmPotentialcustService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCmPotentialcustServiceImpl.class);

	@Autowired
	private CmPotentialcustMapper cmPotentialcustMapper;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;

	@Override
	public void encyCmPotentialcustData(String arg) {
		logger.info("处理潜在客户信息脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<CmPotentialcust> listCmPotentialcust = null;
		//程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
		//循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		String pcustId = null;
		param.put("pcustId", pcustId);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询潜在客户信息：每次查询两百条
				listCmPotentialcust = cmPotentialcustMapper.listCmPotentialcustLimit200(param);
				pcustId = CollectionUtils.isEmpty(listCmPotentialcust) ? null : listCmPotentialcust.get(0).getPcustid();
				param.put("pcustId", pcustId);
				
				//处理脱敏三要素
				handleCmPotentialcustList(listCmPotentialcust);
				
				//更新潜在客户信息表
				int count = this.updateCmPotentialcustBatch(listCmPotentialcust);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理潜在客户信息脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理潜在客户信息脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCmPotentialcustList(List<CmPotentialcust> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> idnoList = new ArrayList<String>(200);
			List<String> addrList = new ArrayList<String>(200);
			List<String> addr2List = new ArrayList<String>(200);
			List<String> addr3List = new ArrayList<String>(200);
			List<String> mobileList = new ArrayList<String>(200);
			List<String> mobile2List = new ArrayList<String>(200);
			List<String> telnoList = new ArrayList<String>(200);
			List<String> emailList = new ArrayList<String>(200);
			List<String> email2List = new ArrayList<String>(200);
			List<String> hometelnoList = new ArrayList<String>(200);
			List<String> officetelnoList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CmPotentialcust cmPotentialcust : list){
				String idno = cmPotentialcust.getIdno();
				String addr = cmPotentialcust.getAddr();
				String addr2 = cmPotentialcust.getAddr2();
				String addr3 = cmPotentialcust.getAddr3();
				String mobile = cmPotentialcust.getMobile();
				String mobile2 = cmPotentialcust.getMobile2();
				String telno = cmPotentialcust.getTelno();
				String email = cmPotentialcust.getEmail();
				String email2 = cmPotentialcust.getEmail2();
				String hometelno = cmPotentialcust.getHometelno();
				String officetelno = cmPotentialcust.getOfficetelno();
				
				if(StringUtils.isNotBlank(idno)){
					cmPotentialcust.setIdnodigest(DigestUtil.digest(idno.trim()));
					cmPotentialcust.setIdnomask(MaskUtil.maskIdNo(idno.trim()));
					idnoList.add(idno.trim());
				}
				if(StringUtils.isNotBlank(addr)){
					cmPotentialcust.setAddrdigest(DigestUtil.digest(addr.trim()));
					cmPotentialcust.setAddrmask(MaskUtil.maskAddr(addr.trim()));
					addrList.add(addr.trim());
				}
				if(StringUtils.isNotBlank(addr2)){
					cmPotentialcust.setAddr2digest(DigestUtil.digest(addr2.trim()));
					cmPotentialcust.setAddr2mask(MaskUtil.maskAddr(addr2.trim()));
					addr2List.add(addr2.trim());
				}
				if(StringUtils.isNotBlank(addr3)){
					cmPotentialcust.setAddr3digest(DigestUtil.digest(addr3.trim()));
					cmPotentialcust.setAddr3mask(MaskUtil.maskAddr(addr3.trim()));
					addr3List.add(addr3.trim());
				}
				if(StringUtils.isNotBlank(mobile)){
					cmPotentialcust.setMobiledigest(DigestUtil.digest(mobile.trim()));
					cmPotentialcust.setMobilemask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
				if(StringUtils.isNotBlank(mobile2)){
					cmPotentialcust.setMobile2digest(DigestUtil.digest(mobile2.trim()));
					cmPotentialcust.setMobile2mask(MaskUtil.maskMobile(mobile2.trim()));
					mobile2List.add(mobile2.trim());
				}
				if(StringUtils.isNotBlank(telno)){
					cmPotentialcust.setTelnodigest(DigestUtil.digest(telno.trim()));
					cmPotentialcust.setTelnomask(MaskUtil.maskMobile(telno.trim()));
					telnoList.add(telno.trim());
				}
				if(StringUtils.isNotBlank(email)){
					cmPotentialcust.setEmaildigest(DigestUtil.digest(email.trim().toLowerCase()));
					cmPotentialcust.setEmailmask(MaskUtil.maskEmail(email.trim().toLowerCase()));
					emailList.add(email.trim().toLowerCase());
				}
				if(StringUtils.isNotBlank(email2)){
					cmPotentialcust.setEmail2digest(DigestUtil.digest(email2.trim().toLowerCase()));
					cmPotentialcust.setEmail2mask(MaskUtil.maskEmail(email2.trim().toLowerCase()));
					email2List.add(email2.trim().toLowerCase());
				}
				if(StringUtils.isNotBlank(hometelno)){
					cmPotentialcust.setHometelnodigest(DigestUtil.digest(hometelno.trim()));
					cmPotentialcust.setHometelnomask(MaskUtil.maskMobile(hometelno.trim()));
					hometelnoList.add(hometelno.trim());
				}
				if(StringUtils.isNotBlank(officetelno)){
					cmPotentialcust.setOfficetelnodigest(DigestUtil.digest(officetelno.trim()));
					cmPotentialcust.setOfficetelnomask(MaskUtil.maskMobile(officetelno.trim()));
					officetelnoList.add(officetelno.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(idnoList)){
				logger.info("预约idnoList条数:"+idnoList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户信息证件加密参数："+JSON.toJSONString(idnoList));
	    		CodecBatchResponse resIdno = encryptBatchFacade.encryptBatch(idnoList);
	    		logger.info("请求潜在客户信息证件加密返回："+JSON.toJSONString(resIdno));
	    		if(resIdno != null && resIdno.getCodecMap() != null){
	    			Map<String,String> idnomap = resIdno.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getIdno())){
	    					cmPotentialcust.setIdnocipher(idnomap.get(cmPotentialcust.getIdno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息idnoList加密结束时间"+System.currentTimeMillis());
			}
			
			
			if(CollectionUtils.isNotEmpty(addrList)){
				logger.info("预约addrList条数:"+addrList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户信息地址加密参数："+JSON.toJSONString(addrList));
	    		CodecBatchResponse resAddr = encryptBatchFacade.encryptBatch(addrList);
	    		logger.info("请求潜在客户信息地址加密返回："+JSON.toJSONString(resAddr));
	    		if(resAddr != null && resAddr.getCodecMap() != null){
	    			Map<String,String> addrmap = resAddr.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getAddr())){
	    					cmPotentialcust.setAddrcipher(addrmap.get(cmPotentialcust.getAddr().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息addrList加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(addr2List)){
				logger.info("预约addr2List条数:"+addr2List.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户信息地址2加密参数："+JSON.toJSONString(addr2List));
	    		CodecBatchResponse resAddr2 = encryptBatchFacade.encryptBatch(addr2List);
	    		logger.info("请求潜在客户信息地址2加密返回："+JSON.toJSONString(resAddr2));
	    		if(resAddr2 != null && resAddr2.getCodecMap() != null){
	    			Map<String,String> addr2map = resAddr2.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getAddr2())){
	    					cmPotentialcust.setAddr2cipher(addr2map.get(cmPotentialcust.getAddr2().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息addr2List加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(addr3List)){
				logger.info("预约addr3List条数:"+addr3List.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户信息3地址加密参数："+JSON.toJSONString(addr3List));
	    		CodecBatchResponse resAddr3 = encryptBatchFacade.encryptBatch(addr3List);
	    		logger.info("请求潜在客户信息地址3加密返回："+JSON.toJSONString(resAddr3));
	    		if(resAddr3 != null && resAddr3.getCodecMap() != null){
	    			Map<String,String> addr3map = resAddr3.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getAddr3())){
	    					cmPotentialcust.setAddr3cipher(addr3map.get(cmPotentialcust.getAddr3().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息addr3List加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("预约mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求潜在客户信息手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getMobile())){
	    					cmPotentialcust.setMobilecipher(mobilemap.get(cmPotentialcust.getMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息mobileList加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(mobile2List)){
				logger.info("预约mobile2List条数:"+mobile2List.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户手机2加密参数："+JSON.toJSONString(mobile2List));
	    		CodecBatchResponse resMobile2 = encryptBatchFacade.encryptBatch(mobile2List);
	    		logger.info("请求潜在客户信息手机2加密返回："+JSON.toJSONString(resMobile2));
	    		if(resMobile2 != null && resMobile2.getCodecMap() != null){
	    			Map<String,String> mobile2map = resMobile2.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getMobile2())){
	    					cmPotentialcust.setMobile2cipher(mobile2map.get(cmPotentialcust.getMobile2().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息mobile2List加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(telnoList)){
				logger.info("预约telnoList条数:"+telnoList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户座机号加密参数："+JSON.toJSONString(telnoList));
	    		CodecBatchResponse resTelno = encryptBatchFacade.encryptBatch(telnoList);
	    		logger.info("请求潜在客户信息座机加密返回："+JSON.toJSONString(resTelno));
	    		if(resTelno != null && resTelno.getCodecMap() != null){
	    			Map<String,String> telnomap = resTelno.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getTelno())){
	    					cmPotentialcust.setTelnocipher(telnomap.get(cmPotentialcust.getTelno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息telnoList加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(emailList)){
				logger.info("预约emailList条数:"+emailList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户邮箱加密参数："+JSON.toJSONString(emailList));
	    		CodecBatchResponse resEmail = encryptBatchFacade.encryptBatch(emailList);
	    		logger.info("请求潜在客户信息邮箱加密返回："+JSON.toJSONString(resEmail));
	    		if(resEmail != null && resEmail.getCodecMap() != null){
	    			Map<String,String> emaimap = resEmail.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getEmail())){
	    					cmPotentialcust.setEmailcipher(emaimap.get(cmPotentialcust.getEmail().trim().toLowerCase()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息emailList加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(email2List)){
				logger.info("预约email2List条数:"+email2List.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户邮箱2加密参数："+JSON.toJSONString(email2List));
	    		CodecBatchResponse resEmail2 = encryptBatchFacade.encryptBatch(email2List);
	    		logger.info("请求潜在客户信息邮箱2加密返回："+JSON.toJSONString(resEmail2));
	    		if(resEmail2 != null && resEmail2.getCodecMap() != null){
	    			Map<String,String> emai2map = resEmail2.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getEmail2())){
	    					cmPotentialcust.setEmail2cipher(emai2map.get(cmPotentialcust.getEmail2().trim().toLowerCase()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息email2List加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(hometelnoList)){
				logger.info("预约hometelnoList条数:"+hometelnoList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户hometelno加密参数："+JSON.toJSONString(hometelnoList));
	    		CodecBatchResponse resHometelno = encryptBatchFacade.encryptBatch(hometelnoList);
	    		logger.info("请求潜在客户信息hometelno加密返回："+JSON.toJSONString(resHometelno));
	    		if(resHometelno != null && resHometelno.getCodecMap() != null){
	    			Map<String,String> hometelnomap = resHometelno.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getHometelno())){
	    					cmPotentialcust.setHometelnocipher(hometelnomap.get(cmPotentialcust.getHometelno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息hometelnoList加密结束时间"+System.currentTimeMillis());
			}
			
			if(CollectionUtils.isNotEmpty(officetelnoList)){
				logger.info("预约officetelnoList条数:"+officetelnoList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求潜在客户officetelno加密参数："+JSON.toJSONString(officetelnoList));
	    		CodecBatchResponse resOfficetelno = encryptBatchFacade.encryptBatch(officetelnoList);
	    		logger.info("请求潜在客户信息officetelno加密返回："+JSON.toJSONString(resOfficetelno));
	    		if(resOfficetelno != null && resOfficetelno.getCodecMap() != null){
	    			Map<String,String> officetelnomap = resOfficetelno.getCodecMap();
	    			for(CmPotentialcust cmPotentialcust : list){
	    				if(StringUtils.isNotBlank(cmPotentialcust.getOfficetelno())){
	    					cmPotentialcust.setOfficetelnocipher(officetelnomap.get(cmPotentialcust.getOfficetelno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("潜在客户信息officetelnoList加密结束时间"+System.currentTimeMillis());
			}
		}
		
		logger.info("保存潜在客户信息脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入潜在客户信息
	 * @param list
	 * @return
	 */
	private int updateCmPotentialcustBatch(List<CmPotentialcust> list){
		if(CollectionUtils.isNotEmpty(list)){
			cmPotentialcustMapper.batchUpdateCmPotentialcust(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

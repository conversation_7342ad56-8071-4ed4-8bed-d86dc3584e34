package com.howbuy.crm.nt.sensitive.service;

import com.howbuy.crm.nt.sensitive.dao.UpdateSurveyDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("UpdateSurveyDataService")
public class UpdateSurveyDataSeviceImpl implements UpdateSurveyDataService{

    @Autowired
    private UpdateSurveyDataMapper updateSurveyDataMapper;

    @Override
    public String execute(String s){
        log.info("----------------------------updateSurveyDataMapper start");
        updateSurveyDataMapper.update();
        log.info("----------------------------updateSurveyDataMapper end");
        return "success";
    }
}

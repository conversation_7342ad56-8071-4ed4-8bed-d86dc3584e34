package com.howbuy.crm.nt.sensitive.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyConscustHisService")
public class EncyConscustHisServiceImpl implements EncyConscustHisService {
	
	private Logger logger= LoggerFactory.getLogger(EncyConscustHisServiceImpl.class);

	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyConscustHisData(String arg) {
		Thread t0 = new Thread() { @Override public void run() { test0(); } };
		Thread t1 = new Thread() { @Override public void run() { test1(); } };
		Thread t2 = new Thread() { @Override public void run() { test2(); } };
		Thread t3 = new Thread() { @Override public void run() { test3(); } };
		Thread t4 = new Thread() { @Override public void run() { test4(); } };
		Thread t5 = new Thread() { @Override public void run() { test5(); } };
		Thread t6 = new Thread() { @Override public void run() { test6(); } };
		Thread t7 = new Thread() { @Override public void run() { test7(); } };
		Thread t8 = new Thread() { @Override public void run() { test8(); } };
		Thread t9 = new Thread() { @Override public void run() { test9(); } };
		t0.start();
		t1.start();
		t2.start();
		t3.start();
		t4.start();
		t5.start();
		t6.start();
		t7.start();
		t8.start();
		t9.start();
	}
	
	private void test0(){
		boolean flag0 = true;
		while(flag0){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "0");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了0："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag0 = false;
			}
		}
	}
	
	private void test1(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "1");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了1："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	private void test2(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "2");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了2："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
	private void test3(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "3");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了3："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
	private void test4(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "4");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了4："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
	private void test5(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "5");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了5："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
	private void test6(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "6");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了6："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
	private void test7(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "7");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了7："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}

	private void test8(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "8");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了8："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
		
	}
	
	private void test9(){
		boolean flag = true;
		while(flag){
			Map<String,String> param = new HashMap<String,String>();
			param.put("page", "9");
			List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustHisInfo(param);
			logger.info("查询了9："+listinfo.size()+"条！");
			
			for(ConscustSensitiveInfo conscustinfo : listinfo){
				if(StringUtils.isNotBlank(conscustinfo.getIdno())){
					conscustinfo.setIdno(conscustinfo.getIdno());
					conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
					conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile())){
					conscustinfo.setMobile(conscustinfo.getMobile());
					conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
					conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
					conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
					conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
					conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getTelno())){
					conscustinfo.setTelno(conscustinfo.getTelno());
					conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
					conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr())){
					conscustinfo.setAddr(conscustinfo.getAddr());
					conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
					conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
					conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
					conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
					conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
					conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
					conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
					conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
					conscustinfo.setLinktel(conscustinfo.getLinktel());
					conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
					conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
				}
				
				if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
					conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
					conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
					conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
				}
				if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
					conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
					conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
					conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
					conscustinfo.setMobile2(conscustinfo.getMobile2());
					conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
					conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
				}
				if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
					conscustinfo.setAddr2(conscustinfo.getAddr2());
					conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
					conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
				}
			}
			cmDealCustnoFlagMapper.batchInsertCmConscustHis(listinfo);
			if(listinfo.size() == 0){
				flag = false;
			}
		}
	}
	
}

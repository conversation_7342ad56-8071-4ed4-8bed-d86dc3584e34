<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CmBookingcustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CmBookingcust">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emailDigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailMask" jdbcType="VARCHAR"/>
        <result column="EMAIL_CIPHER" property="emailCipher" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCmBookingcustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT ID,
		       MOBILE,
		       EMAIL
		 FROM CM_BOOKINGCUST 
		 WHERE (   (MOBILE is not null AND (MOBILE_DIGEST is null or MOBILE_MASK is null or MOBILE_CIPHER is null) )
		        OR (EMAIL  is not null AND (EMAIL_DIGEST  is null or EMAIL_MASK is null or EMAIL_CIPHER is null) )
		       )
		 <if test="startId !=null"> AND ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY BOOKINGDT
    </select>
    
    
    <insert id="batchUpdateCmBookingcust" parameterType="com.howbuy.crm.nt.sensitive.dto.CmBookingcust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CM_BOOKINGCUST 
        set 
           EMAIL_DIGEST = #{item.emailDigest,jdbcType=VARCHAR},EMAIL_MASK = #{item.emailMask,jdbcType=VARCHAR},EMAIL_CIPHER = #{item.emailCipher,jdbcType=VARCHAR},
           MOBILE_DIGEST = #{item.mobileDigest,jdbcType=VARCHAR},MOBILE_MASK = #{item.mobileMask,jdbcType=VARCHAR},MOBILE_CIPHER = #{item.mobileCipher,jdbcType=VARCHAR}
        where ID = #{item.id};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
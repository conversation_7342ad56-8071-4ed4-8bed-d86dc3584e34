<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo">
        <result column="APPSERIALNO" property="appserialno" jdbcType="VARCHAR"/>
        <result column="CONSCUSTNO" property="conscustno" jdbcType="VARCHAR"/>
        <result column="IDNO" property="idno" jdbcType="VARCHAR"/>
        <result column="INSURIDNO" property="insuridno" jdbcType="VARCHAR"/>
        <result column="BANKACCT" property="bankacct" jdbcType="VARCHAR"/>
        <result column="ADDR" property="addr" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="TELNO" property="telno" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="ADDR2" property="addr2" jdbcType="VARCHAR"/>
        <result column="MOBILE2" property="mobile2" jdbcType="VARCHAR"/>
        <result column="EMAIL2" property="email2" jdbcType="VARCHAR"/>
        <result column="LINKTEL" property="linktel" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE" property="linkmobile" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL" property="linkemail" jdbcType="VARCHAR"/>
        <result column="LINKADDR" property="linkaddr" jdbcType="VARCHAR"/>
        <result column="IDNO_DIGEST" property="idnoDigest" jdbcType="VARCHAR"/>
        <result column="ADDR_DIGEST" property="addrDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="TELNO_DIGEST" property="telnoDigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emailDigest" jdbcType="VARCHAR"/>
        <result column="ADDR2_DIGEST" property="addr2Digest" jdbcType="VARCHAR"/>
        <result column="MOBILE2_DIGEST" property="mobile2Digest" jdbcType="VARCHAR"/>
        <result column="EMAIL2_DIGEST" property="email2Digest" jdbcType="VARCHAR"/>
        <result column="LINKTEL_DIGEST" property="linktelDigest" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_DIGEST" property="linkmobileDigest" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_DIGEST" property="linkemailDigest" jdbcType="VARCHAR"/>
        <result column="LINKADDR_DIGEST" property="linkaddrDigest" jdbcType="VARCHAR"/>
        <result column="IDNO_MASK" property="idnoMask" jdbcType="VARCHAR"/>
        <result column="ADDR_MASK" property="addrMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="TELNO_MASK" property="telnoMask" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailMask" jdbcType="VARCHAR"/>
        <result column="ADDR2_MASK" property="addr2Mask" jdbcType="VARCHAR"/>
        <result column="MOBILE2_MASK" property="mobile2Mask" jdbcType="VARCHAR"/>
        <result column="EMAIL2_MASK" property="email2Mask" jdbcType="VARCHAR"/>
        <result column="LINKTEL_MASK" property="linktelMask" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_MASK" property="linkmobileMask" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_MASK" property="linkemailMask" jdbcType="VARCHAR"/>
        <result column="LINKADDR_MASK" property="linkaddrMask" jdbcType="VARCHAR"/>
        <result column="IDNO_CIPHER" property="idnoCipher" jdbcType="VARCHAR"/>
        <result column="ADDR_CIPHER" property="addrCipher" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
        <result column="TELNO_CIPHER" property="telnoCipher" jdbcType="VARCHAR"/>
        <result column="EMAIL_CIPHER" property="emailCipher" jdbcType="VARCHAR"/>
        <result column="ADDR2_CIPHER" property="addr2Cipher" jdbcType="VARCHAR"/>
        <result column="MOBILE2_CIPHER" property="mobile2Cipher" jdbcType="VARCHAR"/>
        <result column="EMAIL2_CIPHER" property="email2Cipher" jdbcType="VARCHAR"/>
        <result column="LINKTEL_CIPHER" property="linktelCipher" jdbcType="VARCHAR"/>
        <result column="LINKMOBILE_CIPHER" property="linkmobileCipher" jdbcType="VARCHAR"/>
        <result column="LINKEMAIL_CIPHER" property="linkemailCipher" jdbcType="VARCHAR"/>
        <result column="LINKADDR_CIPHER" property="linkaddrCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listConscustSensitiveInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
    SELECT T.CONSCUSTNO,
	       T.IDNO,
	       T.MOBILE,
	       T.EMAIL,
	       T.TELNO,
	       T.ADDR,
	       T.MOBILE2,
	       T.EMAIL2,
	       T.ADDR2,
	       T.LINKMOBILE,
	       T.LINKTEL,
	       T.LINKEMAIL,
	       T.LINKADDR
	  FROM CM_CONSCUST T
	  LEFT JOIN CM_DEAL_CUSTNO_FLAG T1
	    ON T.CONSCUSTNO = T1.CONSCUSTNO
	 WHERE T1.FLAG = '0'
	   AND ROWNUM &lt;= 200
    </select>
    
    <select id="listConscustSensitiveInfo1" parameterType="map" resultMap="BaseResultMap"  useCache="false">
    SELECT T.CONSCUSTNO,
	       T.IDNO,
	       T.MOBILE,
	       T.EMAIL,
	       T.TELNO,
	       T.ADDR,
	       T.MOBILE2,
	       T.EMAIL2,
	       T.ADDR2,
	       T.LINKMOBILE,
	       T.LINKTEL,
	       T.LINKEMAIL,
	       T.LINKADDR
	  FROM CM_CONSCUST T
	  LEFT JOIN CM_DEAL_CUSTNO_FLAG1 T1
	    ON T.CONSCUSTNO = T1.CONSCUSTNO
	 WHERE T1.FLAG = '0'
	   AND ROWNUM &lt;= 200
    </select>
    
    <select id="getSensitiveCount" parameterType="Map" resultType="int" useCache="false">
    	SELECT COUNT(*) FROM CM_CONSCUST_SENSIT_TEMP T
    </select>
    
    <select id="getSensitiveCount1" parameterType="Map" resultType="int" useCache="false">
    	SELECT COUNT(*) FROM CM_CONSCUST_SENSIT_TEMP1 T
    </select>
    
    <select id="getCmDealCustNoFlagCount" parameterType="Map" resultType="int" useCache="false">
    	SELECT COUNT(*) FROM CM_DEAL_CUSTNO_FLAG T where T.FLAG = '0'
    </select>
    
    <select id="getCmDealCustNoFlagCount1" parameterType="Map" resultType="int" useCache="false">
    	SELECT COUNT(*) FROM CM_DEAL_CUSTNO_FLAG1 T where T.FLAG = '0'
    </select>
    
    <insert id="batchInsertCmConscustSensitTemp" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo">
        DECLARE
        begin
      <foreach collection="list" item="item">
        INSERT INTO CM_CONSCUST_SENSIT_TEMP(
        conscustno,
		idno,
		idno_digest,
		idno_mask,
		idno_cipher,
		mobile,
		mobile_digest,
		mobile_mask,
		mobile_cipher,
		email,
		email_digest,
		email_mask,
		telno,
		telno_digest,
		telno_mask,
		telno_cipher,
		addr,
		addr_digest,
		addr_mask,
		addr_cipher,
		mobile2,
		mobile2_digest,
		mobile2_mask,
		mobile2_cipher,
		email2,
		email2_digest,
		email2_mask,
		email2_cipher,
		addr2,
		addr2_digest,
		addr2_mask,
		addr2_cipher,
		linkmobile,
		linkmobile_digest,
		linkmobile_mask,
		linkmobile_cipher,
		linktel,
		linktel_digest,
		linktel_mask,
		linktel_cipher,
		linkemail,
		linkemail_digest,
		linkemail_mask,
		linkemail_cipher,
		linkaddr,
		linkaddr_digest,
		linkaddr_mask,
		linkaddr_cipher,
		email_cipher
        )VALUES (
        #{item.conscustno},
		#{item.idno,jdbcType=VARCHAR},
		#{item.idnoDigest,jdbcType=VARCHAR},
		#{item.idnoMask,jdbcType=VARCHAR},
		#{item.idnoCipher,jdbcType=VARCHAR},
		#{item.mobile,jdbcType=VARCHAR},
		#{item.mobileDigest,jdbcType=VARCHAR},
		#{item.mobileMask,jdbcType=VARCHAR},
		#{item.mobileCipher,jdbcType=VARCHAR},
		#{item.email,jdbcType=VARCHAR},
		#{item.emailDigest,jdbcType=VARCHAR},
		#{item.emailMask,jdbcType=VARCHAR},
		#{item.telno,jdbcType=VARCHAR},
		#{item.telnoDigest,jdbcType=VARCHAR},
		#{item.telnoMask,jdbcType=VARCHAR},
		#{item.telnoCipher,jdbcType=VARCHAR},
		#{item.addr,jdbcType=VARCHAR},
		#{item.addrDigest,jdbcType=VARCHAR},
		#{item.addrMask,jdbcType=VARCHAR},
		#{item.addrCipher,jdbcType=VARCHAR},
		#{item.mobile2,jdbcType=VARCHAR},
		#{item.mobile2Digest,jdbcType=VARCHAR},
		#{item.mobile2Mask,jdbcType=VARCHAR},
		#{item.mobile2Cipher,jdbcType=VARCHAR},
		#{item.email2,jdbcType=VARCHAR},
		#{item.email2Digest,jdbcType=VARCHAR},
		#{item.email2Mask,jdbcType=VARCHAR},
		#{item.email2Cipher,jdbcType=VARCHAR},
		#{item.addr2,jdbcType=VARCHAR},
		#{item.addr2Digest,jdbcType=VARCHAR},
		#{item.addr2Mask,jdbcType=VARCHAR},
		#{item.addr2Cipher,jdbcType=VARCHAR},
		#{item.linkmobile,jdbcType=VARCHAR},
		#{item.linkmobileDigest,jdbcType=VARCHAR},
		#{item.linkmobileMask,jdbcType=VARCHAR},
		#{item.linkmobileCipher,jdbcType=VARCHAR},
		#{item.linktel,jdbcType=VARCHAR},
		#{item.linktelDigest,jdbcType=VARCHAR},
		#{item.linktelMask,jdbcType=VARCHAR},
		#{item.linktelCipher,jdbcType=VARCHAR},
		#{item.linkemail,jdbcType=VARCHAR},
		#{item.linkemailDigest,jdbcType=VARCHAR},
		#{item.linkemailMask,jdbcType=VARCHAR},
		#{item.linkemailCipher,jdbcType=VARCHAR},
		#{item.linkaddr,jdbcType=VARCHAR},
		#{item.linkaddrDigest,jdbcType=VARCHAR},
		#{item.linkaddrMask,jdbcType=VARCHAR},
		#{item.linkaddrCipher,jdbcType=VARCHAR},
		#{item.emailCipher,jdbcType=VARCHAR}
        );
      </foreach>
        commit;
        END;
    </insert>
    
    <insert id="batchInsertCmConscustSensitTemp1" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo">
        DECLARE
        begin
      <foreach collection="list" item="item">
        INSERT INTO CM_CONSCUST_SENSIT_TEMP1(
        conscustno,
		idno,
		idno_digest,
		idno_mask,
		idno_cipher,
		mobile,
		mobile_digest,
		mobile_mask,
		mobile_cipher,
		email,
		email_digest,
		email_mask,
		telno,
		telno_digest,
		telno_mask,
		telno_cipher,
		addr,
		addr_digest,
		addr_mask,
		addr_cipher,
		mobile2,
		mobile2_digest,
		mobile2_mask,
		mobile2_cipher,
		email2,
		email2_digest,
		email2_mask,
		email2_cipher,
		addr2,
		addr2_digest,
		addr2_mask,
		addr2_cipher,
		linkmobile,
		linkmobile_digest,
		linkmobile_mask,
		linkmobile_cipher,
		linktel,
		linktel_digest,
		linktel_mask,
		linktel_cipher,
		linkemail,
		linkemail_digest,
		linkemail_mask,
		linkemail_cipher,
		linkaddr,
		linkaddr_digest,
		linkaddr_mask,
		linkaddr_cipher,
		email_cipher
        )VALUES (
        #{item.conscustno},
		#{item.idno,jdbcType=VARCHAR},
		#{item.idnoDigest,jdbcType=VARCHAR},
		#{item.idnoMask,jdbcType=VARCHAR},
		#{item.idnoCipher,jdbcType=VARCHAR},
		#{item.mobile,jdbcType=VARCHAR},
		#{item.mobileDigest,jdbcType=VARCHAR},
		#{item.mobileMask,jdbcType=VARCHAR},
		#{item.mobileCipher,jdbcType=VARCHAR},
		#{item.email,jdbcType=VARCHAR},
		#{item.emailDigest,jdbcType=VARCHAR},
		#{item.emailMask,jdbcType=VARCHAR},
		#{item.telno,jdbcType=VARCHAR},
		#{item.telnoDigest,jdbcType=VARCHAR},
		#{item.telnoMask,jdbcType=VARCHAR},
		#{item.telnoCipher,jdbcType=VARCHAR},
		#{item.addr,jdbcType=VARCHAR},
		#{item.addrDigest,jdbcType=VARCHAR},
		#{item.addrMask,jdbcType=VARCHAR},
		#{item.addrCipher,jdbcType=VARCHAR},
		#{item.mobile2,jdbcType=VARCHAR},
		#{item.mobile2Digest,jdbcType=VARCHAR},
		#{item.mobile2Mask,jdbcType=VARCHAR},
		#{item.mobile2Cipher,jdbcType=VARCHAR},
		#{item.email2,jdbcType=VARCHAR},
		#{item.email2Digest,jdbcType=VARCHAR},
		#{item.email2Mask,jdbcType=VARCHAR},
		#{item.email2Cipher,jdbcType=VARCHAR},
		#{item.addr2,jdbcType=VARCHAR},
		#{item.addr2Digest,jdbcType=VARCHAR},
		#{item.addr2Mask,jdbcType=VARCHAR},
		#{item.addr2Cipher,jdbcType=VARCHAR},
		#{item.linkmobile,jdbcType=VARCHAR},
		#{item.linkmobileDigest,jdbcType=VARCHAR},
		#{item.linkmobileMask,jdbcType=VARCHAR},
		#{item.linkmobileCipher,jdbcType=VARCHAR},
		#{item.linktel,jdbcType=VARCHAR},
		#{item.linktelDigest,jdbcType=VARCHAR},
		#{item.linktelMask,jdbcType=VARCHAR},
		#{item.linktelCipher,jdbcType=VARCHAR},
		#{item.linkemail,jdbcType=VARCHAR},
		#{item.linkemailDigest,jdbcType=VARCHAR},
		#{item.linkemailMask,jdbcType=VARCHAR},
		#{item.linkemailCipher,jdbcType=VARCHAR},
		#{item.linkaddr,jdbcType=VARCHAR},
		#{item.linkaddrDigest,jdbcType=VARCHAR},
		#{item.linkaddrMask,jdbcType=VARCHAR},
		#{item.linkaddrCipher,jdbcType=VARCHAR},
		#{item.emailCipher,jdbcType=VARCHAR}
        );
      </foreach>
        commit;
        END;
    </insert>
    
    <insert id="insertCmConscustSensitTemp" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo">
		INSERT INTO CM_CONSCUST_SENSIT_TEMP (
		<trim suffix="" suffixOverrides=",">
			<if test="conscustno != null">conscustno,</if>
			<if test="idno != null">idno,</if>
			<if test="addr != null">addr,</if>
			<if test="mobile != null">mobile,</if>
			<if test="telno != null">telno,</if>
			<if test="email != null">email,</if>
			<if test="addr2 != null">addr2,</if>
			<if test="mobile2 != null">mobile2,</if>
			<if test="email2 != null">email2,</if>
			<if test="linktel != null">linktel,</if>
			<if test="linkmobile != null">linkmobile,</if>
			<if test="linkemail != null">linkemail,</if>
			<if test="linkaddr != null">linkaddr,</if>
			<if test="idnoDigest !=null">idno_Digest,</if>
		 	  <if test="idnoMask !=null">idno_Mask,</if>
		 	  <if test="addrDigest !=null">addr_Digest,</if>
		 	  <if test="addrMask !=null">addr_Mask,</if>
		 	  <if test="mobileDigest !=null">mobile_Digest,</if>
		 	  <if test="mobileMask !=null">mobile_Mask,</if>
		 	  <if test="telnoDigest !=null">telno_Digest,</if>
		 	  <if test="telnoMask !=null">telno_Mask,</if>
		 	  <if test="emailDigest !=null">email_Digest,</if>
		 	  <if test="emailMask !=null">email_Mask,</if>
		 	  <if test="addr2Digest !=null">addr2_Digest,</if>
		 	  <if test="addr2Mask !=null">addr2_Mask,</if>
		 	  <if test="mobile2Digest !=null">mobile2_Digest,</if>
		 	  <if test="mobile2Mask !=null">mobile2_Mask,</if>
		 	  <if test="email2Digest !=null">email2_Digest,</if>
		 	  <if test="email2Mask !=null">email2_Mask,</if>
		 	  <if test="linkaddrDigest !=null">linkaddr_Digest,</if>
		 	  <if test="linkaddrMask !=null">linkaddr_Mask,</if>
		 	  <if test="linkmobileDigest !=null">linkmobile_Digest,</if>
		 	  <if test="linkmobileMask !=null">linkmobile_Mask,</if>
		 	  <if test="linktelDigest !=null">linktel_Digest,</if>
		 	  <if test="linktelMask !=null">linktel_Mask,</if>
		 	  <if test="linkemailDigest !=null">linkemail_Digest,</if>
		 	  <if test="linkemailMask !=null">linkemail_Mask,</if>
		 	  <if test="idnoCipher !=null">idno_Cipher,</if>
		 	  <if test="addrCipher !=null">addr_Cipher,</if>
		 	  <if test="mobileCipher !=null">mobile_Cipher,</if>
		 	  <if test="telnoCipher !=null">telno_Cipher,</if>
		 	  <if test="emailCipher !=null">email_Cipher,</if>
		 	  <if test="addr2Cipher !=null">addr2_Cipher,</if>
		 	  <if test="mobile2Cipher !=null">mobile2_Cipher,</if>
		 	  <if test="email2Cipher !=null">email2_Cipher,</if>
		 	  <if test="linkaddrCipher !=null">linkaddr_Cipher,</if>
		 	  <if test="linkmobileCipher !=null">linkmobile_Cipher,</if>
		 	  <if test="linktelCipher !=null">linktel_Cipher,</if>
		 	  <if test="linkemailCipher !=null">linkemail_Cipher,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="conscustno != null">#{conscustno},</if>
			<if test="idno != null">#{idno},</if>
			<if test="addr != null">#{addr},</if>
			<if test="mobile != null">#{mobile},</if>
			<if test="telno != null">#{telno},</if>
			<if test="email != null">#{email},</if>
			<if test="addr2 != null">#{addr2},</if>
			<if test="mobile2 != null">#{mobile2},</if>
			<if test="email2 != null">#{email2},</if>
			<if test="linktel != null">#{linktel},</if>
			<if test="linkmobile != null">#{linkmobile},</if>
			<if test="linkemail != null">#{linkemail},</if>
			<if test="linkaddr != null">#{linkaddr},</if>
			<if test="idnoMask !=null">#{idnoMask, jdbcType = VARCHAR},</if>
		 	  <if test="addrDigest !=null">#{addrDigest, jdbcType = VARCHAR},</if>
		 	  <if test="addrMask !=null">#{addrMask, jdbcType = VARCHAR},</if>
		 	  <if test="mobileDigest !=null">#{mobileDigest, jdbcType = VARCHAR},</if>
		 	  <if test="mobileMask !=null">#{mobileMask, jdbcType = VARCHAR},</if>
		 	  <if test="telnoDigest !=null">#{telnoDigest, jdbcType = VARCHAR},</if>
		 	  <if test="telnoMask !=null">#{telnoMask, jdbcType = VARCHAR},</if>
		 	  <if test="emailDigest !=null">#{emailDigest, jdbcType = VARCHAR},</if>
		 	  <if test="emailMask !=null">#{emailMask, jdbcType = VARCHAR},</if>
		 	  <if test="addr2Digest !=null">#{addr2Digest, jdbcType = VARCHAR},</if>
		 	  <if test="addr2Mask !=null">#{addr2Mask, jdbcType = VARCHAR},</if>
		 	  <if test="mobile2Digest !=null">#{mobile2Digest, jdbcType = VARCHAR},</if>
		 	  <if test="mobile2Mask !=null">#{mobile2Mask, jdbcType = VARCHAR},</if>
		 	  <if test="email2Digest !=null">#{email2Digest, jdbcType = VARCHAR},</if>
		 	  <if test="email2Mask !=null">#{email2Mask, jdbcType = VARCHAR},</if>
		 	  <if test="linkaddrDigest !=null">#{linkaddrDigest, jdbcType = VARCHAR},</if>
		 	  <if test="linkaddrMask !=null">#{linkaddrMask, jdbcType = VARCHAR},</if>
		 	  <if test="linkmobileDigest !=null">#{linkmobileDigest, jdbcType = VARCHAR},</if>
		 	  <if test="linkmobileMask !=null">#{linkmobileMask, jdbcType = VARCHAR},</if>
		 	  <if test="linktelDigest !=null">#{linktelDigest, jdbcType = VARCHAR},</if>
		 	  <if test="linktelMask !=null">#{linktelMask, jdbcType = VARCHAR},</if>
		 	  <if test="linkemailDigest !=null">#{linkemailDigest, jdbcType = VARCHAR},</if>
		 	  <if test="linkemailMask !=null">#{linkemailMask, jdbcType = VARCHAR},</if>
		 	  <if test="idnoCipher !=null">#{idnoCipher, jdbcType = VARCHAR},</if>
		 	  <if test="addrCipher !=null">#{addrCipher, jdbcType = VARCHAR},</if>
		 	  <if test="mobileCipher !=null">#{mobileCipher, jdbcType = VARCHAR},</if>
		 	  <if test="telnoCipher !=null">#{telnoCipher, jdbcType = VARCHAR},</if>
		 	  <if test="emailCipher !=null">#{emailCipher, jdbcType = VARCHAR},</if>
		 	  <if test="addr2Cipher !=null">#{addr2Cipher, jdbcType = VARCHAR},</if>
		 	  <if test="mobile2Cipher !=null">#{mobile2Cipher, jdbcType = VARCHAR},</if>
		 	  <if test="email2Cipher !=null">#{email2Cipher, jdbcType = VARCHAR},</if>
		 	  <if test="linkaddrCipher !=null">#{linkaddrCipher, jdbcType = VARCHAR},</if>
		 	  <if test="linkmobileCipher !=null">#{linkmobileCipher, jdbcType = VARCHAR},</if>
		 	  <if test="linktelCipher !=null">#{linktelCipher, jdbcType = VARCHAR},</if>
		 	  <if test="linkemailCipher !=null">#{linkemailCipher, jdbcType = VARCHAR},</if>
		</trim>
		)
	</insert>


	<select id="getCmOpenCloseFlag" parameterType="Map" resultType="string" useCache="false">
    	SELECT flag FROM cm_sensitive_openclose
    </select>
    
    <select id="getCmOpenCloseFlag1" parameterType="Map" resultType="string" useCache="false">
    	SELECT flag FROM cm_sensitive_openclose1
    </select>
    
    <update id="updateCmOpenCloseFlag" parameterType="Map">
        update cm_sensitive_openclose set flag=#{flag}
    </update>

	<update id="updateCmOpenCloseFlag1" parameterType="Map">
        update cm_sensitive_openclose1 set flag=#{flag}
    </update>

	
	<select id="listCompareSensitive" parameterType="Map" resultType="Map" useCache="false">
    	SELECT T.CONSCUSTNO, T.SENVAL, T.DIGEST
		  FROM CM_COMPARE_SENSITIVE T
		 WHERE T.PAGE = #{page}
    </select>
    
    <insert id="insertCompareSensitiveLog" parameterType="Map">
        INSERT INTO CM_COMPARE_SENSITIVE_LOG
		  (CONSCUSTNO, REMARK)
		VALUES
		  (#{conscustno}, #{remark})
    </insert>
    
    
    <select id="listConscustIcInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	    SELECT T.CONSCUSTNO,
	         T.IDNO,
	         T.MOBILE,
	         T.EMAIL,
	         T.TELNO,
	         T.ADDR,
	         T.MOBILE2,
	         T.EMAIL2,
	         T.ADDR2,
	         T.LINKMOBILE,
	         T.LINKTEL,
	         T.LINKEMAIL,
	         T.LINKADDR
	    FROM CM_CONSCUST_ic T
	    LEFT JOIN CM_CONSCUST_ic_cipher T1
	      ON T.CONSCUSTNO = T1.CONSCUSTNO
	      where t1.conscustno is null
    </select>
    
    <select id="listConscustIcAllInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	    SELECT T.CONSCUSTNO,
		       T.IDNO,
		       T.IDNO_DIGEST,
		       T.MOBILE,
		       T.MOBILE_DIGEST,
		       T.EMAIL,
		       T.EMAIL_DIGEST,
		       T.TELNO,
		       T.TELNO_DIGEST,
		       T.ADDR,
		       T.ADDR_DIGEST,
		       T.MOBILE2,
		       T.MOBILE2_DIGEST,
		       T.EMAIL2,
		       T.EMAIL2_DIGEST,
		       T.ADDR2,
		       T.ADDR2_DIGEST,
		       T.LINKMOBILE,
		       T.LINKMOBILE_DIGEST,
		       T.LINKTEL,
		       T.LINKTEL_DIGEST,
		       T.LINKEMAIL,
		       T.LINKEMAIL_DIGEST,
		       T.LINKADDR,
		       T.LINKADDR_DIGEST
		  FROM CM_CONSCUST_IC T
    </select>
    
    <update id="updateConscustIc" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    UPDATE CM_CONSCUST_IC	    
	    <set>
			  <if test="idnoDigest !=null">idno_Digest = #{idnoDigest},</if>
		 	  <if test="idnoMask !=null">idno_Mask = #{idnoMask},</if>
		 	  <if test="addrDigest !=null">addr_Digest = #{addrDigest},</if>
		 	  <if test="addrMask !=null">addr_Mask = #{addrMask},</if>
		 	  <if test="mobileDigest !=null">mobile_Digest = #{mobileDigest},</if>
		 	  <if test="mobileMask !=null">mobile_Mask = #{mobileMask},</if>
		 	  <if test="telnoDigest !=null">telno_Digest = #{telnoDigest},</if>
		 	  <if test="telnoMask !=null">telno_Mask = #{telnoMask},</if>
		 	  <if test="emailDigest !=null">email_Digest = #{emailDigest},</if>
		 	  <if test="emailMask !=null">email_Mask = #{emailMask},</if>
		 	  <if test="addr2Digest !=null">addr2_Digest = #{addr2Digest},</if>
		 	  <if test="addr2Mask !=null">addr2_Mask = #{addr2Mask},</if>
		 	  <if test="mobile2Digest !=null">mobile2_Digest = #{mobile2Digest},</if>
		 	  <if test="mobile2Mask !=null">mobile2_Mask = #{mobile2Mask},</if>
		 	  <if test="email2Digest !=null">email2_Digest = #{email2Digest},</if>
		 	  <if test="email2Mask !=null">email2_Mask = #{email2Mask},</if>
		 	  <if test="linkaddrDigest !=null">linkaddr_Digest = #{linkaddrDigest},</if>
		 	  <if test="linkaddrMask !=null">linkaddr_Mask = #{linkaddrMask},</if>
		 	  <if test="linkmobileDigest !=null">linkmobile_Digest = #{linkmobileDigest},</if>
		 	  <if test="linkmobileMask !=null">linkmobile_Mask = #{linkmobileMask},</if>
		 	  <if test="linktelDigest !=null">linktel_Digest = #{linktelDigest},</if>
		 	  <if test="linktelMask !=null">linktel_Mask = #{linktelMask},</if>
		 	  <if test="linkemailDigest !=null">linkemail_Digest = #{linkemailDigest},</if>
		 	  <if test="linkemailMask !=null">linkemail_Mask = #{linkemailMask},</if>
		</set>
          where conscustno = #{conscustno}
	  </update>
    
    <insert id="insertConscustIcCipher" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    INSERT INTO CM_CONSCUST_IC_CIPHER (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="conscustno != null"> conscustno, </if> 
	      	      <if test="idnoCipher !=null">idno_Cipher,</if>
			 	  <if test="addrCipher !=null">addr_Cipher,</if>
			 	  <if test="mobileCipher !=null">mobile_Cipher,</if>
			 	  <if test="telnoCipher !=null">telno_Cipher,</if>
			 	  <if test="emailCipher !=null">email_Cipher,</if>
			 	  <if test="addr2Cipher !=null">addr2_Cipher,</if>
			 	  <if test="mobile2Cipher !=null">mobile2_Cipher,</if>
			 	  <if test="email2Cipher !=null">email2_Cipher,</if>
			 	  <if test="linkaddrCipher !=null">linkaddr_Cipher,</if>
			 	  <if test="linkmobileCipher !=null">linkmobile_Cipher,</if>
			 	  <if test="linktelCipher !=null">linktel_Cipher,</if>
			 	  <if test="linkemailCipher !=null">linkemail_Cipher,</if>
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="conscustno != null"> #{conscustno}, </if> 
			 	 <if test="idnoCipher !=null">#{idnoCipher},</if>
			 	  <if test="addrCipher !=null">#{addrCipher},</if>
			 	  <if test="mobileCipher !=null">#{mobileCipher},</if>
			 	  <if test="telnoCipher !=null">#{telnoCipher},</if>
			 	  <if test="emailCipher !=null">#{emailCipher},</if>
			 	  <if test="addr2Cipher !=null">#{addr2Cipher},</if>
			 	  <if test="mobile2Cipher !=null">#{mobile2Cipher},</if>
			 	  <if test="email2Cipher !=null">#{email2Cipher},</if>
			 	  <if test="linkaddrCipher !=null">#{linkaddrCipher},</if>
			 	  <if test="linkmobileCipher !=null">#{linkmobileCipher},</if>
			 	  <if test="linktelCipher !=null">#{linktelCipher},</if>
			 	  <if test="linkemailCipher !=null">#{linkemailCipher},</if>
	               </trim>	
         )      
	  </insert>
	  
	  <select id="listBxInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	      SELECT T.ID, T.IDNO, T.INSURIDNO
		    FROM CM_BX_PREBOOKINFO T
		    LEFT JOIN CM_BX_PREBOOKINFO_CIPHER T1
		      ON T.ID = T1.ID
		   WHERE T1.ID IS NULL
    </select>
    <update id="updateBxPrebookinfo" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    UPDATE CM_BX_PREBOOKINFO	    
	    <set>
	          <if test="idnoDigest !=null">idno_Digest = #{idnoDigest},</if>
		 	  <if test="idnoMask !=null">idno_Mask = #{idnoMask},</if>
			  <if test="insuridnoDigest !=null">INSURIDNO_DIGEST = #{insuridnoDigest},</if>
		 	  <if test="insuridnoMask !=null">INSURIDNO_MASK = #{insuridnoMask},</if>
		</set>
          where id = #{id}
	  </update>
	  
	 <insert id="insertBxPrebookinfoCipher" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    INSERT INTO CM_BX_PREBOOKINFO_CIPHER (
	     <trim suffix="" suffixOverrides=",">	
	      	      <if test="id != null"> id, </if> 
	      	      <if test="idnoCipher !=null">idno_Cipher,</if>
			 	  <if test="insuridnoCipher !=null">insuridno_Cipher,</if>
	               </trim>
           ) values (
         <trim suffix="" suffixOverrides=",">
          	      <if test="id != null"> #{id}, </if> 
			 	 <if test="idnoCipher !=null">#{idnoCipher},</if>
			 	 <if test="insuridnoCipher !=null">#{insuridnoCipher},</if>
	               </trim>	
         )      
	  </insert>
	  
    <select id="listOperationInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	      SELECT T.OPERATIONID ID, T.MOBILE
		    FROM CM_CONSCUST_OPERATION T
		 WHERE T.MOBILE_DIGEST IS NULL
		   AND T.MOBILE IS NOT NULL
    </select>
    
    <update id="updateOperationInfo" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    UPDATE CM_CONSCUST_OPERATION	    
	    <set>
	          <if test="mobileDigest !=null">mobile_Digest = #{mobileDigest},</if>
		 	  <if test="mobileMask !=null">mobile_Mask = #{mobileMask},</if>
			  <if test="mobileCipher !=null">mobile_Cipher = #{mobileCipher},</if>
		</set>
          where OPERATIONID = #{id}
	  </update>	  
	  <select id="listPreBankInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	      SELECT T.PREID, T.BANKACCT
		  FROM CM_PREBOOK_EXTEND T
		 WHERE T.BANKACCT IS NOT NULL
    </select>
    
    <update id="updatePreBankinfo" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    UPDATE CM_PREBOOK_EXTEND	    
	    <set>
	          <if test="bankacctDigest !=null">BANKACCT_DIGEST = #{bankacctDigest},</if>
		 	  <if test="bankacctMask !=null">BANKACCT_MASK = #{bankacctMask},</if>
			  <if test="bankacctCipher !=null">BANKACCT_CIPHER = #{bankacctCipher},</if>
		</set>
          where preid = #{preid}
	  </update>
	  
	  <select id="listHasVerifyIdnoInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	      SELECT T.IDNO
		  FROM CM_HASVERIFYIDNO T
		 WHERE T.IDNO IS NOT NULL
    </select>
    
    <update id="updateHasVerifyIdnoInfo" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo" >
	    UPDATE CM_HASVERIFYIDNO	    
	    <set>
	          <if test="idnoDigest !=null">idno_DIGEST = #{idnoDigest},</if>
		 	  <if test="idnoMask !=null">idno_MASK = #{idnoMask},</if>
			  <if test="idnoCipher !=null">idno_CIPHER = #{idnoCipher},</if>
		</set>
          where idno = #{idno}
	  </update>
	  
	  <select id="updateFlushCusthisFlag" parameterType="map" >
	  	update  CM_FLUSH_CUSTHIS t set t.flag = '1',t.DEALTIME=sysdate WHERE t.APPSERIALNO=#{appserialno}
	  </select>
	  
	  <select id="listConscustHisInfo" parameterType="map" resultMap="BaseResultMap"  useCache="false">
	    SELECT T.APPSERIALNO,
	           T.conscustno,
		       T.IDNO,
		       T.MOBILE,
		       T.EMAIL,
		       T.TELNO,
		       T.ADDR,
		       T.MOBILE2,
		       T.EMAIL2,
		       T.ADDR2,
		       T.LINKMOBILE,
		       T.LINKTEL,
		       T.LINKEMAIL,
		       T.LINKADDR
		  FROM CM_CONSCUSTHIS T
		 INNER JOIN CM_FLUSH_CUSTHIS T1
		    ON T.APPSERIALNO = T1.APPSERIALNO
		 WHERE T1.FLAG = '0'
		 and T1.page = #{page}
		 AND ROWNUM &lt;= 150
    </select>
    
    <update id="batchInsertCmConscustHis" parameterType="com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CM_CONSCUSTHIS 
        set 
		idno_Digest = #{item.idnoDigest,jdbcType=VARCHAR},
		idno_Mask = #{item.idnoMask,jdbcType=VARCHAR},
		mobile_Digest = #{item.mobileDigest,jdbcType=VARCHAR},
		mobile_Mask = #{item.mobileMask,jdbcType=VARCHAR},
		email_Digest = #{item.emailDigest,jdbcType=VARCHAR},
		email_Mask = #{item.emailMask,jdbcType=VARCHAR},
		telno_Digest = #{item.telnoDigest,jdbcType=VARCHAR},
		telno_Mask = #{item.telnoMask,jdbcType=VARCHAR},
		addr_Digest = #{item.addrDigest,jdbcType=VARCHAR},
		addr_Mask = #{item.addrMask,jdbcType=VARCHAR},
		mobile2_Digest = #{item.mobile2Digest,jdbcType=VARCHAR},
		mobile2_Mask = #{item.mobile2Mask,jdbcType=VARCHAR},
		email2_Digest = #{item.email2Digest,jdbcType=VARCHAR},
		email2_Mask = #{item.email2Mask,jdbcType=VARCHAR},
		addr2_Digest = #{item.addr2Digest,jdbcType=VARCHAR},
		addr2_Mask = #{item.addr2Mask,jdbcType=VARCHAR},
		linkmobile_Digest = #{item.linkmobileDigest,jdbcType=VARCHAR},
		linkmobile_Mask = #{item.linkmobileMask,jdbcType=VARCHAR},
		linktel_Digest = #{item.linktelDigest,jdbcType=VARCHAR},
		linktel_Mask = #{item.linktelMask,jdbcType=VARCHAR},
		linkemail_Digest = #{item.linkemailDigest,jdbcType=VARCHAR},
		linkemail_Mask = #{item.linkemailMask,jdbcType=VARCHAR},
		linkaddr_Digest = #{item.linkaddrDigest,jdbcType=VARCHAR},
		linkaddr_Mask = #{item.linkaddrMask,jdbcType=VARCHAR}
        where appserialno = #{item.appserialno,jdbcType=VARCHAR};
      
        INSERT INTO CM_CONSCUSTHIS_cipher(
        appserialno,
        conscustno,
		idno_cipher,
		mobile_cipher,
		email_cipher,
		telno_cipher,
		addr_cipher,
		mobile2_cipher,
		email2_cipher,
		addr2_cipher,
		linkmobile_cipher,
		linktel_cipher,
		linkemail_cipher,
		linkaddr_cipher
        )VALUES (
        #{item.appserialno,jdbcType=VARCHAR},
        #{item.conscustno,jdbcType=VARCHAR},
		#{item.idnoCipher,jdbcType=VARCHAR},
		#{item.mobileCipher,jdbcType=VARCHAR},
		#{item.emailCipher,jdbcType=VARCHAR},
		#{item.telnoCipher,jdbcType=VARCHAR},
		#{item.addrCipher,jdbcType=VARCHAR},
		#{item.mobile2Cipher,jdbcType=VARCHAR},
		#{item.email2Cipher,jdbcType=VARCHAR},
		#{item.addr2Cipher,jdbcType=VARCHAR},
		#{item.linkmobileCipher,jdbcType=VARCHAR},
		#{item.linktelCipher,jdbcType=VARCHAR},
		#{item.linkemailCipher,jdbcType=VARCHAR},
		#{item.linkaddrCipher,jdbcType=VARCHAR}
        );
        
        update CM_FLUSH_CUSTHIS set flag = '1',dealtime=sysdate where appserialno = #{item.appserialno,jdbcType=VARCHAR};
      </foreach>
        commit;
        END;
    </update>
</mapper>
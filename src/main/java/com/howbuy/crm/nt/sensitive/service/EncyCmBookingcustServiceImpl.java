package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CmBookingcustMapper;
import com.howbuy.crm.nt.sensitive.dto.CmBookingcust;

/**
 * 刷网站预约脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCmBookingcustService")
public class EncyCmBookingcustServiceImpl implements EncyCmBookingcustService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCmBookingcustServiceImpl.class);

	@Autowired
	private CmBookingcustMapper cmBookingcustMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;

	@Override
	public void encyCmBookingcustData(String arg) {
		logger.info("处理预约脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<CmBookingcust> listCmBookingcust = null;
		//程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
		//循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询网站预约数据：每次查询两百条
				listCmBookingcust = cmBookingcustMapper.listCmBookingcustLimit200(param);
				
				//处理脱敏三要素
				handleCmBookingCustList(listCmBookingcust);
				
				//更新网站预约表
				int count = this.updateCmBookingCustBatch(listCmBookingcust);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理预约脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理预约脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCmBookingCustList(List<CmBookingcust> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			List<String> emailList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CmBookingcust cmBookingcust : list){
				String mobile = cmBookingcust.getMobile();
				String email = cmBookingcust.getEmail();
				if(StringUtils.isNotBlank(mobile)){
					cmBookingcust.setMobileDigest(DigestUtil.digest(mobile.trim()));
					cmBookingcust.setMobileMask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
				
				if(StringUtils.isNotBlank(email)){
					cmBookingcust.setEmailDigest(DigestUtil.digest(email.trim().toLowerCase()));
					cmBookingcust.setEmailMask(MaskUtil.maskEmail(email.trim().toLowerCase()));
					emailList.add(email.trim().toLowerCase());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("预约mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求预约手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求预约手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CmBookingcust cmBookingcust : list){
	    				if(StringUtils.isNotBlank(cmBookingcust.getMobile())){
	    					cmBookingcust.setMobileCipher(mobilemap.get(cmBookingcust.getMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("预约mobileList加密结束时间"+System.currentTimeMillis());
			}
			
			
			if(emailList != null && emailList.size() > 0){
    			logger.info("预约emailList条数:"+emailList.size()+";加密开始时间"+System.currentTimeMillis());
    			logger.info("请求预约邮箱加密参数："+JSON.toJSONString(emailList));
	    		CodecBatchResponse resemail = encryptBatchFacade.encryptBatch(emailList);
	    		logger.info("请求预约邮箱加密返回："+JSON.toJSONString(resemail));
	    		if(resemail != null && resemail.getCodecMap() != null){
	    			Map<String,String> emailmap = resemail.getCodecMap();
	    			for(CmBookingcust cmBookingcust : list){
	    				if(StringUtils.isNotBlank(cmBookingcust.getEmail())){
	    					cmBookingcust.setEmailCipher(emailmap.get(cmBookingcust.getEmail().trim().toLowerCase()));
	    				}
	    			}
	    		}
	    		logger.info("预约emailList加密结束时间"+System.currentTimeMillis());
    		}
		}
		
		logger.info("保存预约脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入网站预约数据
	 * @param list
	 * @return
	 */
	private int updateCmBookingCustBatch(List<CmBookingcust> list){
		if(CollectionUtils.isNotEmpty(list)){
			cmBookingcustMapper.batchUpdateCmBookingcust(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

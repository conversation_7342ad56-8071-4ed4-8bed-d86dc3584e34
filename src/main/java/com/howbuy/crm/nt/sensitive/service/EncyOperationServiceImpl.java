package com.howbuy.crm.nt.sensitive.service;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyOperationService")
public class EncyOperationServiceImpl implements EncyOperationService {
	
	private Logger logger= LoggerFactory.getLogger(EncyOperationServiceImpl.class);

	
	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyOperationData(String arg) {
		List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listOperationInfo(null);
		logger.info("查询了："+listinfo.size()+"条！");
		for(ConscustSensitiveInfo conscustinfo : listinfo){
			if(StringUtils.isNotBlank(conscustinfo.getMobile())){
				conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
				conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
				conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
			}
			cmDealCustnoFlagMapper.updateOperationInfo(conscustinfo);
		}
	}

}

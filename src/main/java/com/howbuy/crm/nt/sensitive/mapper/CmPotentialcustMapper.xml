<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CmPotentialcustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CmPotentialcust">
        <result column="PCUSTID" property="pcustid" jdbcType="VARCHAR"/>
        <result column="IDNO" property="idno" jdbcType="VARCHAR"/>
        <result column="IDNOMASK" property="idnomask" jdbcType="VARCHAR"/>
        <result column="IDNODIGEST" property="idnodigest" jdbcType="VARCHAR"/>
        <result column="IDNOCIPHER" property="idnocipher" jdbcType="VARCHAR"/>
        
        <result column="ADDR" property="addr" jdbcType="VARCHAR"/>
        <result column="ADDRMASK" property="addrmask" jdbcType="VARCHAR"/>
        <result column="ADDRDIGEST" property="addrdigest" jdbcType="VARCHAR"/>
        <result column="ADDRCIPHER" property="addrcipher" jdbcType="VARCHAR"/>
        
        <result column="ADDR2" property="addr2" jdbcType="VARCHAR"/>
        <result column="ADDR2MASK" property="addr2mask" jdbcType="VARCHAR"/>
        <result column="ADDR2DIGEST" property="addr2digest" jdbcType="VARCHAR"/>
        <result column="ADDR2CIPHER" property="addr2cipher" jdbcType="VARCHAR"/>
        
        <result column="ADDR3" property="addr3" jdbcType="VARCHAR"/>
        <result column="ADDR3MASK" property="addr3mask" jdbcType="VARCHAR"/>
        <result column="ADDR3DIGEST" property="addr3digest" jdbcType="VARCHAR"/>
        <result column="ADDR3CIPHER" property="addr3cipher" jdbcType="VARCHAR"/>
        
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="MOBILEMASK" property="mobilemask" jdbcType="VARCHAR"/>
        <result column="MOBILEDIGEST" property="mobiledigest" jdbcType="VARCHAR"/>
        <result column="MOBILECIPHER" property="mobilecipher" jdbcType="VARCHAR"/>
        
        <result column="MOBILE2" property="mobile2" jdbcType="VARCHAR"/>
        <result column="MOBILE2MASK" property="mobile2mask" jdbcType="VARCHAR"/>
        <result column="MOBILE2DIGEST" property="mobile2digest" jdbcType="VARCHAR"/>
        <result column="MOBILE2CIPHER" property="mobile2cipher" jdbcType="VARCHAR"/>
        
        <result column="TELNO" property="telno" jdbcType="VARCHAR"/>
        <result column="TELNOMASK" property="telnomask" jdbcType="VARCHAR"/>
        <result column="TELNODIGEST" property="telnodigest" jdbcType="VARCHAR"/>
        <result column="TELNOCIPHER" property="telnocipher" jdbcType="VARCHAR"/>
        
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="EMAILMASK" property="emailmask" jdbcType="VARCHAR"/>
        <result column="EMAILDIGEST" property="emaildigest" jdbcType="VARCHAR"/>
        <result column="EMAILCIPHER" property="emailcipher" jdbcType="VARCHAR"/>
        
        <result column="EMAIL2" property="email2" jdbcType="VARCHAR"/>
        <result column="EMAIL2MASK" property="email2mask" jdbcType="VARCHAR"/>
        <result column="EMAIL2DIGEST" property="email2digest" jdbcType="VARCHAR"/>
        <result column="EMAIL2CIPHER" property="email2cipher" jdbcType="VARCHAR"/>
        
        <result column="HOMETELNO" property="hometelno" jdbcType="VARCHAR"/>
        <result column="HOMETELNOMASK" property="hometelnomask" jdbcType="VARCHAR"/>
        <result column="HOMETELNODIGEST" property="hometelnodigest" jdbcType="VARCHAR"/>
        <result column="HOMETELNOCIPHER" property="hometelnocipher" jdbcType="VARCHAR"/>
        
        <result column="OFFICETELNO" property="officetelno" jdbcType="VARCHAR"/>
        <result column="OFFICETELNOMASK" property="officetelnomask" jdbcType="VARCHAR"/>
        <result column="OFFICETELNODIGEST" property="officetelnodigest" jdbcType="VARCHAR"/>
        <result column="OFFICETELNOCIPHER" property="officetelnocipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCmPotentialcustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT PCUSTID,IDNO,ADDR,ADDR2,ADDR3,MOBILE,MOBILE2,TELNO,EMAIL,EMAIL2,HOMETELNO,OFFICETELNO
		 FROM CM_POTENTIALCUST 
		 WHERE (   (IDNO is not null AND (IDNOMASK is null or IDNODIGEST is null or IDNOCIPHER is null) )
		        OR (ADDR  is not null AND (ADDRMASK  is null or ADDRDIGEST is null or ADDRCIPHER is null) )
		        OR (ADDR2  is not null AND (ADDR2MASK  is null or ADDR2DIGEST is null or ADDR2CIPHER is null) )
		        OR (ADDR3  is not null AND (ADDR3MASK  is null or ADDR3DIGEST is null or ADDR3CIPHER is null) )
		        OR (MOBILE  is not null AND (MOBILEMASK  is null or MOBILEDIGEST is null or MOBILECIPHER is null) )
		        OR (MOBILE2  is not null AND (MOBILE2MASK  is null or MOBILE2DIGEST is null or MOBILE2CIPHER is null) )
		        OR (TELNO  is not null AND (TELNOMASK  is null or TELNODIGEST is null or TELNOCIPHER is null) )
		        OR (EMAIL  is not null AND (EMAILMASK  is null or EMAILDIGEST is null or EMAILCIPHER is null) )
		        OR (EMAIL2  is not null AND (EMAIL2MASK  is null or EMAIL2DIGEST is null or EMAIL2CIPHER is null) )
		        OR (HOMETELNO  is not null AND (HOMETELNOMASK  is null or HOMETELNODIGEST is null or HOMETELNOCIPHER is null) )
		        OR (OFFICETELNO  is not null AND (OFFICETELNOMASK  is null or OFFICETELNODIGEST is null or OFFICETELNOCIPHER is null) )
		       )
		 <if test="startId !=null"> AND PCUSTID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND PCUSTID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 500 
		 ORDER BY PCUSTID+0 desc
    </select>
    
    
    <insert id="batchUpdateCmPotentialcust" parameterType="com.howbuy.crm.nt.sensitive.dto.CmPotentialcust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CM_POTENTIALCUST 
        set 
           IDNOMASK = #{item.idnomask,jdbcType=VARCHAR},IDNODIGEST = #{item.idnodigest,jdbcType=VARCHAR},IDNOCIPHER = #{item.idnocipher,jdbcType=VARCHAR},
           ADDRMASK = #{item.addrmask,jdbcType=VARCHAR},ADDRDIGEST = #{item.addrdigest,jdbcType=VARCHAR},ADDRCIPHER = #{item.addrcipher,jdbcType=VARCHAR},
           ADDR2MASK = #{item.addr2mask,jdbcType=VARCHAR},ADDR2DIGEST = #{item.addr2digest,jdbcType=VARCHAR},ADDR2CIPHER = #{item.addr2cipher,jdbcType=VARCHAR},
           ADDR3MASK = #{item.addr3mask,jdbcType=VARCHAR},ADDR3DIGEST = #{item.addr3digest,jdbcType=VARCHAR},ADDR3CIPHER = #{item.addr3cipher,jdbcType=VARCHAR},
           MOBILEMASK = #{item.mobilemask,jdbcType=VARCHAR},MOBILEDIGEST = #{item.mobiledigest,jdbcType=VARCHAR},MOBILECIPHER = #{item.mobilecipher,jdbcType=VARCHAR},
           MOBILE2MASK = #{item.mobile2mask,jdbcType=VARCHAR},MOBILE2DIGEST = #{item.mobile2digest,jdbcType=VARCHAR},MOBILE2CIPHER = #{item.mobile2cipher,jdbcType=VARCHAR},
           TELNOMASK = #{item.telnomask,jdbcType=VARCHAR},TELNODIGEST = #{item.telnodigest,jdbcType=VARCHAR},TELNOCIPHER = #{item.telnocipher,jdbcType=VARCHAR},
           EMAILMASK = #{item.emailmask,jdbcType=VARCHAR},EMAILDIGEST = #{item.emaildigest,jdbcType=VARCHAR},EMAILCIPHER = #{item.emailcipher,jdbcType=VARCHAR},
           EMAIL2MASK = #{item.email2mask,jdbcType=VARCHAR},EMAIL2DIGEST = #{item.email2digest,jdbcType=VARCHAR},EMAIL2CIPHER = #{item.email2cipher,jdbcType=VARCHAR},
           HOMETELNOMASK = #{item.hometelnomask,jdbcType=VARCHAR},HOMETELNODIGEST = #{item.hometelnodigest,jdbcType=VARCHAR},HOMETELNOCIPHER = #{item.hometelnocipher,jdbcType=VARCHAR},
           OFFICETELNOMASK = #{item.officetelnomask,jdbcType=VARCHAR},OFFICETELNODIGEST = #{item.officetelnodigest,jdbcType=VARCHAR},OFFICETELNOCIPHER = #{item.officetelnocipher,jdbcType=VARCHAR}
        where PCUSTID = #{item.pcustid};
      </foreach>
        commit;
        END;
    </insert>

</mapper>
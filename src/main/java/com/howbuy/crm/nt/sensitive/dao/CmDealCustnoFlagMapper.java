package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

/**
 * <AUTHOR>
 * @Description: 脱敏数据处理
 * @reason:
 * @Date: 2021/3/8 10:33
 */
public interface CmDealCustnoFlagMapper {


    /**
     * 获取
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listConscustSensitiveInfo(Map<String,String> map);
    
    /**
     * 获取
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listConscustSensitiveInfo1(Map<String,String> map);
    
    /**
     * 获取待处理的数据
     * @param map
     * @return
     */
    int getSensitiveCount(Map<String,String> map);
    
    /**
     * 获取待处理的数据
     * @param map
     * @return
     */
    int getSensitiveCount1(Map<String,String> map);
    
    /**
     * 批量插入临时表
     * @param list
     * @return
     */
    int batchInsertCmConscustSensitTemp(List<ConscustSensitiveInfo> list);
    
    /**
     * 批量插入临时表
     * @param list
     * @return
     */
    int batchInsertCmConscustSensitTemp1(List<ConscustSensitiveInfo> list);
    
    /**
     * 入临时表
     * @param info
     * @return
     */
    int insertCmConscustSensitTemp(ConscustSensitiveInfo info);


	
	/**
     * 获取剩余待处理的数据
     * @param map
     * @return
     */
    int getCmDealCustNoFlagCount(Map<String,String> map);
    
    /**
     * 获取剩余待处理的数据
     * @param map
     * @return
     */
    int getCmDealCustNoFlagCount1(Map<String,String> map);
    
    /**
     * 获取开关状态
     * @param map
     * @return
     */
    String getCmOpenCloseFlag(Map<String,String> map);
    
    /**
     * 获取开关状态
     * @param map
     * @return
     */
    String getCmOpenCloseFlag1(Map<String,String> map);
    
    /**
     * 更新开关状态
     * @param map
     * @return
     */
    int updateCmOpenCloseFlag(Map<String,String> map);
    
    /**
     * 更新开关状态
     * @param map
     * @return
     */
    int updateCmOpenCloseFlag1(Map<String,String> map);
    

    /**
     * 获取对比信息
     * @param params
     * @return
     */
    List<Map<String, String> > listCompareSensitive(Map<String, Object> params);
    
    /**
     * 插入对比日志
     * @param params
     * @return
     */
    int insertCompareSensitiveLog(Map<String, String> params);
    
    /**
     * 获取
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listConscustIcInfo(Map<String,String> map);
    
    /**
     * 获取
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listConscustIcAllInfo(Map<String,String> map);
    
    /**
     * 入临时表
     * @param info
     * @return
     */
    int insertConscustIcCipher(ConscustSensitiveInfo info);
    
    /**
     * 入临时表
     * @param info
     * @return
     */
    int updateConscustIc(ConscustSensitiveInfo info);
    
    /**
     * 获取创新产品数据
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listBxInfo(Map<String,String> map);
    
    /**
     * 获取创新产品数据
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listPreBankInfo(Map<String,String> map);
    
    /**
     * 更新创新产品表
     * @param info
     * @return
     */
    int updateBxPrebookinfo(ConscustSensitiveInfo info);
    
    /**
     * 插入创新产品密文表
     * @param info
     * @return
     */
    int insertBxPrebookinfoCipher(ConscustSensitiveInfo info);
    
    /**
     * 更新预约拓展表的银行账号
     * @param info
     * @return
     */
    int updatePreBankinfo(ConscustSensitiveInfo info);
    
    /**
     * 获取绿色通道验证的证件号数据
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listHasVerifyIdnoInfo(Map<String,String> map);
    
    /**
     * 更新绿色通道验证的证件号数据
     * @param info
     * @return
     */
    int updateHasVerifyIdnoInfo(ConscustSensitiveInfo info);
    

    /**
     * 获取重复客户列表
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listOperationInfo(Map<String,String> map);
    
    /**
     * 更新重复客户表
     * @param info
     * @return
     */
    int updateOperationInfo(ConscustSensitiveInfo info);
    
    /**
     * 更新
     * @param map
     * @return
     */
    int updateFlushCusthisFlag(Map<String,String> map);
    
    /**
     * 获取待处理信息
     * @param map
     * @return
     */
    List<ConscustSensitiveInfo> listConscustHisInfo(Map<String,String> map);
    
    /**
     * 批量更新
     * @param info
     * @return
     */
    int batchInsertCmConscustHis(List<ConscustSensitiveInfo> info);
    
    
}

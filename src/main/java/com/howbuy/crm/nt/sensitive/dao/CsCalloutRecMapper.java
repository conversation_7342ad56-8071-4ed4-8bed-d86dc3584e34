package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CsCalloutRec;

/**
 * <AUTHOR>
 * @Description: 外呼记录流水表脱敏处理
 * @reason:
 * @Date: 2021/8/13 16:30
 */
public interface CsCalloutRecMapper {
    
	/**
	 * 获取需要脱敏的外呼记录流水
	 * @return
	 */
    List<CsCalloutRec> listCsCalloutRecLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏外呼记录流水
     * @param list
     * @return
     */
    int batchUpdateCsCalloutRec(List<CsCalloutRec> list);

}

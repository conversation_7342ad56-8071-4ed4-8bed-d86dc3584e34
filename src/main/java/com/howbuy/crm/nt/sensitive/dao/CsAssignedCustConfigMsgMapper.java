package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.CsAssignedCustConfigMsg;

/**
 * <AUTHOR>
 * @Description: 待分配客户配置信息脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 11:33
 */
public interface CsAssignedCustConfigMsgMapper {
    
	/**
	 * 获取需要脱敏的预约数据
	 * @return
	 */
    List<CsAssignedCustConfigMsg> listCsAssignedCustConfigMsgLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏预约数据
     * @param list
     * @return
     */
    int batchUpdateCsAssignedCustConfigMsg(List<CsAssignedCustConfigMsg> list);

}

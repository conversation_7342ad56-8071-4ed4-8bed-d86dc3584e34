package com.howbuy.crm.nt.sensitive.service;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;



/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyPreBankService")
public class EncyPreBankServiceImpl implements EncyPreBankService {
	
	private Logger logger= LoggerFactory.getLogger(EncyPreBankServiceImpl.class);

	
	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Override
	public void encyBankData(String arg) {
		List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listPreBankInfo(null);
		logger.info("查询了："+listinfo.size()+"条！");
		for(ConscustSensitiveInfo preinfo : listinfo){
			if(StringUtils.isNotBlank(preinfo.getBankacct())){
				preinfo.setBankacctDigest(DigestUtil.digest(preinfo.getBankacct().trim()));
				preinfo.setBankacctMask(MaskUtil.maskBankAcct(preinfo.getBankacct().trim()));
				preinfo.setBankacctCipher(encryptSingleFacade.encrypt(preinfo.getBankacct().trim()).getCodecText());
			}
			cmDealCustnoFlagMapper.updatePreBankinfo(preinfo);
		}
	}

}

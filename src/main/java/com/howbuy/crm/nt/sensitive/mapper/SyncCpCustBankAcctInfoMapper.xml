<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.SyncCpCustBankAcctInfoMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.SyncCpCustBankAcctInfo">
        <result column="CUST_BANK_ID" property="custbankid" jdbcType="VARCHAR"/>
        <result column="BANK_ACCT" property="bankacct" jdbcType="VARCHAR"/>
        <result column="BANK_ACCT_MASK" property="bankacctmask" jdbcType="VARCHAR"/>
        <result column="BANK_ACCT_DIGEST" property="bankacctdigest" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listSyncCpCustBankAcctInfoLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT CUST_BANK_ID,
		       BANK_ACCT
		 FROM SYNC_CP_CUST_BANK_ACCT_INFO 
		 WHERE ( BANK_ACCT is not null AND (BANK_ACCT_MASK is null or BANK_ACCT_DIGEST is null) )
		 <if test="startId !=null"> AND CUST_BANK_ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND CUST_BANK_ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY CUST_BANK_ID
    </select>
    
    
    <insert id="batchUpdateSyncCpCustBankAcctInfo" parameterType="com.howbuy.crm.nt.sensitive.dto.SyncCpCustBankAcctInfo">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update SYNC_CP_CUST_BANK_ACCT_INFO
        set 
           BANK_ACCT_MASK = #{item.bankacctmask,jdbcType=VARCHAR},BANK_ACCT_DIGEST = #{item.bankacctdigest,jdbcType=VARCHAR}
        where CUST_BANK_ID = #{item.custbankid};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CsCalloutWaitdistribute;

/**
 * <AUTHOR>
 * @Description: 待分配脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 17:32
 */
public interface CsCalloutWaitdistributeMapper {
    
	/**
	 * 获取需要脱敏的待分配数据：200条
	 * @return
	 */
    List<CsCalloutWaitdistribute> listCsCalloutWaitdistributeLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏待分配数据
     * @param list
     * @return
     */
    int batchUpdateCsCalloutWaitdistribute(List<CsCalloutWaitdistribute> list);

}

package com.howbuy.crm.nt.sensitive.service;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyBxService")
public class EncyBxServiceImpl implements EncyBxService {
	
	private Logger logger= LoggerFactory.getLogger(EncyConscustIcServiceImpl.class);

	
	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyBxData(String arg) {
		List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listBxInfo(null);
		logger.info("查询了："+listinfo.size()+"条！");
		for(ConscustSensitiveInfo conscustinfo : listinfo){
			if(StringUtils.isNotBlank(conscustinfo.getIdno())){
				conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
				conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
				conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getInsuridno())){
				conscustinfo.setInsuridnoDigest(DigestUtil.digest(conscustinfo.getInsuridno().trim()));
				conscustinfo.setInsuridnoMask(MaskUtil.maskIdNo(conscustinfo.getInsuridno().trim()));
				conscustinfo.setInsuridnoCipher(encryptSingleFacade.encrypt(conscustinfo.getInsuridno().trim()).getCodecText());
			}
			cmDealCustnoFlagMapper.updateBxPrebookinfo(conscustinfo);
			cmDealCustnoFlagMapper.insertBxPrebookinfoCipher(conscustinfo);
		}
	}

}

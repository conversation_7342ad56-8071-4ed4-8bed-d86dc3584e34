package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CsCalllossCust;

/**
 * <AUTHOR>
 * @Description: 呼损脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 17:38
 */
public interface CsCalllossCustMapper {
    
	/**
	 * 获取需要脱敏的呼损数据：200条
	 * @return
	 */
    List<CsCalllossCust> listCsCalllossCustLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏呼损数据
     * @param list
     * @return
     */
    int batchUpdateCsCalllossCust(List<CsCalllossCust> list);

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.UpdateSurveyDataMapper">

    <select id="update" timeout="36000">
        DECLARE
        begin
          for mobileData in (select distinct t1.mobile,
                                             t2.mobile_mask,
                                             t2.mobile_digest,
                                             t3.mobile_cipher
                               from CM_CONSCUSTSURVEYREC t1
                              inner join cm_conscust t2
                                 on t1.mobile = t2.mobile
                               left join cm_conscust_cipher t3
                                 on t2.conscustno = t3.conscustno
                              where t1.mobile is not null and t1.mobile_digest is null) loop
            update CM_CONSCUSTSURVEYREC
               set mobile_mask   = mobileData.mobile_mask,
                   mobile_digest = mobileData.mobile_digest,
                   mobile_cipher = mobileData.mobile_cipher
             where mobile = mobileData.Mobile;
             commit;
          end loop;

          update CM_CONSCUSTSURVEYREC
               set mobile_mask   = '1860169****',
                   mobile_digest = '3e163a633aed89c9ba674d0c07146e73',
                   mobile_cipher = 'ysSz86K50H5gbG2u0rfP0Q==01'
             where mobile = '18601695408';
          commit;

          for idnoData in (select distinct t1.idno,
                                           t2.idno_mask,
                                           t2.idno_digest,
                                           t3.idno_cipher
                             from CM_CONSCUSTSURVEYREC t1
                            inner join cm_conscust t2
                               on t1.idno = t2.idno
                             left join cm_conscust_cipher t3
                               on t2.conscustno = t3.conscustno
                            where t1.idno is not null and t1.idno_digest is null) loop
            update CM_CONSCUSTSURVEYREC
               set idno_mask   = idnoData.idno_mask,
                   idno_digest = idnoData.idno_digest,
                   idno_cipher = idnoData.idno_cipher
             where idno = idnoData.Idno;
              commit;
          end loop;

          update CM_CONSCUSTSURVEYREC
               set idno_mask   = '32128419910605****',
                   idno_digest = '80139d7bc6113ce4bd450cd34c3d648d',
                   idno_cipher = 'vL8ywm_hBvV-fXd4nDhwrgqudukYjkSTOFlBv56oOW8=01'
             where idno = '32128419910605482X';
          commit;
        end;
    </select>
</mapper>
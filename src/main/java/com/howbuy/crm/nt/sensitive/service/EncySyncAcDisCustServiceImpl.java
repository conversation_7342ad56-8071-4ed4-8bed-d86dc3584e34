package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.crm.nt.sensitive.dao.SyncAcDisCustMapper;
import com.howbuy.crm.nt.sensitive.dto.SyncAcDisCust;

/**
 * 刷分销客户信息脱敏数据
 * <AUTHOR>
 *
 */
@Service("encySyncAcDisCustService")
public class EncySyncAcDisCustServiceImpl implements EncySyncAcDisCustService {
	
	private Logger logger= LoggerFactory.getLogger(EncySyncAcDisCustServiceImpl.class);

	@Autowired
	private SyncAcDisCustMapper syncAcDisCustMapper; 
	
	@Override
	public void encySyncAcDisCustData(String arg) {
		logger.info("处理分销客户信息脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<SyncAcDisCust> listSyncAcDisCust = null;
        // 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
        // 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询分销客户信息：每次查询两百条
				listSyncAcDisCust = syncAcDisCustMapper.listSyncAcDisCustLimit200(param);
				
				//处理脱敏三要素
				handleSyncAcDisCustList(listSyncAcDisCust);
				
				//更新分销客户信息表
				int count = this.updateSyncAcDisCustBatch(listSyncAcDisCust);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理分销客户信息脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理分销客户信息脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleSyncAcDisCustList(List<SyncAcDisCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			//获取掩码和摘要
			for(SyncAcDisCust syncAcDisCust : list){
				String addr = syncAcDisCust.getAddr();
				String telno = syncAcDisCust.getTelno();
				String linkidno = syncAcDisCust.getLinkidno();
				String linktel = syncAcDisCust.getLinktel();
				String email = syncAcDisCust.getEmail();
				String hometelno = syncAcDisCust.getHometelno();
				String mobile = syncAcDisCust.getMobile();
				if(StringUtils.isNotBlank(addr)){
					syncAcDisCust.setAddrdigest(DigestUtil.digest(addr.trim()));
					syncAcDisCust.setAddrmask(MaskUtil.maskAddr(addr.trim()));
				}
				
				if(StringUtils.isNotBlank(telno)){
					syncAcDisCust.setTelnodigest(DigestUtil.digest(telno.trim()));
					syncAcDisCust.setTelnomask(MaskUtil.maskMobile(telno.trim()));
				}
				
				if(StringUtils.isNotBlank(linkidno)){
					syncAcDisCust.setLinkidnodigest(DigestUtil.digest(linkidno.trim()));
					syncAcDisCust.setLinkidnomask(MaskUtil.maskIdNo(linkidno.trim()));
				}
				
				if(StringUtils.isNotBlank(linktel)){
					syncAcDisCust.setLinkteldigest(DigestUtil.digest(linktel.trim()));
					syncAcDisCust.setLinktelmask(MaskUtil.maskMobile(linktel.trim()));
				}
				
				if(StringUtils.isNotBlank(email)){
					syncAcDisCust.setEmaildigest(DigestUtil.digest(email.trim().toLowerCase()));
					syncAcDisCust.setEmailmask(MaskUtil.maskEmail(email.trim().toLowerCase()));
				}
				
				if(StringUtils.isNotBlank(hometelno)){
					syncAcDisCust.setHometelnodigest(DigestUtil.digest(hometelno.trim()));
					syncAcDisCust.setHometelnomask(MaskUtil.maskMobile(hometelno.trim()));
				}
				
				if(StringUtils.isNotBlank(mobile)){
					syncAcDisCust.setMobiledigest(DigestUtil.digest(mobile.trim()));
					syncAcDisCust.setMobilemask(MaskUtil.maskMobile(mobile.trim()));
				}
			}
		}
		
		//logger.info("保存预约脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入分销客户信息
	 * @param list
	 * @return
	 */
	private int updateSyncAcDisCustBatch(List<SyncAcDisCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			syncAcDisCustMapper.batchUpdateSyncAcDisCust(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

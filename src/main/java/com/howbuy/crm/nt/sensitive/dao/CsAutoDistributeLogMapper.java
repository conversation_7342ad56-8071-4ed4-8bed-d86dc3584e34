package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.CsAutoDistributeLog;

/**
 * <AUTHOR>
 * @Description: 网站脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 11:33
 */
public interface CsAutoDistributeLogMapper {
    
	/**
	 * 获取需要脱敏的预约数据
	 * @return
	 */
    List<CsAutoDistributeLog> listCsAutoDistributeLogLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏预约数据
     * @param list
     * @return
     */
    int batchUpdateCsAutoDistributeLog(List<CsAutoDistributeLog> list);

}

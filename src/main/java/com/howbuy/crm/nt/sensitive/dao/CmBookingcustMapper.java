package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CmBookingcust;

/**
 * <AUTHOR>
 * @Description: 网站脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 11:33
 */
public interface CmBookingcustMapper {

	/**
	 * 获取需要脱敏的预约数据
	 * @param map
	 * @return
	 */
    List<CmBookingcust> listCmBookingcustLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏预约数据
     * @param list
     * @return
     */
    int batchUpdateCmBookingcust(List<CmBookingcust> list);

}

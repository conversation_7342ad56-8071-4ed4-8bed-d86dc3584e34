package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsLeavemsgCustMapper;
import com.howbuy.crm.nt.sensitive.dto.CsLeavemsgCust;

/**
 * 刷留言脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsLeavemsgCustService")
public class EncyCsLeavemsgCustServiceImpl implements EncyCsLeavemsgCustService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsLeavemsgCustServiceImpl.class);

	@Autowired
	private CsLeavemsgCustMapper csLeavemsgCustMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyCsLeavemsgCustData(String arg) {
		logger.info("处理留言脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");
		
		List<CsLeavemsgCust> listCsLeavemsgCust = null;
        // 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
        // 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询留言数据：每次查询两百条
				listCsLeavemsgCust = csLeavemsgCustMapper.listCsLeavemsgCustLimit200(param);
				
				//处理脱敏三要素
				handleCsLeavemsgCustList(listCsLeavemsgCust);
				
				//更新留言表
				int count = this.updateCsLeavemsgCustBatch(listCsLeavemsgCust);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理留言脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理留言脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsLeavemsgCustList(List<CsLeavemsgCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> callerList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsLeavemsgCust csLeavemsgCust : list){
				String caller = csLeavemsgCust.getCaller();
				if(StringUtils.isNotBlank(caller)){
					csLeavemsgCust.setCallerDigest(DigestUtil.digest(caller.trim()));
					csLeavemsgCust.setCallerMask(MaskUtil.maskMobile(caller.trim()));
					callerList.add(caller.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(callerList)){
				logger.info("留言手机callerList条数:"+callerList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求留言手机加密参数："+JSON.toJSONString(callerList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(callerList);
	    		logger.info("请求留言手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> callermap = resMobile.getCodecMap();
	    			for(CsLeavemsgCust csLeavemsgCust : list){
	    				if(StringUtils.isNotBlank(csLeavemsgCust.getCaller())){
	    					csLeavemsgCust.setCallerCipher(callermap.get(csLeavemsgCust.getCaller().trim()));
	    				}
	    			}
	    		}
	    		logger.info("留言callerList加密结束时间"+System.currentTimeMillis());
	    		logger.info("保存留言脱敏数据："+JSON.toJSONString(list));
			}
		}
		logger.info("保存留言脱敏数据："+JSON.toJSONString(list));
	}
	
	/**
	 * 插入留言数据
	 * @param list
	 * @return
	 */
	private int updateCsLeavemsgCustBatch(List<CsLeavemsgCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			csLeavemsgCustMapper.batchUpdateCsLeavemsgCust(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

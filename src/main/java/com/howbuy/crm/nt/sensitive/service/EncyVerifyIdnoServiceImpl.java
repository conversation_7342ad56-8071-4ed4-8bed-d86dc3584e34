package com.howbuy.crm.nt.sensitive.service;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;



/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyVerifyIdnoService")
public class EncyVerifyIdnoServiceImpl implements EncyVerifyIdnoService {
	
	private Logger logger= LoggerFactory.getLogger(EncyVerifyIdnoServiceImpl.class);

	
	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Override
	public void encyIdnoData(String arg) {
		List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listHasVerifyIdnoInfo(null);
		logger.info("查询了："+listinfo.size()+"条！");
		for(ConscustSensitiveInfo preinfo : listinfo){
			if(StringUtils.isNotBlank(preinfo.getIdno())){
				preinfo.setIdnoDigest(DigestUtil.digest(preinfo.getIdno().trim()));
				preinfo.setIdnoMask(MaskUtil.maskIdNo(preinfo.getIdno().trim()));
				preinfo.setIdnoCipher(encryptSingleFacade.encrypt(preinfo.getIdno().trim()).getCodecText());
			}
			cmDealCustnoFlagMapper.updateHasVerifyIdnoInfo(preinfo);
		}
	}

}

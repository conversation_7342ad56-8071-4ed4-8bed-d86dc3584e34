<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsAutoDistributeLogMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsAutoDistributeLog">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="CUSTMOBILE" property="custMobile" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILEDIGEST" property="custMobileDigest" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILEMASK" property="custMobileMask" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILECIPHER" property="custMobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsAutoDistributeLogLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT ID,
		       CUSTMOBILE
		 FROM CS_AUTODISTRIBUTE_LOG 
		 WHERE ( CUSTMOBILE is not null AND (CUSTMOBILEDIGEST is null or CUSTMOBILEMASK is null or CUSTMOBILECIPHER is null) )
		 <if test="startId !=null"> AND ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY ID
    </select>
    
    
    <insert id="batchUpdateCsAutoDistributeLog" parameterType="com.howbuy.crm.nt.sensitive.dto.CsAutoDistributeLog">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_AUTODISTRIBUTE_LOG 
        set 
           CUSTMOBILEDIGEST = #{item.custMobileDigest,jdbcType=VARCHAR},CUSTMOBILEMASK = #{item.custMobileMask,jdbcType=VARCHAR},CUSTMOBILECIPHER = #{item.custMobileCipher,jdbcType=VARCHAR}
        where ID = #{item.id};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
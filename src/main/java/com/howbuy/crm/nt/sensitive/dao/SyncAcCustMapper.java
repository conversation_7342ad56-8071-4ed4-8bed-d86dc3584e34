package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.SyncAcCust;

/**
 * <AUTHOR>
 * @Description: 客户信息脱敏数据处理
 * @reason:
 * @Date: 2021/6/2 10:30
 */
public interface SyncAcCustMapper {
    
	/**
	 * 获取需要脱敏的客户信息
	 * @return
	 */
    List<SyncAcCust> listSyncAcCustLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏客户信息
     * @param list
     * @return
     */
    int batchUpdateSyncAcCust(List<SyncAcCust> list);

}

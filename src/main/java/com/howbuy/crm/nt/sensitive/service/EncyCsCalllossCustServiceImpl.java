package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsCalllossCustMapper;
import com.howbuy.crm.nt.sensitive.dto.CsCalllossCust;

/**
 * 刷呼损脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsCalllossCustService")
public class EncyCsCalllossCustServiceImpl implements EncyCsCalllossCustService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsCalllossCustServiceImpl.class);

	@Autowired
	private CsCalllossCustMapper csCalllossCustMapper;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyCsCalllossCustData(String arg) {
		logger.info("处理呼损脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");
		
		List<CsCalllossCust> listCsCalllossCust = null;
		int errorCount = 0;//程序执行错误次数：超过五次，退出循环，终止调度
		boolean flag = true;//循环状态
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询呼损数据：每次查询两百条
				listCsCalllossCust = csCalllossCustMapper.listCsCalllossCustLimit200(param);
				
				//处理脱敏三要素
				handleCsCalllossCustList(listCsCalllossCust);
				
				//更新呼损表
				int count = this.updateCsCalllossCustBatch(listCsCalllossCust);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理呼损脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理呼损脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsCalllossCustList(List<CsCalllossCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> callernoList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsCalllossCust csCalllossCust : list){
				String callerno = csCalllossCust.getCallerno();
				if(StringUtils.isNotBlank(callerno)){
					csCalllossCust.setCallernoDigest(DigestUtil.digest(callerno.trim()));
					csCalllossCust.setCallernoMask(MaskUtil.maskMobile(callerno.trim()));
					callernoList.add(callerno.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(callernoList)){
				logger.info("呼损callernoList条数:"+callernoList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求呼损手机加密参数："+JSON.toJSONString(callernoList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(callernoList);
	    		logger.info("请求呼损手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> callernomap = resMobile.getCodecMap();
	    			for(CsCalllossCust csCalllossCust : list){
	    				if(StringUtils.isNotBlank(csCalllossCust.getCallerno())){
	    					csCalllossCust.setCallernoCipher(callernomap.get(csCalllossCust.getCallerno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("呼损手机callernoList加密结束时间"+System.currentTimeMillis());
			}
		}

		logger.info("保存呼损脱敏数据："+JSON.toJSONString(list));
	}
	
	/**
	 * 插入呼损数据
	 * @param list
	 * @return
	 */
	private int updateCsCalllossCustBatch(List<CsCalllossCust> list){
		if(CollectionUtils.isNotEmpty(list)){
			csCalllossCustMapper.batchUpdateCsCalllossCust(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

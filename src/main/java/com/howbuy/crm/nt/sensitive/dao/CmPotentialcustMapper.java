package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.CmPotentialcust;

/**
 * <AUTHOR>
 * @Description: 潜在客户信息表脱敏数据处理
 * @reason:
 * @Date: 2021/9/22 11:33
 */
public interface CmPotentialcustMapper {
    
	/**
	 * 获取需要脱敏的潜在客户信息
	 * @return
	 */
    List<CmPotentialcust> listCmPotentialcustLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏潜在客户信息
     * @param list
     * @return
     */
    int batchUpdateCmPotentialcust(List<CmPotentialcust> list);

}

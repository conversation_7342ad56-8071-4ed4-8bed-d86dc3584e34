package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.HboneCustinfo;

/**
 * <AUTHOR>
 * @Description: 一账通客户信息脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 11:33
 */
public interface HboneCustinfoMapper {
    
	/**
	 * 获取需要脱敏的一账通客户信息
	 * @return
	 */
    List<HboneCustinfo> listHboneCustinfoLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏一账通客户信息
     * @param list
     * @return
     */
    int batchUpdateHboneCustinfo(List<HboneCustinfo> list);

}

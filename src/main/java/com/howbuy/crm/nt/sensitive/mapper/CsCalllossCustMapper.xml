<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsCalllossCustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsCalllossCust">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="CALLERNO" property="callerno" jdbcType="VARCHAR"/>
        <result column="CALLERNO_DIGEST" property="callernoDigest" jdbcType="VARCHAR"/>
        <result column="CALLERNO_MASK" property="callernoMask" jdbcType="VARCHAR"/>
        <result column="CALLERNO_CIPHER" property="callernoCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsCalllossCustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT ID,
		       CALLERNO
		 FROM CS_CALLLOSS_CUST 
		 WHERE ( CALLERNO is not null and (CALLERNO_DIGEST is null or CALLERNO_MASK is null or CALLERNO_CIPHER is null) )
		 <if test="startId !=null"> AND ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY calldate
    </select>
    
    
    <insert id="batchUpdateCsCalllossCust" parameterType="com.howbuy.crm.nt.sensitive.dto.CsCalllossCust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_CALLLOSS_CUST 
        set 
           CALLERNO_DIGEST = #{item.callernoDigest,jdbcType=VARCHAR},CALLERNO_MASK = #{item.callernoMask,jdbcType=VARCHAR},CALLERNO_CIPHER = #{item.callernoCipher,jdbcType=VARCHAR}
        where ID = #{item.id};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CsCallin;

/**
 * <AUTHOR>
 * @Description: 呼入脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 16:33
 */
public interface CsCallinMapper {
    
	/**
	 * 获取需要脱敏的呼入数据
	 * @return
	 */
    List<CsCallin> listCsCallinLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏呼入数据
     * @param list
     * @return
     */
    int batchUpdateCsCallin(List<CsCallin> list);

}

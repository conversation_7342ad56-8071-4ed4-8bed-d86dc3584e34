<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.HboneCustinfoMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.HboneCustinfo">
        <result column="HBONE_NO" property="hboneno" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="ID_NO" property="idno" jdbcType="VARCHAR"/>
        <result column="ID_NO_DIGEST" property="idnoDigest" jdbcType="VARCHAR"/>
        <result column="ID_NO_MASK" property="idnoMask" jdbcType="VARCHAR"/>
        <result column="ID_NO_CIPHER" property="idnoCipher" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listHboneCustinfoLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT HBONE_NO,
		       MOBILE,
		       ID_NO
		 FROM HBONE_CUSTINFO 
		 WHERE (   (MOBILE is not null AND (MOBILE_DIGEST is null or MOBILE_MASK is null or MOBILE_CIPHER is null) )
		        OR (ID_NO  is not null AND (ID_NO_DIGEST  is null or ID_NO_MASK is null or ID_NO_CIPHER is null) )
		       )
		 <if test="startId !=null"> AND HBONE_NO &gt;= #{startId} </if>
		 <if test="endId !=null"> AND HBONE_NO &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY HBONE_NO
    </select>
    
    
    <insert id="batchUpdateHboneCustinfo" parameterType="com.howbuy.crm.nt.sensitive.dto.HboneCustinfo">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update HBONE_CUSTINFO 
        set 
           ID_NO_DIGEST = #{item.idnoDigest,jdbcType=VARCHAR},ID_NO_MASK = #{item.idnoMask,jdbcType=VARCHAR},ID_NO_CIPHER = #{item.idnoCipher,jdbcType=VARCHAR},
           MOBILE_DIGEST = #{item.mobileDigest,jdbcType=VARCHAR},MOBILE_MASK = #{item.mobileMask,jdbcType=VARCHAR},MOBILE_CIPHER = #{item.mobileCipher,jdbcType=VARCHAR}
        where HBONE_NO = #{item.hboneno};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
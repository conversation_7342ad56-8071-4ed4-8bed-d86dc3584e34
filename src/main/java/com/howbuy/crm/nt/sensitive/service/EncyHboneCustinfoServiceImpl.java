package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.HboneCustinfoMapper;
import com.howbuy.crm.nt.sensitive.dto.HboneCustinfo;

/**
 * 刷一账通客户信息脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyHboneCustinfoService")
public class EncyHboneCustinfoServiceImpl implements EncyHboneCustinfoService {
	
	private Logger logger= LoggerFactory.getLogger(EncyHboneCustinfoServiceImpl.class);

	@Autowired
	private HboneCustinfoMapper hboneCustinfoMapper;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyHboneCustinfoData(String arg) {
		logger.info("处理一账通客户信息脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<HboneCustinfo> listHboneCustinfo = null;
        // 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
        // 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询一账通客户信息：每次查询两百条
				listHboneCustinfo = hboneCustinfoMapper.listHboneCustinfoLimit200(param);
				
				//处理脱敏三要素
				handleHboneCustinfoList(listHboneCustinfo);
				
				//更新一账通客户信息
				int count = this.updateHboneCustinfoBatch(listHboneCustinfo);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理一账通客户信息脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理一账通客户信息脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleHboneCustinfoList(List<HboneCustinfo> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			List<String> idnoList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(HboneCustinfo hboneCustinfo : list){
				String mobile = hboneCustinfo.getMobile();
				String idno = hboneCustinfo.getIdno();
				if(StringUtils.isNotBlank(mobile)){
					hboneCustinfo.setMobileDigest(DigestUtil.digest(mobile.trim()));
					hboneCustinfo.setMobileMask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
				
				if(StringUtils.isNotBlank(idno)){
					hboneCustinfo.setIdnoDigest(DigestUtil.digest(idno.trim()));
					hboneCustinfo.setIdnoMask(MaskUtil.maskIdNo(idno.trim()));
					idnoList.add(idno.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("预约mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(HboneCustinfo hboneCustinfo : list){
	    				if(StringUtils.isNotBlank(hboneCustinfo.getMobile())){
	    					hboneCustinfo.setMobileCipher(mobilemap.get(hboneCustinfo.getMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("预约mobileList加密结束时间"+System.currentTimeMillis());
			}
			
			
			if(idnoList != null && idnoList.size() > 0){
    			logger.info("idnoList条数:"+idnoList.size()+";加密开始时间"+System.currentTimeMillis());
    			logger.info("请求邮箱加密参数："+JSON.toJSONString(idnoList));
	    		CodecBatchResponse reseidno = encryptBatchFacade.encryptBatch(idnoList);
	    		logger.info("请求邮箱加密返回："+JSON.toJSONString(reseidno));
	    		if(reseidno != null && reseidno.getCodecMap() != null){
	    			Map<String,String> emailmap = reseidno.getCodecMap();
	    			for(HboneCustinfo hboneCustinfo : list){
	    				if(StringUtils.isNotBlank(hboneCustinfo.getIdno())){
	    					hboneCustinfo.setIdnoCipher(emailmap.get(hboneCustinfo.getIdno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("idnoList加密结束时间"+System.currentTimeMillis());
    		}
		}
		
		logger.info("保存一账通客户信息脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入一账通客户信息
	 * @param list
	 * @return
	 */
	private int updateHboneCustinfoBatch(List<HboneCustinfo> list){
		if(CollectionUtils.isNotEmpty(list)){
			hboneCustinfoMapper.batchUpdateHboneCustinfo(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

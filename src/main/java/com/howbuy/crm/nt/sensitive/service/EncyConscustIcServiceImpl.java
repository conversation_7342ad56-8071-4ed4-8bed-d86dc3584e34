package com.howbuy.crm.nt.sensitive.service;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("EncyConscustIcService")
public class EncyConscustIcServiceImpl implements EncyConscustIcService {
	
	private Logger logger= LoggerFactory.getLogger(EncyConscustIcServiceImpl.class);

	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyConscustIcData(String arg) {
		List<ConscustSensitiveInfo> listinfo = cmDealCustnoFlagMapper.listConscustIcInfo(null);
		logger.info("查询了："+listinfo.size()+"条！");
		for(ConscustSensitiveInfo conscustinfo : listinfo){
			if(StringUtils.isNotBlank(conscustinfo.getIdno())){
				conscustinfo.setIdno(conscustinfo.getIdno());
				conscustinfo.setIdnoDigest(DigestUtil.digest(conscustinfo.getIdno().trim()));
				conscustinfo.setIdnoMask(MaskUtil.maskIdNo(conscustinfo.getIdno().trim()));
				conscustinfo.setIdnoCipher(encryptSingleFacade.encrypt(conscustinfo.getIdno().trim()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getMobile())){
				conscustinfo.setMobile(conscustinfo.getMobile());
				conscustinfo.setMobileDigest(DigestUtil.digest(conscustinfo.getMobile().trim()));
				conscustinfo.setMobileMask(MaskUtil.maskMobile(conscustinfo.getMobile().trim()));
				conscustinfo.setMobileCipher(encryptSingleFacade.encrypt(conscustinfo.getMobile().trim()).getCodecText());
			}
			if (StringUtils.isNotBlank(conscustinfo.getEmail())) {
				conscustinfo.setEmail(conscustinfo.getEmail().toLowerCase());
				conscustinfo.setEmailDigest(DigestUtil.digest(conscustinfo.getEmail().trim().toLowerCase()));
				conscustinfo.setEmailMask(MaskUtil.maskEmail(conscustinfo.getEmail().trim().toLowerCase()));
				conscustinfo.setEmailCipher(encryptSingleFacade.encrypt(conscustinfo.getEmail().trim().toLowerCase()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getTelno())){
				conscustinfo.setTelno(conscustinfo.getTelno());
				conscustinfo.setTelnoDigest(DigestUtil.digest(conscustinfo.getTelno().trim()));
				conscustinfo.setTelnoMask(MaskUtil.maskMobile(conscustinfo.getTelno().trim()));
				conscustinfo.setTelnoCipher(encryptSingleFacade.encrypt(conscustinfo.getTelno().trim()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getAddr())){
				conscustinfo.setAddr(conscustinfo.getAddr());
				conscustinfo.setAddrDigest(DigestUtil.digest(conscustinfo.getAddr().trim()));
				conscustinfo.setAddrMask(MaskUtil.maskAddr(conscustinfo.getAddr().trim()));
				conscustinfo.setAddrCipher(encryptSingleFacade.encrypt(conscustinfo.getAddr().trim()).getCodecText());
			}
			if (StringUtils.isNotBlank(conscustinfo.getLinkemail())) {
				conscustinfo.setLinkemail(conscustinfo.getLinkemail().toLowerCase());
				conscustinfo.setLinkemailDigest(DigestUtil.digest(conscustinfo.getLinkemail().trim().toLowerCase()));
				conscustinfo.setLinkemailMask(MaskUtil.maskEmail(conscustinfo.getLinkemail().trim().toLowerCase()));
				conscustinfo.setLinkemailCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkemail().trim().toLowerCase()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getLinkmobile())){
				conscustinfo.setLinkmobile(conscustinfo.getLinkmobile());
				conscustinfo.setLinkmobileDigest(DigestUtil.digest(conscustinfo.getLinkmobile().trim()));
				conscustinfo.setLinkmobileMask(MaskUtil.maskMobile(conscustinfo.getLinkmobile().trim()));
				conscustinfo.setLinkmobileCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkmobile().trim()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getLinktel())){
				conscustinfo.setLinktel(conscustinfo.getLinktel());
				conscustinfo.setLinktelDigest(DigestUtil.digest(conscustinfo.getLinktel().trim()));
				conscustinfo.setLinktelMask(MaskUtil.maskMobile(conscustinfo.getLinktel().trim()));
				conscustinfo.setLinktelCipher(encryptSingleFacade.encrypt(conscustinfo.getLinktel().trim()).getCodecText());
			}
			
			if(StringUtils.isNotBlank(conscustinfo.getLinkaddr())){
				conscustinfo.setLinkaddr(conscustinfo.getLinkaddr());
				conscustinfo.setLinkaddrDigest(DigestUtil.digest(conscustinfo.getLinkaddr().trim()));
				conscustinfo.setLinkaddrMask(MaskUtil.maskAddr(conscustinfo.getLinkaddr().trim()));
				conscustinfo.setLinkaddrCipher(encryptSingleFacade.encrypt(conscustinfo.getLinkaddr().trim()).getCodecText());
			}
			if (StringUtils.isNotBlank(conscustinfo.getEmail2())) {
				conscustinfo.setEmail2(conscustinfo.getEmail2().toLowerCase());
				conscustinfo.setEmail2Digest(DigestUtil.digest(conscustinfo.getEmail2().trim().toLowerCase()));
				conscustinfo.setEmail2Mask(MaskUtil.maskEmail(conscustinfo.getEmail2().trim().toLowerCase()));
				conscustinfo.setEmail2Cipher(encryptSingleFacade.encrypt(conscustinfo.getEmail2().trim().toLowerCase()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getMobile2())){
				conscustinfo.setMobile2(conscustinfo.getMobile2());
				conscustinfo.setMobile2Digest(DigestUtil.digest(conscustinfo.getMobile2().trim()));
				conscustinfo.setMobile2Mask(MaskUtil.maskMobile(conscustinfo.getMobile2().trim()));
				conscustinfo.setMobile2Cipher(encryptSingleFacade.encrypt(conscustinfo.getMobile2().trim()).getCodecText());
			}
			if(StringUtils.isNotBlank(conscustinfo.getAddr2()) ){
				conscustinfo.setAddr2(conscustinfo.getAddr2());
				conscustinfo.setAddr2Digest(DigestUtil.digest(conscustinfo.getAddr2().trim()));
				conscustinfo.setAddr2Mask(MaskUtil.maskAddr(conscustinfo.getAddr2().trim()));
				conscustinfo.setAddr2Cipher(encryptSingleFacade.encrypt(conscustinfo.getAddr2().trim()).getCodecText());
			}
			//更新ic表
			cmDealCustnoFlagMapper.updateConscustIc(conscustinfo);
			//插入ic密文表
			cmDealCustnoFlagMapper.insertConscustIcCipher(conscustinfo);
		}
	}

}

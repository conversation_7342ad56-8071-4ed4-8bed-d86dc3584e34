package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;
import com.howbuy.crm.nt.sensitive.dto.SyncCpCustBankAcctInfo;

/**
 * <AUTHOR>
 * @Description: 客户银行账户信息脱敏数据处理
 * @reason:
 * @Date: 2021/6/2 10:30
 */
public interface SyncCpCustBankAcctInfoMapper {
    
	/**
	 * 获取需要脱敏的客户银行账户信息
	 * @return
	 */
    List<SyncCpCustBankAcctInfo> listSyncCpCustBankAcctInfoLimit200(Map<String,Object> map);
    
    /**
     * 更新脱敏客户银行账户信息
     * @param list
     * @return
     */
    int batchUpdateSyncCpCustBankAcctInfo(List<SyncCpCustBankAcctInfo> list);

}

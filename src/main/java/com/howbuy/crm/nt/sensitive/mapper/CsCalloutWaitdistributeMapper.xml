<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsCalloutWaitdistributeMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsCalloutWaitdistribute">
        <result column="WAITID" property="waitid" jdbcType="DECIMAL"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emailDigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailMask" jdbcType="VARCHAR"/>
        <result column="EMAIL_CIPHER" property="emailCipher" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobileDigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="MOBILE_CIPHER" property="mobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsCalloutWaitdistributeLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT WAITID,
		       MOBILE,
		       EMAIL
		 FROM CS_CALLOUT_WAITDISTRIBUTE 
		 WHERE (   ( MOBILE is not null and (MOBILE_DIGEST is null or MOBILE_MASK is null or MOBILE_CIPHER is null) )
		           OR ( EMAIL is not null and (EMAIL_DIGEST is null or EMAIL_MASK is null or EMAIL_CIPHER is null) )
		       )
		 <if test="startId !=null"> AND WAITID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND WAITID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY WAITDATE
    </select>
    
    
    <insert id="batchUpdateCsCalloutWaitdistribute" parameterType="com.howbuy.crm.nt.sensitive.dto.CsCalloutWaitdistribute">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_CALLOUT_WAITDISTRIBUTE 
        set 
           EMAIL_DIGEST = #{item.emailDigest,jdbcType=VARCHAR},EMAIL_MASK = #{item.emailMask,jdbcType=VARCHAR},EMAIL_CIPHER = #{item.emailCipher,jdbcType=VARCHAR},
           MOBILE_DIGEST = #{item.mobileDigest,jdbcType=VARCHAR},MOBILE_MASK = #{item.mobileMask,jdbcType=VARCHAR},MOBILE_CIPHER = #{item.mobileCipher,jdbcType=VARCHAR}
        where WAITID = #{item.waitid};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
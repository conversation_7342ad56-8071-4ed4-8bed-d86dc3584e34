package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsAutoDistributeLogMapper;
import com.howbuy.crm.nt.sensitive.dto.CsAutoDistributeLog;

/**
 * 刷自动分配日志脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsAutoDistributeLogService")
public class EncyCsAutoDistributeLogServiceImpl implements EncyCsAutoDistributeLogService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsAutoDistributeLogServiceImpl.class);

	@Autowired
	private CsAutoDistributeLogMapper csAutoDistributeLogMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyCsAutoDistributeLogData(String arg) {
		logger.info("处理自动分配日志脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<CsAutoDistributeLog> listCsAutoDistributeLog = null;
		//程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
		//循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询自动分配日志数据：每次查询两百条
				listCsAutoDistributeLog = csAutoDistributeLogMapper.listCsAutoDistributeLogLimit200(param);
				
				//处理脱敏三要素
				handleCsAutoDistributeLogList(listCsAutoDistributeLog);
				
				//更新自动分配日志表
				int count = this.updateCsAutoDistributeLogBatch(listCsAutoDistributeLog);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理自动分配日志脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理自动分配日志脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsAutoDistributeLogList(List<CsAutoDistributeLog> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsAutoDistributeLog csAutoDistributeLog : list){
				String mobile = csAutoDistributeLog.getCustMobile();
				if(StringUtils.isNotBlank(mobile)){
					csAutoDistributeLog.setCustMobileDigest(DigestUtil.digest(mobile.trim()));
					csAutoDistributeLog.setCustMobileMask(MaskUtil.maskMobile(mobile.trim()));
					mobileList.add(mobile.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("自动分配日志mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求自动分配日志手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求自动分配日志手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CsAutoDistributeLog csAutoDistributeLog : list){
	    				if(StringUtils.isNotBlank(csAutoDistributeLog.getCustMobile())){
	    					csAutoDistributeLog.setCustMobileCipher(mobilemap.get(csAutoDistributeLog.getCustMobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("自动分配日志mobileList加密结束时间"+System.currentTimeMillis());
			}
			
		}
		
		logger.info("保存自动分配日志脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入自动分配日志数据
	 * @param list
	 * @return
	 */
	private int updateCsAutoDistributeLogBatch(List<CsAutoDistributeLog> list){
		if(CollectionUtils.isNotEmpty(list)){
			csAutoDistributeLogMapper.batchUpdateCsAutoDistributeLog(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

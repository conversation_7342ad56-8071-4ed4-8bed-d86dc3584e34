package com.howbuy.crm.nt.sensitive.dao;


import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.sensitive.dto.CsLeavemsgCust;

/**
 * <AUTHOR>
 * @Description: 留言脱敏数据处理
 * @reason:
 * @Date: 2021/5/18 17:32
 */
public interface CsLeavemsgCustMapper {
    
	/**
	 * 获取需要脱敏的留言数据：200条
	 * @return
	 */
    List<CsLeavemsgCust> listCsLeavemsgCustLimit200(Map<String,Object> map);
    
    /**
     * 更新留言待分配数据
     * @param list
     * @return
     */
    int batchUpdateCsLeavemsgCust(List<CsLeavemsgCust> list);

}

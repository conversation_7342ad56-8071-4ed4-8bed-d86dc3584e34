package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.crm.nt.sensitive.dao.SyncCpCustBankAcctInfoMapper;
import com.howbuy.crm.nt.sensitive.dto.SyncCpCustBankAcctInfo;

/**
 * 刷客户银行账户脱敏数据
 * <AUTHOR>
 *
 */
@Service("encySyncCpCustBankAcctInfoService")
public class EncySyncCpCustBankAcctInfoServiceImpl implements EncySyncCpCustBankAcctInfoService {
	
	private Logger logger= LoggerFactory.getLogger(EncySyncCpCustBankAcctInfoServiceImpl.class);

	@Autowired
	private SyncCpCustBankAcctInfoMapper syncCpCustBankAcctInfoMapper; 
	
	@Override
	public void encySyncCpCustBankAcctInfoData(String arg) {
		logger.info("处理客户银行账户信息脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<SyncCpCustBankAcctInfo> listSyncCpCustBankAcctInfo = null;
        // 程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
        // 循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询客户银行账户信息：每次查询两百条
				listSyncCpCustBankAcctInfo = syncCpCustBankAcctInfoMapper.listSyncCpCustBankAcctInfoLimit200(param);
				
				//处理脱敏三要素
				handleSyncCpCustBankAcctInfoList(listSyncCpCustBankAcctInfo);
				
				//更新客户银行账户信息表
				int count = this.updateSyncCpCustBankAcctInfoBatch(listSyncCpCustBankAcctInfo);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理客户银行账户信息脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理客户银行账户信息脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleSyncCpCustBankAcctInfoList(List<SyncCpCustBankAcctInfo> list){
		if(CollectionUtils.isNotEmpty(list)){
			//获取掩码和摘要
			for(SyncCpCustBankAcctInfo syncCpCustBankAcctInfo : list){
				String bankacct = syncCpCustBankAcctInfo.getBankacct();
				if(StringUtils.isNotBlank(bankacct)){
					syncCpCustBankAcctInfo.setBankacctdigest(DigestUtil.digest(bankacct.trim()));
					syncCpCustBankAcctInfo.setBankacctmask(MaskUtil.maskBankAcct(bankacct.trim()));
				}
			}
		}

	}
	
	/**
	 * 插入客户银行账户信息
	 * @param list
	 * @return
	 */
	private int updateSyncCpCustBankAcctInfoBatch(List<SyncCpCustBankAcctInfo> list){
		if(CollectionUtils.isNotEmpty(list)){
			syncCpCustBankAcctInfoMapper.batchUpdateSyncCpCustBankAcctInfo(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

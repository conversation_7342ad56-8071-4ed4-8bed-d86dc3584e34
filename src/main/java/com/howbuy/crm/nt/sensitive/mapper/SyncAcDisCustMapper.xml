<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.SyncAcDisCustMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.SyncAcDisCust">
        <result column="DIS_CUST_ID" property="discustid" jdbcType="VARCHAR"/>
        <result column="ATTR" property="attr" jdbcType="VARCHAR"/>
        <result column="TEL_NO" property="telno" jdbcType="VARCHAR"/>
        <result column="LINK_ID_NO" property="linkidno" jdbcType="VARCHAR"/>
        <result column="LINK_TEL" property="linktel" jdbcType="VARCHAR"/>
        <result column="EMAIL" property="email" jdbcType="VARCHAR"/>
        <result column="HOME_TEL_NO" property="hometelno" jdbcType="VARCHAR"/>
        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"/>
        
        <result column="ADDR_MASK" property="addrmask" jdbcType="VARCHAR"/>
        <result column="TEL_NO_MASK" property="telnomask" jdbcType="VARCHAR"/>
        <result column="LINK_ID_NO_MASK" property="linkidnomask" jdbcType="VARCHAR"/>
        <result column="LINK_TEL_MASK" property="linktelmask" jdbcType="VARCHAR"/>
        <result column="EMAIL_MASK" property="emailmask" jdbcType="VARCHAR"/>
        <result column="HOME_TEL_NO_MASK" property="hometelnomask" jdbcType="VARCHAR"/>
        <result column="MOBILE_MASK" property="mobilemask" jdbcType="VARCHAR"/>
        
        <result column="ADDR_DIGEST" property="addrdigest" jdbcType="VARCHAR"/>
        <result column="TEL_NO_DIGEST" property="telnodigest" jdbcType="VARCHAR"/>
        <result column="LINK_ID_NO_DIGEST" property="linkidnodigest" jdbcType="VARCHAR"/>
        <result column="LINK_TEL_DIGEST" property="linkteldigest" jdbcType="VARCHAR"/>
        <result column="EMAIL_DIGEST" property="emaildigest" jdbcType="VARCHAR"/>
        <result column="HOME_TEL_NO_DIGEST" property="hometelnodigest" jdbcType="VARCHAR"/>
        <result column="MOBILE_DIGEST" property="mobiledigest" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listSyncAcDisCustLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT DIS_CUST_ID,
		       ADDR,
		       TEL_NO,
		       LINK_ID_NO,
		       LINK_TEL,
		       EMAIL,
		       HOME_TEL_NO,
		       MOBILE
		 FROM SYNC_AC_DIS_CUST 
		 WHERE ( (ADDR is not null AND (ADDR_MASK is null or ADDR_DIGEST is null) )
				 OR ( TEL_NO is not null AND (TEL_NO_MASK is null or TEL_NO_DIGEST is null) )
				 OR ( LINK_ID_NO is not null AND (LINK_ID_NO_MASK is null or LINK_ID_NO_DIGEST is null) )
				 OR ( LINK_TEL is not null AND (LINK_TEL_MASK is null or LINK_TEL_DIGEST is null) )
				 OR ( EMAIL is not null AND (EMAIL_MASK is null or EMAIL_DIGEST is null) )
				 OR ( HOME_TEL_NO is not null AND (HOME_TEL_NO_MASK is null or HOME_TEL_NO_DIGEST is null) )
				 OR ( MOBILE is not null AND (MOBILE_MASK is null or MOBILE_DIGEST is null) )
		       )
		 <if test="startId !=null"> AND DIS_CUST_ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND DIS_CUST_ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY DIS_CUST_ID
    </select>
    
    
    <insert id="batchUpdateSyncAcDisCust" parameterType="com.howbuy.crm.nt.sensitive.dto.SyncAcDisCust">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update SYNC_AC_DIS_CUST
        set 
           ADDR_MASK = #{item.addrmask,jdbcType=VARCHAR},ADDR_DIGEST = #{item.addrdigest,jdbcType=VARCHAR},
           TEL_NO_MASK = #{item.telnomask,jdbcType=VARCHAR},TEL_NO_DIGEST = #{item.telnodigest,jdbcType=VARCHAR},
           LINK_ID_NO_MASK = #{item.linkidnomask,jdbcType=VARCHAR},LINK_ID_NO_DIGEST = #{item.linkidnodigest,jdbcType=VARCHAR},
           LINK_TEL_MASK = #{item.linktelmask,jdbcType=VARCHAR},LINK_TEL_DIGEST = #{item.linkteldigest,jdbcType=VARCHAR},
           EMAIL_MASK = #{item.emailmask,jdbcType=VARCHAR},EMAIL_DIGEST = #{item.emaildigest,jdbcType=VARCHAR},
           HOME_TEL_NO_MASK = #{item.hometelnomask,jdbcType=VARCHAR},HOME_TEL_NO_DIGEST = #{item.hometelnodigest,jdbcType=VARCHAR},
           MOBILE_MASK = #{item.mobilemask,jdbcType=VARCHAR},MOBILE_DIGEST = #{item.mobiledigest,jdbcType=VARCHAR}
        where DIS_CUST_ID = #{item.discustid};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
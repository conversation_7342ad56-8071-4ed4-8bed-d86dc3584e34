package com.howbuy.crm.nt.sensitive.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.encrypt.EncryptBatchFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.nt.sensitive.dao.CsAssignedCustConfigLogMapper;
import com.howbuy.crm.nt.sensitive.dto.CsAssignedCustConfigLog;

/**
 * 刷待分配客户配置日志脱敏数据
 * <AUTHOR>
 *
 */
@Service("encyCsAssignedCustConfigLogService")
public class EncyCsAssignedCustConfigLogServiceImpl implements EncyCsAssignedCustConfigLogService {
	
	private Logger logger= LoggerFactory.getLogger(EncyCsAssignedCustConfigLogServiceImpl.class);

	@Autowired
	private CsAssignedCustConfigLogMapper csAssignedCustConfigLogMapper; 
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private EncryptBatchFacade encryptBatchFacade;
	
	@Override
	public void encyCsAssignedCustConfigLogData(String arg) {
		logger.info("处理待分配客户配置日志脱敏任务开始,接收参数：" + arg);
		JSONObject taskJson = JSON.parseObject(arg);
        JSONObject taskParam = taskJson.getJSONObject("taskParam");
        String startId = taskParam == null ? null : (String) taskParam.get("startId");
        String endId = taskParam == null ? null : (String) taskParam.get("endId");

		List<CsAssignedCustConfigLog> listCsAssignedCustConfigLog = null;
		//程序执行错误次数：超过五次，退出循环，终止调度
		int errorCount = 0;
		//循环状态
		boolean flag = true;
		Map<String,Object> param = new HashMap<String,Object>(2);
		param.put("startId", StringUtils.isNotBlank(startId) ? new BigDecimal(startId) : null);
		param.put("endId", StringUtils.isNotBlank(endId) ? new BigDecimal(endId) : null);
		
		while(flag && errorCount < CrmNtConstant.ENCY_ERROR_MAX_TRY_TIMES){
			try {
				//查询待分配客户配置日志数据：每次查询两百条
				listCsAssignedCustConfigLog = csAssignedCustConfigLogMapper.listCsAssignedCustConfigLogLimit200(param);
				
				//处理脱敏三要素
				handleCsAssignedCustConfigLogList(listCsAssignedCustConfigLog);
				
				//更新待分配客户配置日志表
				int count = this.updateCsAssignedCustConfigLogBatch(listCsAssignedCustConfigLog);
				
				if(count<200){
					flag = false;
				}
			}catch (Exception e) {
				errorCount++;
				logger.error("======处理待分配客户配置日志脱敏数据时出现异常，错误信息："  + e.getMessage(),e);
	            if(errorCount >= 5){
	            	throw e;
	            }
			}
		}
		
		logger.info("处理待分配客户配置日志脱敏任务结束......");
	}
	

	/**
	 * 处理三要素：摘要，密文，掩码
	 * @param 
	 * @return
	 */
	private void handleCsAssignedCustConfigLogList(List<CsAssignedCustConfigLog> list){
		if(CollectionUtils.isNotEmpty(list)){
			List<String> mobileList = new ArrayList<String>(200);
			List<String> idnoList = new ArrayList<String>(200);
			
			//获取掩码和摘要
			for(CsAssignedCustConfigLog csAssignedCustConfigLog : list){
				String custmobile = csAssignedCustConfigLog.getCustmobile();
				String idno = csAssignedCustConfigLog.getIdno();
				if(StringUtils.isNotBlank(custmobile)){
					csAssignedCustConfigLog.setCustmobileDigest(DigestUtil.digest(custmobile.trim()));
					csAssignedCustConfigLog.setCustmobileMask(MaskUtil.maskMobile(custmobile.trim()));
					mobileList.add(custmobile.trim());
				}
				
				if(StringUtils.isNotBlank(idno)){
					csAssignedCustConfigLog.setIdnoDigest(DigestUtil.digest(idno.trim()));
					csAssignedCustConfigLog.setIdnoMask(MaskUtil.maskIdNo(idno.trim()));
					idnoList.add(idno.trim());
				}
			}
			
			//获取密文
			if(CollectionUtils.isNotEmpty(mobileList)){
				logger.info("待分配客户配置日志mobileList条数:"+mobileList.size()+";加密开始时间"+System.currentTimeMillis());
				logger.info("请求待分配客户配置日志手机加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resMobile = encryptBatchFacade.encryptBatch(mobileList);
	    		logger.info("请求待分配客户配置日志手机加密返回："+JSON.toJSONString(resMobile));
	    		if(resMobile != null && resMobile.getCodecMap() != null){
	    			Map<String,String> mobilemap = resMobile.getCodecMap();
	    			for(CsAssignedCustConfigLog csAssignedCustConfigLog : list){
	    				if(StringUtils.isNotBlank(csAssignedCustConfigLog.getCustmobile())){
	    					csAssignedCustConfigLog.setCustmobileCipher(mobilemap.get(csAssignedCustConfigLog.getCustmobile().trim()));
	    				}
	    			}
	    		}
	    		logger.info("待分配客户配置日志mobileList加密结束时间"+System.currentTimeMillis());
			}
			
			
			if(idnoList != null && idnoList.size() > 0){
    			logger.info("预约idnoList条数:"+idnoList.size()+";加密开始时间"+System.currentTimeMillis());
    			logger.info("请求待分配客户配置日志证件加密参数："+JSON.toJSONString(mobileList));
	    		CodecBatchResponse resemail = encryptBatchFacade.encryptBatch(idnoList);
	    		logger.info("请求待分配客户配置日志证件加密返回："+JSON.toJSONString(resemail));
	    		if(resemail != null && resemail.getCodecMap() != null){
	    			Map<String,String> idnomap = resemail.getCodecMap();
	    			for(CsAssignedCustConfigLog csAssignedCustConfigLog : list){
	    				if(StringUtils.isNotBlank(csAssignedCustConfigLog.getIdno())){
	    					csAssignedCustConfigLog.setIdnoCipher(idnomap.get(csAssignedCustConfigLog.getIdno().trim()));
	    				}
	    			}
	    		}
	    		logger.info("待分配客户配置日志idnoList加密结束时间"+System.currentTimeMillis());
    		}
		}
		logger.info("保存待分配客户配置日志脱敏数据："+JSON.toJSONString(list));

	}
	
	/**
	 * 插入待分配客户配置日志数据
	 * @param list
	 * @return
	 */
	private int updateCsAssignedCustConfigLogBatch(List<CsAssignedCustConfigLog> list){
		if(CollectionUtils.isNotEmpty(list)){
			csAssignedCustConfigLogMapper.batchUpdateCsAssignedCustConfigLog(list);
		}
		
		return CollectionUtils.isNotEmpty(list) ? list.size() : 0;
	}

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.sensitive.dao.CsAssignedCustConfigMsgMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.sensitive.dto.CsAssignedCustConfigMsg">
        <result column="ID" property="id" jdbcType="DECIMAL"/>
        <result column="CUSTMOBILE" property="custmobile" jdbcType="VARCHAR"/>
        <result column="IDNO" property="idno" jdbcType="VARCHAR"/>
        <result column="IDNOMASK" property="idnoMask" jdbcType="VARCHAR"/>
        <result column="IDNODIGEST" property="idnoDigest" jdbcType="VARCHAR"/>
        <result column="IDNOCIPHER" property="idnoCipher" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILEMASK" property="custmobileMask" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILEDIGEST" property="custmobileDigest" jdbcType="VARCHAR"/>
        <result column="CUSTMOBILECIPHER" property="custmobileCipher" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listCsAssignedCustConfigMsgLimit200"  parameterType="map"  resultMap="BaseResultMap"  useCache="false">
		 SELECT ID,
		       CUSTMOBILE,
		       IDNO
		 FROM CS_ASSIGNED_CUST_CONFIG_MSG 
		 WHERE (   ( CUSTMOBILE is not null AND (CUSTMOBILEMASK is null or CUSTMOBILEDIGEST is null or CUSTMOBILECIPHER is null) )
		           OR ( IDNO is not null AND (IDNOMASK is null or IDNODIGEST is null or IDNOCIPHER is null) )
		       )
		 <if test="startId !=null"> AND ID &gt;= #{startId} </if>
		 <if test="endId !=null"> AND ID &lt;= #{endId} </if>
		 AND ROWNUM &lt;= 200 
		 ORDER BY ID
    </select>
    
    
    <insert id="batchUpdateCsAssignedCustConfigMsg" parameterType="com.howbuy.crm.nt.sensitive.dto.CsAssignedCustConfigMsg">
        DECLARE
        begin
      <foreach collection="list" item="item">
        update CS_ASSIGNED_CUST_CONFIG_MSG 
        set 
           IDNOMASK = #{item.idnoMask,jdbcType=VARCHAR},IDNODIGEST = #{item.idnoDigest,jdbcType=VARCHAR},IDNOCIPHER = #{item.idnoCipher,jdbcType=VARCHAR},
           CUSTMOBILEMASK = #{item.custmobileMask,jdbcType=VARCHAR},CUSTMOBILEDIGEST = #{item.custmobileDigest,jdbcType=VARCHAR},CUSTMOBILECIPHER = #{item.custmobileCipher,jdbcType=VARCHAR}
        where ID = #{item.id};
      </foreach>
        commit;
        END;
    </insert>
</mapper>
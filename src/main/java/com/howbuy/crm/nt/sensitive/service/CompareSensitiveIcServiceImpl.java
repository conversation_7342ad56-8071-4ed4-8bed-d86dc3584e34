package com.howbuy.crm.nt.sensitive.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.nt.sensitive.dao.CmDealCustnoFlagMapper;
import com.howbuy.crm.nt.sensitive.dto.ConscustSensitiveInfo;

import crm.howbuy.base.utils.StringUtil;

/**
 * 刷投顾客户脱敏数据
 * <AUTHOR>
 *
 */
@Service("CompareSensitiveIcService")
public class CompareSensitiveIcServiceImpl implements CompareSensitiveIcService {
	private Logger logger= LoggerFactory.getLogger(CompareSensitiveIcServiceImpl.class);
	
	@Autowired
	private CmDealCustnoFlagMapper cmDealCustnoFlagMapper; 
	
	@Override
	public void compareSensitiveIc(String arg) {
			List<ConscustSensitiveInfo> list = cmDealCustnoFlagMapper.listConscustIcAllInfo(null);
			for(ConscustSensitiveInfo info : list){
				if(StringUtil.isNotNullStr(info.getIdno()) && !DigestUtil.digest(info.getIdno().trim()).equals(info.getIdnoDigest())){
					inserterror(info.getConscustno(),"idno");
				}
				if(StringUtil.isNotNullStr(info.getMobile()) && !DigestUtil.digest(info.getMobile().trim()).equals(info.getMobileDigest())){
					inserterror(info.getConscustno(),"mobile");
				}
				if(StringUtil.isNotNullStr(info.getEmail()) && !DigestUtil.digest(info.getEmail().toLowerCase().trim()).equals(info.getEmailDigest())){
					inserterror(info.getConscustno(),"email");
				}
				if(StringUtil.isNotNullStr(info.getTelno()) && !DigestUtil.digest(info.getTelno().trim()).equals(info.getTelnoDigest())){
					inserterror(info.getConscustno(),"telno");
				}
				if(StringUtil.isNotNullStr(info.getAddr()) && !DigestUtil.digest(info.getAddr().trim()).equals(info.getAddrDigest())){
					inserterror(info.getConscustno(),"addr");
				}
				if(StringUtil.isNotNullStr(info.getMobile2()) && !DigestUtil.digest(info.getMobile2().trim()).equals(info.getMobile2Digest())){
					inserterror(info.getConscustno(),"mobile2");
				}
				if(StringUtil.isNotNullStr(info.getEmail2()) && !DigestUtil.digest(info.getEmail2().toLowerCase().trim()).equals(info.getEmail2Digest())){
					inserterror(info.getConscustno(),"email2");
				}
				if(StringUtil.isNotNullStr(info.getAddr2()) && !DigestUtil.digest(info.getAddr2().trim()).equals(info.getAddr2Digest())){
					inserterror(info.getConscustno(),"addr2");
				}
				if(StringUtil.isNotNullStr(info.getLinkmobile()) && !DigestUtil.digest(info.getLinkmobile().trim()).equals(info.getLinkmobileDigest())){
					inserterror(info.getConscustno(),"linkmobile");
				}
				if(StringUtil.isNotNullStr(info.getLinktel()) && !DigestUtil.digest(info.getLinktel().trim()).equals(info.getLinktelDigest())){
					inserterror(info.getConscustno(),"linktel");
				}
				if(StringUtil.isNotNullStr(info.getLinkemail()) && !DigestUtil.digest(info.getLinkemail().toLowerCase().trim()).equals(info.getLinkemailDigest())){
					inserterror(info.getConscustno(),"linkemail");
				}
				if(StringUtil.isNotNullStr(info.getLinkaddr()) && !DigestUtil.digest(info.getLinkaddr().trim()).equals(info.getLinkaddrDigest())){
					inserterror(info.getConscustno(),"linkaddr");
				}
			}
			logger.info("ic对比执行完成！");
	}
	
	private void inserterror(String conscustno,String des){
		Map<String, String> errorparam = new HashMap<String,String>(2);
		errorparam.put("conscustno", conscustno);
		errorparam.put("remark", des);
		cmDealCustnoFlagMapper.insertCompareSensitiveLog(errorparam);
	}
}

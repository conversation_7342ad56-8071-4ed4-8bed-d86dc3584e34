package com.howbuy.crm.nt.pushmsg.dao;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import org.apache.ibatis.annotations.Param;


/**
 * 
 * <AUTHOR>
 *
 */
public interface CmPushMsgDao {

	/**
	 * 根据业务id查询业务模板相关的内容
	 * @param param
	 * @return
	 */
	Map<String,String> getBusinessTemplateByBusinessId(Map<String,String> param);
	
	/**
	 * 根据业务id查询业务模板相关的参数
	 * @param param
	 * @return
	 */
	List<Map<String,String>> getBusinessTemplateParamByBusinessId(Map<String,String> param);
	
	/**
	 * 根据用户和消息类型查询是否在黑名单中
	 * @param param
	 * @return
	 */
	int getBlackCountByParam(Map<String,String> param);
	
	/**
	 * 新增数据对象
	 * @param cmPushMsg
	 */
	void insertCmPushMsg(CmPushMsg cmPushMsg);
	
	/**
	 * 根据一账通号查询投顾代码和投顾所属部门
	 * @param param
	 * @return
	 */
	Map<String,String> getConsCodeAndOrgCodeByHboneno(Map<String,String> param);
	
	/**
	 * 根据投顾号查询投顾所属部门
	 * @param param
	 * @return
	 */
	Map<String,String> getOrgCodeByConscode(Map<String,String> param);
	
	/**
	 * 根据投顾号查询投顾所属部门
	 * @param param
	 * @return
	 */
	List<Map<String,String>> getConsCodeAndOrgCodeByRoleCode(Map<String,String> param);

	/**
	 * 根据投顾号取投顾微信号
	 * @param conscode
	 * @return
	 */
    String getWechatconscodeByConscode(String conscode);


	/**
	 * 功能描述: <br>
	 * <根据conscode查询>附带授权
	 * [该投顾所在部门下|授权部门 ]拥有 [roleCodeList]角色的用户
	 * @Param: conscode
	 * @param  roleCodeList
	 * @Return: List<String>
	 */
	List<String> getSalesAssistantWithAuth(@Param("conscode") String conscode, @Param("roleCodeList") List<String> roleCodeList);

	/**
	 * 功能描述: <br>
	 * <根据conscode查询>附带授权
	 * 获取[该投顾所在父级部门下|授权部门为父级部门 ] 拥有[roleCodeList]角色的用户
	 * @Param: conscode
	 * @param  roleCodeList
	 * @Return: List<String>
	 */
	List<String> getSalesAssistantByPcodeWithAuth(@Param("conscode") String conscode,@Param("roleCodeList") List<String> roleCodeList);

}

package com.howbuy.crm.nt.pushmsg.buss;

import com.google.common.collect.Maps;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex;
import com.howbuy.crm.nt.pushmsg.response.PushMsgAnnexResponse;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmPushMsgByConsCodeBuss.java
 * @Description TODO
 * @createTime 2022年03月01日 14:37:00
 */
@Component
@Slf4j
public class CmPushMsgByConsCodeBuss {

    @Autowired
    private CommonDao commondao;

    @Autowired
    private CmPushMsgDao cmPushMsgDao;

    @Autowired
    private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;

    public void pushMsgByConsCode(String conscode,String msgcontent){

        String pushMsgId = commondao.getSeqValue("SEQ_PUSH_MSG");
        CmPushMsg pushmsg = new CmPushMsg();
        pushmsg.setPushid(pushMsgId);
        pushmsg.setMsgtype("12");
        pushmsg.setMsgcontent(msgcontent);
        pushmsg.setConscode(conscode);
        pushmsg.setOrgcode(getOutletCodeByConsCode(conscode));
        pushmsg.setCreator("xxzx");
        pushmsg.setExpectpushdt(new Date());
        // 发送状态：待发送
        pushmsg.setPushflag(StaticVar.MSG_PUSHFLAG_TOBESEND);
        // 消息形式：人工
        pushmsg.setMsgstyle(StaticVar.PUSH_MSG_PERSON);
        // 消息通道：PC端
        pushmsg.setMsgchannel(StaticVar.PUSH_MSG_CHANNEL_PC);
        pushmsg.setRemark("中台推送");

        cmPushMsgDao.insertCmPushMsg(pushmsg);
    }

    /**
     * g根据投顾查询outletCode
     * @param conscode
     * @return
     */
    private String getOutletCodeByConsCode(String conscode){
        Map<String,String> paramMap= Maps.newHashMap();
        paramMap.put("conscode", conscode);
        //TODO: mapper乱定义。参数/返回 不要Map.
        Map<String,String> maporgcode = cmPushMsgDao.getOrgCodeByConscode(paramMap);
        if(maporgcode != null) {
            return maporgcode.get("OUTLETCODE");
        }
        return  null;
    }

    public PushMsgAnnexResponse queryPushMsgAnnexList(String pushid){

        PushMsgAnnexResponse response = new PushMsgAnnexResponse();
        List<CmPushMsgAnnex> newlist = new ArrayList<>();
        CmPushMsgAnnex annextitle = cmRemindMsgDaoBuss.getCmPushMsgAnnexTitle(pushid);
        List<CmPushMsgAnnex> annexlist = cmRemindMsgDaoBuss.listPushMsgAnnex(pushid);

        newlist.add(annextitle);
        newlist.addAll(annexlist);

        response.setCmPushMsgAnnexList(newlist);
        response.success();
        return response;
    }

}

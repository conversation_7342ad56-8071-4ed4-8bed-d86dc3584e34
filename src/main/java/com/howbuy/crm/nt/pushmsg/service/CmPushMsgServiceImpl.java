package com.howbuy.crm.nt.pushmsg.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.howbuy.cc.message.request.SendCompanyCommonRequest;
import com.howbuy.cc.message.send.company.CompanySendService;
import com.howbuy.cc.message.send.company.CompanyTaskRequest;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.hkcust.dto.CmHkConscust;
import com.howbuy.crm.hkcust.service.CmHkConscustService;
import com.howbuy.crm.nt.base.model.SpecialRoleEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.nt.consultant.dto.CmConsInfoDomain;
import com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.GroupMsgLinkInfo;
import com.howbuy.crm.nt.pushmsg.dto.GroupMsgPushResp;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.util.CrmNtConstant;
import com.howbuy.crm.util.MainLogUtils;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Service("CmPushMsgService")
public class CmPushMsgServiceImpl implements CmPushMsgService {
	
	@Autowired
	private CmPushMsgDao cmPushMsgDao;
	
	@Autowired
	private CommonDao commonDao;

	/**
	 * 企业微信  /send的  path地址
	 */
	@Value("${wechathttpurl}")
	private String wechathttpurl;


	/**
	 * 企业微信   domain地址
	 */
	@Value("${WECHAT_URL}")
	private String WECHAT_URL;


	@Autowired
	private CmConsultantMapper consultantMapper;
	@Autowired
	private ConscustMapper conscustMapper;
	@Autowired
	private CompanySendService companySendService;
	@Autowired
	private CmHkConscustService cmHkConscustService;
	/**
	 * 秘钥
	 */
	@Value("${CRM_SECRET}")
	private String CRM_SECRET;
    @Value("${CUST_SECRET}")
    private String CUST_SECRET;

	//特殊处理  参数 属性
	private static String  CUST_NO="custNo";
	private static String  CUST_NAME="custName";

	/**
	 * @description 群发消息
	 * @param messageType
	 * @param title
	 * @param wechatConsCode
	 * @param custNos
	 * @param messageContent
	 * @return
	 * @author: jianjian.yang
	 * @date: 2023/10/11 14:51
	 * @since JDK 1.8
	 */
	@Override
	public void sendGroupMessage(String messageType, String title, String wechatConsCode, String url, List<String> custNos, String messageContent){
		CompanyTaskRequest companyTaskRequest = new CompanyTaskRequest();
		companyTaskRequest.setMessageType(Integer.valueOf(messageType));
		companyTaskRequest.setTitle(title);
		companyTaskRequest.setUrl(url);
		companyTaskRequest.setCreateUser(wechatConsCode);
		companyTaskRequest.setCustNos(custNos);
		companyTaskRequest.setMessageContent(messageContent);
		Long taskId = companySendService.createCompanyTask(companyTaskRequest);
		if(taskId != null){
			SendCompanyCommonRequest sendCompanyCommonRequest = new SendCompanyCommonRequest();
			sendCompanyCommonRequest.setTaskId(taskId);
			sendCompanyCommonRequest.setCustNos(custNos);
			sendCompanyCommonRequest.setTitle(title);
			sendCompanyCommonRequest.setUrl(url);
			sendCompanyCommonRequest.setMessageType(Integer.valueOf(messageType));
			sendCompanyCommonRequest.setConsCode(wechatConsCode);
			sendCompanyCommonRequest.setMessageContent(messageContent);
			if(CrmNtConstant.MESSAGE_TYPE_CARD.equals(messageType)) {
                sendCompanyCommonRequest.setSecret(CRM_SECRET);
            }
            if(CrmNtConstant.MESSAGE_TYPE_GROUP.equals(messageType)) {
                sendCompanyCommonRequest.setSecret(CUST_SECRET);
            }
			companySendService.sendCompanyCommonMsg(sendCompanyCommonRequest);
		}
	}

	/**
	 * 根据投顾号获取[该投顾所在部门下|授权部门 ]拥有ROLE_SIC_ASSISTANT角色的投顾，
	 * 如果没找到，则获取[该投顾所在父级部门下|授权部门为父级部门 ]拥有ROLE_SIC_ASSISTANT角色的投顾，
	 * @param consCode  投顾code  NOT NULL
	 * @return
	 */
	@Override
	@Deprecated
	public List<String> getSalesAssistantWithAuth(String consCode){
		return  getSalesAssistantWithAuth(consCode,SpecialRoleEnum.ROLE_SIC_ASSISTANT);
	}


	/**
	 * 根据投顾号获取[该投顾所在部门下|授权部门 ]拥有[参数角色]的投顾，
	 * 如果没找到，则获取[该投顾所在父级部门下|授权部门为父级部门 ]拥有[参数角色]的投顾，
	 * @param consCode  投顾code  NOT NULL
	 * @param specialRoleEnum
	 * @return
	 */
	@Override
	public List<String> getSalesAssistantWithAuth(String consCode,SpecialRoleEnum specialRoleEnum){
		Assert.notNull(consCode);
		Assert.notNull(specialRoleEnum);
		List<String> salesAssistantList = cmPushMsgDao.getSalesAssistantWithAuth(consCode,Lists.newArrayList(specialRoleEnum.getCode()));
		log.info("consCode:{}.[当前部门|授权部门]下包含角色[{}-{}]的投顾列表：{}",consCode,specialRoleEnum.getCode(),specialRoleEnum.getDescription(), JSON.toJSONString(salesAssistantList));

		//如果是角色：ROLE_SIC_ASSISTANT 查询为空，则查询 父级部门持有 [ROLE_SIC_ASSISTANT]角色的人员
		if (SpecialRoleEnum.ROLE_SIC_ASSISTANT.equals(specialRoleEnum)
		                   &&CollectionUtils.isEmpty(salesAssistantList)) {
			salesAssistantList = cmPushMsgDao.getSalesAssistantByPcodeWithAuth(consCode,Lists.newArrayList(SpecialRoleEnum.ROLE_SIC_ASSISTANT.getCode()));
			log.info("consCode:{}.[上级部门|授权部门]下包含角色[{}-{}]的投顾列表：{}",consCode,specialRoleEnum.getCode(),specialRoleEnum.getDescription(), JSON.toJSONString(salesAssistantList));
		}
		return salesAssistantList;
	}

	/**
	 *根据 consCode投顾列表  推送消息
	 *包含[specialRoleEnum]角色控制
	 * @param businessId
	 * @param consCode
	 * @param paramMap
	 * @return
	 */
	@Override
	public BaseResponse pushMsgByConsCodeListWithAuth(String businessId,
											   String consCode,
											   SpecialRoleEnum specialRoleEnum,
											   Map<String,String> paramMap){
		List<String> alertCodeList=getSalesAssistantWithAuth(consCode,specialRoleEnum);
		return  pushMsgByConsCodeList(businessId,alertCodeList,paramMap);
	}


	/**
	 * 根据角色ROLE_CODE 推送给角色下所有投顾   消息
	 * @param businessId NOT NULL
	 * @param roleCode NOT NULL
	 * @param paramMap NOT NULL
	 * @return
	 */
	@Override
	public BaseResponse pushMsgByRoleCode(String businessId, String roleCode, Map<String,String> paramMap){
		Assert.notNull(businessId);
		Assert.notNull(roleCode);
		Assert.notNull(paramMap);
		List<String> consCodeList = consultantMapper.getConsCodeListByRoleList(Lists.newArrayList(roleCode));

		if(!CollectionUtils.isEmpty(consCodeList)){
			return pushMsgByConsCodeList(businessId,consCodeList,paramMap);
		}
		BaseResponse res = new BaseResponse();
		res.success();
		return res;
	}


	/**
	 * @description:(根据 一账通号 )
	 * @param hboneNo
	 * @return java.lang.String
	 * @author: haoran.zhang
	 * @date: 2024/1/15 15:40
	 * @since JDK 1.8
	 */
	private String getConsCodeByHboneNo(String hboneNo){
		Assert.notNull(hboneNo);
		Map<String, Object> params = new HashMap<String, Object>(1);
		params.put("hboneNo", hboneNo);
		CmConsInfoDomain custInfo = consultantMapper.queryConsInfoByHboneno(params);
		return custInfo==null?null:custInfo.getConsCode();
	}

	@Override
	public BaseResponse pushMsgByHboneNo(String businessId, String hboneNo, Map<String,String> paramMap){
		Assert.notNull(hboneNo);
		String  consCode=getConsCodeByHboneNo(hboneNo);
		if(StringUtil.isNotNullStr(consCode)){
			return pushMsgByConsCodeList(businessId,Lists.newArrayList(consCode),paramMap);
		}else{
			log.info("发送业务消息id:{} ,一账通号：{}，未找到对应的投顾信息",businessId,hboneNo);
			BaseResponse res = new BaseResponse();
			res.processedFail("未找到对应的投顾信息!");
			return res;
		}
	}

	@Override
	public BaseResponse pushMsgByHkTxAcctNo(String businessId, String hkTxAcctNo, Map<String,String> paramMap){
		Assert.notNull(hkTxAcctNo);

		String conseCode = consultantMapper.queryConsCodeByHkTxAcctNo(hkTxAcctNo);
		if(StringUtil.isNotNullStr(conseCode)){
			return pushMsgByConsCodeList(businessId,Lists.newArrayList(conseCode),paramMap);
		}else{
			log.info("发送业务消息id:{} ,香港客户号：{}，未找到对应的投顾信息",businessId,hkTxAcctNo);
			BaseResponse res = new BaseResponse();
			res.processedFail("未找到对应的投顾信息!");
			return res;
		}
	}

	@Override
	public BaseResponse pushMsgByHkTxAcctNoWithCustInfo(String businessId, String hkTxAcctNo, Map<String,String> paramMap){
		//覆写 custNo  custName  参数 属性。取值 crm客户信息
		CmHkConscust hkConscust=cmHkConscustService.selectByHkTxAcctNo(hkTxAcctNo);
		if(hkConscust==null || StringUtil.isEmpty(hkConscust.getConscustno())){
			log.info("发送业务消息id:{} ,香港客户号：{}，未找到对应的客户",businessId,hkTxAcctNo);
			BaseResponse res = new BaseResponse();
			res.processedFail("未找到对应的客户信息!");
			return res;
		}
		//特殊处理。 覆写 参数中的 客户号 客户姓名
		String custNo=hkConscust.getConscustno();
		String custName=conscustMapper.getCustNameByCustNo(custNo);
		if(paramMap.containsKey(CUST_NO)){
			paramMap.put(CUST_NO,custNo);
		}
		if(paramMap.containsKey(CUST_NAME)){
			paramMap.put(CUST_NAME,custName);
		}
		return  pushMsgByHkTxAcctNo(businessId,hkTxAcctNo,paramMap);
	}

	/**
	 * @description:(根据 一账通号 和 香港客户号 发送消息，发送给对应的投顾 。如果一账通号和香港客户号对应的投顾是同一个人，则发送一次消息)
	 * @param businessId
	 * @param hkTxAcctNo
	 * @param hboneNo
	 * @param paramMap
	 * @return crm.howbuy.base.dubbo.response.BaseResponse
	 * @author: haoran.zhang
	 * @date: 2024/1/15 16:06
	 * @since JDK 1.8
	 */
	@Override
	public BaseResponse pushMsgByHkAndHboneNo(String businessId, String hkTxAcctNo, String hboneNo, Map<String,String> paramMap){
		BaseResponse finalRes = new BaseResponse();

		if(StringUtil.isEmpty(hboneNo) && StringUtil.isEmpty(hkTxAcctNo)){
			BaseResponse res = new BaseResponse();
			res.invalidReqParams("香港交易账号、一账通号不能同时为空！");
			return res;
		}
        //根据 一账通号  获取对应的投顾
		String  consCodeByHboneNo;
		//根据 香港客户号 获取对应的投顾
		String  consCodeByHk;
		//实际发送的投顾
		List<String> sendConsCodeList=Lists.newArrayList();
		//根据 一账通 决定是否发送
		if(StringUtil.isNotNullStr(hboneNo)){
			consCodeByHboneNo=getConsCodeByHboneNo(hboneNo);
			if(StringUtil.isNotNullStr(consCodeByHboneNo)){
				sendConsCodeList.add(consCodeByHboneNo);
				//发送给一账通号对应的投顾
				pushMsgByConsCodeList(businessId,Lists.newArrayList(consCodeByHboneNo),paramMap);
			}
		}
		if(StringUtil.isNotNullStr(hkTxAcctNo)){
			consCodeByHk = consultantMapper.queryConsCodeByHkTxAcctNo(hkTxAcctNo);
			//如果一账通号和香港客户号对应的投顾不是同一个人，则发送给香港客户号对应的投顾
			if(StringUtil.isNotNullStr(consCodeByHk) && !sendConsCodeList.contains(consCodeByHk)){
				sendConsCodeList.add(consCodeByHk);
				pushMsgByConsCodeList(businessId,Lists.newArrayList(consCodeByHk),paramMap);
			}
		}
		//如果一账通号和香港客户号对应的投顾都是同一个人，则只发送一次
		if(CollectionUtils.isEmpty(sendConsCodeList)){
			log.info("发送业务消息id:{} ,香港客户号：{}，一账通号：{}，未找到对应的投顾信息",businessId,hkTxAcctNo,hboneNo);
			finalRes.processedFail("未找到对应的投顾信息!");
		}else{
			finalRes.success();
		}
		return finalRes;

	}


	/**
	 * 根据角色ROLE_CODE 列表  推送给参数角色列表 下所有投顾   消息
	 * @param businessId NOT NULL 业务Id
	 * @param roleCodeList NOT Empty  角色列表
	 * @param paramMap NOT NULL
	 * @return
	 */
	@Override
	public BaseResponse pushMsgByRoleCodeList(String businessId, List<String> roleCodeList, Map<String,String> paramMap){
		Assert.notNull(businessId);
		Assert.notNull(roleCodeList);
		Assert.notNull(paramMap);

		List<String> consCodeList = consultantMapper.getConsCodeListByRoleList(roleCodeList);

		if(!CollectionUtils.isEmpty(consCodeList)){
			return pushMsgByConsCodeList(businessId,consCodeList,paramMap);
		}
		BaseResponse res = new BaseResponse();
		res.success();
		return res;
	}

	/**
	 * 根据角色ROLE_CODE 推送给角色下所有投顾   消息
	 * @param businessId  业务ID NOT NULL
	 * @param consCodeList  发送投顾的code列表 NOT NULL
	 * @param paramMap NOT NULL
	 * @return
	 */
	@Override
	public BaseResponse pushMsgByConsCodeList(String businessId, List<String>  consCodeList, Map<String,String> paramMap){
		BaseResponse res = new BaseResponse();
		Assert.notNull(businessId);
		Assert.notNull(consCodeList);
		Assert.notNull(paramMap);
		consCodeList.forEach(consCode->{
			CmPushMsgRequest request=new CmPushMsgRequest();
			// 测试：200162；产线：200042
			request.setBusinessId(businessId);
			//1、一账通号；2、投顾号
			request.setAccountType("2");
			request.setAccount(consCode); // 投顾号
			request.setParamJson(JSON.toJSONString(paramMap));
			BaseResponse response= pushMsg(request);
			log.info("(发送消息，业务Id:{},投顾code:{} ，发送结果：{}",businessId,consCode,JSON.toJSONString(response));
		});
		res.success();
		return res;
	}


	/**
	 * 推送卡片信息
	 * @param consCodeList  发送人
	 * @param title 标题
	 * @param msgContent 消息体
	 * @param url 跳转链接
	 * @return
	 */
	@Override
	public	BaseResponse pushCardMsgByConsCodeList(List<String>  consCodeList,String title,String msgContent,String url){
		BaseResponse res = new BaseResponse();
		consCodeList.stream().forEach(consCode->{
			postWechatMsg(getWebChatCode(consCode),title,msgContent,CrmNtConstant.TEMP_TEXT_CARD,url);
		});
		res.success();
		return res;
	}


	/**
	 * 推送文本信息
	 * @param consCodeList  发送人
	 * @param title 标题
	 * @param msgContent 消息体
	 * @return
	 */
	@Override
	public	BaseResponse pushTextMsgByConsCodeList(List<String>  consCodeList,String title,String msgContent){
		BaseResponse res = new BaseResponse();
		consCodeList.stream().forEach(consCode->{
			postWechatMsg(getWebChatCode(consCode),title,msgContent,CrmNtConstant.TEMP_TEXT_TEXT,null);
		});
		res.success();
		return res;
	}


	/**
	 * 推送--群发助手 消息 [附件为link类型 ]
	 * @param groupMsgLinkInfo
	 * @return
	 */
	@Override
	public NtReturnMessageDto<GroupMsgPushResp> pushGroupLinkMsgByConsCodeList(GroupMsgLinkInfo groupMsgLinkInfo){
           String fullUrl=String.join("",WECHAT_URL,"/wechatusermsg/addGroupLinkMsg");


		NtReturnMessageDto returnDto=NtReturnMessageDto.fail("");

		//consCode -->webChat code
		String wechatCode=getWebChatCode(groupMsgLinkInfo.getSender());
		groupMsgLinkInfo.setSender(wechatCode);

        try {
			ObjectMapper oMapper = new ObjectMapper();
			Map<String, Object> paramMap=oMapper.convertValue(groupMsgLinkInfo, Map.class);

			long startTime = System.currentTimeMillis();
			String responsejson = HttpUtils.jsonPost(fullUrl,paramMap);
			long endTime = System.currentTimeMillis();
			MainLogUtils.httpCallOut(fullUrl, String.valueOf(HttpStatus.OK.value()), endTime - startTime);
			log.info("http请求企业微信[群发助手]，url:{} , request:{}，response:{} ",
					fullUrl,paramMap.toString(),responsejson);
            if(StringUtil.isNotNullStr(responsejson)){
				GroupMsgPushResp resp=JSONObject.parseObject(responsejson,GroupMsgPushResp.class);
				if(resp.isSuccess()){
					returnDto.setReturnCode(NtReturnMessageDto.SUCCESS_CODE);
				}
				returnDto.setReturnObject(resp);

			}
		} catch (Exception e) {
//			e.printStackTrace();
			log.error("postWechatMsg：[群发助手] 报错！",e);
		}
		return  returnDto;
	}

	@Override
	public BaseResponse pushMsg(CmPushMsgRequest req) {
		BaseResponse res = new BaseResponse();
		String businessId = req.getBusinessId();
		String pushChannel = req.getPushChannel();
		String paramJson =  req.getParamJson();
		String account = req.getAccount();
		String accountType = req.getAccountType();
		log.info("req请求:"+ JSON.toJSONString(req));
		if(StringUtil.isNotNullStr(businessId) && StringUtil.isNotNullStr(account) && StringUtil.isNotNullStr(accountType)){
			Map<String,String> paramid = new HashMap<String,String>(1);
			paramid.put("businessId", businessId);
			Map<String,String> templatemap = cmPushMsgDao.getBusinessTemplateByBusinessId(paramid);
			if(templatemap != null){
				String text = templatemap.get("PC_TEMPLATE_TEXT");
				String msgTypeCode = templatemap.get("MSG_TYPE_CODE");
				String msgTypeId = templatemap.get("MSG_TYPE_ID");
				String wechattext = templatemap.get("WEIXIN_TEMPLATE_TEXT");
				String msgname = templatemap.get("MSG_TYPE_NAME");
				if(StringUtil.isNullStr(text)){
					pushSysError(businessId);
					res.invalidReqParams("模板内容为空！");
					return res;
				}

				//判断是不是在黑名单中
				if(!isInThenBlack(msgTypeId,account)){
					List<String> listChannel = new ArrayList<String>();
					if(StringUtil.isNotNullStr(pushChannel)){
						
						if(StaticVar.PUSH_MSG_CHANNEL_PC.equals(pushChannel)){
							listChannel.add(StaticVar.PUSH_MSG_CHANNEL_PC);
						}else if(StaticVar.PUSH_MSG_CHANNEL_WECHAT.equals(pushChannel)){
							listChannel.add(StaticVar.PUSH_MSG_CHANNEL_WECHAT);
						}
					}else{
						String isPcTemplate = templatemap.get("IS_PC_TEMPLATE");
						String isWeixinTemplate = templatemap.get("IS_WEIXIN_TEMPLATE");
						if(StaticVar.YES.equals(isPcTemplate)){
							listChannel.add(StaticVar.PUSH_MSG_CHANNEL_PC);
						}
						if(StaticVar.YES.equals(isWeixinTemplate)){
							listChannel.add(StaticVar.PUSH_MSG_CHANNEL_WECHAT);
						}
					}
					String conscode = "";
					String orgcode = "";
					Map<String,String> paramquery = new HashMap<String,String>(1);
					if(StaticVar.PUSH_MSG_ACCOUNT_CUST.equals(accountType)){
						paramquery.put("hboneno", account);
						Map<String,String> maphboneno = cmPushMsgDao.getConsCodeAndOrgCodeByHboneno(paramquery);
						if(maphboneno != null){
							conscode = maphboneno.get("CONSCODE");
							orgcode = maphboneno.get("OUTLETCODE");
						}else{
							pushSysError(businessId);
							res.invalidReqParams("一账通号不存在");
							return res;
						}
					}else{
						conscode = account;
						paramquery.put("conscode", account);
						Map<String,String> maporgcode = cmPushMsgDao.getOrgCodeByConscode(paramquery);
						if(maporgcode != null){
							String outletcode = maporgcode.get("OUTLETCODE");
							if(StringUtil.isNotNullStr(outletcode)){
								orgcode = maporgcode.get("OUTLETCODE");
							}else{
								pushSysError(businessId);
								res.invalidReqParams("投顾号不存在");
								return res;
							}
						}else{
							pushSysError(businessId);
							res.invalidReqParams("投顾号不存在");
							return res;
						}
					}
					for(String channel : listChannel){
						//如果是PC端
						if(StaticVar.PUSH_MSG_CHANNEL_PC.equals(channel)){
							
							//匹配text参数和传过来的传输
							Map<String,String> josnval = (Map<String,String>)JSON.parse(paramJson);
							log.info("传入参数:"+ JSON.toJSONString(josnval));
							String regex = "\\$(.*?)\\}";
					    	Pattern p = Pattern.compile(regex);
					    	Matcher m = p.matcher(text);
					    	Set<String> arrayParams = new HashSet<>(8);
					    	while(m.find()) {
					    		String par = StringUtil.replaceNull(m.group(1));
					    		if(par.startsWith("{") ){
					    			arrayParams.add(par.replaceFirst("[{]", ""));
					    		}
					    	}
							if(arrayParams.size() > 0 ){
								log.info("模板参数:"+ JSON.toJSONString(arrayParams));
								if(josnval != null && josnval.keySet().size() > 0){
									if(arrayParams.size() == josnval.keySet().size()){
										for(String mparam : arrayParams){
											if(StringUtil.isNotNullStr(josnval.get(mparam))){
												String oldstr = "\\$\\{"+mparam+"\\}";
									    		text = text.replaceAll(oldstr, josnval.get(mparam));
											}else{
												pushSysError(businessId);
												res.invalidReqParams("模板有参数，传参参数错误");
												return res;
											}
										}
									}else{
										pushSysError(businessId);
										res.invalidReqParams("模板参数和传入参数数量不匹配！");
										return res;
									}
								}else{
									pushSysError(businessId);
									res.invalidReqParams("模板有参数，传参未传入参数值");
									return res;
								}
							}
							String id = commonDao.getSeqValue("SEQ_PUSH_MSG");
							CmPushMsg pushmsg = new CmPushMsg();
							pushmsg.setPushid(id);
							pushmsg.setMsgtype(msgTypeCode);
							pushmsg.setMsgcontent(text);
							pushmsg.setConscode(conscode);
							pushmsg.setOrgcode(orgcode);
							pushmsg.setCreator("nt-sys");
							pushmsg.setExpectpushdt(new Date());
							pushmsg.setMsgstyle(StaticVar.PUSH_MSG_SYS);
							pushmsg.setMsgchannel(StaticVar.PUSH_MSG_CHANNEL_PC);
							cmPushMsgDao.insertCmPushMsg(pushmsg);
						}else if(StaticVar.PUSH_MSG_CHANNEL_WECHAT.equals(channel)){
							//TODO 微信端处理

							//匹配text参数和传过来的传输
							Map<String,String> josnval = (Map<String,String>)JSON.parse(paramJson);
							log.info("传入参数:"+ JSON.toJSONString(josnval));
							String regex = "\\$(.*?)\\}";
							Pattern p = Pattern.compile(regex);
							Matcher m = p.matcher(wechattext);
							Set<String> arrayParams = new HashSet<>(8);
							while(m.find()) {
								String par = StringUtil.replaceNull(m.group(1));
								if(par.startsWith("{") ){
									arrayParams.add(par.replaceFirst("[{]", ""));
								}
							}
							if(arrayParams.size() > 0 ){
								log.info("模板参数:"+ JSON.toJSONString(arrayParams));
								if(josnval != null && josnval.keySet().size() > 0){
									if(arrayParams.size() == josnval.keySet().size()){
										for(String mparam : arrayParams){
											if(StringUtil.isNotNullStr(josnval.get(mparam))){
												String oldstr = "\\$\\{"+mparam+"\\}";
												wechattext = wechattext.replaceAll(oldstr, josnval.get(mparam));
											}else{
												pushSysError(businessId);
												res.invalidReqParams("模板有参数，传参参数错误");
												return res;
											}
										}
									}else{
										pushSysError(businessId);
										res.invalidReqParams("模板参数和传入参数数量不匹配！");
										return res;
									}
								}else{
									pushSysError(businessId);
									res.invalidReqParams("模板有参数，传参未传入参数值");
									return res;
								}
							}

							//增加处理  投顾号   现在取企业微信号
							String wechatconscode = cmPushMsgDao.getWechatconscodeByConscode(conscode);
							String id = commonDao.getSeqValue("SEQ_PUSH_MSG");
							CmPushMsg pushmsg = new CmPushMsg();
							pushmsg.setPushid(id);
							pushmsg.setMsgtype(msgTypeCode);
							pushmsg.setMsgcontent(wechattext);
							pushmsg.setConscode(StringUtil.isNotNullStr(wechatconscode)? wechatconscode:conscode);
							pushmsg.setOrgcode(orgcode);
							pushmsg.setCreator("nt-sys");
							pushmsg.setExpectpushdt(new Date());
							pushmsg.setPushdt(new Date());
							pushmsg.setPushflag("1");
							pushmsg.setMsgstyle(StaticVar.PUSH_MSG_SYS);
							pushmsg.setMsgchannel(StaticVar.PUSH_MSG_CHANNEL_WECHAT);
							cmPushMsgDao.insertCmPushMsg(pushmsg);

							postWechatMsg(StringUtil.isNotNullStr(wechatconscode)? wechatconscode:conscode,msgname,wechattext,CrmNtConstant.TEMP_TEXT_TEXT,"#");
						}
					}
				}
			}else{
				pushSysError(businessId);
				res.invalidReqParams("模板id不存在");
				return res;
			}
			
		}else{
			pushSysError(businessId);
			res.invalidReqParams("参数传入不全");
			return res;
		}
		res.success();
		return res;
	}


	/**
	 * 根据 consCode --> 查找企业微信的code
	 * @param consCode
	 * @return
	 */
	private String getWebChatCode(String consCode){
	   String webChatCode=cmPushMsgDao.getWechatconscodeByConscode(consCode);
	   log.info("根据consCode:{} 获取 企业微信的code:{}",consCode,webChatCode);
       return StringUtil.isNotNullStr(webChatCode)? webChatCode:consCode;
	}

	/**
	 * 调用企业微信发送消息
	 * @param conscode  企业微信code [投顾consCode已处理过的发送code].
	 * @param title
	 * @param msg
	 * @param type
	 * @param url
	 */
	@Override
	public void postWechatMsg(String conscode,String title,String msg,String type,String url){

		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("type",type);
		paramMap.put("userid",conscode);
		paramMap.put("title","【"+title+"】");
		paramMap.put("msg",msg);
		paramMap.put("url",url);
		try {
			log.info("http请求企业微信发送消息，url:{} , request:{}",wechathttpurl,JSONObject.toJSONString(paramMap));
			long startTime = System.currentTimeMillis();
			String responsejson = HttpUtils.get2(wechathttpurl,paramMap);
			long endTime = System.currentTimeMillis();
			MainLogUtils.httpCallOut(wechathttpurl, String.valueOf(HttpStatus.OK.value()), endTime - startTime);
			log.info("http请求企业微信发送消息，url:{} , request:{}，response:{} ",wechathttpurl,JSONObject.toJSONString(paramMap),responsejson);
		} catch (Exception e) {
//			e.printStackTrace();
			log.error(String.format("postWechatMsg：%s 报错！", JSONObject.toJSONString(paramMap)),e);
		}
	}

	private boolean isInThenBlack(String msgTypeId,String account){
		Map<String,String> param = new HashMap<String,String>(2);
		param.put("userCode", account);
		param.put("msgTypeId", msgTypeId);
		int blackcount = cmPushMsgDao.getBlackCountByParam(param);
		log.info("黑名单查询结果：{}",blackcount);
		if(blackcount > 0){
			return true;
		}else{
			return false;
		}
	}
	
	private void pushSysError(String bussinessid){
		Map<String,String> paramrole=new HashMap<String,String>(1);
		paramrole.put("rolecode", StaticVar.ROLE_SUPERVISOR);
		List<Map<String,String>> listuser = cmPushMsgDao.getConsCodeAndOrgCodeByRoleCode(paramrole);
		for(Map<String,String> user : listuser){
			String id = commonDao.getSeqValue("SEQ_PUSH_MSG");
			CmPushMsg pushmsg = new CmPushMsg();
			pushmsg.setPushid(id);
			pushmsg.setMsgtype(StaticVar.MSG_TYPE_OTHER);
			pushmsg.setMsgcontent("外部系统调用业务ID："+bussinessid+" 失败");
			pushmsg.setConscode(StringUtil.replaceNullStr(user.get("CONSCODE")));
			pushmsg.setOrgcode(StringUtil.replaceNullStr(user.get("OUTLETCODE")));
			pushmsg.setCreator("nt-sys");
			pushmsg.setExpectpushdt(new Date());
			pushmsg.setMsgstyle(StaticVar.PUSH_MSG_SYS);
			pushmsg.setMsgchannel(StaticVar.PUSH_MSG_CHANNEL_PC);
			cmPushMsgDao.insertCmPushMsg(pushmsg);
		}
	}

}

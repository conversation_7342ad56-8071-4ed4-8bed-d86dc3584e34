<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao">

    
    
    <select id="getBusinessTemplateByBusinessId" parameterType="Map" resultType="Map" useCache="false">
		SELECT T.BUSINESS_ID,
			T.MSG_TYPE_ID,
			T1.MSG_TYPE_CODE,
			T1.MSG_TYPE_NAME,
			T.PC_TEMPLATE_TEXT,
			T.IS_PC_TEMPLATE,
			dbms_lob.substr(T.WEIXIN_START_TEXT || ' ' || (SELECT WM_CONCAT(Concat(Concat(A.param_name, ':'), A.param_value)) AS paramvalue
			FROM CM_MSG_BUSINESS_TEMPLATE_PARAM A
			WHERE A.BUSINESS_ID = T.BUSINESS_ID) || ' ' || T.WEIXIN_END_TEXT) AS WEIXIN_TEMPLATE_TEXT,
			T.IS_WEIXIN_TEMPLATE
		FROM CM_MSG_BUSINESS_TEMPLATE T
		LEFT JOIN CM_MSG_TYPE T1
		ON T.MSG_TYPE_ID = T1.ID
		WHERE T.IS_DEL = '0'
		AND T.BUSINESS_ID = #{businessId}
    </select>
    
    <select id="getBusinessTemplateParamByBusinessId" parameterType="Map" resultType="Map" useCache="false">
    	SELECT T.PARAM_NAME, T.PARAM_VALUE FROM CM_MSG_BUSINESS_TEMPLATE_PARAM t WHERE t.business_id = #{businessId}
    </select>
    
    <select id="getBlackCountByParam" parameterType="Map" resultType="int" useCache="false">
    	SELECT COUNT(*)
		  FROM CM_MSG_BLACK T
		 WHERE T.USER_CODE = #{userCode}
		   AND T.MSG_TYPE_ID = #{msgTypeId}
    </select>
    
    <insert id="insertCmPushMsg" parameterType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsg">
		INSERT INTO CM_PUSH_MSG (
		<trim suffix="" suffixOverrides=",">
			<if test="pushid != null">pushid,</if>
			<if test="title != null">title,</if>
			<if test="msgtype != null">msgtype,</if>
			<if test="msgcontent != null">msgcontent,</if>
			<if test="conscode != null">conscode,</if>
			<if test="orgcode != null">orgcode,</if>
			<if test="creator != null">creator,</if>
			<if test="credt != null">credt,</if>
			<if test="modifier != null">modifier,</if>
			<if test="moddt != null">moddt,</if>
			<if test="pushflag != null">pushflag,</if>
			<if test="pushdt != null">pushdt,</if>
			<if test="expectpushdt != null">expectpushdt,</if>
			<if test="msgstyle != null">msgstyle,</if>
			<if test="msgchannel != null">msgchannel,</if>
			<if test="remark != null">remark,</if>
		</trim>
		) VALUES (
		<trim suffix="" suffixOverrides=",">
			<if test="pushid != null">#{pushid},</if>
			<if test="title != null">#{title},</if>
			<if test="msgtype != null">#{msgtype},</if>
			<if test="msgcontent != null">#{msgcontent},</if>
			<if test="conscode != null">#{conscode},</if>
			<if test="orgcode != null">#{orgcode},</if>
			<if test="creator != null">#{creator},</if>
			<if test="credt != null">#{credt},</if>
			<if test="modifier != null">#{modifier},</if>
			<if test="moddt != null">#{moddt},</if>
			<if test="pushflag != null">#{pushflag},</if>
			<if test="pushdt != null">#{pushdt},</if>
			<if test="expectpushdt != null">#{expectpushdt},</if>
			<if test="msgstyle != null">#{msgstyle},</if>
			<if test="msgchannel != null">#{msgchannel},</if>
			<if test="remark != null">#{remark},</if>
		</trim>
		)
	</insert>
	
	<select id="getConsCodeAndOrgCodeByHboneno" parameterType="Map" resultType="Map" useCache="false">
		SELECT T.CONSCODE, T2.OUTLETCODE
		  FROM CM_CUSTCONSTANT T
		  LEFT JOIN CM_CONSCUST T1
		    ON T.CUSTNO = T1.CONSCUSTNO
		  LEFT JOIN CM_CONSULTANT T2
		    ON T.CONSCODE = T2.CONSCODE
		 WHERE T1.HBONE_NO = #{hboneno}
	</select>
	
	<select id="getOrgCodeByConscode" parameterType="Map" resultType="Map" useCache="false">
		 SELECT T.OUTLETCODE FROM CM_CONSULTANT T WHERE T.CONSCODE = #{conscode}
	</select>
    
    <select id="getConsCodeAndOrgCodeByRoleCode" parameterType="Map" resultType="Map" useCache="false">
	 SELECT T1.CONSCODE, T1.OUTLETCODE
	   FROM HB_USERROLE T
	   LEFT JOIN CM_CONSULTANT T1
	     ON T.USERCODE = T1.CONSCODE
	  WHERE T.ROLECODE = #{rolecode}
	  AND T1.CONSCODE is not null
	  AND T1.CONSSTATUS = '1'
   </select>
	<select id="getWechatconscodeByConscode" resultType="java.lang.String">
		select a.wechatconscode from CM_CONSULTANT a where a.conscode=#{conscode} and a.CONSSTATUS = '1'
	</select>


	<select id="getSalesAssistantWithAuth" parameterType="java.lang.String" resultType="java.lang.String" useCache="false">
		WITH ORGINFO AS (
		SELECT H.ORGCODE
		FROM CM_CONSULTANT C2, HB_ORGANIZATION H
		WHERE C2.OUTLETCODE = H.ORGCODE
		AND C2.CONSCODE = #{conscode}
		)
		SELECT CONSCODE FROM
		(
		SELECT T.CONSCODE FROM CM_CONSULTANT T,ORGINFO O
		WHERE T.CONSSTATUS = '1'
		AND O.ORGCODE=T.OUTLETCODE
		UNION
		SELECT CAO.AUTHOR_ID  FROM CM_AUTHORATION_ORG CAO ,ORGINFO O
		WHERE CAO.AUTHOR_MODULE IN ('0010', 'ALL')
		AND CAO.AUTHOR_ORG = O.ORGCODE
		) TEMP
		WHERE
		EXISTS (
		SELECT 1 FROM HB_USERROLE A
		WHERE A.ROLECODE IN
		<foreach collection="roleCodeList" close=")" item="item" open="(" separator=",">
			#{item}
		</foreach>
		AND A.RECSTAT = '0'
		AND A.USERCODE=TEMP.CONSCODE
		)
	</select>

	<select id="getSalesAssistantByPcodeWithAuth" parameterType="java.lang.String" resultType="java.lang.String" useCache="false">
		WITH ORGINFO AS (
		SELECT H.PARENTORGCODE AS ORGCODE
		FROM CM_CONSULTANT C2, HB_ORGANIZATION H
		WHERE C2.OUTLETCODE = H.ORGCODE
		AND C2.CONSCODE = #{conscode}
		)
		SELECT CONSCODE FROM
		(
		SELECT T.CONSCODE FROM CM_CONSULTANT T,ORGINFO O
		WHERE T.CONSSTATUS = '1'
		AND O.ORGCODE=T.OUTLETCODE
		UNION
		SELECT CAO.AUTHOR_ID  FROM CM_AUTHORATION_ORG CAO ,ORGINFO O
		WHERE CAO.AUTHOR_MODULE IN ('0010', 'ALL')
		AND CAO.AUTHOR_ORG = O.ORGCODE
		) TEMP
		WHERE
		EXISTS (
		SELECT 1 FROM HB_USERROLE A
		WHERE A.ROLECODE IN
		<foreach collection="roleCodeList" close=")" item="item" open="(" separator=",">
		#{item}
    	</foreach>
		AND A.RECSTAT = '0'
		AND A.USERCODE=TEMP.CONSCODE
		)
	</select>

</mapper>
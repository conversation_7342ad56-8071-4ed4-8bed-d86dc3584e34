package com.howbuy.crm.nt.pushmsg.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.base.response.NtBaseResponse;
import com.howbuy.crm.nt.pushmsg.buss.CmPushMsgByConsCodeBuss;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgByConsCodeRequest;
import com.howbuy.crm.nt.pushmsg.request.PushMsgAnnexRequest;
import com.howbuy.crm.nt.pushmsg.response.PushMsgAnnexResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmPushMsgByConsCodeServiceImpl.java
 * @Description TODO
 * @createTime 2022年03月01日 14:36:00
 */
@Slf4j
@Service("CmPushMsgByConsCodeService")
public class CmPushMsgByConsCodeServiceImpl implements CmPushMsgByConsCodeService{

    @Autowired
    private CmPushMsgByConsCodeBuss cmPushMsgByConsCodeBuss;

    @Override
    public NtBaseResponse pushMsgByConsCode(CmPushMsgByConsCodeRequest req) {

        log.info("CmPushMsgByConsCodeRequest is: {}",JSON.toJSONString(req));
        NtBaseResponse response = new NtBaseResponse();

        if(this.validate(response, req)){
            cmPushMsgByConsCodeBuss.pushMsgByConsCode(req.getConscode(), req.getMsgcontent());
            response.success();
        }

        return response;
    }

    @Override
    public PushMsgAnnexResponse queryPushMsgAnnexList(PushMsgAnnexRequest request) {

        log.info("PushMsgAnnexRequest is: {}",JSON.toJSONString(request));
        PushMsgAnnexResponse response = new PushMsgAnnexResponse();

        if(StringUtils.isNotEmpty(request.getPushid())){
            response = cmPushMsgByConsCodeBuss.queryPushMsgAnnexList(request.getPushid());
        }else{
            response.invalidReqParams("传参数据为空！");
        }
        return response;
    }

    public boolean validate(NtBaseResponse response, CmPushMsgByConsCodeRequest request) {
        boolean flag = true;
        // 校验传入数据
        if (StringUtils.isBlank(request.getConscode()) && StringUtils.isBlank(request.getMsgcontent())) {
            response.invalidReqParams("传参数据为空！");
            flag = false;
        }

        return flag;
    }
}

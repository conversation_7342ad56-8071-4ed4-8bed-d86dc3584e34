/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.pushmsg.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.base.enums.NotifyMsgTypeEnum;
import com.howbuy.crm.nt.pushmsg.dto.NofifyMsgDto;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;

/**
 * @description: 发送通知类消息接收处理器
 * <AUTHOR>
 * @date 2023/3/8 17:07
 * @since JDK 1.8
 */
@Slf4j
@Component
public  class CmPushNotifyMsgProccessor extends MessageProcessor {


    /**
     * 调用 CmPushMsgService  实现 发送 站内信通知
     * 去除外部 对nt-client的依赖
     */
    @Value("${crm.notify.msg.channel}")
    private String CRM_NOTIFY_MSG_CHANNEL;


    @Autowired
    private CmPushMsgService cmPushMsgService;


    @PostConstruct
    public void init() {
        // 加载消息处理器
        MessageService.getInstance().addMessageProcessor(CRM_NOTIFY_MSG_CHANNEL, this);
    }

    /**
     * @description: 处理消息
     * @param message	消息
     * @author: hongdong.xie
     * @date: 2023/3/8 18:04
     * @since JDK 1.8
     */
    @Override
    public void processMessage(SimpleMessage message) {
        NofifyMsgDto  nofifyMsgDto=getContent(message);
        Assert.notNull(nofifyMsgDto,"消息内容不能为空");
        sendNofityMsg(nofifyMsgDto);
    }



    /**
     * @description: 发送通知消息
     * @param nofifyMsgDto 通知消息内容
     * @return void
     * @throws
     * @since JDK 1.8
     */
    private void sendNofityMsg(NofifyMsgDto nofifyMsgDto){
        String businessId=nofifyMsgDto.getBusinessId();
        Assert.notNull(businessId,"业务id不能为空");
        NotifyMsgTypeEnum notifyTypeEnum= NotifyMsgTypeEnum.getEnum(nofifyMsgDto.getNotifyType());
        Assert.notNull(notifyTypeEnum,"通知类型不能为空");
        Map<String,String> paramMap=nofifyMsgDto.getParamMap();
        switch (notifyTypeEnum){
            case CONS_CODE_LIST:
                //按照投顾code列表 发送
                Assert.notNull(nofifyMsgDto.getConsCodeList(),"投顾code列表不能为空");
                cmPushMsgService.pushMsgByConsCodeList(businessId,nofifyMsgDto.getConsCodeList(),paramMap);
                break;
            case ROLE_CODE_LIST:
                //按照角色列表 发送
                Assert.notEmpty(nofifyMsgDto.getRoleCodeList(),"角色列表不能为空");
                cmPushMsgService.pushMsgByRoleCodeList(businessId,nofifyMsgDto.getRoleCodeList(),paramMap);
                break;
            case HBONE_NO:
                //按照 hboneNo 发送
                Assert.notNull(nofifyMsgDto.getHboneNo(),"hboneNo不能为空");
                cmPushMsgService.pushMsgByHboneNo(businessId,nofifyMsgDto.getHboneNo(),paramMap);
                break;
            case HK_TX_ACCT_NO:
                //按照 hkTxAcctNo 发送
                Assert.notNull(nofifyMsgDto.getHkTxAcctNo(),"hkTxAcctNo不能为空");
                //注意： 参数对[custNo,custName]有特殊处理
                cmPushMsgService.pushMsgByHkTxAcctNoWithCustInfo(businessId,nofifyMsgDto.getHkTxAcctNo(),paramMap);
                break;
            default:
                break;
        }

    }


    /**
     * @description: 获取消息内容
     * @param message 消息
     * @return java.lang.String
     * @author: hongdong.xie
     * @date: 2023/8/29 14:40
     * @since JDK 1.8
     */
    protected NofifyMsgDto getContent(SimpleMessage message){
        String msg = null;
        if(Objects.nonNull(message) && Objects.nonNull(message.getContent())){
            msg = message.getContent().toString();
        }

        return msg==null?null: JSON.parseObject(msg,NofifyMsgDto.class);
    }
}
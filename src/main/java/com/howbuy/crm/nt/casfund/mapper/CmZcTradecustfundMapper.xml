<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.casfund.dao.CmZcTradecustfundDao">
	<insert id="batchInsertCustFund" parameterType="java.util.List" useGeneratedKeys="false">
		INSERT INTO CM_ZC_TRADECUSTFUND (hboneno       ,
										fundcode      ,
										fundname      ,
										balancevol    ,
										nav           ,
										navdt         ,
										marketval     ,
										marketvalrmb  ,
										currencyunit  ,
										fundtype      ,
										balancecost   ,
										balanceincome ,
										firstackdt    ,
										ackdt         ,
										version       )
		select a.* from (								
		<foreach collection="list" item="item" index="index"  separator="union all" >
		  SELECT
		   #{item.hboneno, jdbcType=VARCHAR},
		   #{item.fundcode, jdbcType=VARCHAR},
		   #{item.fundname, jdbcType=VARCHAR},
		   #{item.balancevol, jdbcType = NUMERIC},
		   #{item.nav, jdbcType = NUMERIC},
		   #{item.navdt, jdbcType=VARCHAR},
		   #{item.marketval, jdbcType = NUMERIC},
		   #{item.marketvalrmb, jdbcType = NUMERIC},
		   #{item.currencyunit, jdbcType=VARCHAR},
		   #{item.fundtype, jdbcType=VARCHAR},
		   #{item.balancecost, jdbcType = NUMERIC},
		   #{item.balanceincome, jdbcType = NUMERIC},
		   #{item.firstackdt, jdbcType=VARCHAR},
		   #{item.ackdt, jdbcType=VARCHAR},
		   #{item.version, jdbcType=VARCHAR}
		   FROM DUAL
		</foreach>
		) a
	</insert>
	
	<delete id="delZcCustFund">
		TRUNCATE TABLE CM_ZC_TRADECUSTFUND
	</delete>
	
	<update id="cleanCmZcTradCustFundBak">
		truncate TABLE CM_ZC_TRADECUSTFUND_BAK
	</update>

	<insert id="insertCmZcTradCustFundBak">
		INSERT INTO CM_ZC_TRADECUSTFUND_BAK (HBONENO, FUNDCODE, FUNDNAME, BALANCEVOL, NAV, NAVDT, MARKETVAL,
											 MARKETVALRMB, CURRENCYUNIT, FUNDTYPE, BALANCECOST, BALANCEINCOME,
											 FIRSTACKDT, ACKDT, VERSION, CREDDT, UPDDT)
		SELECT HBONENO,
			   FUNDCODE,
			   FUNDNAME,
			   BALANCEVOL,
			   NAV,
			   NAVDT,
			   MARKETVAL,
			   MARKETVALRMB,
			   CURRENCYUNIT,
			   FUNDTYPE,
			   BALANCECOST,
			   BALANCEINCOME,
			   FIRSTACKDT,
			   ACKDT,
			   VERSION,
			   CREDDT,
			   UPDDT
		FROM CM_ZC_TRADECUSTFUND
	</insert>
	
	<update id="updateCasFundCount" parameterType="Map">
		UPDATE CM_ZC_TRADECUSTFUND_COUNT T
   			SET T.DEALDT = #{dealdt}, 
   				T.VERSION = #{version},
   				T.PAGENO = #{pageno},
   				T.TOTALNO=#{totalno}, 
   				T.UPDDT = SYSDATE
	</update>
	
	<select id="listCasFundData" parameterType="Map" resultType="Map" >
       SELECT T.DEALDT,T.VERSION,T.PAGENO,T.TOTALNO
		  FROM CM_ZC_TRADECUSTFUND_COUNT T
    </select>
    
    <insert id="insertCmZcTradecustfundError" parameterType="Map">
    insert into CM_ZC_TRADECUSTFUND_ERROR (ERRORDES) values ( #{errordes} )
    </insert>
    
    <delete id="dorpCmZcIndex">
    	drop index INDX_CM_ZC_TRADECUSTFUND
    </delete>
    
    <insert id="cretCmZcIndex">
    	create index INDX_CM_ZC_TRADECUSTFUND on CM_ZC_TRADECUSTFUND (HBONENO, FUNDCODE)
    </insert>
</mapper>
package com.howbuy.crm.nt.casfund.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.howbuy.cc.center.assetservice.centralization.domain.CasFundBalanceDomain;
import com.howbuy.cc.center.assetservice.centralization.request.QueryCasFundBalanceRequest;
import com.howbuy.cc.center.assetservice.centralization.response.QueryCasFundBalanceResponse;
import com.howbuy.cc.center.assetservice.centralization.service.QueryCasFundBalanceService;
import com.howbuy.crm.nt.casfund.dao.CmZcTradecustfundDao;
import com.howbuy.crm.nt.casfund.domain.CmZcTradecustfund;
import com.howbuy.crm.util.CrmNtConstant;
import com.howbuy.crm.util.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * (类型功能说明描述)
 *
 * 
 * 修改历史:										
 * 修改日期    		修改人员   	版本	 		修改内容
 * -------------------------------------------------
 * 2018年3月16日 下午2:29:21   yu.zhang     1.0    	初始化创建
 *  
 *
 * <AUTHOR> 
 * @version		1.0  
 * @since		JDK1.7
 */
@Slf4j
@Service("casFundBalanceServiceImpl")
public class CasFundBalanceServiceImpl implements CasFundBalanceService {

	@Autowired
	private QueryCasFundBalanceService queryCasFundBalanceService;
	
	@Autowired
	private CmZcTradecustfundDao cmZcTradecustfundMapper;

	/** 出现异常后的最大尝试次数 */
	public static final int MAX_TRY_TIMES = 5;
	
	@Override
	public String autoSyncCustFund(String arg) {
		QueryCasFundBalanceRequest req = new QueryCasFundBalanceRequest();
		req.setPageNo(1);
		log.info("首次调用查询开始时间：{}", System.currentTimeMillis());
		QueryCasFundBalanceResponse casFundresponse = queryCasFundBalanceService.execute(req);
		log.info("首次调用查询结束时间：{}", System.currentTimeMillis());
		String version = "";
		int totalPage = 0;
		if(casFundresponse != null && CrmNtConstant.RMISucc.equals(casFundresponse.getReturnCode())){
			version = casFundresponse.getBatchNo();
			totalPage = casFundresponse.getTotalPage();
			List<CasFundBalanceDomain>  funds = casFundresponse.getBalanceDomains();
			log.info("首次调用查询出：{}条", funds.size());
			List<CmZcTradecustfund> listinsert = changeTradeList(funds,version);
			//删除历史数据
			cmZcTradecustfundMapper.delZcCustFund();
			//插入数据
			log.info("首次插入开始时间：{}", System.currentTimeMillis());
			cmZcTradecustfundMapper.batchInsertCustFund(listinsert);
			log.info("首次插入结束时间：{}", System.currentTimeMillis());
			for(int i = 2;i <= totalPage;i++){
				if(!batchinsert(i,version,totalPage)){
					//出现异常再跑五次，还是出现异常，直接跳出
					boolean flag = false;
					for(int j = 0; i < MAX_TRY_TIMES;j++){
						if(batchinsert(i,version,totalPage)){
							flag = true;
							break;
						}
					}
					//重复5次后仍然没有成功，直接退出
					if(!flag){
						break;
					}
				}
			}
		}
		return "0000";
	}
	
	private boolean batchinsert(int i,String version,int totalno){
		try{
			QueryCasFundBalanceRequest r = new QueryCasFundBalanceRequest();
			r.setPageNo(i);
			r.setBatchNo(version);
			log.info("第{}次调用查询开始时间：{}", i, System.currentTimeMillis());
			QueryCasFundBalanceResponse resfor = queryCasFundBalanceService.execute(r);
			log.info("第{}次调用查询结束时间：{}", i, System.currentTimeMillis());
			List<CasFundBalanceDomain>  fundsfor = resfor.getBalanceDomains();
			log.info("第{}次调用查询出：{}条", i, fundsfor.size());
			List<CmZcTradecustfund> listinsertfor = changeTradeList(fundsfor,version);
			log.info("第{}次插入开始时间：{}", i, System.currentTimeMillis());
			cmZcTradecustfundMapper.batchInsertCustFund(listinsertfor);
			log.info("第{}次插入结束时间：{}", i, System.currentTimeMillis());
			//修改页码
			Map<String,Object> param = new HashMap<String,Object>();
			param.put("dealdt", DateTimeUtil.getCurDate());
			param.put("version", version);
			param.put("pageno", i);
			param.put("totalno", totalno);
			cmZcTradecustfundMapper.updateCasFundCount(param);
		}catch(Exception e){
			Map<String,Object> param = new HashMap<String,Object>();
			//param.put("errordes", "版本号："+version+"；第"+i+"页；"+e.getMessage().substring(0, 1500));
			//cmZcTradecustfundMapper.insertCmZcTradecustfundError(param);
			log.info(e.getMessage());
			return false;
		}
		return true;
	}

	/**
	 * 转换封装beanList
	 * @param funds
	 * @param version
	 * @return
	 */
	public List<CmZcTradecustfund> changeTradeList(List<CasFundBalanceDomain> funds,String version){
		List<CmZcTradecustfund> listinserts = new ArrayList<CmZcTradecustfund>();
		for(CasFundBalanceDomain domain : funds){
			CmZcTradecustfund obj = new CmZcTradecustfund();
			obj.setHboneno(StringUtil.replaceNull(domain.getHboneNo()));
			obj.setFundcode(StringUtil.replaceNull(domain.getFundCode()));
			obj.setFundname(StringUtil.replaceNull(domain.getFundName()));
			if(StringUtil.isNotNullStr(domain.getBalanceVol())){
				obj.setBalancevol(BigDecimal.valueOf(Double.parseDouble(domain.getBalanceVol())));
			}else{
				obj.setBalancevol(null);
			}
			if(StringUtil.isNotNullStr(domain.getNav())){
				obj.setNav(BigDecimal.valueOf(Double.parseDouble(domain.getNav())));
			}else{
				obj.setNav(null);
			}
			if(StringUtil.isNotNullStr(domain.getMarketVal())){
				obj.setMarketval(BigDecimal.valueOf(Double.parseDouble(domain.getMarketVal())));
			}else{
				obj.setMarketval(null);
			}
			if(StringUtil.isNotNullStr(domain.getMarketValRmb())){
				obj.setMarketvalrmb(BigDecimal.valueOf(Double.parseDouble(domain.getMarketValRmb())));
			}else{
				obj.setMarketvalrmb(null);
			}
			if(StringUtil.isNotNullStr(domain.getBalanceCost())){
				obj.setBalancecost(BigDecimal.valueOf(Double.parseDouble(domain.getBalanceCost())));
			}else{
				obj.setBalancecost(null);
			}
			if(StringUtil.isNotNullStr(domain.getBalanceIncome())){
				obj.setBalanceincome(BigDecimal.valueOf(Double.parseDouble(domain.getBalanceIncome())));
			}else{
				obj.setBalanceincome(null);
			}
			obj.setNavdt(StringUtil.replaceNull(domain.getNavDt()));
			obj.setCurrencyunit(StringUtil.replaceNull(domain.getCurrencyUnit()));
			obj.setFundtype(StringUtil.replaceNull(domain.getFundType()));
			obj.setFirstackdt(StringUtil.replaceNull(domain.getFirstAckDt()));
			obj.setAckdt(StringUtil.replaceNull(domain.getAckDt()));
			obj.setVersion(version);
			listinserts.add(obj);
		}
		return listinserts;
	}
}

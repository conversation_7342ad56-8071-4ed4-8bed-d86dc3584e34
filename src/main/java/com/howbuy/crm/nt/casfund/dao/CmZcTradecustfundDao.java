package com.howbuy.crm.nt.casfund.dao;

import com.howbuy.crm.nt.casfund.domain.CmZcTradecustfund;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;

@MapperScan
public interface CmZcTradecustfundDao {

    void batchInsertCustFund(List<CmZcTradecustfund> list);

    void delZcCustFund();


    /**
     * @description:(清空cm_zc_tradecustfund_bak 表的数据)
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 13:58
     * @since JDK 1.8
     */
    void cleanCmZcTradCustFundBak();


    /**
     * @description:(把cm_zc_tradecustfund表数据备份)
     * @param
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 13:58
     * @since JDK 1.8
     */
    int insertCmZcTradCustFundBak();

    void updateCasFundCount(Map<String, Object> param);

    void insertCmZcTradecustfundError(Map<String, Object> param);

    void dorpCmZcIndex();

    void cretCmZcIndex();
}

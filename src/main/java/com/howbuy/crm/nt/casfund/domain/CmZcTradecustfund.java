package com.howbuy.crm.nt.casfund.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CmZcTradecustfund implements Serializable {
    private static final long serialVersionUID = -2128922389173435325L;
    private String hboneno;
    private String fundcode;
    private String fundname;
    private BigDecimal balancevol;
    private BigDecimal nav;
    private String navdt;
    private BigDecimal marketval;
    private BigDecimal marketvalrmb;
    private String currencyunit;
    private String fundtype;
    private BigDecimal balancecost;
    private BigDecimal balanceincome;
    private String firstackdt;
    private String ackdt;
    private String version;
    private Date creddt;
    private Date upddt;
}

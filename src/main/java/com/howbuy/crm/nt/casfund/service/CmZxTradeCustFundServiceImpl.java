/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.casfund.service;

import com.howbuy.crm.nt.casfund.dao.CmZcTradecustfundDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/6/6 13:16
 * @since JDK 1.8
 */
@Service("cmZxTradeCustFundService")
@Transactional(rollbackFor = Exception.class)
public class CmZxTradeCustFundServiceImpl implements CmZxTradeCustFundService{

    @Autowired
    private CmZcTradecustfundDao cmZcTradecustfundDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyZxTradeCustFund() {
        // 1. 清空cm_zc_tradecustfund_bak 表的数据
        cmZcTradecustfundDao.cleanCmZcTradCustFundBak();
        // 2. 把cm_zc_tradecustfund表数据备份
        cmZcTradecustfundDao.insertCmZcTradCustFundBak();
    }
}
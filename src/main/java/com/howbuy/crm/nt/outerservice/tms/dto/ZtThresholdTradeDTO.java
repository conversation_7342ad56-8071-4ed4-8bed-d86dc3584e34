/**
 * Copyright (c) 2024, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.outerservice.tms.dto;

import com.howbuy.tms.robot.orders.facade.enums.QueryCustAmtTradeTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 请求中台接口返回值，客户单个交易日 金额超过阈值的数据list
 * @date 2024/6/17 19:04
 * @since JDK 1.8
 */

@Getter
@Setter
public class ZtThresholdTradeDTO {

    /**
     * 0返回值为空、1成功、2正在计算、3非交易日
     */
    private String status;

    private List<TradeAmtDetail> tradeList = new ArrayList<>();

    @Getter
    @Setter
    public static class TradeAmtDetail {

        /**
         * 一账通号
         */
        private String hboneNo;
        /**
         * 交易日期:yyyyMMdd
         */
        private String tradeDt;
        /**
         * 交易金额
         */
        private BigDecimal tradeAmt;
        /**
         * @see QueryCustAmtTradeTypeEnum
         */
        private String tradeType;

        /**
         * 交易份额
         */
        private BigDecimal tradeVol;
    }


}
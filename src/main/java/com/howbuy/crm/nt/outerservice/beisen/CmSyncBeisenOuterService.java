/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.outerservice.beisen;

import com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade;
import com.howbuy.crm.account.client.response.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/15 17:26
 * @since JDK 1.8
 */
@Service
public class CmSyncBeisenOuterService {
    @Autowired
    private CmSyncBeisenFacade cmSyncBeisenFacade;

    public List<String> execSyncBeisenToCrmByJobFacade(){
        Response<List<String>> response =  cmSyncBeisenFacade.execSyncBeisenToCrmByJobFacade();
        if(null != response && response.isSuccess()){
            return response.getData();
        }
        return Collections.emptyList();
    }
}
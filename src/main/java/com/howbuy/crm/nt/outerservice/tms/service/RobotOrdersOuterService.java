/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.outerservice.tms.service;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.constant.TmsRequestConstants;
import com.howbuy.crm.nt.outerservice.acc.service.AccOuterService;
import com.howbuy.crm.nt.outerservice.tms.dto.ZtThresholdTradeDTO;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.robot.orders.facade.query.querycusttradevol.QueryCustTradeAmtFacade;
import com.howbuy.tms.robot.orders.facade.query.querycusttradevol.QueryCustTradeAmtRequest;
import com.howbuy.tms.robot.orders.facade.query.querycusttradevol.QueryCustTradeAmtResponse;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/6/17 15:42
 * @since JDK 1.8
 */

@Slf4j
@Service
public class RobotOrdersOuterService {

    @Autowired
    private QueryCustTradeAmtFacade queryCustTradeAmtFacade;

    @Autowired
    private AccOuterService accOuterService;

    /**
     * @param date
     * @param buyAmtThreshold
     * @param sellAmtThreshold
     * @return com.howbuy.crm.nt.outerservice.zt.dto.ZtThresholdTradeDTO
     * @description: 查询中台接口：查询[指定日期]、[指定买入金额阈值]、[指定卖出金额阈值]以上的交易数据
     * @author: jin.wang03
     * @date: 2024/6/18 13:11
     * @since JDK 1.8
     */
    public ZtThresholdTradeDTO queryThresholdTrade(String date, String buyAmtThreshold, String sellAmtThreshold) {
        ZtThresholdTradeDTO resultDto = new ZtThresholdTradeDTO();

        QueryCustTradeAmtRequest queryCustTradeAmtRequest = new QueryCustTradeAmtRequest();
        if (StringUtils.isNotBlank(date)) {
            queryCustTradeAmtRequest.setTradeDt(date);
        }
        if (StringUtils.isNotBlank(buyAmtThreshold)) {
            queryCustTradeAmtRequest.setBuyAmtThreshold(new BigDecimal(buyAmtThreshold));
        }
        if (StringUtils.isNotBlank(sellAmtThreshold)) {
            queryCustTradeAmtRequest.setSellAmtThreshold(new BigDecimal(sellAmtThreshold));
        }

        // 必须入参设置默认值
        queryCustTradeAmtRequest.setTxAcctNo(TmsRequestConstants.DEFAULT_TX_ACCT_NO);
        queryCustTradeAmtRequest.setDisCode(DisCodeEnum.HM.getCode());
        queryCustTradeAmtRequest.setOutletCode(TmsRequestConstants.TMS_REQUEST_OUTLET_CODE);
        queryCustTradeAmtRequest.setOperIp(TmsRequestConstants.TMS_REQUEST_OPER_IP);
        queryCustTradeAmtRequest.setTxChannel(TmsRequestConstants.TMS_REQUEST_CHANNEL);

        QueryCustTradeAmtResponse amtResponse = queryCustTradeAmtFacade.execute(queryCustTradeAmtRequest);
        log.info("QueryCustTradeAmtFacade Response:{}", JSONObject.toJSONString(amtResponse));
        if (Objects.nonNull(amtResponse)) {
            resultDto.setStatus(amtResponse.getStatus());
            List<QueryCustTradeAmtResponse.TradeAmtDetail> tradeAmtDetailList = amtResponse.getTradeAmtDetailList();
            if (CollectionUtils.isNotEmpty(tradeAmtDetailList)) {
                tradeAmtDetailList.forEach(tradeAmtDetail -> {
                    ZtThresholdTradeDTO.TradeAmtDetail detail = new ZtThresholdTradeDTO.TradeAmtDetail();
                    detail.setTradeType(tradeAmtDetail.getTradeType());
                    detail.setHboneNo(accOuterService.queryHbOneNoByTxAcctNo(tradeAmtDetail.getTxAcctNo()));
                    detail.setTradeDt(tradeAmtDetail.getTradeDt());
                    detail.setTradeAmt(tradeAmtDetail.getTradeAmt());
                    detail.setTradeVol(tradeAmtDetail.getRedeemVol());
                    resultDto.getTradeList().add(detail);
                });
            }
        } else {
            resultDto.setStatus(StaticVar.NO);
        }

        return resultDto;
    }

}
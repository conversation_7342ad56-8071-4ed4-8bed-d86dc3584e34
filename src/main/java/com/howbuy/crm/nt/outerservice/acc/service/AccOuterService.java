/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.outerservice.acc.service;

import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneRequest;
import com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneResponse;
import com.howbuy.crm.util.TmsInvokerUtil;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 账号中心outerService
 * @date 2024/6/19 14:08
 * @since JDK 1.8
 */

@Slf4j
@Service
public class AccOuterService {

    @Autowired
    private QueryTxAcctByHboneFacade queryTxAcctByHboneFacade;


    /**
     * @param txAcctNo 交易账号
     * @return java.lang.String
     * @description: 根据交易账号获取一账通号
     * @author: jin.wang03
     * @date: 2024/6/19 13:58
     * @since JDK 1.8
     */
    public String queryHbOneNoByTxAcctNo(String txAcctNo) {
        QueryTxAcctByHboneRequest request = new QueryTxAcctByHboneRequest();
        request.setTxAcctNo(txAcctNo);
        request.setDisCode(DisCodeEnum.HM.getCode());
        QueryTxAcctByHboneResponse response = queryTxAcctByHboneFacade.execute(request);
        if (Objects.nonNull(response) && TmsInvokerUtil.isSuccess(response.getReturnCode())) {
            return response.getHboneNo();
        }
        return null;
    }

}
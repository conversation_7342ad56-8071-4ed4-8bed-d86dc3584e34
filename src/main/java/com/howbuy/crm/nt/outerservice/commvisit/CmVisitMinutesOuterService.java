package com.howbuy.crm.nt.outerservice.commvisit;

import com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/29 19:52
 * @since JDK 1.8
 */
@Service
public class CmVisitMinutesOuterService {

    @Autowired
    private CmVisitMinutesFacade cmVisitMinutesFacade;

    /**
     * @description 推送未反馈的访问记录
     * @param curPreDayParam	当前日期前N天的日期参数
     * @return void
     * @author: jianjian.yang
     * @date: 2025/4/29 20:03
     * @since JDK 1.8
     */
    public void pushNoFeedback(String curPreDayParam){
        cmVisitMinutesFacade.pushNoFeedback(curPreDayParam);
    }
}
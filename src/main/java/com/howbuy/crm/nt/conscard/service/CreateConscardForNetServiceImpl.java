package com.howbuy.crm.nt.conscard.service;

import com.howbuy.crm.conscard.request.CreateConscardInfoRequest;
import com.howbuy.crm.conscard.response.CreateConscardInfoResponse;
import com.howbuy.crm.conscard.service.CreateConscardInfoService;
import com.howbuy.crm.nt.conscard.request.CreateConscardForNetRequest;
import com.howbuy.crm.nt.conscard.response.CreateConscardForNetResponse;
import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

@Service("createConscardForNetService")
public class CreateConscardForNetServiceImpl implements CreateConscardForNetService{

	private static final Logger log = LoggerFactory.getLogger(CreateConscardForNetServiceImpl.class);
	
	@Autowired
	private CreateConscardInfoService createConscardInfoService;

	/** 客户手机最大长度 */
	public static final int MOBILE_MAX_LENGTH = 30;
	/** 客户姓名最大长度 */
	public static final int CUSTNAME_MAX_LENGTH = 90;

	@Override
	public CreateConscardForNetResponse createConscardInfo(CreateConscardForNetRequest request) {

		log.info("CreateConscardForNetRequest is: " + JSON.toJSONString(request));
		CreateConscardForNetResponse response = new CreateConscardForNetResponse();

		if(this.validate(response, request)) {

			CreateConscardInfoRequest corerequest = new CreateConscardInfoRequest();
			corerequest.setConscode(request.getConscode());
			corerequest.setCustname(request.getCustname());
			corerequest.setHboneno(request.getHboneno());
			corerequest.setMobile(request.getMobile());
			corerequest.setSourceno(request.getSourceno());
			corerequest.setCustsourceremark(request.getCustsourceremark());
			corerequest.setSourcetitle(request.getSourceTitle());
			corerequest.setSourceurl(request.getSourceUrl());
			corerequest.setZtyname(request.getZtyname());
			corerequest.setExtraSendTarget(request.getExtraSendTarget());
			corerequest.setSourceChannel(request.getSourceChannel());
			log.info("createConscardInfoService createConscardInfo is: " + JSON.toJSONString(corerequest));
			CreateConscardInfoResponse coreresponse = createConscardInfoService.createConscardInfo(corerequest);

			if(CrmNtConstant.RMISucc.equals(coreresponse.getReturnCode())){
				// 设置返回参数
				response.setConscode(coreresponse.getConscode());
				response.setIsVirtual(coreresponse.getIsVirtual());
				response.success();
			}else{
				response.paramError("createConscardInfoService接口异常"+coreresponse.getDescription());
			}

			log.info("CreateConscardInfoService createConscardInfo is: " + JSON.toJSONString(response));
			
		}
		
		return response;
	}

	public boolean validate(CreateConscardForNetResponse response, CreateConscardForNetRequest request) {
		boolean flag = true;
		
		String mobile = StringUtils.trim(request.getMobile());
		String conscode = StringUtils.trim(request.getConscode());
		String custname = StringUtils.trim(request.getCustname());
		String hboneno = StringUtils.trim(request.getHboneno());
		// 校验传入数据
		if (StringUtils.isBlank(hboneno)) {
			response.invalidReqParams("传入参数（一账通）为空");
			flag = false;
		}
		
		if (StringUtils.isBlank(mobile)) {
			response.invalidReqParams("传入参数（客户手机）为空");
			flag = false;
		}
		
		if (StringUtils.isNotBlank(mobile) && mobile.length()>MOBILE_MAX_LENGTH) {
			response.invalidReqParams("传入参数（客户手机太长），不符合要求！");
			flag = false;
		}
		
		if (StringUtils.isBlank(conscode)) {
			response.invalidReqParams("传入参数（客户推荐投顾）为空");
			flag = false;
		}
		
		if (StringUtils.isBlank(custname)) {
			response.invalidReqParams("传入参数（客户姓名）为空");
			flag = false;
		}
		
		if (StringUtils.isNotBlank(custname) && custname.length()>CUSTNAME_MAX_LENGTH) {
			response.invalidReqParams("传入参数（客户姓名太长），不符合要求！");
			flag = false;
		}
		
		return flag;
	}
}

/**
 * Create at 2010-4-1 by kai.liao
 *
 * Copyright 2008, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd. 
 * All right reserved.
 *
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT 
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 * 
 * 2010-08-17 chris.song	BeginDt和EndDt反了
 * 2010-08-20 chris.song	近几月未联系的情况
 * 2010-08-27 chris.song	visittime为14位长, 所以把时分秒也带上
 */
package com.howbuy.crm.nt.conscard.util;


import crm.howbuy.base.constants.StaticVar;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2010-4-1
 */
public class VisitUtil {

	public static final int IDNO_15_BIT = 15;
	public static final int IDNO_18_BIT = 18;

	public static boolean checkResult(String idno,String custname){
		boolean check = true;
		
		//证件号码不允许为空
		if(StringUtils.isBlank(idno)){
			check = false;
		}
		
		if(StringUtils.isNotBlank(idno) && !(idno.length() == IDNO_15_BIT || idno.length() == IDNO_18_BIT)){
			check = false;
		}
		//姓名不允许为空
		if(StringUtils.isBlank(custname)){
			check = false;
		}
		
		if(check){
			String[] containsStr = {"先生","小姐","女士"};
			for(int i = 0 ; i < containsStr.length ; i++){
				if(custname.contains(containsStr[i])){
					check = false;
					break;
				}
			}
				
		}
		return check;
	}
	
	public static boolean checkResultByOther(String idno,String idtype,String usertype,String custname){
		boolean check = true;
		
		//用户类型不允许为空并且必须为个人
		if(StringUtils.isBlank(usertype)){
			check = false;
		}
		//证件类型不允许为空并且必须为身份证
		if(StringUtils.isBlank(idtype) || (StringUtils.isNotBlank(idtype) && StaticVar.ID_TYPE_SFZ.equals(idtype))){
			check = false;
		}
		//证件号码不允许为空
		if(StringUtils.isBlank(idno)){
			check = false;
		}
		//姓名不允许为空
		if(StringUtils.isBlank(custname)){
			check = false;
		}
		
		return check;
	}
}

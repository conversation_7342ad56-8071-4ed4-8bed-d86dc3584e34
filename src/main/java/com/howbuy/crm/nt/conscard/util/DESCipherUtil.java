package com.howbuy.crm.nt.conscard.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.Charset;

public class DESCipherUtil {

	private final static Logger log = LoggerFactory.getLogger(DESCipherUtil.class);

	private static SecretKey securekey;
	private final static String DES_TOKEN = "woyouyizhixiaomaolvayiyayiyayo";

	static {
		try {
			DESKeySpec desKey = new DESKeySpec(DES_TOKEN.getBytes(Charset.forName("UTF-8")));
			SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
			securekey = keyFactory.generateSecret(desKey);
		} catch (Exception e) {
			log.error("DESCipherUtilError", e);
		}
	}


}

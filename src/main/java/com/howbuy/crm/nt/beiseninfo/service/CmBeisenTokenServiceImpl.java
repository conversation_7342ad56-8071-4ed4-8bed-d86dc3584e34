/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.cache.CacheConstants;
import com.howbuy.crm.cache.cacheService.lock.LockService;
import com.howbuy.crm.nt.beiseninfo.CmBeisenConstant;
import com.howbuy.gateway.captcha.facade.beisen.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/18 17:11
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmBeisenTokenServiceImpl implements CmBeisenTokenService{
    @Autowired
    private CmBeisenTokenFacade cmBeisenTokenFacade;
    @Autowired
    private CmBeisenConstant cmBeisenConstant;
    @Autowired
    protected LockService lockService;
    /**
     * 北森token缓存的过期时间
     */
    private static final Integer BEISEN_TOKEN_CACHE_EXPIRE_SECOND = 5400;

    /**
     * @description:(从北森获得token结果)
     * @return com.howbuy.crm.nt.beiseninfo.response.CmBeisenTokenResponse
     * @author: shijie.wang
     * @date: 2024/10/15 13:14
     * @since JDK 1.8
     */
    @Override
    public String getToken(){
        String beisenToken;
        try{
            beisenToken = CacheServiceImpl.getInstance().get(CacheConstants.BEISEN_TOKEN_CACHE);
        }catch (Exception e){
            beisenToken = getTokenByBeisen();
        }
        if(StringUtils.isEmpty(beisenToken)){
            return getTokenByBeisen();
        }
        return beisenToken;
    }

    /**
     * @description:(从北森获得token结果)
     * @return com.howbuy.crm.nt.beiseninfo.response.CmBeisenTokenResponse
     * @author: shijie.wang
     * @date: 2024/10/14 19:58
     * @since JDK 1.8
     */
    private String getTokenByBeisen() {
        CmBeisenTokenRequest request = new CmBeisenTokenRequest();
        request.setAppKey(cmBeisenConstant.getBeisenRequestKey());
        request.setAppSecret(cmBeisenConstant.getBeisenRequestSecret());
        CmBeisenRequest cmBeisenRequest = new CmBeisenRequest();
        cmBeisenRequest.setCmBeisenTokenRequest(request);
        cmBeisenRequest.setTokenUrl(cmBeisenConstant.getBeisenUrlPrefix() + CmBeisenConstant.BEISEN_TOKEN_URL);
        String accessToken = null;
        try{
            CmBeisenResponse result = cmBeisenTokenFacade.getBeisenTokenInfo(cmBeisenRequest);
            CmBeisenTokenResponse tokenResponse = result.getCmBeisenTokenResponse();
            if(Objects.isNull(tokenResponse) || StringUtils.isEmpty(tokenResponse.getAccess_token())){
                log.info("getTokenByPBeisen| tokenResponse is null");
                throw new BusinessException("9999", "tokenResponse is null");
            }
            CacheServiceImpl.getInstance().put(BEISEN_TOKEN_CACHE_EXPIRE_SECOND, CacheConstants.BEISEN_TOKEN_CACHE, tokenResponse.getAccess_token());
            accessToken = tokenResponse.getAccess_token();
        }catch (Exception e){
            log.error("getTokenByPBeisen| error:{}", e, e.getMessage());
            throw new BusinessException("9999", "tokenResponse is error");
        }
        return accessToken;
    }

}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.beiseninfo.dao.CmBeisenUserInfoMapper">
    <select id="selectMaxTime" resultType="java.util.Date">
        SELECT max(to_date(t.EXT35,'YYYY-MM-DD HH24:mi:ss')) ext35 from CM_BEISEN_USERINFO t
    </select>


    <insert id="saveCmBeisenUserInfo" parameterType="list">
        merge into cm_beisen_userinfo t1
        using (
        <foreach collection="list" item="item" index="index" separator="union">
            select
            #{item.ext2,jdbcType=VARCHAR} as ext2,
            #{item.ext3,jdbcType=VARCHAR} as ext3,
            #{item.ext4,jdbcType=VARCHAR} as ext4,
            #{item.ext5,jdbcType=VARCHAR} as ext5,
            #{item.ext6,jdbcType=VARCHAR} as ext6,
            #{item.ext7,jdbcType=VARCHAR} as ext7,
            #{item.ext8,jdbcType=VARCHAR} as ext8,
            #{item.ext9,jdbcType=VARCHAR} as ext9,
            #{item.ext10,jdbcType=VARCHAR} as ext10,
            #{item.ext11,jdbcType=VARCHAR} as ext11,
            #{item.ext12,jdbcType=VARCHAR} as ext12,
            #{item.ext13,jdbcType=VARCHAR} as ext13,
            #{item.ext14,jdbcType=VARCHAR} as ext14,
            #{item.ext15,jdbcType=VARCHAR} as ext15,
            #{item.ext16,jdbcType=VARCHAR} as ext16,
            #{item.ext17,jdbcType=VARCHAR} as ext17,
            #{item.ext18,jdbcType=VARCHAR} as ext18,
            #{item.ext19,jdbcType=VARCHAR} as ext19,
            #{item.ext20,jdbcType=VARCHAR} as ext20,
            #{item.ext21,jdbcType=VARCHAR} as ext21,
            #{item.ext22,jdbcType=VARCHAR} as ext22,
            #{item.ext23,jdbcType=VARCHAR} as ext23,
            #{item.ext24,jdbcType=VARCHAR} as ext24,
            #{item.ext25,jdbcType=VARCHAR} as ext25,
            #{item.ext26,jdbcType=VARCHAR} as ext26,
            #{item.ext27,jdbcType=VARCHAR} as ext27,
            #{item.ext28,jdbcType=VARCHAR} as ext28,
            #{item.ext29,jdbcType=VARCHAR} as ext29,
            #{item.ext30,jdbcType=VARCHAR} as ext30,
            #{item.ext31,jdbcType=VARCHAR} as ext31,
            #{item.ext32,jdbcType=VARCHAR} as ext32,
            #{item.ext33,jdbcType=VARCHAR} as ext33,
            #{item.ext34,jdbcType=VARCHAR} as ext34,
            #{item.ext35,jdbcType=VARCHAR} as ext35
            from dual</foreach>
        ) t2
        on (t1.ext2 = t2.ext2)
        when matched then
            update set
                t1.ext3 = t2.ext3,
                t1.ext4 = t2.ext4,
                t1.ext5 = t2.ext5,
                t1.ext6 = t2.ext6,
                t1.ext7 = t2.ext7,
                t1.ext8 = t2.ext8,
                t1.ext9 = t2.ext9,
                t1.ext10 = t2.ext10,
                t1.ext11 = t2.ext11,
                t1.ext12 = t2.ext12,
                t1.ext13 = t2.ext13,
                t1.ext14 = t2.ext14,
                t1.ext15 = t2.ext15,
                t1.ext16 = t2.ext16,
                t1.ext17 = t2.ext17,
                t1.ext18 = t2.ext18,
                t1.ext19 = t2.ext19,
                t1.ext20 = t2.ext20,
                t1.ext21 = t2.ext21,
                t1.ext22 = t2.ext22,
                t1.ext23 = t2.ext23,
                t1.ext24 = t2.ext24,
                t1.ext25 = t2.ext25,
                t1.ext26 = t2.ext26,
                t1.ext27 = t2.ext27,
                t1.ext28 = t2.ext28,
                t1.ext29 = t2.ext29,
                t1.ext30 = t2.ext30,
                t1.ext31 = t2.ext31,
                t1.ext32 = t2.ext32,
                t1.ext33 = t2.ext33,
                t1.ext34 = t2.ext34,
                t1.ext35 = t2.ext35,
                t1.modifier = 'AUTO',
                t1.moddt = sysdate
        when not matched then
        insert (id, ext2 ,ext3 ,ext4 ,ext5 ,ext6 ,ext7 ,ext8 ,ext9 ,ext10,ext11,ext12,ext13,ext14
        ,ext15,ext16,ext17,ext18,ext19,ext20,ext21,ext22,ext23,ext24,ext25,ext26,ext27
        ,ext28,ext29,ext30,ext31,ext32,ext33,ext34,ext35,creator,modifier,credt,moddt)
        values (CM_BEISEN_USERINFO_SEQ.nextval,t2.ext2,t2.ext3,t2.ext4,t2.ext5,t2.ext6,t2.ext7,t2.ext8,t2.ext9,t2.ext10,
            t2.ext11,t2.ext12,t2.ext13,t2.ext14,t2.ext15,t2.ext16,t2.ext17,t2.ext18,t2.ext19,t2.ext20,t2.ext21,t2.ext22,
            t2.ext23,t2.ext24,t2.ext25,t2.ext26,t2.ext27,t2.ext28,t2.ext29,t2.ext30,t2.ext31,t2.ext32,t2.ext33,t2.ext34,
            t2.ext35,'AUTO', 'AUTO', sysdate, sysdate)
    </insert>

</mapper>
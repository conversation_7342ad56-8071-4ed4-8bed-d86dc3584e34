/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo.service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.nt.beiseninfo.CmBeisenConstant;
import com.howbuy.crm.nt.beiseninfo.dao.CmBeisenOrgMapper;
import com.howbuy.crm.nt.beiseninfo.domain.CmBeisenOrgDO;
import com.howbuy.crm.util.DateTimeUtil;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenOrgFacade;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenRequest;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @description: (北森信息组织架构service)
 * <AUTHOR>
 * @date 2024/10/14 17:52
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmBeisenOrgServiceImpl implements CmBeisenOrgService {
    @Autowired
    private CmBeisenConstant cmBeisenConstant;
    @Autowired
    private CmBeisenOrgMapper cmBeisenOrgMapper;
    @Autowired
    private CmBeisenOrgFacade cmBeisenOrgFacade;

    /**
     * 架构数据返回的机构名称的key
     */
    private static final String ORG_DATA_NAME_KEY = "name";
    /**
     * 架构数据返回的机构ID的key
     */
    private static final String ORG_DATA_Id_KEY = "code";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getAndSaveBeisenOrg(String token) {
        String[] orgParamIds = getOrgParamIds();
        String orgDataStr = getOrgDataByPost(token, orgParamIds);
        if(StringUtils.isEmpty(orgDataStr)){
            log.info("getAndSaveBeisenOrg| orgDataStr is null");
            throw new BusinessException("9999", "北森机构数据为空");
        }
        List<CmBeisenOrgDO> orgList = analysisOrgResponse(orgDataStr, orgParamIds);
        saveOrgData(orgList);
    }

    /**
     * @description:(post方式获得机构数据)
     * @param token
     * @param orgParamIds
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/15 18:29
     * @since JDK 1.8
     */
    private String getOrgDataByPost(String token, String[] orgParamIds){
        CmBeisenResponse response = null;
        try{
            CmBeisenRequest cmBeisenRequest = new CmBeisenRequest();
            cmBeisenRequest.setOrgUrl(cmBeisenConstant.getBeisenUrlPrefix()+CmBeisenConstant.BEISEN_ORG_URL);
            cmBeisenRequest.setOrgParamIds(orgParamIds);
            cmBeisenRequest.setHeadKey(CmBeisenConstant.HEADERS_KEY);
            cmBeisenRequest.setHeadValue(CmBeisenConstant.HEADERS_AUTHORIZATION+token);
            response = cmBeisenOrgFacade.getBeisenOrg(cmBeisenRequest);
            if(Objects.isNull(response)){
                log.info("getOrgDataByPost| response is null");
                throw new BusinessException("9999", "北森机构数据返回为空");
            }
            if(CmBeisenConstant.CALL_OUTER_SYSTEM_ERROR.equals(response.getReturnCode())){
                String messge = JSON.toJSONString(cmBeisenRequest);
                log.info("refreshData|北森机构数据接口返回异常， cmBeisenRequest:{}, message:{}  ", messge,
                        response.getDescription());
            }
        }catch (Exception e){
            log.info("getOrgDataByPost| error:{}", e, e.getMessage());
            throw new BusinessException("9999", "北森机构数据接口异常");
        }

        return response.getResponse();
    }

    /**
     * @description:(获取机构ID参数)
     * @param
     * @return java.lang.String[]
     * @author: shijie.wang
     * @date: 2024/10/15 17:45
     * @since JDK 1.8
     */
    private String[] getOrgParamIds(){
        //机构ID
        String orgIdStr = cmBeisenConstant.getBeisenOrgParams();
        if(StringUtils.isEmpty(orgIdStr)){
            throw new BusinessException("9999", "getOrgParamIds| 获取北森数据机构ID参数为空");
        }
        return orgIdStr.split(",");
    }

    /**
     * @description:(解析机构数据)
     * @param orgDataStr
     * @param orgParamIds
     * @return java.util.List<com.howbuy.crm.nt.beiseninfo.domain.CmBeisenOrgDO>
     * @author: shijie.wang
     * @date: 2024/10/15 19:41
     * @since JDK 1.8
     */
    private List<CmBeisenOrgDO> analysisOrgResponse(String orgDataStr, String[] orgParamIds){
        JSONObject response = JSON.parseObject(orgDataStr);
        JSONObject data = response.getJSONObject("data");
        if(Objects.isNull(data)){
            throw new BusinessException("9999", "analysisOrgResponse| 北森机构数据为空");
        }
        List<CmBeisenOrgDO> orgList = new ArrayList<>();
        for(String orgId : orgParamIds){
            orgList.addAll(getOrgData(data.getJSONArray(orgId)));
        }
        return orgList;
    }

    /**
     * @description:(获得机构数据)
     * @param orgArrays
     * @return java.util.List<com.howbuy.crm.nt.beiseninfo.domain.CmBeisenOrgDO>
     * @author: shijie.wang
     * @date: 2024/10/15 19:40
     * @since JDK 1.8
     */
    private List<CmBeisenOrgDO> getOrgData(JSONArray orgArrays){
        if(Objects.isNull(orgArrays)){
            throw new BusinessException("9999", "getOrgData| 北森机构数据数组为空");
        }
        List<CmBeisenOrgDO> orgList = new ArrayList<>();
        for(int i=0; i < orgArrays.size(); i++){
            JSONObject arrayData = orgArrays.getJSONObject(i);
            Object orgId = arrayData.get(ORG_DATA_Id_KEY);
            Object orgName = arrayData.get(ORG_DATA_NAME_KEY);
            if(Objects.isNull(orgId) || Objects.isNull(orgName)){
                continue;
            }
            CmBeisenOrgDO orgDO = new CmBeisenOrgDO();
            orgDO.setOrgIdBeisen(orgId.toString());
            orgDO.setOrgNameBeisen(orgName.toString());
            orgList.add(orgDO);
        }
        return orgList;
    }

    /**
     * @description:(保存数据)
     * @param orgList
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/16 9:51
     * @since JDK 1.8
     */
    private void saveOrgData(List<CmBeisenOrgDO> orgList){
        if(CollectionUtils.isEmpty(orgList)){
            log.info("saveOrgData| orgList is null");
            return;
        }
        try{
            Date nowTime = DateTimeUtil.getDate(new Date());
            cmBeisenOrgMapper.saveCmBeisenOrg(orgList, nowTime);
            log.info("saveOrgData| save orgData success| size:{}", orgList.size());
            cmBeisenOrgMapper.cleanCmBeisenOrgByDataTime(nowTime);
        }catch (Exception e){
            log.error("saveOrgData| error:{}", e, e.getMessage());
            throw new BusinessException("9999", "saveOrgData| 保存北森机构数据异常");
        }
    }

}
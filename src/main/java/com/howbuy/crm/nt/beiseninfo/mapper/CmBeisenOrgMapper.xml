<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.beiseninfo.dao.CmBeisenOrgMapper">
    <insert id="saveCmBeisenOrg" parameterType="list">
        merge into cm_beisen_org t1
        using (
        <foreach collection="list" item="item" index="index" separator="union">
            select
            #{item.orgIdBeisen,jdbcType=VARCHAR} as org_id_beisen,
            #{item.orgNameBeisen,jdbcType=VARCHAR} as org_name_beisen
            from dual</foreach>
        ) t2
        on (t1.org_id_beisen = t2.org_id_beisen)
        when matched then
            update set
                t1.org_name_beisen = t2.org_name_beisen,
                t1.modifier = 'AUTO',
                t1.moddt = #{nowTime}
        when not matched then
        insert (id, org_id_beisen, org_name_beisen, creator, modifier, credt, moddt)
        values (CM_BEISEN_ORG_SEQ.nextval, t2.org_id_beisen, t2.org_name_beisen, 'AUTO', 'AUTO', #{nowTime}, #{nowTime})
    </insert>

    <delete id="cleanCmBeisenOrgByDataTime" parameterType="Date">
        delete from cm_beisen_org where moddt &lt; #{nowTime}
    </delete>


</mapper>
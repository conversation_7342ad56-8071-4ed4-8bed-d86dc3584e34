/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * @description: (北森常量)
 * <AUTHOR>
 * @date 2024/10/14 18:17
 * @since JDK 1.8
 */
@Component
public class CmBeisenConstant {
    /**
     * 北森请求key
     */
    @Value("${BEISEN_REQUEST_KEY}")
    private String BEISEN_REQUEST_KEY;

    /**
     * 北森请求密钥
     */
    @Value("${BEISEN_REQUEST_SECRET}")
    private String BEISEN_REQUEST_SECRET;

    /**
     * 北森机构信息参数：多个机构以都好分隔（ 1782862,1782885 ）
     */
    @Value("${BEISEN_ORG_PARAMS}")
    private String BEISEN_ORG_PARAMS;

    /**
     * 北森url访问路径前缀
     */
    @Value("${BEISEN_URL_PREFIX}")
    private String BEISEN_URL_PREFIX;

    /**
     * 北森token接口地址
     */
    public static final String BEISEN_TOKEN_URL = "/token";

    /**
     * 北森机构信息接口地址
     */
    public static final String BEISEN_ORG_URL = "/TenantBaseExternal/api/v5/Organization/GetSubOrganizationTrees";


    /**
     * 北森报表刷新接口地址
     */
    public static final String BEISEN_REPORT_REFRESH_URL = "/Ocean/api/v2/Reports/Refresh?reportId=729743a2-d443-4141-b1e2-4deacbf2f789";

    /**
     * 北森报表状态接口地址
     */
    public static final String BEISEN_REPORT_STATUS_URL = "/Ocean/api/v2/Reports/ReportDataStatus?reportId=729743a2-d443-4141-b1e2-4deacbf2f789";

    /**
     * 北森报表数据接口地址
     */
    public static final String BEISEN_REPORT_DATA_URL = "/Ocean/api/v2/Reports/GridData?reportId=729743a2-d443-4141-b1e2-4deacbf2f789";

    /**
     * 请求头key
     */
    public static final String HEADERS_KEY = "Authorization";
    /**
     * 请求头数据
     */
    public static final String HEADERS_AUTHORIZATION ="Bearer ";
    /**
     * 调用外部接口异常
     */
    public static final String CALL_OUTER_SYSTEM_ERROR = "Z8100002";

    /**
     * 日期格式
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public String getBeisenRequestKey() {
        return BEISEN_REQUEST_KEY;
    }

    public String getBeisenRequestSecret() {
        return BEISEN_REQUEST_SECRET;
    }

    public String getBeisenOrgParams() {
        return BEISEN_ORG_PARAMS;
    }

    public String getBeisenUrlPrefix() {
        return BEISEN_URL_PREFIX;
    }
}
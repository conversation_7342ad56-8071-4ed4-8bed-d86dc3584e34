/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.nt.outerservice.beisen.CmSyncBeisenOuterService;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: (北森信息获取service)
 * <AUTHOR>
 * @date 2024/10/14 17:51
 * @since JDK 1.8
 */
@Slf4j
@Service("cmBeisenServiceImpl")
public class CmBeisenServiceImpl implements CmBeisenService {
    @Autowired
    private CmBeisenTokenService cmBeisenTokenService;
    @Autowired
    private CmBeisenOrgService cmBeisenOrgService;
    @Autowired
    private CmBeisenUserInfoService cmBeisenUserInfoService;
    @Autowired
    private CmSyncBeisenOuterService cmSyncBeisenOuterService;
    @Autowired
    private CmPushMsgService cmPushMsgService;

    /**
     * 北森同步异常消息码
     */
    public static final String BEISEN_ERROR_MESSAGE_ID="201861";

    @Override
    public void queryBeisenInfo() {
        try{
            String beisenToken = getAccessToken();
            if(StringUtils.isEmpty(beisenToken)){
                log.info("getBeisenInfo| beisenToken is null");
                throw new BusinessException("9999", "getBeisenInfo| beisenToken is null");
            }
            cmBeisenOrgService.getAndSaveBeisenOrg(beisenToken);
            cmBeisenUserInfoService.getAndSaveBeisenUserInfo(beisenToken);
        }catch (Exception e){
            log.error("getBeisenInfo| error",e, e.getMessage());
            throw new BusinessException("9999", "getBeisenInfo| error",e);
        }
    }

    @Override
    public void execSyncBeisenToCrmByJobFacade() {
        List<String> errorUserIdList = cmSyncBeisenOuterService.execSyncBeisenToCrmByJobFacade();
        pushFailMsgByBeisen(errorUserIdList);
    }

    /**
     * @description:(获得token)
     * @param
     * @return com.howbuy.crm.nt.beiseninfo.response.CmBeisenTokenResponse
     * @author: shijie.wang
     * @date: 2024/10/15 13:18
     * @since JDK 1.8
     */
    private String getAccessToken(){
        return cmBeisenTokenService.getToken();
    }



    /**
     * @description:(北森异常消息提示)
     * @param userIdList
     * @return void
     * @author: shijie.wang
     * @date: 2024/11/14 10:27
     * @since JDK 1.8
     */
    private void pushFailMsgByBeisen(List<String> userIdList){
        if(CollectionUtils.isEmpty(userIdList)){
            log.info("pushFailMsgByBeisen| userIdList is null");
            return;
        }
        log.info("pushFailMsgByBeisen| userIdList:{} is error! ", userIdList.toString());
        CmPushMsgRequest request = new CmPushMsgRequest();
        //发送给中心绩效负责人、中心员工关系岗、总部绩效岗A角、总部绩效岗B角
        List<String> roleCodeList = MessageRoleEnum.getMessageRoleCode();
        Map<String, String> paramMap = new HashMap<>();
        BaseResponse response = cmPushMsgService.pushMsgByRoleCodeList(BEISEN_ERROR_MESSAGE_ID,
                roleCodeList, paramMap);
        if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
            log.info("处理完成后发送消息:" + JSON.toJSONString(request));
        } else {
            log.info("处理完成后发送消息失败，失败原因:{}", response == null ? "返回值为null" : response.getDescription());
        }
    }

    /**
     * @description: (消息角色枚举)
     * <AUTHOR>
     * @date 2024/10/31 15:17
     * @since JDK 1.8
     */
     enum MessageRoleEnum {
        MESSAGE_ROLE_1("ROLE_IC_KPI_NEW", "中心绩效负责人"),
        MESSAGE_ROLE_2("ROLE_IC_KPI_A", "总部绩效岗A角"),
        MESSAGE_ROLE_3("ROLE_SALE_CONSRELATION", "中心员工关系岗"),
        MESSAGE_ROLE_4("ROLE_IC_KPI_B", "总部绩效岗B角");

        private String code;
        private String name;

        MessageRoleEnum(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        /**
         * @param
         * @return java.util.List<java.lang.String>
         * @description:(返回所有枚举数据)
         * @author: shijie.wang
         * @date: 2024/11/14 9:52
         * @since JDK 1.8
         */
        public static List<String> getMessageRoleCode() {
            return Arrays.stream(MessageRoleEnum.values()).map(MessageRoleEnum::getCode).collect(Collectors.toList());
        }
    }
}
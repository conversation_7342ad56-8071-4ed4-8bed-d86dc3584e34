/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo.service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.nt.beiseninfo.CmBeisenConstant;
import com.howbuy.crm.nt.beiseninfo.dao.CmBeisenUserInfoMapper;
import com.howbuy.crm.nt.beiseninfo.domain.CmBeisenUserInfoDO;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenRequest;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenResponse;
import com.howbuy.gateway.captcha.facade.beisen.CmBeisenUserInfoFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/14 17:54
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CmBeisenUserInfoServiceImpl implements CmBeisenUserInfoService {
    @Autowired
    private CmBeisenUserInfoFacade cmBeisenUserInfoFacade;
    @Autowired
    private CmBeisenConstant cmBeisenConstant;
    @Autowired
    private CmBeisenUserInfoMapper cmBeisenUserInfoMapper;

    /**
     * 用户刷新接口返回的code的key
     */
    private static final String USERINFO_REFRESH_CODE_KEY = "code";
    /**
     * 用户刷新接口返回的message的key
     */
    private static final String USERINFO_REFRESH_MESSAGE_KEY = "message";
    /**
     * 用户异步状态接口返回的message的key
     */
    private static final String USERINFO_ASYNC_STATUS_KEY = "data";
    /**
     * 正常返回码
     */
    private static final String SUCCESS_CODE ="200";
    /**
     * 正常返回值
     */
    private static final String SUCCESS_VALUE ="成功";
    /**
     * 异步状态生成完成返回值
     */
    private static final String ASYNC_STATUS_VALUE ="0";
    /**
     * 页码连接符
     */
    private static final String PAGE_SYMBOL = "&page=";
    /**
     * 页数连接符
     */
    private static final String PAGE_SIZE_SYMBOL = "&pageSize=";
    /**
     * 用户数据接口返回的data
     */
    private static final String USERINFO_DATA_KEY = "data";
    /**
     * 用户数据接口返回的datas
     */
    private static final String USERINFO_DATAS_KEY = "datas";
    /**
     * 分页最大页数（阈值）
     */
    private static final int PAGE_MAX_THRESHOLD = 100;

    @Override
    public void getAndSaveBeisenUserInfo(String token) {
        CmBeisenRequest cmBeisenRequest = new CmBeisenRequest();
        cmBeisenRequest.setHeadKey(CmBeisenConstant.HEADERS_KEY);
        cmBeisenRequest.setHeadValue(CmBeisenConstant.HEADERS_AUTHORIZATION+token);
        refreshData(cmBeisenRequest);
        asyncStatus(cmBeisenRequest);
        getUserInfoData(cmBeisenRequest);
    }

    /**
     * 刷新接口执行
     * @param cmBeisenRequest
     */
    private void refreshData(CmBeisenRequest cmBeisenRequest){
        cmBeisenRequest.setUserInfoRefreshUrl(cmBeisenConstant.getBeisenUrlPrefix()+CmBeisenConstant.BEISEN_REPORT_REFRESH_URL);
        CmBeisenResponse response = cmBeisenUserInfoFacade.refreshData(cmBeisenRequest);
        checkResponse(response);
        String message = JSON.toJSONString(cmBeisenRequest);
        if(CmBeisenConstant.CALL_OUTER_SYSTEM_ERROR.equals(response.getReturnCode())){
            log.info("refreshData|北森用户刷新接口返回异常， cmBeisenRequest:{}, message:{}  ", message,
                    response.getDescription());
            throw new BusinessException("9999", "refreshData|北森用户刷新接口返回异常， cmBeisenRequest is " + message);
        }
        String refreshMessage = analysisRefreshResponse(response.getResponse());
        if(!SUCCESS_VALUE.equals(refreshMessage)){
            log.error("getAndSaveBeisenUserInfo|刷新北森用户信息数据返回， cmBeisenRequest:{}, message:{}  ", message, refreshMessage);
            throw new BusinessException("9999", "getAndSaveBeisenUserInfo|刷新北森用户信息数据返回， cmBeisenRequest is " + message);
        }
    }

    /**
     * @description:(异步状态接口执行，状态获取最多3次)
     * @param cmBeisenRequest
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/23 18:31
     * @since JDK 1.8
     */
    private void asyncStatus(CmBeisenRequest cmBeisenRequest){
        Boolean status = false;
        cmBeisenRequest.setUserInfoAsyncStatusUrl(cmBeisenConstant.getBeisenUrlPrefix()+CmBeisenConstant.BEISEN_REPORT_STATUS_URL);
        //等待用户报表生成状态最多执行3次
        for(int i = 0; i < 3; i++){
            status = asyncStatusData(cmBeisenRequest);
            if(Boolean.TRUE.equals(status)){
                break;
            }
            log.info("数据还未生成等待30秒再执行");
            try {
                Thread.sleep(30000);
            }
            catch (InterruptedException e) {
                log.info("asyncStatus|等待用户报表生成状态线程异常");
            }
        }
        if(Boolean.FALSE.equals(status)){
            log.info("asyncStatus| 等待用户报表生成状态已执行3次，但还是未成功， url:{} ", cmBeisenRequest.getUserInfoAsyncStatusUrl());
            throw new BusinessException("9999",  "asyncStatus|等待用户报表生成状态已执行3次，但还是未成功");
        }
    }


    /**
     * @description:(异步状态接口数据)
     * @param cmBeisenRequest
     * @return java.lang.Boolean
     * @author: shijie.wang
     * @date: 2024/10/23 18:31
     * @since JDK 1.8
     */
    private Boolean asyncStatusData(CmBeisenRequest cmBeisenRequest){
        String statusData = null;
        try {
            CmBeisenResponse response = cmBeisenUserInfoFacade.asyncStatus(cmBeisenRequest);
            checkResponse(response);
            if (CmBeisenConstant.CALL_OUTER_SYSTEM_ERROR.equals(response.getReturnCode())) {
                String message = JSON.toJSONString(cmBeisenRequest);
                log.info("refreshData|北森用户刷新接口返回异常， cmBeisenRequest:{}, message:{}  ", message,
                        response.getDescription());
                throw new BusinessException("9999", "refreshData|北森用户刷新接口返回异常， url is " + cmBeisenRequest.getUserInfoAsyncStatusUrl());
            }
            String resposeStr = response.getResponse();
            String refreshMessage = analysisRefreshResponse(resposeStr);
            if (!SUCCESS_VALUE.equals(refreshMessage)) {
                log.error("getAndSaveBeisenUserInfo|获取北森用户信息异步状态返回异常， refreshUrl:{}, message:{}  ", resposeStr, refreshMessage);
                throw new BusinessException("9999", "getAndSaveBeisenUserInfo|获取北森用户信息异步状态返回异常， refreshUrl is " + resposeStr);
            }
            statusData = JSON.parseObject(resposeStr).getString(USERINFO_ASYNC_STATUS_KEY);
        }catch (Exception e){
            log.error("getAndSaveBeisenUserInfo|获取北森用户信息异步状态返回异常", e, e.getMessage());
            throw new BusinessException("9999", "getAndSaveBeisenUserInfo|获取北森用户信息异步状态返回异常");
        }
        return ASYNC_STATUS_VALUE.equals(statusData);
    }

    /**
     * @description:(获得用户接口数据)
     * @param cmBeisenRequest
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/22 14:14
     * @since JDK 1.8
     */
    private void getUserInfoData(CmBeisenRequest cmBeisenRequest){
        int page = 1;
        int pageSize = 100;
        try{
            Date maxTime = cmBeisenUserInfoMapper.selectMaxTime();
            while(true){
                cmBeisenRequest.setUserInfoDataUrl(getUserInfoUrl(page, pageSize));
                CmBeisenResponse response = cmBeisenUserInfoFacade.getData(cmBeisenRequest);
                checkResponse(response);
                String message = JSON.toJSONString(cmBeisenRequest);
                if(CmBeisenConstant.CALL_OUTER_SYSTEM_ERROR.equals(response.getReturnCode())){
                    log.info("refreshData|北森用户数据接口返回异常， cmBeisenRequest:{}, message:{}  ", message,
                            response.getDescription());
                    throw new BusinessException("9999", "refreshData|北森用户数据接口返回异常， url is " + cmBeisenRequest.getUserInfoDataUrl());
                }
                List<CmBeisenUserInfoDO> userInfoList = analysisDataResponse(response.getResponse());
                if(CollectionUtils.isEmpty(userInfoList) || PAGE_MAX_THRESHOLD <= page){
                    break;
                }
                saveAndFilterBeisenUserInfo(userInfoList, maxTime);
                page++;
            }
            log.info("getAndSaveBeisenUserInfo|北森用户数据入库完成， 获取北森总数:{}", page*pageSize);
        }catch (Exception e){
            log.error("getAndSaveBeisenUserInfo|获取北森用户数据接口异常 error:{}", e, e.getMessage());
            throw new BusinessException("9999", "getAndSaveBeisenUserInfo|获取北森用户数据接口异常");
        }
    }

    /**
     * @description:(校验返回值)
     * @param response
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/22 10:42
     * @since JDK 1.8
     */
    private void checkResponse(CmBeisenResponse response){
        if(Objects.isNull(response)){
            log.info("refreshData| response is null");
            throw new BusinessException("9999", "北森用户刷新接口为空");
        }
    }

    /**
     * 返回用户数据url
     * @param page
     * @param pageSize
     * @return
     */
    private String getUserInfoUrl(int page, int pageSize){
        //&page=1&pageSize=100
        return cmBeisenConstant.getBeisenUrlPrefix()+CmBeisenConstant.BEISEN_REPORT_DATA_URL+PAGE_SYMBOL+page+PAGE_SIZE_SYMBOL+pageSize;
    }

    /**
     * @description:(解析刷新数据接口返回值)
     * @param resposeStr
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/17 15:47
     * @since JDK 1.8
     */
    private String analysisRefreshResponse(String resposeStr){
        if(StringUtils.isEmpty(resposeStr)){
            throw new BusinessException("9999", "analysisRefreshResponse|北森用户信息数据返回为空，resposeStr is null");
        }
        JSONObject response = JSON.parseObject(resposeStr);
        String code = response.getString(USERINFO_REFRESH_CODE_KEY);
        if(StringUtils.isEmpty(code)){
            throw new BusinessException("9999", "analysisRefreshResponse|北森用户信息数据返回code为空，code is null");
        }
        if(SUCCESS_CODE.equals(code)){
            return SUCCESS_VALUE;
        }
        return response.getString(USERINFO_REFRESH_MESSAGE_KEY);
    }

    /**
     * @description:(解析数据接口返回值)
     * @param resposeStr
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/17 15:47
     * @since JDK 1.8
     */
    private List<CmBeisenUserInfoDO> analysisDataResponse(String resposeStr){
        if(StringUtils.isEmpty(resposeStr)){
            throw new BusinessException("9999", "analysisRefreshResponse|北森用户信息数据返回为空，resposeStr is null");
        }
        JSONObject response = JSON.parseObject(resposeStr);
        String code = response.getString(USERINFO_REFRESH_CODE_KEY);
        if(StringUtils.isEmpty(code)){
            throw new BusinessException("9999", "analysisRefreshResponse|北森用户信息数据返回code为空，code is null");
        }
        if(!SUCCESS_CODE.equals(code)){
            log.info("analysisDataResponse| code:{}, message:{}", code, response.getString(USERINFO_REFRESH_MESSAGE_KEY));
            throw new BusinessException("9999", "analysisRefreshResponse|北森用户信息数据返回code异常，code is"+code);
        }
        JSONObject data = response.getJSONObject(USERINFO_DATA_KEY);
        if(Objects.isNull(data)){
            log.info("analysisDataResponse|datas is null");
            return Collections.emptyList();
        }
        return getDatas(data.getJSONArray(USERINFO_DATAS_KEY));
    }

    /**
     * @description:(解析北森用户数据)
     * @param datasArray
     * @return java.util.List<com.howbuy.crm.nt.beiseninfo.domain.CmBeisenUserInfoDO>
     * @author: shijie.wang
     * @date: 2024/10/22 13:26
     * @since JDK 1.8
     */
    private List<CmBeisenUserInfoDO> getDatas(JSONArray datasArray){
        List<CmBeisenUserInfoDO> saveList = new ArrayList<>();
        for(int i=0; i<datasArray.size(); i++){
            JSONObject userInfoJson = datasArray.getJSONObject(i);
            CmBeisenUserInfoDO userInfoDO = new CmBeisenUserInfoDO();
            userInfoDO.setExt2(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT2));
            userInfoDO.setExt3(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT3));
            userInfoDO.setExt4(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT4));
            userInfoDO.setExt5(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT5));
            userInfoDO.setExt6(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT6));
            userInfoDO.setExt7(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT7));
            userInfoDO.setExt8(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT8));
            userInfoDO.setExt9(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT9));
            userInfoDO.setExt10(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT10));
            userInfoDO.setExt11(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT11));
            userInfoDO.setExt12(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT12));
            userInfoDO.setExt13(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT13));
            userInfoDO.setExt14(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT14));
            userInfoDO.setExt15(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT15));
            userInfoDO.setExt16(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT16));
            userInfoDO.setExt17(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT17));
            userInfoDO.setExt18(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT18));
            userInfoDO.setExt19(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT19));
            userInfoDO.setExt20(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT20));
            userInfoDO.setExt21(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT21));
            userInfoDO.setExt22(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT22));
            userInfoDO.setExt23(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT23));
            userInfoDO.setExt24(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT24));
            userInfoDO.setExt25(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT25));
            userInfoDO.setExt26(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT26));
            userInfoDO.setExt27(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT27));
            userInfoDO.setExt28(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT28));
            userInfoDO.setExt29(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT29));
            userInfoDO.setExt30(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT30));
            userInfoDO.setExt31(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT31));
            userInfoDO.setExt32(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT32));
            userInfoDO.setExt33(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT33));
            userInfoDO.setExt34(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT34));
            userInfoDO.setExt35(getUserInfoValue(userInfoJson, BeisenUserInfoEnum.EXT35));
            saveList.add(userInfoDO);
        }
        return saveList;
    }

    /**
     * @description:(保存和过滤北森用户数据)
     * @param list
     * @param maxTime
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/23 17:20
     * @since JDK 1.8
     */
    private void saveAndFilterBeisenUserInfo(List<CmBeisenUserInfoDO> list, Date maxTime){
        List<CmBeisenUserInfoDO> saveList = list.stream().filter(info -> {
            LocalDateTime updateTimeLocalDateTime = LocalDateTime.parse(info.getExt35(), DateTimeFormatter.ofPattern(CmBeisenConstant.DATE_TIME_FORMAT));
            LocalDateTime maxTimeLocalDateTime = maxTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            //北森更新时间必须大于表中最大更新时间
            if(!Objects.isNull(maxTime) && updateTimeLocalDateTime.isBefore(maxTimeLocalDateTime)){
                return false;
            }
            if(!Objects.isNull(maxTime) && updateTimeLocalDateTime.equals(maxTimeLocalDateTime)){
                return false;
            }
            if(StringUtils.isEmpty(info.getExt2())){
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saveList)){
            log.info("saveCmBeisenUserInfo| no data to save");
            return;
        }
        cmBeisenUserInfoMapper.saveCmBeisenUserInfo(saveList);
    }

    /**
     * @description:(获得用户信息值)
     * @param userInfoJson
     * @param beisenUserInfoEnum
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/10/22 13:23
     * @since JDK 1.8
     */
    private String getUserInfoValue(JSONObject userInfoJson, BeisenUserInfoEnum beisenUserInfoEnum){
        Object value = userInfoJson.get(beisenUserInfoEnum.key);
        return Objects.isNull(value) ? null : value.toString();
    }

    /**
     * 北森用户信息枚举
     */
    enum BeisenUserInfoEnum{
        EXT2("ext2"),
        EXT3("ext3"),
        EXT4("ext4"),
        EXT5("ext5"),
        EXT6("ext6"),
        EXT7("ext7"),
        EXT8("ext8"),
        EXT9("ext9"),
        EXT10("ext10"),
        EXT11("ext11"),
        EXT12("ext12"),
        EXT13("ext13"),
        EXT14("ext14"),
        EXT15("ext15"),
        EXT16("ext16"),
        EXT17("ext17"),
        EXT18("ext18"),
        EXT19("ext19"),
        EXT20("ext20"),
        EXT21("ext21"),
        EXT22("ext22"),
        EXT23("ext23"),
        EXT24("ext24"),
        EXT25("ext25"),
        EXT26("ext26"),
        EXT27("ext27"),
        EXT28("ext28"),
        EXT29("ext29"),
        EXT30("ext30"),
        EXT31("ext31"),
        EXT32("ext32"),
        EXT33("ext33"),
        EXT34("ext34"),
        EXT35("ext35");

        private String key;

        BeisenUserInfoEnum(String key){
            this.key=key;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

    }
}
/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.beiseninfo.domain;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/17 15:04
 * @since JDK 1.8
 */
@Data
public abstract class CmCommonDO {
    /**
     * 主键ID
     */
    private long id;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Timestamp credt;
    /**
     * 修改时间
     */
    private Timestamp moddt;



}
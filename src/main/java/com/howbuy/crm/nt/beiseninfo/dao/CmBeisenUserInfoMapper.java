package com.howbuy.crm.nt.beiseninfo.dao;

import com.howbuy.crm.nt.beiseninfo.domain.CmBeisenUserInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: (北森信息组织架构mapper)
 * <AUTHOR>
 * @date 2024/10/14 17:52
 * @since JDK 1.8
 */
public interface CmBeisenUserInfoMapper {
    /**
     * 获得表中最大日期
     * @return
     */
    Date selectMaxTime();

    /**
     * @description:(保存组织架构)
     * @param list
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/16 9:37
     * @since JDK 1.8
     */
    void saveCmBeisenUserInfo(@Param("list") List<CmBeisenUserInfoDO> list);
}
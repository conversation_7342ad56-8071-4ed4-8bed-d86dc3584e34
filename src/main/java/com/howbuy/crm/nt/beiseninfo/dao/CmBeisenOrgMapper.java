package com.howbuy.crm.nt.beiseninfo.dao;

import com.howbuy.crm.nt.beiseninfo.domain.CmBeisenOrgDO;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @description: (北森信息组织架构mapper)
 * <AUTHOR>
 * @date 2024/10/14 17:52
 * @since JDK 1.8
 */
public interface CmBeisenOrgMapper {
    /**
     * @description:(保存组织架构)
     * @param list
     * @param nowTime
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/16 9:37
     * @since JDK 1.8
     */
    void saveCmBeisenOrg(@Param("list") List<CmBeisenOrgDO> list, @Param("nowTime") Date nowTime);

    /**
     * @description:(删除小于指定时间的数据)
     * @param nowTime
     * @return void
     * @author: shijie.wang
     * @date: 2024/10/17 13:59
     * @since JDK 1.8
     */
    void cleanCmBeisenOrgByDataTime(@Param("nowTime") Date nowTime);

}
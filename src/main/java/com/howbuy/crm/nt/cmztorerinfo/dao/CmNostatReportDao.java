package com.howbuy.crm.nt.cmztorerinfo.dao;

import org.apache.ibatis.annotations.Param;

/**
 * @description:(CM_NOSTAT_REPORT 表的dao层操作)
 * @param null
 * @return
 * @author: xufanchao
 * @date: 2023/8/15 15:41
 * @since JDK 1.8
 */
public interface CmNostatReportDao {

    /**
     * @description:(根据基金代码,投顾号，交易类型备份数据)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 15:39
     * @since JDK 1.8
     */
    int insertBakData(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码，投顾号，交易类型，找出金额最大时间最早的一条预约进行备份)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 15:41
     * @since JDK 1.8
     */
    int insertbakdataByMaxDt(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码,投顾号，交易类型删除数据)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:58
     * @since JDK 1.8
     */
    void deleteCmReport(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码，投顾号，交易类型，找出金额最大时间最早的一条预约进行备份) )
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 15:58
     * @since JDK 1.8
     */
    int insertByLogParam(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码,投顾号，交易类型查询数据)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 15:59
     * @since JDK 1.8
     */
    int selectCountByParams(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码，投顾号，交易类型，查询黑名单的数量)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 16:00
     * @since JDK 1.8
     */
    int selectNostatReportCountByParams(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);


    /**
     * @description:(根据基金代码，投顾号，交易类型，查询在预约表里面的数量) )
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 16:01
     * @since JDK 1.8
     */
    int selectCountBlackpre(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(根据基金代码，投顾号，交易类型 查询是否有打款到账的预约)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 16:01
     * @since JDK 1.8
     */
    int selectpreNumByPay(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(把非交易打款的数据插入到黑名单表中)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 16:02
     * @since JDK 1.8
     */
    int inserNoStatBypay(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

    /**
     * @description:(没有打款到账的单子 插入所有相关预约到黑名单表中)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 16:03
     * @since JDK 1.8
     */
    int inserNoStatByNopay(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);


    /**
     * @description:(找出金额最大时间最早的一条预约在黑名单中删除)
     * @param fundcode
     * @param custno
     * @param tradeType
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 16:03
     * @since JDK 1.8
     */
    void deleteCmReportByMaxDt(@Param("fundcode") String fundcode, @Param("custno") String custno, @Param("tradeType") String tradeType);

}
package com.howbuy.crm.nt.cmztorerinfo.dao;

import com.howbuy.crm.nt.cmztorerinfo.dto.CmZtOrderinfoLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmZtOrderinfoLogDao {


    /**
     * @description:(查询中台订单数据变更流水表 的数据)
     * @param
     * @return java.util.List<com.howbuy.crm.nt.cmztorerinfo.dto.CmZtOrderinfoLog>
     * @author: xufanchao
     * @date: 2023/8/15 14:28
     * @since JDK 1.8
     */
    List<CmZtOrderinfoLogVo> getCmZtOrderInfoList();


    /**
     * @description:(更新日志)
     * @param id
     * @return int
     * @author: xufanchao
     * @date: 2023/8/15 14:29
     * @since JDK 1.8
     */
    int updateZtOrderLog(@Param("id") Long id);
}
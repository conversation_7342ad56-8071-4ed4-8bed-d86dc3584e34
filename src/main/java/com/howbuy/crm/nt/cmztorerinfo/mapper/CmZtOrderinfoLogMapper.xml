<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.cmztorerinfo.dao.CmZtOrderinfoLogDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.cmztorerinfo.dto.CmZtOrderinfoLogVo">
    <!--@mbg.generated-->
    <!--@Table CM_ZT_ORDERINFO_LOG-->
    <id column="ID" jdbcType="DECIMAL" property="id" />
    <result column="DEALNO" jdbcType="VARCHAR" property="dealno" />
    <result column="TXACCTNO" jdbcType="VARCHAR" property="txacctno" />
    <result column="APPOINTMENTDEALNO" jdbcType="VARCHAR" property="appointmentdealno" />
    <result column="CUSTNAME" jdbcType="VARCHAR" property="custname" />
    <result column="MBUSICODE" jdbcType="VARCHAR" property="mbusicode" />
    <result column="PRODUCTNAME" jdbcType="VARCHAR" property="productname" />
    <result column="PRODUCTCODE" jdbcType="VARCHAR" property="productcode" />
    <result column="PAYMENTTYPE" jdbcType="VARCHAR" property="paymenttype" />
    <result column="APPAMT" jdbcType="DECIMAL" property="appamt" />
    <result column="APPVOL" jdbcType="DECIMAL" property="appvol" />
    <result column="FEE" jdbcType="DECIMAL" property="fee" />
    <result column="DISCOUNTRATE" jdbcType="DECIMAL" property="discountrate" />
    <result column="PAYENDDATE" jdbcType="TIMESTAMP" property="payenddate" />
    <result column="APPDATE" jdbcType="VARCHAR" property="appdate" />
    <result column="APPTIME" jdbcType="VARCHAR" property="apptime" />
    <result column="TXPMTFLAG" jdbcType="VARCHAR" property="txpmtflag" />
    <result column="PMTCOMPFLAG" jdbcType="VARCHAR" property="pmtcompflag" />
    <result column="PMTCOMPLETEDTM" jdbcType="TIMESTAMP" property="pmtcompletedtm" />
    <result column="ORDERSTATUS" jdbcType="VARCHAR" property="orderstatus" />
    <result column="CALMDTM" jdbcType="TIMESTAMP" property="calmdtm" />
    <result column="TXCHANNEL" jdbcType="VARCHAR" property="txchannel" />
    <result column="TATRADEDT" jdbcType="VARCHAR" property="tatradedt" />
    <result column="FEECALMODE" jdbcType="VARCHAR" property="feecalmode" />
    <result column="SUBMITAPPFLAG" jdbcType="VARCHAR" property="submitappflag" />
    <result column="CURRENCY" jdbcType="VARCHAR" property="currency" />
    <result column="DEALDT" jdbcType="VARCHAR" property="dealdt" />
    <result column="USEFLAG" jdbcType="VARCHAR" property="useflag" />
    <result column="BANKACCT" jdbcType="VARCHAR" property="bankacct" />
    <result column="BANKCODE" jdbcType="VARCHAR" property="bankcode" />
    <result column="IDTYPE" jdbcType="VARCHAR" property="idtype" />
    <result column="IDNO" jdbcType="VARCHAR" property="idno" />
    <result column="CPACCTNO" jdbcType="VARCHAR" property="cpacctno" />
    <result column="REDEEMDIRECTION" jdbcType="VARCHAR" property="redeemdirection" />
    <result column="EXPECTEDTRADEDT" jdbcType="VARCHAR" property="expectedtradedt" />
    <result column="APPAMTRMB" jdbcType="DECIMAL" property="appamtrmb" />
    <result column="NAV" jdbcType="DECIMAL" property="nav" />
    <result column="ADVANCEFLAG" jdbcType="VARCHAR" property="advanceflag" />
    <result column="FIRSTBUYFLAG" jdbcType="VARCHAR" property="firstbuyflag" />
    <result column="ACKAMT" jdbcType="DECIMAL" property="ackamt" />
    <result column="ACKVOL" jdbcType="DECIMAL" property="ackvol" />
    <result column="NOSTATFLAG" jdbcType="VARCHAR" property="nostatflag" />
    <result column="CREDT" jdbcType="TIMESTAMP" property="credt" />
    <result column="PLANID" jdbcType="VARCHAR" property="planid" />
    <result column="FIRSTPLANFLAG" jdbcType="VARCHAR" property="firstplanflag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, DEALNO, TXACCTNO, APPOINTMENTDEALNO, CUSTNAME, MBUSICODE, PRODUCTNAME, PRODUCTCODE, 
    PAYMENTTYPE, APPAMT, APPVOL, FEE, DISCOUNTRATE, PAYENDDATE, APPDATE, APPTIME, TXPMTFLAG, 
    PMTCOMPFLAG, PMTCOMPLETEDTM, ORDERSTATUS, CALMDTM, TXCHANNEL, TATRADEDT, FEECALMODE, 
    SUBMITAPPFLAG, CURRENCY, DEALDT, USEFLAG, BANKACCT, BANKCODE, IDTYPE, IDNO, CPACCTNO, 
    REDEEMDIRECTION, EXPECTEDTRADEDT, APPAMTRMB, NAV, ADVANCEFLAG, FIRSTBUYFLAG, ACKAMT, 
    ACKVOL, NOSTATFLAG, CREDT, PLANID, FIRSTPLANFLAG
  </sql>

  <select id="getCmZtOrderInfoList" resultType="com.howbuy.crm.nt.cmztorerinfo.dto.CmZtOrderinfoLogVo">
    SELECT T.id, T1.CONSCUSTNO, T1.PCODE, T1.TRADE_TYPE as tradeType
    FROM CM_ZT_ORDERINFO_LOG T
           left JOIN CM_PREBOOKPRODUCTINFO T1
                     ON T.APPOINTMENTDEALNO = T1.ID
    WHERE T.APPOINTMENTDEALNO IS NOT NULL
      AND T.MBUSICODE IN ('1120', '1122')
      AND T.NOSTATFLAG = '0'
    ORDER BY T.ID
  </select>

  <update id="updateZtOrderLog" parameterType="Long">
    UPDATE CM_ZT_ORDERINFO_LOG T SET T.NOSTATFLAG='1'  WHERE T.ID = #{id}
  </update>


</mapper>
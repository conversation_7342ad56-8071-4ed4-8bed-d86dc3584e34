<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.cmztorerinfo.dao.CmNostatReportDao">
    <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.cmztorerinfo.dto.CmNostatReport">
        <!--@mbg.generated-->
        <!--@Table CM_NOSTAT_REPORT-->
        <result column="PREID" jdbcType="DECIMAL" property="preid"/>
        <result column="PCODE" jdbcType="VARCHAR" property="pcode"/>
        <result column="CONSCUSTNO" jdbcType="VARCHAR" property="conscustno"/>
        <result column="TRADETYPE" jdbcType="VARCHAR" property="tradetype"/>
        <result column="CREATOR" jdbcType="VARCHAR" property="creator"/>
        <result column="CREDDT" jdbcType="TIMESTAMP" property="creddt"/>
        <result column="UPDDT" jdbcType="TIMESTAMP" property="upddt"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        PREID,
        PCODE,
        CONSCUSTNO,
        TRADETYPE,
        CREATOR,
        CREDDT,
        UPDDT
    </sql>
    <insert id="insert" parameterType="com.howbuy.crm.nt.cmztorerinfo.dto.CmNostatReport">
        <!--@mbg.generated-->
        insert into CM_NOSTAT_REPORT (PREID, PCODE, CONSCUSTNO,
                                      TRADETYPE, CREATOR, CREDDT,
                                      UPDDT)
        values (#{preid,jdbcType=DECIMAL}, #{pcode,jdbcType=VARCHAR}, #{conscustno,jdbcType=VARCHAR},
                #{tradetype,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{creddt,jdbcType=TIMESTAMP},
                #{upddt,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.howbuy.crm.nt.cmztorerinfo.dto.CmNostatReport">
        <!--@mbg.generated-->
        insert into CM_NOSTAT_REPORT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="preid != null">
                PREID,
            </if>
            <if test="pcode != null">
                PCODE,
            </if>
            <if test="conscustno != null">
                CONSCUSTNO,
            </if>
            <if test="tradetype != null">
                TRADETYPE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creddt != null">
                CREDDT,
            </if>
            <if test="upddt != null">
                UPDDT,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="preid != null">
                #{preid,jdbcType=DECIMAL},
            </if>
            <if test="pcode != null">
                #{pcode,jdbcType=VARCHAR},
            </if>
            <if test="conscustno != null">
                #{conscustno,jdbcType=VARCHAR},
            </if>
            <if test="tradetype != null">
                #{tradetype,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creddt != null">
                #{creddt,jdbcType=TIMESTAMP},
            </if>
            <if test="upddt != null">
                #{upddt,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="insertBakData">
        INSERT INTO CM_NOSTAT_REPORT_BAK (PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR, CREDDT, UPDDT)
        SELECT PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR, CREDDT, UPDDT
        FROM CM_NOSTAT_REPORT A
        WHERE A.PCODE = #{fundcode}
          AND A.CONSCUSTNO = #{custno}
          AND A.TRADETYPE = #{tradeType}
    </insert>

    <insert id="insertbakdataByMaxDt">
        insert into CM_NOSTAT_REPORT_bak T (PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR, CREDDT, UPDDT)
        select PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR, CREDDT, UPDDT
        FROM CM_NOSTAT_REPORT B
        WHERE B.PREID IN (SELECT A.ID
                          FROM (SELECT T.ID
                                FROM CM_PREBOOKPRODUCTINFO T
                                WHERE T.PCODE = #{fundcode}
                                  AND T.TRADE_TYPE = #{tradeType}
                                  AND T.CONSCUSTNO = #{custno}
                                  AND T.PREBOOKSTATE = '2'
                                  AND T.TRADESTATE = '0'
                                ORDER BY T.BUYAMT DESC,
                                         T.CREDT ASC,
                                         T.CRETIME ASC,
                                         T.ID ASC) A
                          WHERE ROWNUM = 1)
    </insert>

    <delete id="deleteCmReport" parameterType="String">
        DELETE
        FROM CM_NOSTAT_REPORT T
        WHERE T.PCODE = #{fundcode}
          AND T.CONSCUSTNO = #{custno}
          AND T.TRADETYPE = #{tradeType}
    </delete>

    <insert id="insertByLogParam" parameterType="String">
        INSERT INTO CM_NOSTAT_REPORT
            (PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR)
        SELECT T.ID,
               T.PCODE,
               T.CONSCUSTNO,
               T.TRADE_TYPE TRADETYPE,
               'sys'        CREATOR
        FROM CM_PREBOOKPRODUCTINFO T
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
          AND T.PREBOOKSTATE = '2'
          AND T.TRADESTATE = '0'
          AND T.PAYSTATE IN ('1', '2', '4')
    </insert>

    <select id="selectCountByParams" resultType="java.lang.Integer" parameterType="String">
        SELECT COUNT(*)
        FROM CM_PREBOOKPRODUCTINFO T
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
          AND T.PREBOOKSTATE = '2'
          AND T.TRADESTATE = '0'
    </select>

    <select id="selectNostatReportCountByParams" resultType="java.lang.Integer" parameterType="String">
        SELECT COUNT(*)
        FROM CM_NOSTAT_REPORT T
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADETYPE = #{tradeType,jdbcType=VARCHAR}
    </select>

    <select id="selectCountBlackpre" resultType="java.lang.Integer" parameterType="String">
        SELECT COUNT(*)
        FROM CM_BLACKPRE_CREATOR T
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADETYPE = #{tradeType,jdbcType=VARCHAR}
    </select>

    <select id="selectpreNumByPay" resultType="java.lang.Integer" parameterType="String">
        SELECT COUNT(*)
        FROM CM_PREBOOKPRODUCTINFO T
                 LEFT JOIN CM_ZT_ORDERINFO T1
                           ON T.ID = T1.APPOINTMENTDEALNO
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
          AND T.PREBOOKSTATE = '2'
          AND T.TRADESTATE = '0'
          AND T1.TXPMTFLAG IN ('2', '11', '16', '17', '18')
    </select>

    <insert id="inserNoStatBypay" parameterType="String">
        INSERT INTO CM_NOSTAT_REPORT
            (PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR)
        SELECT T.ID,
               T.PCODE,
               T.CONSCUSTNO,
               T.TRADE_TYPE TRADETYPE,
               'sys'        CREATOR
        FROM CM_PREBOOKPRODUCTINFO T
                 LEFT JOIN CM_ZT_ORDERINFO T1
                           ON T.ID = T1.APPOINTMENTDEALNO
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
          AND T.PREBOOKSTATE = '2'
          AND T.TRADESTATE = '0'
          AND T1.TXPMTFLAG NOT IN ('2', '11', '16', '17', '18')
    </insert>


    <insert id="inserNoStatByNopay" parameterType="String">
        INSERT INTO CM_NOSTAT_REPORT
            (PREID, PCODE, CONSCUSTNO, TRADETYPE, CREATOR)
        SELECT T.ID,
               T.PCODE,
               T.CONSCUSTNO,
               T.TRADE_TYPE TRADETYPE,
               'sys'        CREATOR
        FROM CM_PREBOOKPRODUCTINFO T
        WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
          AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
          AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
          AND T.PREBOOKSTATE = '2'
          AND T.TRADESTATE = '0'
    </insert>

    <delete id="deleteCmReportByMaxDt" parameterType="String">
        DELETE
        FROM CM_NOSTAT_REPORT B
        WHERE B.PREID IN (SELECT A.ID
                          FROM (SELECT T.ID
                                FROM CM_PREBOOKPRODUCTINFO T
                                WHERE T.PCODE = #{fundcode,jdbcType=VARCHAR}
                                  AND T.TRADE_TYPE = #{tradeType,jdbcType=VARCHAR}
                                  AND T.CONSCUSTNO = #{custno,jdbcType=VARCHAR}
                                  AND T.PREBOOKSTATE = '2'
                                  AND T.TRADESTATE = '0'
                                ORDER BY T.BUYAMT DESC,
                                         T.CREDT ASC,
                                         T.CRETIME ASC,
                                         T.ID ASC) A
                          WHERE ROWNUM = 1)
    </delete>
</mapper>
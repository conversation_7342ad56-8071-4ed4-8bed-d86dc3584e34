package com.howbuy.crm.nt.cmztorerinfo.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.nt.asset.dao.CmProductInfoMapper;
import com.howbuy.crm.nt.cmztorerinfo.dao.CmNostatReportDao;
import com.howbuy.crm.nt.cmztorerinfo.dao.CmZtOrderinfoLogDao;
import com.howbuy.crm.nt.cmztorerinfo.dto.CmZtOrderinfoLogVo;
import com.howbuy.crm.nt.consultant.dao.CmConsultantMapper;
import com.howbuy.crm.util.CrmNtConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:(更新是否进入统计-- service 实现层)
 * @author: xufanchao
 * @date: 2023/8/15 15:32
 * @since JDK 1.8
 */
@Service(value = "cmZtOrderInfoService")
@Slf4j
public class CmZtOrderinfoServiceImpl implements CmZtOrderInfoService {

    @Autowired
    private CmZtOrderinfoLogDao cmZtOrderinfoLogDao;

    @Autowired
    private CmProductInfoMapper cmProductInfoMapper;

    @Autowired
    private CmConsultantMapper cmConsultantMapper;

    @Autowired
    private CmNostatReportDao cmNostatReportDao;


    /**
     * @description:(更新是否进入统计 ---业务逻辑处理
     * @param
     * @return void
     * @author: xufanchao
     * @date: 2023/8/15 15:33
     * @since JDK 1.8
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void dealNostatReportTask() {
        try {
            // 1.查询中台订单数据变更流水表 的数据
            List<CmZtOrderinfoLogVo> cmZtOrderInfoList = cmZtOrderinfoLogDao.getCmZtOrderInfoList();
            // 过滤出来 交易类型( 1：购买，2：追加)的数据
            List<CmZtOrderinfoLogVo> buyOrAddList = cmZtOrderInfoList.stream()
                    .filter(it -> StringUtils.isNotEmpty(it.getTradeType()) && (it.getTradeType().equals(CrmNtConstant.TRADE_TYPE_BUY) || it.getTradeType()
                            .equals(CrmNtConstant.TRADE_TYPE_ADD))).collect(Collectors.toList());
            for (CmZtOrderinfoLogVo log : buyOrAddList) {
                processTransation(log);
                // 更新日志
                cmZtOrderinfoLogDao.updateZtOrderLog(log.getId());
            }
        } catch (Exception e) {
            log.error("处理中台订单数据变更流水表异常", e);
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void calDealBalckPrebook(CmZtOrderinfoLogVo cmZtOrderinfoLogVo) {
        log.info("calDealBalckPrebook start,cmZtOrderinfoLogVo:{}", cmZtOrderinfoLogVo);
        if (CrmNtConstant.TRADE_TYPE_BUY.equals(cmZtOrderinfoLogVo.getTradeType()) ||
                CrmNtConstant.TRADE_TYPE_ADD.equals(cmZtOrderinfoLogVo.getTradeType())) {
            log.info("do calDealBalckPrebook!");
            processTransation(cmZtOrderinfoLogVo);
        }
    }

    /**
     * @description:(每次数据事务控制处理)
     * @param ztOrderInfoLogVo 中台订单数据变更流水表
     * @author: xufanchao
     * @date: 2023/8/15 19:06
     * @since JDK 1.8
     */
    public void processTransation(CmZtOrderinfoLogVo ztOrderInfoLogVo) {
        String pcode = ztOrderInfoLogVo.getPcode();
        // 根据 当前的产品代码查询产品表数据
        List<JjxxInfo> productByCode = cmProductInfoMapper.getProductByCode(pcode);
        log.info("根据pcode查询到的productByCode:{}", JSON.toJSONString(productByCode));
        if (CollectionUtils.isEmpty(productByCode)) {
            log.info("根据pcode查询到的productByCode为空，处理结束！");
            return;
        }
        for (JjxxInfo jjxxInfo : productByCode) {
            String fccl = jjxxInfo.getFccl();
            String sfmsjg = jjxxInfo.getSfmsjg();
            // 分次CALL产品和直销产品不处理
            if ((StringUtils.isEmpty(fccl) || CrmNtConstant.CRM_LABEL_NO.equals(fccl)
                    && (StringUtils.isNotEmpty(sfmsjg) && (CrmNtConstant.PRODUCT_TYPE_DX.equals(sfmsjg)|| CrmNtConstant.PRODUCT_TYPE_ZZD.equals(sfmsjg))))) {
                // 根据当前的投顾客户号查询 客户手机号是否投顾手机号
                String conscustno = ztOrderInfoLogVo.getConscustno();
                int count = cmConsultantMapper.selectMobileCount(conscustno);
                log.info("根据当前的投顾客户号查询MobileCount:{}", count);
                // 当客户的手机号是投顾的手机号的情况时
                if (count > 0) {
                    // 备份数据
                    cmNostatReportDao.insertBakData(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                    // 删除同客户同产品同交易类型的黑名单里的记录
                    cmNostatReportDao.deleteCmReport(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                    cmNostatReportDao.insertByLogParam(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                    log.info("当客户的手机号是投顾的手机号的情况，处理结束！");
                } else {
                    handleNoMobileCount(ztOrderInfoLogVo);
                    log.info("当客户的手机号不是投顾的手机号的情况，处理结束！");
                }
            } else {
                log.info("分次CALL产品和直销产品不处理！");
            }
        }
    }

    /**
     * @description: 处理手机号不是投顾手机号的情况
     * @param ztOrderInfoLogVo 中台订单数据变更流水表
     * @author: jin.wang03
     * @date: 2023/11/21 17:06
     * @since JDK 1.8
     */
    private void handleNoMobileCount(CmZtOrderinfoLogVo ztOrderInfoLogVo) {
        int prebookCount = cmNostatReportDao.selectCountByParams(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
        int nostatBlackCount = cmNostatReportDao.selectNostatReportCountByParams(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
        // 存在多条的情况处理是否进入黑名单
        if (prebookCount > 1 || nostatBlackCount > 0) {
            // 查询同客户同产品同交易类型是不是已经人工处理过，如果人工处理过，就不再处理
            int blackNumb = cmNostatReportDao.selectCountBlackpre(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
            // 没有人工处理过
            if (blackNumb == 0) {
                // 备份数据
                cmNostatReportDao.insertBakData(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                // 删除同客户同产品同交易类型的黑名单里的记录
                cmNostatReportDao.deleteCmReport(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                // 查询是否有打款到账的预约
                int payNum = cmNostatReportDao.selectpreNumByPay(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                // --如果存在打款到账的预约，就把除打款到账的预约放入黑名单表中
                if (payNum > 0) {
                    cmNostatReportDao.inserNoStatBypay(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                } else {
                    // 没有打款到账的单子 插入所有相关预约到黑名单表中
                    cmNostatReportDao.inserNoStatByNopay(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                    // 备份数据  --找出金额最大时间最早的一条预约在黑名单中删除
                    cmNostatReportDao.insertbakdataByMaxDt(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                    cmNostatReportDao.deleteCmReportByMaxDt(ztOrderInfoLogVo.getPcode(), ztOrderInfoLogVo.getConscustno(), ztOrderInfoLogVo.getTradeType());
                }
            }
        }

    }
}

package com.howbuy.crm.nt.reward.dao;

import com.howbuy.crm.nt.reward.dto.CmProductCoefficient;
import com.howbuy.crm.nt.reward.dto.CmProductCoefficientAll;
import com.howbuy.crm.nt.reward.dto.CmProductCoefficientAsy;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/9/15 10:33
 */
public interface ProductCoefficientMapper {


    /**
     * 查询需要补生成全量表的数据（主要是endDate为空或特别长的）
    * <AUTHOR>
     * @return  * @date 2020/9/15
    */
   /* List<Long> selectNeedGenerateAll();

    *//**
     * 查询录入的部门对应的子部门，排除已录入的子部门及其下的所有部门（多个部门有包含关系时，以子部门未准）
     * @param orgCode 录入的部门
     * <AUTHOR>
     * @date 2020/9/11
     *//*
    List<String> selectSubOrgAvail(@Param("orgCode") String orgCode);

    *//**
     * 批量插入全量表:
     * @param ids
     * <AUTHOR>
     * @date 2020/9/14
     *//*
    void generateProductCoefficientAll(@Param("ids") List<Long> ids);

    *//**
     * 删除已有上级部门生成的且是当前范围的数据
     * @param fundCode
     * @param startDt
     * @param endDt
     * @param orgCodes
     * <AUTHOR>
     * @date 2020/9/15
     *//*
    void deleteUpAll(@Param("fundCode") String fundCode,@Param("startDt") String startDt, @Param("endDt") String endDt, @Param("orgCodes") List<String> orgCodes);

    *//**
     * 获取所有需要执行的异步任务
     * @return
     *//*
    List<CmProductCoefficientAsy> selectAllAysTaskByStatus(@Param("status")String status);

    *//**
     * 批量更新异步任务状态
     * @param taskList
     * @param status
     *//*
    int updateAllAysTaskByStatus(@Param("taskList")List<CmProductCoefficientAsy> taskList, @Param("status")String status);*/
}

package com.howbuy.crm.nt.reward.service;

import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.reward.dao.CmPrpInitialTradeNumMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2020/9/22 11:16
 * @Description 批量更新初始交易次数记录状态
 * @Version 1.0
 */
@Service("CmPrpInitialTradeNumService")
public class CmPrpInitialTradeNumServiceImpl implements CmPrpInitialTradeNumService {

    @Autowired
    private CmPrpInitialTradeNumMapper cmPrpInitialTradeNumMapper;

    /**
     * 功能描述: <br>
     * <>
     * @Param: [批量更新初始交易次数记录状态]
     * @Return: void
     * @Author: pei.luo
     * @Date: 2020/9/22 11:17
     */
    @Override
    public void updateBatchCmPrpInitialTradeNumStatus(String arg) {
        cmPrpInitialTradeNumMapper.updateBatchCmPrpInitialTradeNum();
    }
}

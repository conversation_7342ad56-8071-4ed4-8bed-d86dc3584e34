<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.reward.dao.CmPrpInitialTradeNumMapper">

    <update id="updateBatchCmPrpInitialTradeNum">
    UPDATE
    CM_PRP_INITIAL_TRADE_NUM CN
    SET
    CN.IS_VALID = '0'
    WHERE
	CN.IS_VALID = '1' AND
	CN.CONSCODE IN ( SELECT CN.CONSCODE FROM CM_PRP_INITIAL_TRADE_NUM CN LEFT JOIN cm_custconstant CC ON CN.CONSCUSTNO = CC.CUSTNO WHERE CN.IS_VALID = '1' AND CN.CONSCODE != CC.CONSCODE)
    </update>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.reward.dao.ProductCoefficientMapper">
    <!--<insert id="updateAllAysTaskByStatus">
        DECLARE
        begin
        <foreach collection="taskList" item="item">
            update CM_PRP_PRODUCT_COEFFICIENT_ASY a
            set
            a.task_status = #{status}
            where a.id = #{item.id};
        </foreach>
        commit;
        END;
    </insert>
    <select id="selectAllAysTaskByStatus" parameterType="string" resultType="com.howbuy.crm.nt.reward.dto.CmProductCoefficientAsy">
        select a.id,a.task_ids as taskIds ,a.task_status as taskStatus
        from CM_PRP_PRODUCT_COEFFICIENT_ASY a
        where a.task_status = #{status}
        order by a.id
    </select>
    <select id="selectNeedGenerateAll" resultType="long">
        select t1.id
            from CM_PRP_PRODUCT_COEFFICIENT t1
            left join (SELECT pri_Id, MAX(cal_dt) as cal_dt FROM CM_PRP_PRODUCT_COEFFICIENT_ALL GROUP BY pri_Id) t2
            on t1.id = t2.pri_Id
        where  t1.start_dt &lt;= to_char(sysdate + 1, 'yyyyMMdd')
            and (t2.cal_dt is null or t2.cal_dt &lt; t1.end_dt)
    </select>

    <select id="selectSubOrgAvail" parameterType="string" resultType="string" useCache="false">
        select orgcode
        from hb_organization t
        where t.STATUS = 0
        and ORGTYPE = '0'
        and orgcode not in
        (select orgcode
        from hb_organization t
        where t.STATUS = 0
        and ORGTYPE = '0'
        START WITH T.ORGCODE in
        (select orgcode
        from CM_PRP_PRODUCT_COEFFICIENT
        where orgcode in
        (select ORGCODE
        from hb_organization
        where STATUS = 0
        and ORGTYPE = '0'
        and ORGCODE != #{orgCode}
        START WITH ORGCODE = #{orgCode}
        CONNECT BY PRIOR ORGCODE = PARENTORGCODE))
        CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE)
        START WITH T.ORGCODE = #{orgCode}
        CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE
    </select>

    <insert id="generateProductCoefficientAll" parameterType="string" timeout="12000">
        DECLARE
        var_season_end  date;
        var_current_day date;

        begin
        &#45;&#45;第一层循环，取传参范围的底表数据
        for curInfo in (select t1.id,
            t1.fundcode,
            t1.orgcode,
            t1.start_dt,
            t1.end_dt,
            t1.account_product_type,
            t1.fold_coefficient,
            t1.commission_rate,
            t1.annually_set_award,
            t1.second_stock_coeff,
            t1.stock_fee_a,
            t1.stock_fee_b,
            t1.creator,
            t1.sfhscxf
            from CM_PRP_PRODUCT_COEFFICIENT t1
            <if test="ids != null">
                where
                id in <foreach collection="ids" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
            </if>)
        loop
            &#45;&#45;默认开始及结束时间，默认生成至当季度末
            select add_months(trunc(sysdate, 'q'), 6) - 1
                into var_season_end
                from dual;
            var_current_day := to_date(curInfo.start_dt, 'yyyyMMdd');
            &#45;&#45;如果有结束时间且早于当季度末，以底表的结束日期为准
            if curInfo.end_dt is not null and
            to_date(curInfo.end_dt, 'yyyyMMdd') &lt; var_season_end then
                                                  var_season_end := to_date(curInfo.end_dt, 'yyyyMMdd');
            end if;
            &#45;&#45;第二层第一个循环，取录入部门的所有子部门
            for orgCodeCur in (select t.orgcode
                from hb_organization t
                where t.STATUS = 0
                and ORGTYPE = '0'
                and orgcode not in
                (select orgcode
                from hb_organization t
                where t.STATUS = 0
                and ORGTYPE = '0'
                START WITH T.ORGCODE in
                (select orgcode
                from CM_PRP_PRODUCT_COEFFICIENT
                where fundcode = curInfo.fundcode
                and orgcode in
                (select ORGCODE
                from hb_organization
                where STATUS = 0
                and ORGTYPE = '0'
                and ORGCODE !=
                curInfo.orgCode
                START WITH ORGCODE =
                curInfo.orgCode
                CONNECT BY PRIOR
                ORGCODE =
                PARENTORGCODE)) &#45;&#45;底表中的当前部门的子部门
                CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE) &#45;&#45;底表中的当前部门的子部门的所有子部门
                START WITH T.ORGCODE = curInfo.orgCode
                CONNECT BY PRIOR T.ORGCODE = T.PARENTORGCODE) loop

                &#45;&#45;删除即将插入的数据，避免与上级部门设置的重复
                DELETE CM_PRP_PRODUCT_COEFFICIENT_ALL
                where fundcode = curInfo.fundcode
                and cal_Dt >= to_char(var_current_day, 'yyyymmdd')
                and cal_Dt &lt;= to_char(var_season_end, 'yyyymmdd')
                and orgcode = orgCodeCur.orgcode;

                &#45;&#45;第一个第三层循环，插入日期范围内的全量数据
                while var_current_day &lt;= var_season_end loop
                        if(to_char(var_current_day, 'yyyymmdd') >= ********) then
                                insert into CM_PRP_PRODUCT_COEFFICIENT_all
                                    (id,
                                    fundcode,
                                    orgcode,
                                    pri_id,
                                    cal_dt,
                                    account_product_type,
                                    fold_coefficient,
                                    commission_rate,
                                    annually_set_award,
                                    second_stock_coeff,
                                    stock_fee_a,
                                    stock_fee_b,
                                    CREATOR,
                                    create_time,
                                    sfhscxf)
                                    values
                                    (SEQ_REWARD_ID.NEXTVAL,
                                    curInfo.fundcode,
                                    orgCodeCur.orgcode,
                                    curInfo.id,
                                    to_char(var_current_day, 'yyyymmdd'),
                                    curInfo.account_product_type,
                                    curInfo.fold_coefficient,
                                    curInfo.commission_rate,
                                    curInfo.annually_set_award,
                                    curInfo.second_stock_coeff,
                                    curInfo.stock_fee_a,
                                    curInfo.stock_fee_b,
                                    curInfo.CREATOR,
                                    sysdate,
                                    curInfo.sfhscxf);
                        end if;
                    var_current_day := var_current_day + 1;
                end loop;
                var_current_day := to_date(curInfo.start_dt, 'yyyyMMdd');
            END loop;

            &#45;&#45;第二层第二个循环，取录入部门的有底表数据且范围小于当前录入范围的子部门
            for subOrgCode in (select orgcode,
                start_dt,
                nvl(end_dt, '********') end_dt
                from CM_PRP_PRODUCT_COEFFICIENT
                where fundcode = curInfo.fundcode
                and orgcode in
                (select ORGCODE
                from hb_organization
                where STATUS = 0
                and ORGTYPE = '0'
                and ORGCODE != curInfo.orgCode
                START WITH ORGCODE = curInfo.orgCode
                CONNECT BY PRIOR ORGCODE = PARENTORGCODE)
                and to_date(start_dt, 'yyyyMMdd') >= var_current_day
                and to_date(nvl(end_dt, '********'),
                'yyyyMMdd') &lt;= var_season_end) loop

                &#45;&#45;删除即将插入的数据，避免与上级部门设置的重复
                DELETE CM_PRP_PRODUCT_COEFFICIENT_ALL
                where fundcode = curInfo.fundcode
                and (cal_Dt between to_char(var_current_day, 'yyyymmdd') and subOrgCode.start_dt
                or cal_Dt between subOrgCode.end_dt and to_char(var_season_end, 'yyyymmdd'))
                and cal_Dt != subOrgCode.start_dt
                and cal_dt != subOrgCode.end_dt
                and orgcode = subOrgCode.orgcode;

                &#45;&#45;第二个第三层循环，插入日期范围内的全量数据
                while var_current_day &lt;= var_season_end loop
                    if (var_current_day &lt; to_date(subOrgCode.start_dt, 'yyyyMMdd') or
                    var_current_day > to_date(subOrgCode.end_dt, 'yyyyMMdd')) then
                        if(to_char(var_current_day, 'yyyymmdd') >= ********) then
                                insert into CM_PRP_PRODUCT_COEFFICIENT_all
                                    (id,
                                    fundcode,
                                    orgcode,
                                    pri_id,
                                    cal_dt,
                                    account_product_type,
                                    fold_coefficient,
                                    commission_rate,
                                    annually_set_award,
                                    second_stock_coeff,
                                    stock_fee_a,
                                    stock_fee_b,
                                    CREATOR,
                                    create_time,
                                    sfhscxf)
                                    values
                                    (SEQ_REWARD_ID.NEXTVAL,
                                    curInfo.fundcode,
                                    subOrgCode.orgcode,
                                    curInfo.id,
                                    to_char(var_current_day, 'yyyymmdd'),
                                    curInfo.account_product_type,
                                    curInfo.fold_coefficient,
                                    curInfo.commission_rate,
                                    curInfo.annually_set_award,
                                    curInfo.second_stock_coeff,
                                    curInfo.stock_fee_a,
                                    curInfo.stock_fee_b,
                                    curInfo.CREATOR,
                                    sysdate,
                                    curInfo.sfhscxf);
                        end if;
                    end if;
                    var_current_day := var_current_day + 1;
                    end loop;
                end loop;
            COMMIT;
            end loop;
        end;
    </insert>

    <delete id="deleteUpAll" parameterType="String">
        DELETE CM_PRP_PRODUCT_COEFFICIENT_ALL
        where fundcode = #{fundCode}
        and cal_Dt >= #{startDt}
        and cal_Dt &lt;= #{endDt}
        and orgcode in
        <foreach collection="orgCodes" open="(" close=")" separator="," item="orgCode">
            #{orgCode}
        </foreach>
    </delete>-->
</mapper>

package com.howbuy.crm.nt.hmc.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.howbuy.crm.nt.hmc.dao.DealHmcZjSalArchMapper;
import com.howbuy.crm.nt.hmc.dto.HmcZjSalArchVo;

import crm.howbuy.base.utils.StringUtil;

/**
 * 处理职级薪资存档
 * <AUTHOR>
 *
 */
@Service("dealHmcZjSalArchService")
public class DealHmcZjSalArchServiceImpl implements DealHmcZjSalArchService {

	@Autowired
	private DealHmcZjSalArchMapper dealHmcZjSalArchMapper;
	/**
	 * 执行方法
	 */
	@Override
	public void execData(String arg) {
		List<Map<String,String>> listranklevel = dealHmcZjSalArchMapper.listHrPositionslevel();
		List<HmcZjSalArchVo> list = dealHmcZjSalArchMapper.listHmcZjSalArch();
		List<HmcZjSalArchVo> dealList = new ArrayList<>();
		Map<String,String> rankorder = new HashMap<String,String>();
		for(Map<String,String> map : listranklevel){
			rankorder.put(map.get("RANKCODE"), map.get("RANKORDER"));
		}
		Calendar cal = Calendar.getInstance();
		int month = cal.get(Calendar.MONTH);
		int year = cal.get(Calendar.YEAR);
		if(month == 0){
			year = year-1;
			month = 12;
		}
		if(StringUtils.isNotEmpty(arg) ){
			JSONObject parse = (JSONObject)JSONObject.parse(arg);
			Integer month1 = (Integer)parse.get("month");
			if(null!= month1){
				month = month1;
			}
		}
		for(HmcZjSalArchVo vo : list){
			String ranklevel = vo.getRanklevel();
			if(StringUtil.isNotNullStr(ranklevel) && vo.getRanklevel().indexOf(",") != -1){
				String [] ranklevels = vo.getRanklevel().split(",");
				//获取职级最高的
				String maxrank = dealHmcZjSalArchMapper.getMaxUserLevelByZj(Arrays.asList(ranklevels));
				ranklevel = maxrank;
			}
			HmcZjSalArchVo obj = new HmcZjSalArchVo();
			obj.setConscode(vo.getConscode());
			obj.setYears(vo.getYears());
			obj.setYears(String.valueOf(year));
			if(month == 1){
				obj.setRanklevel1m(ranklevel);
				obj.setSalary1m(vo.getSalary());
			}
			if(month == 2){
				obj.setRanklevel2m(ranklevel);
				obj.setSalary2m(vo.getSalary());
			}
			if(month == 3){
				obj.setRanklevel3m(ranklevel);
				obj.setSalary3m(vo.getSalary());
			}
			if(month == 4){
				obj.setRanklevel4m(ranklevel);
				obj.setSalary4m(vo.getSalary());
			}
			if(month == 5){
				obj.setRanklevel5m(ranklevel);
				obj.setSalary5m(vo.getSalary());
			}
			if(month == 6){
				obj.setRanklevel6m(ranklevel);
				obj.setSalary6m(vo.getSalary());
			}
			if(month == 7){
				obj.setRanklevel7m(ranklevel);
				obj.setSalary7m(vo.getSalary());
			}
			if(month == 8){
				obj.setRanklevel8m(ranklevel);
				obj.setSalary8m(vo.getSalary());
			}
			if(month == 9){
				obj.setRanklevel9m(ranklevel);
				obj.setSalary9m(vo.getSalary());
			}
			if(month == 10){
				obj.setRanklevel10m(ranklevel);
				obj.setSalary10m(vo.getSalary());
			}
			if(month == 11){
				obj.setRanklevel11m(ranklevel);
				obj.setSalary11m(vo.getSalary());
			}
			if(month == 12){
				obj.setRanklevel12m(ranklevel);
				obj.setSalary12m(vo.getSalary());
				//花名册信息年底存个档
				dealHmcZjSalArchMapper.backUpConsultantExpArch(vo.getConscode(),String.valueOf(year));
			}
			dealList.add(obj);
		}
		dealHmcZjSalArchMapper.batchDealHmcZjSalArch(dealList);

	}

}

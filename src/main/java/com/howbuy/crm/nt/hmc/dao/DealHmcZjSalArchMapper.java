package com.howbuy.crm.nt.hmc.dao;

import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import com.howbuy.crm.nt.hmc.dto.HmcZjSalArchVo;

import java.util.List;
import java.util.Map;

@MapperScan
public interface DealHmcZjSalArchMapper {

	/**
	 * 批量插入
	 * @param list
	 */
	void batchDealHmcZjSalArch(List<HmcZjSalArchVo> list);
	
	/**
	 * 获取列表
	 * @return
	 */
	List<HmcZjSalArchVo> listHmcZjSalArch();
	
	/**
	 * 查询直接的最大职级
	 * @param ranklevellist
	 * @return
	 */
	String getMaxUserLevelByZj(@Param("ranklevellist")List<String> ranklevellist);

	List<Map<String,String>> listHrPositionslevel();

    void backUpConsultantExpArch(@Param("conscode")String conscode,@Param("year") String year);
}

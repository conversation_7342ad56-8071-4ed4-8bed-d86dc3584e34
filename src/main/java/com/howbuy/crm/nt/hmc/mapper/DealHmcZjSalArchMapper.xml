<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.nt.hmc.dao.DealHmcZjSalArchMapper">
	<resultMap id="BaseResultMap" type="com.howbuy.crm.nt.hmc.dto.HmcZjSalArchVo">
        <result column="USERID" property="conscode" jdbcType="VARCHAR"/>
        <result column="WORKTYPE" property="worktype" jdbcType="VARCHAR"/>
        <result column="QUITDT" property="quitdt" jdbcType="VARCHAR"/>
		<result column="CURMONTHLEVEL" property="ranklevel" jdbcType="VARCHAR"/>
		<result column="CURMONTHSALARY" property="salary" jdbcType="NUMERIC"/>
    </resultMap>
	<insert id="backUpConsultantExpArch">
		insert into cm_consultant_exp_arch (
				select SEQ_CONSULTANT_EXP_ARCH.Nextval,#{year},a.*,sysdate from cm_consultant_exp a  where a.userid=#{conscode} )
	</insert>

	<update id="batchDealHmcZjSalArch" parameterType="java.util.List">
		begin
		<foreach collection="list" item="item" index="index" separator=";" >
			merge into CM_CONSULTANT_ZJ_SAL_ARCH t
			using (select #{item.conscode} as conscode, #{item.years} as years from dual) t1
			on (t.conscode = t1.conscode and t.years=t1.years)
			when not matched then
			insert
			<trim prefix="(" suffix=")"   suffixOverrides=",">
				<if test="item.conscode != null"> conscode, </if>
                <if test="item.years != null"> years, </if>
				<if test="item.ranklevel1m != null"> ranklevel1m, </if>
				<if test="item.salary1m != null"> salary1m, </if>
				<if test="item.ranklevel2m != null"> ranklevel2m, </if>
				<if test="item.salary2m != null"> salary2m, </if>
				<if test="item.ranklevel3m != null"> ranklevel3m, </if>
				<if test="item.salary3m != null"> salary3m, </if>
				<if test="item.ranklevel4m != null"> ranklevel4m, </if>
				<if test="item.salary4m != null"> salary4m, </if>
				<if test="item.ranklevel5m != null"> ranklevel5m, </if>
				<if test="item.salary5m != null"> salary5m, </if>
				<if test="item.ranklevel6m != null"> ranklevel6m, </if>
				<if test="item.salary6m != null"> salary6m, </if>
				<if test="item.ranklevel7m != null"> ranklevel7m, </if>
				<if test="item.salary7m != null"> salary7m, </if>
				<if test="item.ranklevel8m != null"> ranklevel8m, </if>
				<if test="item.salary8m != null"> salary8m, </if>
				<if test="item.ranklevel9m != null"> ranklevel9m, </if>
				<if test="item.salary9m != null"> salary9m, </if>
				<if test="item.ranklevel10m != null"> ranklevel10m, </if>
				<if test="item.salary10m != null"> salary10m, </if>
				<if test="item.ranklevel11m != null"> ranklevel11m, </if>
				<if test="item.salary11m != null"> salary11m, </if>
				<if test="item.ranklevel12m != null"> ranklevel12m, </if>
				<if test="item.salary12m != null"> salary12m, </if>
			</trim>
			VALUES
			<trim prefix="(" suffix=")"  suffixOverrides=",">
				<if test="item.conscode != null"> #{item.conscode}, </if>
				<if test="item.years != null"> #{item.years}, </if>
				<if test="item.ranklevel1m != null"> #{item.ranklevel1m}, </if>
				<if test="item.salary1m != null"> #{item.salary1m}, </if>
				<if test="item.ranklevel2m != null"> #{item.ranklevel2m}, </if>
				<if test="item.salary2m != null"> #{item.salary2m}, </if>
				<if test="item.ranklevel3m != null"> #{item.ranklevel3m}, </if>
				<if test="item.salary3m != null"> #{item.salary3m}, </if>
				<if test="item.ranklevel4m != null"> #{item.ranklevel4m}, </if>
				<if test="item.salary4m != null"> #{item.salary4m}, </if>
				<if test="item.ranklevel5m != null"> #{item.ranklevel5m}, </if>
				<if test="item.salary5m != null"> #{item.salary5m}, </if>
				<if test="item.ranklevel6m != null"> #{item.ranklevel6m}, </if>
				<if test="item.salary6m != null"> #{item.salary6m}, </if>
				<if test="item.ranklevel7m != null"> #{item.ranklevel7m}, </if>
				<if test="item.salary7m != null"> #{item.salary7m}, </if>
				<if test="item.ranklevel8m != null"> #{item.ranklevel8m}, </if>
				<if test="item.salary8m != null"> #{item.salary8m}, </if>
				<if test="item.ranklevel9m != null"> #{item.ranklevel9m}, </if>
				<if test="item.salary9m != null"> #{item.salary9m}, </if>
				<if test="item.ranklevel10m != null"> #{item.ranklevel10m}, </if>
				<if test="item.salary10m != null"> #{item.salary10m}, </if>
				<if test="item.ranklevel11m != null"> #{item.ranklevel11m}, </if>
				<if test="item.salary11m != null"> #{item.salary11m}, </if>
				<if test="item.ranklevel12m != null"> #{item.ranklevel12m}, </if>
				<if test="item.salary12m != null"> #{item.salary12m}, </if>
			</trim>
			when matched then
			update
			<set>
				<if test="item.ranklevel1m != null"> ranklevel1m = #{item.ranklevel1m}, </if>
				<if test="item.salary1m != null"> salary1m = #{item.salary1m}, </if>
				<if test="item.ranklevel2m != null"> ranklevel2m = #{item.ranklevel2m}, </if>
				<if test="item.salary2m != null"> salary2m = #{item.salary2m}, </if>
				<if test="item.ranklevel3m != null"> ranklevel3m = #{item.ranklevel3m}, </if>
				<if test="item.salary3m != null"> salary3m = #{item.salary3m}, </if>
				<if test="item.ranklevel4m != null"> ranklevel4m = #{item.ranklevel4m}, </if>
				<if test="item.salary4m != null"> salary4m = #{item.salary4m}, </if>
				<if test="item.ranklevel5m != null"> ranklevel5m = #{item.ranklevel5m}, </if>
				<if test="item.salary5m != null"> salary5m = #{item.salary5m}, </if>
				<if test="item.ranklevel6m != null"> ranklevel6m = #{item.ranklevel6m}, </if>
				<if test="item.salary6m != null"> salary6m = #{item.salary6m}, </if>
				<if test="item.ranklevel7m != null"> ranklevel7m = #{item.ranklevel7m}, </if>
				<if test="item.salary7m != null"> salary7m = #{item.salary7m}, </if>
				<if test="item.ranklevel8m != null"> ranklevel8m = #{item.ranklevel8m}, </if>
				<if test="item.salary8m != null"> salary8m = #{item.salary8m}, </if>
				<if test="item.ranklevel9m != null"> ranklevel9m = #{item.ranklevel9m}, </if>
				<if test="item.salary9m != null"> salary9m = #{item.salary9m}, </if>
				<if test="item.ranklevel10m != null"> ranklevel10m = #{item.ranklevel10m}, </if>
				<if test="item.salary10m != null"> salary10m = #{item.salary10m}, </if>
				<if test="item.ranklevel11m != null"> ranklevel11m = #{item.ranklevel11m}, </if>
				<if test="item.salary11m != null"> salary11m = #{item.salary11m}, </if>
				<if test="item.ranklevel12m != null"> ranklevel12m = #{item.ranklevel12m}, </if>
				<if test="item.salary12m != null"> salary12m = #{item.salary12m}, </if>
			</set>
		</foreach>
		;end;
	</update>
	
	<select id="listHmcZjSalArch" resultMap="BaseResultMap">
        SELECT T.USERID, T.WORKTYPE, T.QUITDT, T.CURMONTHLEVEL, T.CURMONTHSALARY
		  FROM CM_CONSULTANT_EXP T
		 WHERE T.WORKTYPE = '1'
		    OR (T.WORKTYPE = '2' AND T.QUITDT IS NOT NULL AND
		       SUBSTR(T.QUITDT, 0, 6) = TO_CHAR(ADD_MONTHS(SYSDATE, -1), 'yyyymm'))
    </select>
    
    <select id="getMaxUserLevelByZj" resultType="String">
        select a.CONSTCODE from HB_CONSTANT a
		 WHERE a.TYPECODE = 'hrpositionslevel'
		 and a.CONSTEXT1 in (
		        SELECT MAX(TO_NUMBER(T.CONSTEXT1))
				  FROM HB_CONSTANT T
				 WHERE T.TYPECODE = 'hrpositionslevel'
				<if test="ranklevellist != null">
					AND T.CONSTCODE IN
					<foreach collection="ranklevellist" item="ranklevel"
					    index="index" open="(" close=")" separator=",">
					    #{ranklevel}
					</foreach>
		        </if>
        )
    </select>

    <select id="listHrPositionslevel" resultType="Map" >
       SELECT T.CONSTCODE RANKCODE, T.CONSTEXT1 RANKORDER
		  FROM HB_CONSTANT T
		 WHERE T.TYPECODE = 'hrpositionslevel'
    </select>
</mapper>
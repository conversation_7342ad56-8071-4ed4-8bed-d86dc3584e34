<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.remind.dao.RemindWithdrawMessageDao">

	<select id="getCmDoubleTrade" parameterType="java.lang.String" resultType="com.howbuy.crm.nt.remind.dto.CmDoubleTrade"
			useCache="false">
		select t1.id,
       T6.conscustno,
       t6.custname,
       t1.fundcode,
       t22.jjmc as fundname,
       t22.jjjc,
       t3.creator,
       custconstant.conscode,
       CC.consname
  from cm_double_trade t1
  left join (select tid, fileid, creator
               from (select a.tid,
                            a.creator,
                            a.fileid,
                            row_number() over(partition by tid order by credt desc) r
                       from cm_double_traderfile a
                      where a.rec_stat = '0')
              where r = 1) t3
    on t3.tid = t1.id
  left join ac_tx_hbone t5
    on t5.hbone_no = t1.hbone_no
   and t5.stat = '0'
  left join cm_conscust t6
    on t6.hbone_no = t1.hbone_no
   and t6.conscuststatus = '0'
  left join cm_custconstant custconstant
    on custconstant.custNo = t6.conscustNo
  LEFT JOIN CM_CONSULTANT CC ON CC.CONSCODE = custconstant.Conscode
  left join jjxx1 t22
    on t22.jjdm = t1.fundCode
 where t1.needFlag = '1'
   AND T1.HANDLEFLAG = '4'
   AND T1.id = #{id}
	</select>

</mapper>
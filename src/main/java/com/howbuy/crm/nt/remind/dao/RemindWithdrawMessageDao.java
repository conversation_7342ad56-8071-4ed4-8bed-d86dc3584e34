package com.howbuy.crm.nt.remind.dao;

import com.howbuy.crm.nt.remind.dto.CmDoubleTrade;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/10/15 13:35
 * @Description
 * @Version 1.0
 */
@Mapper
public interface RemindWithdrawMessageDao {

    /**
     * 获取双录记录
     * @param id
     * @return
     */
    CmDoubleTrade getCmDoubleTrade(String id);

}

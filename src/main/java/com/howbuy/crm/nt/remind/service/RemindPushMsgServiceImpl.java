package com.howbuy.crm.nt.remind.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.common.enums.WechatTaskTypeEnum;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.ConscustAcctInfoService;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.nt.remind.domain.CmWeChatCustRelation;
import com.howbuy.crm.util.CrmNtConstant;
import com.howbuy.crm.util.MainLogUtils;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: shuai.zhang
 * @Date: 2022/3/2
 * @Description:
 */

@Service("remindPushMsgService")
@Slf4j
public class RemindPushMsgServiceImpl implements RemindPushMsgService {
    @Autowired
    private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;
    @Autowired
    private CmPushMsgService cmPushMsgService;
    @Autowired
    private ConscustAcctInfoService conscustAcctInfoService;
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Value("${annexUrl}")
    private String annexUrl;
    /**
     * 企业微信 -crm服务 url [https://qiyeweixin.howbuy.com/crm]
     */
    @Value("${WECHAT_URL}")
    private String WECHAT_URL;

    /**
     * 调度魔方  一分钟一次  处理待推送表消息  进每日消息提醒表  /  企业微信
     * @param arg
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushMsgData(String arg) {
        //抓出PC端待发送的消息   非企业微信,未推送,切到了期望推送时间
        int batchSize = 500;
        List<CmPushMsg> msgList =  cmRemindMsgDaoBuss.getPCExpectPushMsg();
        if(CollectionUtils.isNotEmpty(msgList)){
            //500一批   先置为发送中    发送状态 0:待发送,1:已发送,2:取消,3:推送中
            int index = 0;
            while (msgList.size() > index) {
                cmRemindMsgDaoBuss.batchUpdatePushFlag(msgList.subList(index, msgList.size() > index + batchSize ? index + batchSize : msgList.size()),CrmNtConstant.PUSH_MSG_FLAG_ING);
                index += batchSize;
            }
            //挨个插入CM_REMIND_MSG_DAY表   并更新CM_PUSH_MSG 表的 PushFlag   处理一条    commit一条
            for(CmPushMsg dto : msgList){
                cmRemindMsgDaoBuss.insertCmRemindMsgDayAndUpdatePushFlag(dto);
            }
        }

        //抓出企业微信待发送的消息   企业微信,未推送,切到了期望推送时间,人工的
        List<CmPushMsg> wechatMsgList =  cmRemindMsgDaoBuss.getWechatExpectPushMsg();
        if(CollectionUtils.isNotEmpty(wechatMsgList)){
            //500一批   先置为发送中    发送状态 0:待发送,1:已发送,2:取消,3:推送中
            int index = 0;
            while (wechatMsgList.size() > index) {
                cmRemindMsgDaoBuss.batchUpdatePushFlag(wechatMsgList.subList(index, wechatMsgList.size() > index + batchSize ? index + batchSize : wechatMsgList.size()),CrmNtConstant.PUSH_MSG_FLAG_ING);
                index += batchSize;
            }
            //挨个企业微信推送   并更新pushflag
            ArrayList<CmPushMsg> pushList = new ArrayList<>();
            for(CmPushMsg dto : wechatMsgList){
                //处理企业微信卡片  附件
                CmPushMsgAnnex cmPushMsgAnnex = cmRemindMsgDaoBuss.getCmPushMsgAnnexTitle(dto.getPushid());
                if(cmPushMsgAnnex != null){
                    //有附件   推卡片
                    cmPushMsgService.postWechatMsg(dto.getConscode(),dto.getMsgtypeval(),dto.getMsgcontent(),CrmNtConstant.TEMP_TEXT_CARD,annexUrl + cmPushMsgAnnex.getPushid());
                }else{
                    //无附件   推文本
                    cmPushMsgService.postWechatMsg(dto.getConscode(),dto.getMsgtypeval(),dto.getMsgcontent(), CrmNtConstant.TEMP_TEXT_TEXT,"#");
                }

                pushList.clear();
                pushList.add(dto);
                //更新为已推送
                cmRemindMsgDaoBuss.batchUpdatePushFlag(pushList,CrmNtConstant.PUSH_MSG_FLAG_ALREADY);
            }
        }
        //企微任务
        sendWechatTask();
    }

    /**
     * @description 企微任务
     * @param
     * @return void
     * @author: jianjian.yang
     * @date: 2023/10/11 19:44
     * @since JDK 1.8
     */
    public void sendWechatTask(){
        //抓出企业微信待发送的消息   企业微信,未推送,切到了期望推送时间,人工的
        List<CmPushMsg> wechatTaskMsgList = cmRemindMsgDaoBuss.getWechatTaskPushMsg();
        log.info("wechatTaskMsgList Size:{}", wechatTaskMsgList.size());
        if(CollectionUtils.isNotEmpty(wechatTaskMsgList)) {
            //投顾号与消息对应
            Map<String, List<CmPushMsg>> wechatTaskMap = Maps.newHashMap();
            wechatTaskMsgList.forEach(cmPushMsg -> {
                if(WechatTaskTypeEnum.WECHAT_TASK.getKey().equals(cmPushMsg.getTaskType())){
                    //群发数据
                    buildGroup(cmPushMsg, wechatTaskMap);
                }
                if(WechatTaskTypeEnum.CARD.getKey().equals(cmPushMsg.getTaskType())){
                    //1:文本消息，2：卡片消息：3：群发消息
                    cmPushMsgService.sendGroupMessage(CrmNtConstant.MESSAGE_TYPE_CARD, cmPushMsg.getTitle(), cmPushMsg.getWechatConsCode(), cmPushMsg.getCardUrl(), null, cmPushMsg.getMsgcontent());
                    //更新为已推送
                    cmRemindMsgDaoBuss.batchUpdatePushFlag(Lists.newArrayList(cmPushMsg), CrmNtConstant.PUSH_MSG_FLAG_ALREADY);
                }
            });
            log.info("wechatTaskMap:{}", wechatTaskMap.toString());
            if(wechatTaskMap.size() > 0){
                for(Map.Entry<String, List<CmPushMsg>> entry : wechatTaskMap.entrySet()){
                    List<CmPushMsg> list = entry.getValue();
                    List<String> custNos = list.stream().map(CmPushMsg::getConscustNo).collect(Collectors.toList());
                    CmPushMsg cmPushMsg = list.get(0);
                    //1:文本消息，2：卡片消息：3：群发消息
                    cmPushMsgService.sendGroupMessage(CrmNtConstant.MESSAGE_TYPE_GROUP, cmPushMsg.getTitle(), cmPushMsg.getWechatConsCode(), null, custNos, cmPushMsg.getMsgcontent());
                    //更新为已推送
                    cmRemindMsgDaoBuss.batchUpdatePushFlag(list, CrmNtConstant.PUSH_MSG_FLAG_ALREADY);
                }
            }
        }
    }
    /**
     * @description 群发数据
     * @param cmPushMsg
     * @param wechatTaskMap
     * @return void
     * @author: jianjian.yang
     * @date: 2023/10/16 19:54
     * @since JDK 1.8
     */
    private void buildGroup(CmPushMsg cmPushMsg, Map<String, List<CmPushMsg>> wechatTaskMap){
        //取客户的企业微信userId
        String externalUserId = getExternalUserId(cmPushMsg.getConscode(), cmPushMsg.getConscustNo());
        if(StringUtil.isNullStr(externalUserId)){
            //更新为推送失败
            cmRemindMsgDaoBuss.batchUpdatePushFlag(Lists.newArrayList(cmPushMsg), CrmNtConstant.PUSH_MSG_FLAG_FAIL);
        }else {
            cmPushMsg.setConscustNo(externalUserId);
            List<CmPushMsg> conscustList = wechatTaskMap.get(cmPushMsg.getWechatConsCode() + cmPushMsg.getTaskId());
            if(conscustList == null) {
                wechatTaskMap.put(cmPushMsg.getWechatConsCode() + cmPushMsg.getTaskId(), Lists.newArrayList(cmPushMsg));
            }else {
                conscustList.add(cmPushMsg);
            }
        }
    }

    /**
     * @description 查外部用户
     * @param consCode
     * @param conscustNo
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2023/10/16 19:41
     * @since JDK 1.8
     */
    public String getExternalUserId(String consCode, String conscustNo){
        QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
        queryConscustInfoRequest.setConscustno(conscustNo);
        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
        String unionId = conscustAcctInfoService.getUnionIdByHboneNo(queryConscustInfoResponse.getConscustinfo().getHboneno());
        if(StringUtil.isEmpty(unionId)){
            return null;
        }
        Map<String,String> paramMap=Maps.newHashMap();
        paramMap.put("unionId",unionId);
        paramMap.put("consCode",consCode);
        String response = null;
        try {
            long startTime = System.currentTimeMillis();
            response = HttpUtils.post(WECHAT_URL + "/wechatuser/getRealtionByUnionId", paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(WECHAT_URL + "/wechatuser/getRealtionByUnionId", String.valueOf(HttpStatus.OK.value()), endTime - startTime);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        if(StringUtil.isNullStr(response)){
            return null;
        }
        CmWeChatCustRelation cmWeChatCustRelation = JSON.parseObject(response, CmWeChatCustRelation.class);
        if(cmWeChatCustRelation != null) {
            return cmWeChatCustRelation.getExternalUserId();
        }else {
            return null;
        }
    }
}

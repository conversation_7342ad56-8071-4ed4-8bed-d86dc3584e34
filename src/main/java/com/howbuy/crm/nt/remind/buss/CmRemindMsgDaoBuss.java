package com.howbuy.crm.nt.remind.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.cache.cacheService.CacheKeyPrefix;
import com.howbuy.crm.cache.cacheService.lock.LockService;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.message.dao.CmCompanyReportDao;
import com.howbuy.crm.nt.message.domain.CmCompanyReport;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.dao.CmRemindMsgDayDao;
import com.howbuy.crm.nt.remind.domain.CmCustBirthday;
import com.howbuy.crm.nt.remind.domain.CmJzMsgInfo;
import com.howbuy.crm.nt.remind.domain.CmRemindMsgParam;
import com.howbuy.crm.nt.remind.domain.CmZlgxMsgInfo;
import com.howbuy.crm.util.DateTimeUtil;
import com.howbuy.persistence.content.interview.CompanyReport;
import crm.howbuy.base.domain.CmRemindMsgDay;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CmRemindMsgDaoBuss {

	protected final static int TWO_DAY_SECOND = 172800;

	@Autowired
	private CommonDao commondao;

	@Autowired
    private CmRemindMsgDayDao cmRemindMsgDayDao;

	@Autowired
	private CmCompanyReportDao cmCompanyReportDao;

	@Autowired
	private CmPushMsgService cmPushMsgService;

	@Autowired
	private LockService lockService;

	/** 消息类型：产品分红 */
    public static final String MSG_TYPE_CPFH = "1";
	/** 消息类型：产品到期 */
    public static final String MSG_TYPE_CPDQ = "2";
	/** 消息类型：团队产品到期 */
    public static final String MSG_TYPE_TDDQ = "3";
	/** 消息类型：客户生日 */
    public static final String MSG_TYPE_KHSR = "4";
	/** 消息类型：到账通知 */
    public static final String MSG_TYPE_DZTZ = "5";
	/** 消息类型：净值通知 */
    public static final String MSG_TYPE_JZTZ = "6";
	/** 消息类型：预约通知 */
    public static final String MSG_TYPE_YYTZ = "7";
	/** 消息类型：赎回通知 */
    public static final String MSG_TYPE_SHTZ = "15";
	/** 消息类型：资料更新 */
    public static final String MSG_TYPE_ZLGX = "8";
	/** 消息类型：交易类型 = 强赎 */
    public static final String MSG_TYPE_QS = "9";
	/** 消息类型：交易类型 = 红利发放 */
    public static final String MSG_TYPE_HLFF = "10";
	/** 消息类型：缴费提醒-投顾消息中心 */
    public static final String MSG_PAYMENT_REMINDER = "11";

	
	/**
	 * 同步基金分红数据方法
	 */
	public void syncFundFhMsg() {
		log.info("同步基金分红数据方法开始执行。。。");
		List<CmRemindMsgParam> fhList = cmRemindMsgDayDao.queryFundFhMsgList();
        if (fhList.size() > 0) {
            /*List<CmRemindMsgDay> newMsgList = new ArrayList<CmRemindMsgDay>();*/
            for (CmRemindMsgParam cmremindmsg:fhList) {

				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("date", cmremindmsg.getMsgContentDt());
				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用基金分红数据同步)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用基金分红数据同步)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送基金分红数据同步成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送基金分红数据同步失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
            }
        } else {
        	log.info("未找到需同步的基金分红数据！");
        }
		log.info("同步基金分红数据方法执行结束。。。");
	}

	/**
	 * 同步团队产品到期数据方法
	 */
	public void syncCustBirthDayMsg() {
		log.info("同步投顾客户生日数据方法开始执行。。。");
		List<CmCustBirthday> khsrList = cmRemindMsgDayDao.queryBirthDayMsgList();
		khsrList = getValidCmRemindMsgDay(khsrList);
        if (khsrList != null && khsrList.size() > 0) {
            for (CmCustBirthday custbirth: khsrList) {

				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(custbirth.getMsgcontent());

				//1、PC端；2、微信
				//request.setPushChannel("1");
				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(custbirth.getConscode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("custname", custbirth.getCustname());
				paramMap.put("birthday", custbirth.getBirthday());
				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用发送客户生日生成通知)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用发送客户生日生成通知)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送新规任务生成提醒通知成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送新规任务生成提醒通知失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
            }
        } else {
        	log.info("未找到需同步的投顾客户生日数据！");
        }
		log.info("同步投顾客户生日数据方法执行结束。。。");
	}

	private List<CmCustBirthday> getValidCmRemindMsgDay(List<CmCustBirthday> khsrList){
		List<CmCustBirthday> retList = new ArrayList<CmCustBirthday>();
		if(CollectionUtils.isNotEmpty(khsrList)){
			for(CmCustBirthday cmRemindMsgDay : khsrList){
				String birthday = cmRemindMsgDay.getBirthday();
				String expiredt = DateTimeUtil.getCurDate();
				if(birthday.substring(4).compareTo(expiredt.substring(4)) < 0){
					birthday =  (Integer.parseInt(expiredt.substring(0, 4))  + 1) + birthday.substring(4);
				}else{
					birthday =  Integer.parseInt(expiredt.substring(0, 4)) + birthday.substring(4);
				}
				
				if(birthday.equals(expiredt)){
					retList.add(cmRemindMsgDay);
				}else {
					boolean firstStatus = isWeekend(DateTimeUtil.getString2Date(expiredt, DateTimeUtil.DATA_PATTERN8));
					if(firstStatus){
						continue;
					}

					//间隔天数
					int jgts = (int)(DateTimeUtil.getString2Date(birthday, DateTimeUtil.DATA_PATTERN8).getTime() - DateTimeUtil.getString2Date(expiredt, DateTimeUtil.DATA_PATTERN8).getTime())
			    		       /(1000 * 60 * 60 * 24);
					int workdayCount = 0;
					Date tempDate = null;
					for(int i = 1; i<=jgts; i++){
						tempDate = DateUtils.addDays(DateTimeUtil.getString2Date(birthday, DateTimeUtil.DATA_PATTERN8), -1);
						birthday = DateTimeUtil.convertDateToString(DateTimeUtil.DATA_PATTERN8, tempDate);
						if(birthday.compareTo(expiredt) < 0){
							workdayCount = -1;
							break;
						}
						
						boolean status = isWeekend(tempDate);
						if(!status){
							workdayCount++;
						}
					}

					//三个工作日
					if(workdayCount == 3){
						retList.add(cmRemindMsgDay);
					}
				}
			}
		}
		return retList;
	}
	
	private boolean isWeekend(Date date){
		boolean status = false;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
	    if(cal.get(Calendar.DAY_OF_WEEK)==Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK)==Calendar.SUNDAY){
	    	status = true;
	    }
	    
	    return status;
	}
	
	/**
	 * 同步客户到账通知数据方法
	 */
	public void syncCustDzMsg() {
		log.info("同步到账通知数据方法开始执行。。。");
		List<CmRemindMsgParam> dzList = cmRemindMsgDayDao.queryCustDzMsgList();
        if (dzList.size() > 0) {
            for (CmRemindMsgParam cmremindmsg:dzList) {

				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());

				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("amount", StringUtil.isBlank(cmremindmsg.getAmount()) ? BigDecimal.ZERO.toPlainString() : new BigDecimal(cmremindmsg.getAmount()).toPlainString());
				paramMap.put("fee", StringUtil.isBlank(cmremindmsg.getFee()) ? BigDecimal.ZERO.toPlainString() : new BigDecimal(cmremindmsg.getFee()).toPlainString());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用发送到账通知)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用发送到账通知)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送到账通知成功:"+ JSON.toJSONString(request));
				}else{
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送到账通知失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
            }
        } else {
        	log.info("未找到需同步的到账通知数据！");
        }
		log.info("同步到账通知数据方法执行结束。。。");
	}

	/**
	 * 同步基金到期数据方法
	 */
	public void syncFundDqMsg() {
		log.info("同步基金到期数据方法开始执行。。。");
		List<CmRemindMsgParam> dqList = cmRemindMsgDayDao.queryFundDqMsgList();
		if (dqList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg:dqList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());

				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("date", cmremindmsg.getMsgContentDt());

				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用同步基金到期数据)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用同步基金到期数据)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送同步基金到期数据成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送同步基金到期数据失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的基金到期数据！");
		}
		log.info("同步基金到期数据方法执行结束。。。");
	}

	/**
	 * 同步客户净值通知数据方法
	 */
	public void syncFundJzMsg(Map<String, String> param) {
		log.info("生成净值消息数据方法开始执行。。。");
		List<CmRemindMsgDay> jzList = cmRemindMsgDayDao.queryFundJzMsgList(param);
		if (jzList.size() > 0) {

			List<CmRemindMsgDay> newMsgList = new ArrayList<CmRemindMsgDay>();
			for (int i = 0; i < jzList.size(); i++) {
				String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
				CmRemindMsgDay tempCmPushMsgDay = jzList.get(i);
				tempCmPushMsgDay.setId(msgId);
				// 6:净值通知
				tempCmPushMsgDay.setMsgType(MSG_TYPE_JZTZ);
				// 已读标识：0:未读取，1：已读取
				tempCmPushMsgDay.setReadFlag("0");
				tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
				tempCmPushMsgDay.setPushFlag("0");
				tempCmPushMsgDay.setPushDt(DateTimeUtil.fmtDate(tempCmPushMsgDay.getPushDt(), DateTimeUtil.DATE_TIME_PATTERN));
				tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
				newMsgList.add(tempCmPushMsgDay);
			}

			/*// 存放新消息到缓存
			addMsgCache(consMsgMap);*/
			if (newMsgList.size() > 0) {
				log.info("净值通知数据同步开始newMsgList："+newMsgList.size());
				this.batchInsertCmRemindMsgDay(newMsgList);
				log.info("净值通知数据同步成功！");
			}
		} else {
			log.info("未找到需要同步的净值消息数据！");
		}
		log.info("生成净值消息数据方法执行结束。。。");
	}

	/**
	 * 推送客户净值通知数据方法
	 */
	public void syncFundJzMsgForpush(Map<String, String> param) {
		log.info("推送净值消息数据方法开始执行。。。");
		List<CmJzMsgInfo> jzList = cmRemindMsgDayDao.queryFundJzMsgListForPush(param);
		if (jzList.size() > 0) {
			for (int i = 0; i < jzList.size(); i++) {
				CmJzMsgInfo info = jzList.get(i);
				if (null != info) {
					CmPushMsgRequest msgrequest = new CmPushMsgRequest();

					// 1、一账通号；2、投顾号
					msgrequest.setAccountType("2");
					msgrequest.setAccount(info.getConsCode());
					/*
					产品代码	${pcode}
					产品名称	${pname}
					净值日期	${date}
					净值	${nav}
					上一净值日期	${lastdate}
					涨跌幅	${chg}
					持仓客户数	${amount}
					*/
					Map<String, String> paramMap = new HashMap<>();
					paramMap.put("pcode", info.getPcode());
					paramMap.put("pname", info.getPname());
					paramMap.put("date", info.getDate());
					paramMap.put("nav", info.getNav());
					paramMap.put("amount", info.getAmount());
					// 如果上次净值日期为空，则调用“首次净值通知”模板
					if (StringUtils.isBlank(info.getLastDate())) {
						msgrequest.setBusinessId("200901");
					} else {
						paramMap.put("lastdate", info.getLastDate());
						paramMap.put("chg", info.getChg());

						// 不为空的调用“净值通知”模板
						msgrequest.setBusinessId("200386");
					}
					msgrequest.setParamJson(JSON.toJSONString(paramMap));
					log.info("(调用发送净值消息通知)请求:" + JSON.toJSONString(msgrequest));
					BaseResponse response = cmPushMsgService.pushMsg(msgrequest);
					log.info("(调用发送净值消息通知))结果:" + JSON.toJSONString(response));
					if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
						log.info("发送净值消息通知成功:" + JSON.toJSONString(msgrequest));
					} else {
						log.info("发送净值消息通知失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
					}
				}
			}
			log.info("净值通知数据同步成功！");
		} else {
			log.info("未找到需要同步的净值消息数据！");
		}
		log.info("推送净值消息数据方法执行结束。。。");
	}

	/**
	 * 同步预约客户通知数据方法
	 */
	public void syncCustYyMsg() {
		log.info("同步预约客户数据方法开始执行。。。");
		List<CmRemindMsgParam> yyList = cmRemindMsgDayDao.queryCustYyMsgList();
		if (yyList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg:yyList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());

				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("visit", cmremindmsg.getVisit());
				paramMap.put("date", cmremindmsg.getMsgContentDt());
				paramMap.put("starttime", cmremindmsg.getStarttime());
				paramMap.put("endtime", cmremindmsg.getEndtime());
				paramMap.put("schedule", cmremindmsg.getSchedule());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用预约客户数据)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用预约客户数据)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送预约客户数据成功:"+ JSON.toJSONString(request));
				}else{
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送预约客户数据失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的预约客户数据！");
		}
		log.info("同步预约客户数据方法执行结束。。。");
	}

	public void batchInsertCmCompanyReport(List<CompanyReport> reportlist,String datetime){

		log.info("同步资料更新数据方法开始执行。。。reportlistcount:"+reportlist.size());
		List<CmCompanyReport> cmreportlist = new ArrayList<CmCompanyReport>();
		//转换数据，落表
		for(CompanyReport report:reportlist){
			CmCompanyReport cmreport = new CmCompanyReport();
			BeanUtils.copyProperties(report,cmreport);
			cmreport.setSyncDate(DateUtil.parseDate(datetime,DateUtil.DEFAULT_DATESFM));
			cmreportlist.add(cmreport);
		}

		//落表
		if(cmreportlist.size() > 0){
			cmCompanyReportDao.batchInsertCmCompanyReport(cmreportlist);
		}
	}

	/**
	 * 同步资料更新通知数据方法
	 */
	public void syncCustZlgxMsg() {

		log.info("同步资料更新数据方法开始执行");

		//查询表数据
		List<CmRemindMsgDay> dzList = cmRemindMsgDayDao.queryCustZlgxMsgList();

		//放缓存
		if (dzList.size() > 0) {
			List<CmRemindMsgDay> newMsgList = new ArrayList<CmRemindMsgDay>();
			for (int i = 0; i < dzList.size(); i++) {
				String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
				CmRemindMsgDay tempCmPushMsgDay = dzList.get(i);
				tempCmPushMsgDay.setId(msgId);
				// 8:资料更新通知
				tempCmPushMsgDay.setMsgType(MSG_TYPE_ZLGX);
				// 已读标识：0:未读取，1：已读取
				tempCmPushMsgDay.setReadFlag("0");
				tempCmPushMsgDay.setCreator("nt-sys");
				// 推送标识：0:未推送，1：已推送
				tempCmPushMsgDay.setPushFlag("0");
				tempCmPushMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
				tempCmPushMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
				newMsgList.add(tempCmPushMsgDay);
			}

			// 存放新消息到缓存
			if (newMsgList.size() > 0) {
				this.batchInsertCmRemindMsgDay(newMsgList);
				log.info("资料更新数据同步成功！");
			}
		} else {
			log.info("未找到需同步的资料更新通知数据！");
		}
		log.info("同步资料更新数据方法执行结束。。。");
	}

	/**add by zhangshuai20211014
	 * 推送资料更新通知数据方法
	 */
	public void pushCustZlgxMsg(Map timeFlag) {

		log.info("推送资料更新数据方法开始执行");

		//查询表数据
		List<CmZlgxMsgInfo> dzList = cmRemindMsgDayDao.queryCustZlgxMsgListForPush(timeFlag);

		//放缓存
		if (dzList.size() > 0) {
			for (int i = 0; i < dzList.size(); i++) {
				CmZlgxMsgInfo info = dzList.get(i);
				if(null!=info){
					//接口推送消息  产品推送消息业务ID：200385  机构推送消息业务ID：200401
					/*类型  1产品0机构*/
					CmPushMsgRequest msgrequest = new CmPushMsgRequest();
					BaseResponse response;
					msgrequest.setBusinessId("1".equals(info.getType())?"200385":"200401");
					//1、一账通号；2、投顾号
					msgrequest.setAccountType("2");
					msgrequest.setAccount(info.getConsCode());
					/*200385	产品名称	${pname}
					  200385	报告类型	${reportType}
					  200401	机构名称	${conpname}
					  200401	报告类型	${reportType}*/

					Map<String,String> paramMap = new HashMap<String,String>();
					paramMap.put("1".equals(info.getType())?"pname":"conpname", info.getName());
					paramMap.put("title", info.getTitle());
					paramMap.put("reportType", info.getReportType());
					msgrequest.setParamJson(JSON.toJSONString(paramMap));
	
					log.info("(调用发送资料更新通知)请求:"+ JSON.toJSONString(msgrequest));
					response = cmPushMsgService.pushMsg(msgrequest);
					log.info("(调用发送资料更新通知))结果:"+ JSON.toJSONString(response));
	
					if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
						log.info("发送资料更新通知成功:"+ JSON.toJSONString(msgrequest));
					}else{
						log.info("发送资料更新通知失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
					}
				}
			}

			log.info("资料更新数据推送成功！");
		} else {
			log.info("未找到需推送的资料更新通知数据！");
		}
		log.info("推送资料更新数据方法执行结束。。。");
	}

	public void syncJylxqsMsg() {
		log.info("同步交易类型等于强赎的数据方法开始执行。。。");
		List<CmRemindMsgParam> fhList = cmRemindMsgDayDao.queryJylxqsMsgList();
		if (fhList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg:fhList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());

				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("date", cmremindmsg.getMsgContentDt());

				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用交易类型等于强赎)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用交易类型等于强赎)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送交易类型等于强赎成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送交易类型等于强赎失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到交易类型等于强赎的数据！");
		}
		log.info("同步交易类型等于强赎的数据方法结束。");
	}

	public void syncJylxhlffMsg() {
		log.info("同步交易类型等于红利发放的数据方法开始执行。。。");
		List<CmRemindMsgParam> fhList = cmRemindMsgDayDao.queryJylxhlffMsgList();
		if (fhList.size() > 0) {
			//List<CmRemindMsgDay> newMsgList = new ArrayList<CmRemindMsgDay>();
			for (CmRemindMsgParam cmremindmsg:fhList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());

				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("date", cmremindmsg.getMsgContentDt());

				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用同步交易类型等于红利发放)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用同步交易类型等于红利发放)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送同步交易类型等于红利发放成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送同步交易类型等于红利发放失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到交易类型等于红利发放的数据！");
		}
		log.info("同步交易类型等于红利发放的数据方法开始结束。");
	}

	/**
	 * 缴费提醒数据方法
	 */
	public void syncCustPaymentMsg() {

		log.info("同步资料更新数据方法开始执行");

		//查询表数据
		List<CmRemindMsgParam> dzList = cmRemindMsgDayDao.queryCustPaymentMsgList();

		//放缓存
		if (dzList.size() > 0) {
			//List<CmRemindMsgDay> newMsgList = new ArrayList<CmRemindMsgDay>();

			for (CmRemindMsgParam cmremindmsg:dzList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("deadline", cmremindmsg.getDeadline());

				request.setParamJson(JSON.toJSONString(paramMap));

				log.info("(调用创新产品缴费通知数据)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用创新产品缴费通知数据)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送创新产品缴费通知数据成功:"+ JSON.toJSONString(request));
				}else{
					log.info("发送创新产品缴费通知数据失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的创新产品缴费通知数据！");
		}
		log.info("创新产品缴费通知数据方法执行结束。。。");
	}

	/**
	 * 回收当天消息数据到历史表，同时删除当天表数据
	 */
	public void recycleDayMsgToHis() {
		log.info("消息回收方法开始执行。。。");

        // 回收当天表消息到历史表
        boolean updateOne = cmRemindMsgDayDao.batchInsertCmRemindMsgHis() >= 0;
        if (updateOne) {
            log.info("消息回收完成");
        } else {
            log.info("消息回收失败");
        }

        // 回收成功后，删除当天表中数据
        if (updateOne) {
            cmRemindMsgDayDao.deleteCmRemindMsgDay();
            log.info("当天消息表删除已完成");
        } else {
        	log.info("当天消息表处理失败");
        }

		log.info("消息回收方法执行结束。。。");
	}

	/**
	 * 将数据分割成多个批次，按批次插入表中
	 */
	public void batchInsertCmRemindMsgDay(List<CmRemindMsgDay> msgList) {
		// 每批次限制条数
		int batchLimit = 200;
		// 消息总条数
		int totalSize = msgList.size();

		// 消息总数大于每批次限制的，进行分割成多个批次进行插入
		if (totalSize > batchLimit) {
			// 总批次数
			int totalBatch = totalSize / batchLimit;
			for (int i = 0; i < totalBatch; i++) {
				List<CmRemindMsgDay> newMsgList = msgList.subList(0, batchLimit);
                cmRemindMsgDayDao.batchInsertCmRemindMsgDay(newMsgList);
				msgList.subList(0, batchLimit).clear();
			}

			// 表示最后剩下的数据
			if (!msgList.isEmpty()) {
                cmRemindMsgDayDao.batchInsertCmRemindMsgDay(msgList);
			}
		} else {
            cmRemindMsgDayDao.batchInsertCmRemindMsgDay(msgList);
		}

	}

	/**
	 * 客户赎回下单后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustShMsg() {
		List<CmRemindMsgParam> shList = cmRemindMsgDayDao.queryCustShMsgList();
		List<String> preIdList=shList.stream().map(CmRemindMsgParam::getPreId).collect(Collectors.toList());
		log.info("赎回通知数据方法,查询列表条数：{},预约ID列表：{}",shList.size(),JSON.toJSONString(preIdList));
		if (shList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg:shList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				//200430 客户:${custname}，购买产品:${pname}，赎回份额:${share}，赎回金额:${amount}，预计交易日期:${date}，赎回至:${whereabouts}
				//1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String,String> paramMap = new HashMap<String,String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("share", cmremindmsg.getShares());
                //代销 产品，只能按照份额赎回。  赎回 金额。 直接赋值 0
				paramMap.put("amount", "0");

				paramMap.put("date", cmremindmsg.getMsgContentDt());
				paramMap.put("whereabouts", cmremindmsg.getRedeemdirection());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{},业务属性preId:{}",key,cmremindmsg.getPreId());
					continue;
				}
				log.info("(调用赎回通知数据)请求:"+ JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用赎回通知数据)结果:"+ JSON.toJSONString(response));

				if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
					log.info("发送赎回通知数据成功:"+ JSON.toJSONString(request));
				}else{
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送赎回通知数据失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的赎回通知数据！");
		}
		log.info("赎回通知数据方法执行结束。。。");
	}

	/**
	 * 客户下单购买通知后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustXdgmtzMsg() {
		log.info("下单购买通知数据方法开始执行。。。");
		List<CmRemindMsgParam> xdgmList = cmRemindMsgDayDao.queryCustXdgmtzMsgList();
		if (xdgmList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg : xdgmList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				// 1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("amount", cmremindmsg.getAmount());
				paramMap.put("fee", cmremindmsg.getFee());
				paramMap.put("paystate", cmremindmsg.getPaystate());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用下单购买通知数据)请求:" + JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用下单购买通知数据)结果:" + JSON.toJSONString(response));

				if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					log.info("发送下单购买通知数据成功:" + JSON.toJSONString(request));
				} else {
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送下单购买通知数据失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的下单购买通知数据！");
		}
		log.info("下单购买通知数据方法执行结束。。。");
	}

	/**
	 * 客户订单确认失败【购买】后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustDdqrsbgmMsg() {
		log.info("订单确认失败【购买】数据方法开始执行。。。");
		List<CmRemindMsgParam> ddqrList = cmRemindMsgDayDao.queryCustDdqrsbgmMsgList();
		if (ddqrList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg : ddqrList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				// 1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("amount", cmremindmsg.getAmount());
				paramMap.put("fee", cmremindmsg.getFee());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用订单确认失败【购买】数据)请求:" + JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用订单确认失败【购买】数据)结果:" + JSON.toJSONString(response));

				if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					log.info("发送订单确认失败【购买】数据成功:" + JSON.toJSONString(request));
				} else {
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送订单确认失败【购买】数据失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的订单确认失败【购买】数据！");
		}
		log.info("订单确认失败【购买】数据方法执行结束。。。");
	}

	/**
	 * 客户订单确认失败【赎回】后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustDdqrsbshMsg() {
		log.info("订单确认失败【赎回】数据方法开始执行。。。");
		List<CmRemindMsgParam> ddqrList = cmRemindMsgDayDao.queryCustDdqrsbshMsgList();
		if (ddqrList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg : ddqrList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				// 1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("share", cmremindmsg.getShares());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用订单确认失败【赎回】数据)请求:" + JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用订单确认失败【赎回】数据)结果:" + JSON.toJSONString(response));

				if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					log.info("发送订单确认失败【赎回】数据成功:" + JSON.toJSONString(request));
				} else {
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送订单确认失败【赎回】数据失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的订单确认失败【赎回】数据！");
		}
		log.info("订单确认失败【赎回】数据方法执行结束。。。");
	}

	/**
	 * 客户撤销购买通知后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustCxgmtzMsg() {
		log.info("撤销购买通知数据方法开始执行。。。");
		List<CmRemindMsgParam> cxgmList = cmRemindMsgDayDao.queryCustCxgmtzMsgList();
		if (cxgmList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg : cxgmList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				// 1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("amount", cmremindmsg.getAmount());
				paramMap.put("fee", cmremindmsg.getFee());
				paramMap.put("paystate", cmremindmsg.getPaystate());

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用撤销购买通知数据)请求:" + JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用撤销购买通知数据)结果:" + JSON.toJSONString(response));

				if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					log.info("发送撤销购买通知数据成功:" + JSON.toJSONString(request));
				} else {
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送撤销购买通知数据失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的撤销购买通知数据！");
		}
		log.info("撤销购买通知数据方法执行结束。。。");
	}

	/**
	 * 客户撤销赎回通知后，中台将下单状态同步给CRM时，CRM生成消息
	 */
	public void syncCustCxshtzMsg() {
		log.info("撤销赎回通知数据方法开始执行。。。");
		List<CmRemindMsgParam> cxshList = cmRemindMsgDayDao.queryCustCxshtzMsgList();
		if (cxshList.size() > 0) {
			for (CmRemindMsgParam cmremindmsg : cxshList) {
				CmPushMsgRequest request = new CmPushMsgRequest();
				BaseResponse response;
				request.setBusinessId(cmremindmsg.getBusinessid());
				// 1、一账通号；2、投顾号
				request.setAccountType("2");
				request.setAccount(cmremindmsg.getConsCode());
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("custname", cmremindmsg.getCustname());
				paramMap.put("pname", cmremindmsg.getPname());
				paramMap.put("share", cmremindmsg.getShares());
				//代销 产品，只能按照份额赎回。  赎回 金额。 直接赋值 0
				paramMap.put("amount", "0");

				request.setParamJson(JSON.toJSONString(paramMap));

				// 获取锁,防止重复发送
				String key = CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode();
				boolean flag = lockService.getLock(CacheKeyPrefix.LOCK_KEY_PREFIX+cmremindmsg.getMsgCode(),TWO_DAY_SECOND);
				if (!flag){
					log.info("getLock fail,key:{}",key);
					continue;
				}
				log.info("(调用撤销赎回通知数据)请求:" + JSON.toJSONString(request));
				response = cmPushMsgService.pushMsg(request);
				log.info("(调用撤销赎回通知数据)结果:" + JSON.toJSONString(response));

				if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
					log.info("发送撤销赎回通知数据成功:" + JSON.toJSONString(request));
				} else {
					// 如果消息发送失败，释放锁
					lockService.releaseLock(key);
					log.info("发送撤销赎回通知数据失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
				}
			}
		} else {
			log.info("未找到需同步的撤销赎回通知数据！");
		}
		log.info("撤销赎回通知数据方法执行结束。。。");
	}
	
    /**
     * 查询符合新规任务生成提醒通知的信息条数
     * @return
     */
	public Integer countEcDoubleRecordMessage(){
	   return cmRemindMsgDayDao.countEcDoubleRecordMessage();
   }

	/**
	 * //抓出PC待发送的消息
	 * @return
	 */
	public List<CmPushMsg> getPCExpectPushMsg() {
		return cmRemindMsgDayDao.getPCExpectPushMsg();
	}


	/**
	 * 批量更新消息推送状态
	 * @param subList
	 * @param pushFlag
	 */
	public void batchUpdatePushFlag(List<CmPushMsg> subList,String pushFlag) {
		cmRemindMsgDayDao.batchUpdatePushFlag(subList,pushFlag);
	}

	/**
	 *插入CM_REMIND_MSG_DAY表   并更新CM_PUSH_MSG 表的 PushFlag
	 * @param dto
	 */
	public void insertCmRemindMsgDayAndUpdatePushFlag(CmPushMsg dto) {
		cmRemindMsgDayDao.insertCmRemindMsgDayAndUpdatePushFlag(dto);
	}

	/**
	 *抓出企业微信待发送的消息
	 * @return
	 */
	public List<CmPushMsg> getWechatExpectPushMsg() {
		return cmRemindMsgDayDao.getWechatExpectPushMsg();
	}

	/**
	 * @description 企微任务推送
	 * @param
	 * @return java.util.List<com.howbuy.crm.nt.pushmsg.dto.CmPushMsg>
	 * @author: jianjian.yang
	 * @date: 2023/10/11 18:05
	 * @since JDK 1.8
	 */
	public List<CmPushMsg> getWechatTaskPushMsg(){
		return cmRemindMsgDayDao.getWechatTaskPushMsg();
	}

	/**
	 * 定制发送的标题
	 * @return
	 */
	public CmPushMsgAnnex getCmPushMsgAnnexTitle(String pushid) {
		return cmRemindMsgDayDao.getCmPushMsgAnnexTitle(pushid);
	}

	/**
	 * 定制发送的模板消息
	 * @return
	 */
	public List<CmPushMsgAnnex> listPushMsgAnnex(String pushid) {
		return cmRemindMsgDayDao.listPushMsgAnnex(pushid);
	}
}

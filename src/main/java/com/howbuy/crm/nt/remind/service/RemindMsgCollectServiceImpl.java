package com.howbuy.crm.nt.remind.service;

import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("remindMsgCollectService")
@Slf4j
public class RemindMsgCollectServiceImpl implements RemindMsgCollectService {

	@Autowired
	private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;

	/** 产品分红任务--分红提醒 */
	public static final String ARG_CPFH = "cpfh";
	/** 加载产品到期任务--到期提醒 */
	public static final String ARG_CPDQ = "cpdq";
	/** 客户生日任务--客户生日 */
	public static final String ARG_KHSR = "khsr";
	/** 更新通知:交易类型 = 强赎 到期通知 */
	public static final String ARG_JYLXQS = "jylxqs";
	/** 更新通知:交易类型 = 红利发放 分红通知 */
	public static final String ARG_JYLXHLFF = "jylxhlff";
	/** 券商产品缴费提醒任务 缴款通知 */
	public static final String ARG_PAYMENT = "payment";

	/**
	 * 同步消息数据方法
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void syncPushMsgData(String arg) {
		log.info("调度魔方传入参数：" + arg);

		// 回收当天表数据
		cmRemindMsgDaoBuss.recycleDayMsgToHis();

		// 加载产品分红任务--分红提醒
		if (arg != null && arg.contains(ARG_CPFH)) {
			cmRemindMsgDaoBuss.syncFundFhMsg();
		} else {// 同步基金分红数据方法
			log.info("产品分红任务已停用！");
		}

		// 加载产品到期任务--到期提醒
		if (arg != null && arg.contains(ARG_CPDQ)) {
			cmRemindMsgDaoBuss.syncFundDqMsg();
		} else {// 同步产品到期数据方法
			log.info("产品到期任务已停用！");
		}

		// 加载客户生日任务--客户生日
		if (arg != null && arg.contains(ARG_KHSR)) {
			cmRemindMsgDaoBuss.syncCustBirthDayMsg();
		} else {// 同步客户生日数据方法
			log.info("客户生日任务已停用！");
		}

		// 加载更新通知:交易类型 = 强赎 到期通知
		if (arg != null && arg.contains(ARG_JYLXQS)) {
			cmRemindMsgDaoBuss.syncJylxqsMsg();
		} else {
			log.info("交易类型:强赎通知任务已停用！");
		}

		// 加载更新通知:交易类型 = 红利发放 分红通知
		if (arg != null && arg.contains(ARG_JYLXHLFF)) {
			cmRemindMsgDaoBuss.syncJylxhlffMsg();
		} else {
			log.info("交易类型:红利发放通知任务已停用！");
		}

		// 加载券商产品缴费提醒任务 缴款通知
		if (arg != null && arg.contains(ARG_PAYMENT)) {
			cmRemindMsgDaoBuss.syncCustPaymentMsg();
		} else {// 同步产品到期数据方法
			log.info("券商产品缴费提醒已停用！");
		}

		/*
		// 加载资料更新通知
		if (arg != null && arg.contains("zlgxtz")) {
			cmPushMsgDaoBuss.syncCustZlgxMsg();
		} else {// 同步到账通知数据方法
			log.info("资料更新通知任务已停用！");
		}

		// 加载当前列表数据到缓存
		cmPushMsgDaoBuss.loadCurAllMsgCache();*/

	}
	
	
	
}
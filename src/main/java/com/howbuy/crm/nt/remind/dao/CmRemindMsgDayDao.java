package com.howbuy.crm.nt.remind.dao;

import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex;
import com.howbuy.crm.nt.remind.domain.CmCustBirthday;
import com.howbuy.crm.nt.remind.domain.CmJzMsgInfo;
import com.howbuy.crm.nt.remind.domain.CmRemindMsgParam;
import com.howbuy.crm.nt.remind.domain.CmZlgxMsgInfo;
import crm.howbuy.base.domain.CmRemindMsgDay;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface CmRemindMsgDayDao {

    /**
     * 获取基金分红消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryFundFhMsgList();

    /**
     * 获取基金到期消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryFundDqMsgList();

    /**
     * 获取投顾客户生日提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmCustBirthday> queryBirthDayMsgList();

    /**
     * 获取到账通知提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustDzMsgList();

    /**
     * 获取资料更新通知提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgDay> queryCustZlgxMsgList();
    
    /**
	 * 获取净值通知提醒消息数据
	 * @return List<CmPushMsgDay>
	 */
	List<CmRemindMsgDay> queryFundJzMsgList(Map<String, String> param);

	/**
     * 获取预约客户提醒消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustYyMsgList();

    /**
     * 获取下单赎回通知消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustShMsgList();

    /**
     * 获取下单购买通知消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustXdgmtzMsgList();

    /**
     * 获取下单确认失败【购买】消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustDdqrsbgmMsgList();

    /**
     * 获取下单确认失败【赎回】消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustDdqrsbshMsgList();

    /**
     * 获取撤销购买通知消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustCxgmtzMsgList();

    /**
     * 获取撤销赎回通知消息数据
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustCxshtzMsgList();

    /**
     * 获取交易类型 消息数据:交易类型 = 强赎
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryJylxqsMsgList();

    /**
     * 获取交易类型 消息数据:交易类型 = 红利发放
     *
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryJylxhlffMsgList();

    /**
     * 获取缴费提醒消息数据
     * @return List<CmPushMsgDay>
     */
    List<CmRemindMsgParam> queryCustPaymentMsgList();

    /**
     * 批量插入当天消息表
     *
     * @param listCmRemindMsgDay
     */
    void batchInsertCmRemindMsgDay(List<CmRemindMsgDay> listCmRemindMsgDay);

    /**
     * 批量回收当天表数据到历史表
     */
    Integer batchInsertCmRemindMsgHis();

    /**
     * 清空当天表数据
     */
    Integer deleteCmRemindMsgDay();

    /**
     * 清空当天历史表数据
     */
    /*Integer deleteCurCmPushMsgHis();*/
    
    /**
     * 查询符合新规任务生成提醒通知的信息条数
     *
     * @param map
     * @return
     */
    Integer countEcDoubleRecordMessage();

    /**
     * 获取资料更新通知提醒消息数据
     * @return
     */
    List<CmZlgxMsgInfo> queryCustZlgxMsgListForPush(Map<String, String> param);

    /**
     * 获取净值通知提醒消息模板数据
     * @return List<CmJzMsgInfo>
     */
    List<CmJzMsgInfo> queryFundJzMsgListForPush(Map<String, String> param);

    /**
     * //抓出PC待发送的消息
     * @return
     */
    List<CmPushMsg> getPCExpectPushMsg();

    /**
     * 批量更新消息推送状态
     * @param subList
     * @param pushFlag
     */
    void batchUpdatePushFlag(@Param("msgList")List<CmPushMsg> subList, @Param("pushFlag") String pushFlag);

    /**
     * 	 *插入CM_REMIND_MSG_DAY表   并更新CM_PUSH_MSG 表的 PushFlag
     * @param dto
     */
    void insertCmRemindMsgDayAndUpdatePushFlag(CmPushMsg dto);

    /**
     * 抓出企业微信待发送的消息
     * @return
     */
    List<CmPushMsg> getWechatExpectPushMsg();

    /**
     * @description 企微任务推送
     * @param
     * @return java.util.List<com.howbuy.crm.nt.pushmsg.dto.CmPushMsg>
     * @author: jianjian.yang
     * @date: 2023/10/11 18:05
     * @since JDK 1.8
     */
    List<CmPushMsg> getWechatTaskPushMsg();

    /**
     *
     * @param pushid
     * @return
     */
    CmPushMsgAnnex getCmPushMsgAnnexTitle(String pushid);

    /**
     * 查询推送消息
     * @param pushid
     * @return
     */
    List<CmPushMsgAnnex> listPushMsgAnnex(String pushid);
}

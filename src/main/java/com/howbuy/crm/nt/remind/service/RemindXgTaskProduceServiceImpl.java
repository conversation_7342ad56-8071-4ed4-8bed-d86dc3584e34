package com.howbuy.crm.nt.remind.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.util.DateTimeUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.enums.CrmUserRoleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 新规任务生成提醒通知
 *
 * <AUTHOR>
 */
@Service("remindXgTaskProduceService")
@Slf4j
public class RemindXgTaskProduceServiceImpl implements RemindXgTaskProduceService {

    @Autowired
    private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;

    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Autowired
    private CmPushMsgDao cmPushMsgDao;

    @Override
    public void remindXgTaskProduce(String arg) {
        log.info("新规任务生成提醒通知开始");

        try {
            Integer count = cmRemindMsgDaoBuss.countEcDoubleRecordMessage();

            if (count != null && count > 0) {
                CmPushMsgRequest request = new CmPushMsgRequest();
                BaseResponse response = null;
                request.setBusinessId("200041");
                //1、PC端；2、微信
                request.setPushChannel("1");
                //1、一账通号；2、投顾号
                request.setAccountType("2");

                List<String> conscodes = getConscode();
                if (conscodes != null && conscodes.size() != 0) {
                    for (String conscode : conscodes) {
                        request.setAccount(conscode);

                        Map<String, String> paramMap = new HashMap<String, String>();
                        paramMap.put("time", DateTimeUtil.getCurDate("yyyyMMdd HH:mm"));
                        paramMap.put("count", String.valueOf(count));
                        request.setParamJson(JSON.toJSONString(paramMap));

                        log.info("(调用发送新规任务生成提醒通知)请求:" + JSON.toJSONString(request));
                        response = cmPushMsgService.pushMsg(request);
                        log.info("(调用发送新规任务生成提醒通知)结果:" + JSON.toJSONString(response));

                        if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
                            log.info("发送新规任务生成提醒通知成功:" + JSON.toJSONString(request));
                        } else {
                            log.info("发送新规任务生成提醒通知失败，失败原因:" + (null == response ? "response为空" : response.getDescription()));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理新规任务生成提醒通知异常：{},{}", e.getMessage(), e);
        }

        log.info("新规任务生成提醒通知结束");
    }

    private List<String> getConscode() {
        List<String> listConscode = new ArrayList<String>();

        Map<String, String> paramrole = new HashMap<String, String>(1);
        paramrole.put("rolecode", CrmUserRoleEnum.ROLE_PS_HEAD.getCode());
        List<Map<String, String>> listuser = cmPushMsgDao.getConsCodeAndOrgCodeByRoleCode(paramrole);
        if (listuser != null && listuser.size() != 0) {
            for (Map<String, String> user : listuser) {
                if (StringUtils.isNotBlank(user.get("CONSCODE"))) {
                    listConscode.add(user.get("CONSCODE"));
                }
            }
        }

        paramrole.put("rolecode", StaticVar.ROLE_PS);
        listuser = cmPushMsgDao.getConsCodeAndOrgCodeByRoleCode(paramrole);
        if (listuser != null && listuser.size() != 0) {
            String conscode = null;
            for (Map<String, String> user : listuser) {
                conscode = user.get("CONSCODE");
                if (StringUtils.isNotBlank(conscode) && !listConscode.contains(conscode)) {
                    listConscode.add(conscode);
                }
            }
        }

        return listConscode;
    }
}

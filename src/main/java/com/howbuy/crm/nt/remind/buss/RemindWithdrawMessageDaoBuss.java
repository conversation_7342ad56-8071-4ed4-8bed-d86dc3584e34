package com.howbuy.crm.nt.remind.buss;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.pushmsg.dao.CmPushMsgDao;
import com.howbuy.crm.nt.pushmsg.dto.CmPushMsg;
import com.howbuy.crm.nt.remind.dao.RemindWithdrawMessageDao;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class RemindWithdrawMessageDaoBuss {

    @Autowired
    private RemindWithdrawMessageDao remindWithdrawMessageDao;
    @Autowired
    private CommonDao commondao;
    @Autowired
    private CmPushMsgDao cmPushMsgDao;

    /** 消息模板字符串获取占位符的正则表达式 */
    public static final Pattern TEMPLATE_REPLACEMENT_REG = Pattern.compile("\\$\\{\\w+\\}");


    public void pushMsg(String msgtype, String msgContent, String remark) {
        String pushMsgId = commondao.getSeqValue("SEQ_PUSH_MSG");
        CmPushMsg pushmsg = new CmPushMsg();
        pushmsg.setPushid(pushMsgId);
        pushmsg.setMsgtype(msgtype);
        pushmsg.setMsgcontent(msgContent);
        pushmsg.setConscode("");
        pushmsg.setOrgcode("");
        pushmsg.setCreator("nt-sys");
        pushmsg.setExpectpushdt(new Date());
        // 发送状态：取消
        pushmsg.setPushflag(StaticVar.MSG_PUSHFLAG_HASCANCEL);
        // 消息形式：系统
        pushmsg.setMsgstyle(StaticVar.PUSH_MSG_SYS);
        // 消息通道：PC端
        pushmsg.setMsgchannel(StaticVar.PUSH_MSG_CHANNEL_PC);
        pushmsg.setRemark(remark);
        cmPushMsgDao.insertCmPushMsg(pushmsg);

    }

    /**
     * 通过业务模板推送消息
     * @param msgtype 消息类型
     * @param businessId 业务id
     * @param paramMap 模板参数
     * @param remark 备注
     */
    public void pushMsgByBusinessTemplate(String msgtype, String businessId, Map<String,String> paramMap, String remark) {
        String templateStr = getTemplateStrByBusinessId(businessId);
        pushMsgByTemplateStr(msgtype, templateStr, paramMap, remark);
    }

    /**
     * 通过模板字符串推送消息
     * @param msgtype 消息类型
     * @param templateStr 模板字符串
     * @param paramMap 模板参数
     * @param remark 备注
     */
    public void pushMsgByTemplateStr(String msgtype, String templateStr, Map<String,String> paramMap, String remark) {
        String s = processTemplate(templateStr, paramMap);
        pushMsg(msgtype, s, remark);
    }

    /**
     * 根据业务id获取模板字符串
     * @param businessId 业务id
     * @return
     */
    private String getTemplateStrByBusinessId(String businessId) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("businessId", businessId);
        Map<String,String> templateMap = cmPushMsgDao.getBusinessTemplateByBusinessId(paramMap);
        String text;
        if (templateMap == null || StringUtil.isNullStr((text = templateMap.get("PC_TEMPLATE_TEXT")))) {
            throw new RuntimeException("businessId "+businessId+"对应的模板为空，或模板字符串为空");
        }
        return text;
    }

    /**
     * 处理模板字符串
     * @param template 模板字符串
     * @param params 模板参数
     * @return
     */
    private String processTemplate(String template, Map<String, String> params){
        StringBuffer sb = new StringBuffer();
        Matcher m = TEMPLATE_REPLACEMENT_REG.matcher(template);
        while (m.find()) {
            String param = m.group();
            String value = params.get(param.substring(2, param.length() - 1));
            m.appendReplacement(sb, value==null ? "" : value);
        }
        m.appendTail(sb);
        return sb.toString();
    }
}

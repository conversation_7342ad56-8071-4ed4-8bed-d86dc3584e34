package com.howbuy.crm.nt.remind.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.exception.BusinessException;
import com.howbuy.crm.common.dao.CommonDao;
import com.howbuy.crm.nt.base.model.RemindEnum;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.buss.RemindWithdrawMessageDaoBuss;
import com.howbuy.crm.nt.remind.dao.CmRemindMsgDayDao;
import com.howbuy.crm.nt.remind.dao.RemindWithdrawMessageDao;
import com.howbuy.crm.nt.remind.dto.CmDoubleTrade;
import com.howbuy.crm.nt.remind.request.QueryRemindRequest;
import com.howbuy.crm.util.DateTimeUtil;
import crm.howbuy.base.domain.CmRemindMsgDay;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2020/10/15 10:29
 * @Description
 * @Version 1.0
 */
@Service("remindWithdrawMessageService")
@Slf4j
public class RemindWithdrawMessageServiceImpl implements RemindWithdrawMessageService {

    @Autowired
    private RemindWithdrawMessageDao remindWithdrawMessageDao;
    @Autowired
    private RemindWithdrawMessageDaoBuss remindWithdrawMessageDaoBuss;
    @Autowired
    private CommonDao commondao;
    @Autowired
    private CmRemindMsgDayDao cmRemindMsgDayDao;
    @Autowired
    private CmPushMsgService cmPushMsgService;
    /**
     * 消息类型：双录退回
     */
    @Override
    public void withdrawMessage(QueryRemindRequest request) {

        log.info("QueryRemindRequest:"+ JSON.toJSON(request));
        if (request != null) {
            if (RemindEnum.MSG_TYPE_ZLTH.getCode().equals(request.getRemindType())) {
                //TODO: 资料管理业务逻辑 已删除。
                throw new BusinessException("9999","不支持资料退回业务逻辑处理!");
            }else if(RemindEnum.MSG_TYPE_SLTH.getCode().equals(request.getRemindType())){
                this.withdrawSlthMessage(request.getId());
            }
        }
    }

    /**
     * 通过业务模板推送消息--特殊处理用
     * 未知业务 待重构
     * @param msgtype 消息类型
     * @param businessId 业务id
     * @param paramMap 模板参数
     * @param remark 备注
     */
    @Override
    public void pushSpecialMsgByBusinessTemplate(RemindEnum msgtype, String businessId, Map<String,String> paramMap, String remark){
        remindWithdrawMessageDaoBuss.pushMsgByBusinessTemplate(msgtype.getCode(), businessId, paramMap, remark);
    }

    /**
     * withdrawSlthMessage
     * TODO: 待重构
     * @param id
     * @return void
     * @Author: yu.zhang on 2020/11/5 10:52
     */
    public void withdrawSlthMessage(String id){
        CmDoubleTrade order = remindWithdrawMessageDao.getCmDoubleTrade(id);

        if (order != null && StringUtils.isNotBlank(order.getConscode())) {
            //List<CmRemindMsgDay> cmRemindMsgList = new ArrayList<>();
            //获取对应投资顾问助理
            List<String> salesAssistantList = cmPushMsgService.getSalesAssistantWithAuth(order.getConscode());
            //分部销助角色发送消息
            if(salesAssistantList != null && salesAssistantList.size() > 0){
                for(String usercode:salesAssistantList){
                    //改用消息模板推送
                    this.pushSlthMsg("2",usercode,order);
                }
            // 找不到消息接收人（分部销助）
            } else {
                String msgTemplate = "【双录退回】客户：${custname}(客户号：${conscustno}，" +
                        "所属投顾：${consname})产品：${productName}对应上传的双录文件审核不通过，请通知投顾及时处理。";
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("custname", order.getCustname());
                paramMap.put("conscustno", order.getConscustno());
                paramMap.put("consname", order.getConsname());
                paramMap.put("productName", order.getJjjc());
                remindWithdrawMessageDaoBuss.pushMsgByTemplateStr(RemindEnum.MSG_TYPE_SLTH.getCode(), msgTemplate, paramMap, "未取到部门销助");
            }

            // 发送人员 包含了 consCode
            boolean msgcheck = salesAssistantList.contains(order.getConscode());
            // 发送人员 包含了 creator
            boolean crecheck = salesAssistantList.contains(order.getCreator());

            //分部销助中不包含此投顾的情况下，上传人是否等于投顾
            if(!msgcheck){
                if(order.getConscode().equals(order.getCreator())){
                    //拼接消息发送给创建人
                    /*String msg = "【"+RemindEnum.MSG_TYPE_SLTH.getDescription()+"】客户："+order.getCustname()
                            +"(客户号："+order.getConscustno()+"，所属投顾："+order.getConsname()+")产品："+
                            order.getJjjc()+"对应上传的双录文件审核不通过，请通知投顾及时处理。";
                    cmRemindMsgList.add(this.generateMessage(order,order.getConscode(),msg));*/
                    this.pushSlthMsg("2",order.getConscode(),order);
                }else{
                    //拼接消息发送给投顾
                   /* String msg = "【"+RemindEnum.MSG_TYPE_SLTH.getDescription()+"】客户："+order.getCustname()
                            +"(客户号："+order.getConscustno()+")产品："+order.getJjjc()+"对应上传的双录文件审核不通过，请及时处理。";
                    cmRemindMsgList.add(this.generateMessage(order,order.getConscode(),msg));*/
                    this.pushSlthMsg("1",order.getConscode(),order);
                    if(!crecheck && StringUtils.isNotBlank(order.getCreator())){
                        //拼接消息发送给创建人
                        /*String cremsg = "【"+RemindEnum.MSG_TYPE_SLTH.getDescription()+"】客户："+order.getCustname()
                                +"(客户号："+order.getConscustno()+"，所属投顾："+order.getConsname()+")产品："
                                +order.getJjjc()+"对应上传的双录文件审核不通过，请通知投顾及时处理。";
                        cmRemindMsgList.add(this.generateMessage(order,order.getCreator(),cremsg));*/
                        this.pushSlthMsg("2",order.getCreator(),order);
                    }
                }
            }else{
                if(!crecheck && StringUtils.isNotBlank(order.getCreator())){
                    //拼接消息发送给创建人
                   /* String msg = "【"+RemindEnum.MSG_TYPE_SLTH.getDescription()+"】客户："+order.getCustname()
                            +"(客户号："+order.getConscustno()+"，所属投顾："+order.getConsname()+")产品："
                            +order.getJjjc()+"对应上传的双录文件审核不通过，请通知投顾及时处理。";
                    cmRemindMsgList.add(this.generateMessage(order,order.getCreator(),msg));*/
                    this.pushSlthMsg("2",order.getCreator(),order);
                }
            }

            //this.executeBatchInsertMessage(cmRemindMsgList,RemindEnum.MSG_TYPE_SLTH.getCode());
        }
    }

    /**
     * 改用消息模板推送双录推回消息
     * @param type 1双录退回-给投顾   2双录退回-给销助/上传人
     * @param conscode 投顾号
     * @param order 双录信息
     * 模板1：给投顾（业务ID：200403）
     * 【双录退回】客户：${custname}（投顾客户号：${custno}）产品：${pname} 对应上传的双录文件审核不通过，请及时处理。
     *
     * 模板2：给分部销助/上传人（业务ID：200404）
     * 【双录退回】客户：${custname}（投顾客户号：${custno}，所属投顾：${consname}）产品：${pname} 对应上传的双录文件审核不通过，请通知投顾及时处理。
     */
    public void pushSlthMsg(String type,String conscode, CmDoubleTrade order) {
        CmPushMsgRequest msgrequest = new CmPushMsgRequest();
        BaseResponse response;
        //1、一账通号；2、投顾号
        msgrequest.setAccountType("2");
        msgrequest.setAccount(conscode);
        Map<String,String> paramMap = new HashMap<String,String>();
        paramMap.put("custname",order.getCustname());
        paramMap.put("custno",order.getConscustno());
        paramMap.put("pname",order.getFundcode()+" "+order.getJjjc());
        if("1".equals(type)){
            msgrequest.setBusinessId("200403");
        }else{
            msgrequest.setBusinessId("200404");
            paramMap.put("consname",order.getConsname());
        }
        msgrequest.setParamJson(JSON.toJSONString(paramMap));
        log.info("(调用发送双录退回通知)请求:"+ JSON.toJSONString(msgrequest));
        response = cmPushMsgService.pushMsg(msgrequest);
        log.info("(调用发送双录退回通知))结果:"+ JSON.toJSONString(response));

        if(response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())){
            log.info("发送双录退回通知成功:"+ JSON.toJSONString(msgrequest));
        }else{
            log.info("发送双录退回通知失败，失败原因:"+ (null== response? "response为空":response.getDescription()));
        }
    }

    /**
     * generateMessage
     * @param order
     * @param conscode
     * @param message
     * @return crm.howbuy.base.domain.CmRemindMsgDay
     * @Author: yu.zhang on 2020/11/5 10:48
     */
    public CmRemindMsgDay generateMessage(CmDoubleTrade order, String conscode,String message){

        CmRemindMsgDay cmRemindMsgDay = new CmRemindMsgDay();
        cmRemindMsgDay.setConsCode(conscode);
        //投顾号和消息拼接加密
        cmRemindMsgDay.setMsgCode(DigestUtils.md5DigestAsHex((conscode + message).getBytes()));
        cmRemindMsgDay.setMsgContent(message);
        cmRemindMsgDay.setMsgContentDt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));
        cmRemindMsgDay.setExpireDt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));

        return cmRemindMsgDay;
    }
    /**
     * 功能描述: <br>
     * <同步资料退回数据>
     *
     * @Param: [cmPushMsgList]
     * @Return: void
     * @Author: pei.luo
     * @Date: 2020/10/15 16:39
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeBatchInsertMessage(List<CmRemindMsgDay> cmRmindMsgList,String msgType) {
        if (cmRmindMsgList.size() > 0) {
            List<CmRemindMsgDay> newMsgList = new ArrayList<>();
            for (int i = 0; i < cmRmindMsgList.size(); i++) {
                String msgId = commondao.getSeqValue("SEQ_CM_PUSH_MSG_DAY_ID");
                CmRemindMsgDay cmRemindMsgDay = cmRmindMsgList.get(i);
                cmRemindMsgDay.setId(msgId);
                cmRemindMsgDay.setMsgType(msgType);
                cmRemindMsgDay.setReadFlag("0");
                cmRemindMsgDay.setCreator("nt-sys");
                cmRemindMsgDay.setPushDt(DateTimeUtil.getCurDateTimeFmt());
                cmRemindMsgDay.setCreDt(DateTimeUtil.getCurDateTimeFmt());
                // 推送标识：0:未推送，1：已推送
                cmRemindMsgDay.setPushFlag("0");
                cmRemindMsgDay.setStimeStamp(new java.sql.Timestamp(System.currentTimeMillis()));
                newMsgList.add(cmRemindMsgDay);
            }

            // 将新消息插入当天消息表
            if (newMsgList.size() > 0) {
                cmRemindMsgDayDao.batchInsertCmRemindMsgDay(newMsgList);
                log.info("资料退回数据同步成功！");
            }
        } else {
            log.info("未找到需同步的资料退回数据！");
        }
        log.info("同步资料退回数据方法执行结束。。。");
    }
    
}

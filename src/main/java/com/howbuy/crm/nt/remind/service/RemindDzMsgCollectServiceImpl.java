package com.howbuy.crm.nt.remind.service;

import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("remindDzMsgCollectService")
@Slf4j
public class RemindDzMsgCollectServiceImpl implements RemindDzMsgCollectService {

    @Autowired
    private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;

    /** 到账通知 */
    public static final String ARG_DZTZ = "dztz";
    /** 客户预约任务--预约事项 */
    public static final String ARG_YYTZ = "yytz";
    /** 投顾消息中心--赎回通知 */
    public static final String ARG_SHTZ = "shtz";
    /** 投顾消息中心--下单购买通知 */
    public static final String ARG_XDGMTZ = "xdgmtz";
    /** 投顾消息中心--订单确认失败-购买 */
    public static final String ARG_DDQRSB_GM = "ddqrsbgm";
    /** 投顾消息中心--订单确认失败-赎回 */
    public static final String ARG_DDQRSB_SH = "ddqrsbsh";
    /** 投顾消息中心--撤销购买通知 */
    public static final String ARG_CXGMTZ = "cxgmtz";
    /** 投顾消息中心--撤销赎回通知 */
    public static final String ARG_CXSHTZ = "cxshtz";

    /**
     * 同步到账通知消息数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
	public void syncCustDzMsgData(String arg) {
        // 加载到账通知任务
        if (arg != null && arg.contains(ARG_DZTZ)) {
            cmRemindMsgDaoBuss.syncCustDzMsg();
        } else {// 同步到账通知数据方法
            log.info("到账通知任务已停用！");
        }

        // 加载客户预约任务--预约事项
        if (arg != null && arg.contains(ARG_YYTZ)) {
            cmRemindMsgDaoBuss.syncCustYyMsg();
        } else {// 同步客户预约数据方法
            log.info("客户预约任务已停用！");
        }

        // 投顾消息中心--赎回通知
        if (arg != null && arg.contains(ARG_SHTZ)) {
            cmRemindMsgDaoBuss.syncCustShMsg();
        } else {
            log.info("赎回通知任务已停用！");
        }

        // 投顾消息中心--下单购买通知
        if (arg != null && arg.contains(ARG_XDGMTZ)) {
            cmRemindMsgDaoBuss.syncCustXdgmtzMsg();
        } else {
            log.info("下单购买通知任务已停用！");
        }

        // 投顾消息中心--订单确认失败-购买
        if (arg != null && arg.contains(ARG_DDQRSB_GM)) {
            cmRemindMsgDaoBuss.syncCustDdqrsbgmMsg();
        } else {
            log.info("订单确认失败-购买任务已停用！");
        }

        // 投顾消息中心--订单确认失败-赎回
        if (arg != null && arg.contains(ARG_DDQRSB_SH)) {
            cmRemindMsgDaoBuss.syncCustDdqrsbshMsg();
        } else {
            log.info("订单确认失败-赎回任务已停用！");
        }

        // 投顾消息中心--撤销购买通知
        if (arg != null && arg.contains(ARG_CXGMTZ)) {
            cmRemindMsgDaoBuss.syncCustCxgmtzMsg();
        } else {
            log.info("撤销购买通知任务已停用！");
        }

        // 投顾消息中心--撤销赎回通知
        if (arg != null && arg.contains(ARG_CXSHTZ)) {
            cmRemindMsgDaoBuss.syncCustCxshtzMsg();
        } else {
            log.info("撤销赎回通知任务已停用！");
        }
	}

}
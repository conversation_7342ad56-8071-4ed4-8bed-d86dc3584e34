package com.howbuy.crm.nt.remind.domain;

import crm.howbuy.base.domain.CmRemindMsgDay;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CmRemindMsgParam.java
 * @Description TODO
 * @createTime 2021年11月01日 14:42:00
 */
@Data
public class CmRemindMsgParam extends CmRemindMsgDay {

    private String businessid;

    private String custname;

    private String pname;

    private String amount;

    private String fee;

    private String deadline;

    private String shares;

    private String visit;

    private String starttime;

    private String endtime;

    private String schedule;

    private String redeemdirection;

    private String paystate;

    /**
     * 关联的预约id
     */
    private String preId;
}

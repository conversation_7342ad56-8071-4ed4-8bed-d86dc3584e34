<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.remind.dao.CmRemindMsgDayDao">

    <resultMap id="BaseResultMap" type="crm.howbuy.base.domain.CmRemindMsgDay">
        <result column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="MSGTYPE" property="msgType" jdbcType="VARCHAR"/>
        <result column="CONSCODE" property="consCode" jdbcType="VARCHAR"/>
        <result column="MSGCONTENT" property="msgContent" jdbcType="VARCHAR"/>
        <result column="READFLAG" property="readFlag" jdbcType="VARCHAR"/>
        <result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
        <result column="PUSHDT" property="pushDt" jdbcType="VARCHAR"/>
        <result column="CREDT" property="creDt" jdbcType="VARCHAR"/>
        <result column="MODIFIER" property="modifier" jdbcType="VARCHAR"/>
        <result column="MODDT" property="modDt" jdbcType="VARCHAR"/>
        <result column="STIMESTAMP" property="stimeStamp" jdbcType="TIMESTAMP"/>
        <result column="MSGCONTENTDT" property="msgContentDt" jdbcType="VARCHAR"/>
        <result column="EXPIREDT" property="expireDt" jdbcType="VARCHAR"/>
        <result column="MSGCODE" property="msgCode" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryFundFhMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.pname,
               T.businessid,
               T.MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
        FROM (
                 SELECT N.CONSCODE AS CONSCODE,
                        '【分红提醒】产品' || M.JJDM||' '|| M.JJJC || ',预计于' || M.FPRQ || '分红，该产品为分批次到期产品，您名下持有该产品的客户的实际到期日期，请至客户高端交易记录页面查询。' AS MSGCONTENT,
                        M.JJDM||' '|| M.JJJC as pname,
                        '200425' as businessid,
                        M.FPRQ AS MSGCONTENTDT
				  FROM (
				        SELECT DISTINCT T.FUNDCODE AS JJDM,
				               T2.JJJC,
				               TO_CHAR(T5.FPRQ, 'YYYYMMDD') AS FPRQ
				          FROM CM_HIGH_CUSTFUND T
				         INNER JOIN JJXX1 T2
				            ON T.FUNDCODE = T2.JJDM
				         INNER JOIN XTSYFPRL T5
				            ON T2.JJDM = T5.JJDM
				         WHERE T2.HMCPX = '2'
				           AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
				           AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
				           AND T.FUNDTYPE = '3'
				           AND T5.XTFPLX = '1'
				           AND T.BALANCEVOL > 1
				        ) M
				 INNER JOIN (
				             SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
				               FROM CM_HIGH_CUSTFUND T
				              INNER JOIN CM_CONSCUST T1
				                 ON T.CUSTNO = T1.CONSCUSTNO
				              INNER JOIN CM_CUSTCONSTANT B
				                 ON T.CUSTNO = B.CUSTNO
				              INNER JOIN JJXX1 T2
				                 ON T.FUNDCODE = T2.JJDM
				              INNER JOIN XTSYFPRL T5
				                 ON T2.JJDM = T5.JJDM
				              WHERE T2.HMCPX = '2'
				                AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
				                AND TO_CHAR(T5.FPRQ, 'YYYYMMDD') <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
				                AND T.FUNDTYPE = '3'
				                AND T5.XTFPLX = '1'
				                AND T.BALANCEVOL > 1
				              GROUP BY T.FUNDCODE, B.CONSCODE
				             ) N
				    ON M.JJDM = N.FUNDCODE
				 WHERE 1 = 1
				   AND N.CONSCODE IS NOT NULL
				 ORDER BY CNT DESC NULLS LAST, FPRQ
		 	) T
        	WHERE NOT EXISTS (SELECT 1
          FROM (select *
                  from CM_REMIND_MSG_DAY D
                 WHERE D.MSGTYPE = '1' UNION　ALL
                  select *
				  from CM_REMIND_MSG_HIS H
				 WHERE H.MSGTYPE = '1'
                ) CC
         WHERE CC.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryFundDqMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
		SELECT T.CONSCODE,
		       T.MSGCONTENT,
               T.pname,
               T.businessid,
		       T.MSGCONTENTDT,
		       TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
		       F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
		  FROM (
				SELECT N.CONSCODE AS CONSCODE,
					   '【到期提醒】产品：' || M.JJDM||' '||M.JJJC || ',预计于' || M.ZZRQ || '到期，该产品为分批次到期产品，您名下持有该产品的客户的实际到期日期，请至客户高端交易记录页面查询。' AS MSGCONTENT,
                       M.JJDM||' '||M.JJJC as pname,
                       '200424' as businessid,
	               	   M.ZZRQ AS MSGCONTENTDT
				  FROM (SELECT DISTINCT T.FUNDCODE AS JJDM,
				               T2.JJJC,
				               FT.YJDQR AS ZZRQ
				          FROM CM_HIGH_CUSTFUND T
				         INNER JOIN JJXX1 T2
				            ON T.FUNDCODE = T2.JJDM
				             INNER JOIN (SELECT MAX(FX.YJDQR) AS YJDQR, FX.JJDM
							FROM FDB_XTSYL FX
							WHERE FX.JJDM IS NOT NULL
							  AND FX.YJDQR <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
                              AND FX.YJDQR <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
							GROUP BY FX.JJDM) FT
							ON T2.JJDM = FT.JJDM
				         WHERE T.BALANCEVOL > 1
				           AND T.FUNDTYPE = '3'
				           AND T2.HMCPX = '2'
                   AND T2.ZZRQ <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
                   AND T2.ZZRQ <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
                ) M
		        INNER JOIN (SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
		                       FROM CM_HIGH_CUSTFUND T
		                      INNER JOIN CM_CONSCUST T1
		                         ON T.CUSTNO = T1.CONSCUSTNO
		                      INNER JOIN CM_CUSTCONSTANT B
		                         ON T.CUSTNO = B.CUSTNO
		                      INNER JOIN JJXX1 T2
		                         ON T.FUNDCODE = T2.JJDM
		                      WHERE T.BALANCEVOL > 1
		                        AND T.FUNDTYPE = '3'
		                        AND T2.HMCPX = '2'
		                        AND T2.ZZRQ <![CDATA[>=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
		                        AND T2.ZZRQ <![CDATA[<=]]> TO_CHAR(SYSDATE + 7, 'YYYYMMDD')
		                      GROUP BY T.FUNDCODE, B.CONSCODE) N
		            ON M.JJDM = N.FUNDCODE
		         WHERE 1 = 1
		           AND N.CONSCODE IS NOT NULL
		         ORDER BY CNT DESC NULLS LAST, ZZRQ
          ) T
 		  WHERE NOT EXISTS (SELECT 1
          FROM (select *
                  from CM_REMIND_MSG_DAY D
                 WHERE D.MSGTYPE = '2' UNION　ALL
                  select *
				  from CM_REMIND_MSG_HIS H
				 WHERE H.MSGTYPE = '2'
                ) CC
         WHERE CC.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryBirthDayMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmCustBirthday">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.MSGCONTENTEXT,
               T.BIRTHDAY,
               T.CUSTNAME
          FROM (SELECT T1.CONSCODE AS CONSCODE,
                       T3.CUSTNAME AS CUSTNAME,
                       CASE
                         WHEN SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE, 'MMDD') THEN
                          (SELECT '【' || A.MSG_TYPE_NAME || '】' ||
                                  REPLACE(REPLACE(T.PC_TEMPLATE_TEXT,
                                                  '${custname}',
                                                  T3.CUSTNAME),
                                          '${birthday}',
                                          T3.BIRTHDAY)
                             FROM CM_MSG_BUSINESS_TEMPLATE T
                             LEFT JOIN CM_MSG_TYPE A
                               ON T.MSG_TYPE_ID = A.ID
                            WHERE T.BUSINESS_ID = '200101')
                         ELSE
                          (SELECT '【' || A.MSG_TYPE_NAME || '】' ||
                                  REPLACE(REPLACE(T.PC_TEMPLATE_TEXT,
                                                  '${custname}',
                                                  T3.CUSTNAME),
                                          '${birthday}',
                                          T3.BIRTHDAY)
                             FROM CM_MSG_BUSINESS_TEMPLATE T
                             LEFT JOIN CM_MSG_TYPE A
                               ON T.MSG_TYPE_ID = A.ID
                            WHERE T.BUSINESS_ID = '200062')
                       END AS MSGCONTENTEXT,
                       CASE
                         WHEN SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE, 'MMDD') THEN
                          '200101'
                         ELSE
                          '200062'
                       END AS MSGCONTENT,
                       T3.BIRTHDAY AS BIRTHDAY
                  FROM CM_CUSTCONSTANT T1
                  JOIN CM_CONSULTANT T2
                    ON T2.CONSCODE = T1.CONSCODE
                  JOIN CM_CONSCUST T3
                    ON T3.CONSCUSTNO = T1.CUSTNO
                 WHERE T2.ISVIRTUAL = '0'
                   AND T2.CONSLEVEL IS NOT NULL
                   AND T3.BIRTHDAY IS NOT NULL
                   AND LENGTH(T3.BIRTHDAY) = 8
                   AND ((SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE + 3, 'MMDD') or
                       SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE + 4, 'MMDD') or
                       SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE + 5, 'MMDD')) OR
                       SUBSTR(T3.BIRTHDAY, 5) = TO_CHAR(SYSDATE, 'MMDD'))) T
         WHERE NOT EXISTS
         (SELECT 1
                  FROM CM_REMIND_MSG_DAY D
                 WHERE D.MSGTYPE = '4'
                   AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENTEXT))
    </select>

    <select id="queryCustDzMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.businessid,
               T.CUSTNAME,
               T.pname,
               T.amount,
               T.fee,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.PREID) AS MSGCODE
        FROM (SELECT T2.CONSCODE AS CONSCODE,
                     CASE
                         when T3.SFMSJG = '2' OR EXISTS
                             (SELECT CB.HBONENO
                              FROM CM_BLACKLIST_DIRECT CB
                                       LEFT JOIN CM_CONSCUST CC
                                                 ON CB.HBONENO = CC.HBONE_NO
                              WHERE CB.FUNDCODE = T.PCODE
                                AND CC.CONSCUSTNO = T.CONSCUSTNO) then
                                 '【到账通知】客户：' || T4.CUSTNAME || '，认购产品：' || T3.JJDM || ' '||T3.JJJC ||
                                 '，已到账，到账金额：' || TRUNC(T.REALPAYAMT / 10000, 2) || '万，到账手续费：' ||
                                 NVL(CZO.FEE, T.FEE) || '元（请以CRM实际到账为准）。'
                         else
                             CASE
                                 when CZO.APPOINTMENTDEALNO IS NOT NULL then
                                         '【到账通知】客户：' || T4.CUSTNAME || '，认购产品：' || T3.JJDM || ' '||T3.JJJC ||
                                         '，已到账，到账金额：' || TRUNC(T.REALPAYAMT / 10000, 2) || '万，到账手续费：' ||
                                         NVL(CZO.FEE, T.FEE) || '元（请以CRM实际到账为准）。'
                                 else
                                         '【到账通知】客户：' || T4.CUSTNAME || '，认购产品：' || T3.JJDM || ' '||T3.JJJC ||
                                         '，已到账，到账金额：' || TRUNC(T.REALPAYAMT / 10000, 2) || '万，到账手续费：' ||
                                         NVL(CZO.FEE, T.FEE) || '元。温馨提醒：该客户尚未下单，请提醒客户及时完成下单操作。'
                                 end
                         END AS MSGCONTENT,
                     CASE
                         when T3.SFMSJG = '2' OR EXISTS
                             (SELECT CB.HBONENO
                              FROM CM_BLACKLIST_DIRECT CB
                                       LEFT JOIN CM_CONSCUST CC
                                                 ON CB.HBONENO = CC.HBONE_NO
                              WHERE CB.FUNDCODE = T.PCODE
                                AND CC.CONSCUSTNO = T.CONSCUSTNO) then
                             '200421'
                         else
                             CASE
                                 when CZO.APPOINTMENTDEALNO IS NOT NULL then
                                     '200421'
                                 else
                                     '200422'
                                 end
                         END AS businessid,
                     T4.CUSTNAME,
                     T3.JJDM || ' '||T3.JJJC as pname,
                     TRUNC(T.REALPAYAMT / 10000, 2) as amount,
                     CASE
                         when T.ARCH_TYPE = '1' then NVL(CZO.FEE, T.FEE)
                         else T.FEE
                     end as fee,
                     T.ID AS PREID,
                     T.REALPAYAMTDT AS MSGCONTENTDT
				  FROM CM_PREBOOKPRODUCTINFO T
				  LEFT JOIN CM_ZT_ORDERINFO CZO
					ON T.ID = CZO.APPOINTMENTDEALNO
				  LEFT JOIN CM_CUSTCONSTANT T2
					ON T2.CUSTNO = T.CONSCUSTNO
				  LEFT JOIN JJXX1 T3
					ON T3.JJDM = T.PCODE
				  LEFT JOIN CM_CONSCUST T4
            		ON T.CONSCUSTNO = T4.CONSCUSTNO
				  LEFT JOIN CM_CUST_TRANSFERVOL CCT
					ON T.ID = CCT.FCCLPREID
				 WHERE T.PREBOOKSTATE = '2'
				   AND T.PAYSTATE = '3'
				   AND T.TRADE_TYPE IN ('1', '2')
					AND T.SPECTRADETYPE <![CDATA[<>]]> '1'
                    <!--只处理 高端中台、直销 的 到账确认 消息 -->
                    AND T.ARCH_TYPE IN ('1','2')
				   AND (CCT.FCCLTYPE IS NULL OR CCT.FCCLTYPE = '2')
				   AND T.PAYCHECKDT = TO_CHAR(SYSDATE, 'YYYYMMDD')) T
			WHERE NOT EXISTS
			 (SELECT 1
					  FROM CM_REMIND_MSG_DAY D
					 WHERE D.MSGTYPE = '5'
           AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustZlgxMsgList" resultMap="BaseResultMap">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
           T.MSGCONTENTDT,
           TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
        SELECT TIT.CONSCODE,
       case
         WHEN TIT.TYPE = 1 then
          '【资料类更新】产品：' || TIT.STR || ','
         else
          '【资料类更新】机构：' || TIT.STR || ','
       end || DECODE(TIT.REPORTTYPE,
       				 '4',
                     '通知公告',
                     '5',
                     '运作报告',
                     '6',
                     '调研报告',
                     '7',
                     '会议纪要',
                     '8',
                     '信息披露',
                     '9',
                     '分配公告',
                     '13',
                     '项目进展',
                     '') || '已更新' AS MSGCONTENT,
       TO_CHAR(SYSDATE, 'yyyyMMdd') AS MSGCONTENTDT
  FROM (SELECT 1 AS TYPE,
               TTT.CONSCODE,
               TTT.REPORTTYPE,
               to_char(wm_concat(TTT.FUNDCODE)) AS STR,
               TTT.TITLE
          FROM (SELECT CAL.CONSCODE, CAL.REPORTTYPE, CAL.FUNDCODE, CAL.TITLE
                  FROM (SELECT (SELECT JX.JJJC
                                  FROM JJXX1 JX
                                 WHERE JX.JJDM = CR.FUNDCODE) as FUNDCODE,
                               CR.REPORTTYPE,
                               CH.CUSTNO,
                               CT.CONSCODE,
                               CR.TITLE
                          FROM (SELECT T.FUNDCODE, T.REPORTTYPE, T.TITLE
                                  FROM CM_company_report T
                                 where T.FUNDCODE IS NOT NULL
                                   AND T.STATUS = 0
                                   AND T.SENDSTATUS = '1'
                                   AND T.REPORTTYPE IN
                                       ('4', '5', '6', '7', '8', '9', '13')
                                   AND TO_CHAR(T.SYNCDATE, 'yyyyMMdd hh:MM:ss') >= to_char(sysdate - 1,'yyyymmdd hh:MM:ss')) CR
                          LEFT JOIN CM_HIGH_CUSTFUND CH
                            ON CR.FUNDCODE = CH.FUNDCODE
                          LEFT JOIN CM_CUSTCONSTANT CT
                            ON CH.CUSTNO = CT.CUSTNO
                         WHERE CH.CUSTNO IS NOT NULL
                         AND CH.BALANCEVOL > 1) CAL
                 GROUP BY CAL.CONSCODE,
                          CAL.REPORTTYPE,
                          CAL.FUNDCODE,
                          CAL.TITLE) TTT
         GROUP BY TTT.CONSCODE, TTT.REPORTTYPE, TTT.TITLE
        UNION ALL
        SELECT 0 AS TYPE,
               CAL.CONSCODE,
               CAL.REPORTTYPE,
               (SELECT A.JGMC
                  FROM jgxx A
                 WHERE A.JGDM = CAL.COMPANYID ) AS STR,
               CAL.TITLE
          FROM (SELECT CR.COMPANYID,
                       CR.REPORTTYPE,
                       CH.CUSTNO,
                       CT.CONSCODE,
                       CR.TITLE
                  FROM (SELECT T.JJDM AS FUNDCODE,
                               R.COMPANYID,
                               R.REPORTTYPE,
                               R.TITLE
                          FROM JJXX1 T, CM_company_report R
                         where T.GLRM = R.COMPANYID
                           AND R.FUNDCODE IS NULL
                           AND R.STATUS = 0
                           AND R.SENDSTATUS = '1'
                           AND R.REPORTTYPE IN ('4', '5', '6', '7', '8', '9', '13')
                           AND TO_CHAR(R.SYNCDATE, 'yyyyMMdd hh:MM:ss') >= to_char(sysdate - 1,'yyyymmdd hh:MM:ss')) CR
                  LEFT JOIN CM_HIGH_CUSTFUND CH
                    ON CR.FUNDCODE = CH.FUNDCODE
                  LEFT JOIN CM_CUSTCONSTANT CT
                    ON CH.CUSTNO = CT.CUSTNO
                 WHERE CH.CUSTNO IS NOT NULL
                 AND CH.BALANCEVOL > 1) CAL
         GROUP BY CAL.CONSCODE, CAL.REPORTTYPE, CAL.COMPANYID, CAL.TITLE) TIT
         ) T
         WHERE NOT EXISTS
         (SELECT 1
		FROM (select *
		from CM_REMIND_MSG_DAY D
		WHERE D.MSGTYPE = '8' UNION　ALL
		select *
		from CM_REMIND_MSG_HIS H
		WHERE H.MSGTYPE = '8'
		AND H.EXPIREDT >= TO_CHAR(sysdate - 1,'yyyyMMdd')
		) CC
		WHERE CC.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryFundJzMsgList" parameterType="Map" resultMap="BaseResultMap">
        SELECT T.CONSCODE,
        T.MSGCONTENT,
        T.MSGCONTENTDT,
        T.SYNCDT AS PUSHDT,
        TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
        F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
        FROM (
        SELECT N.CONSCODE AS CONSCODE,
        '【净值通知】' || J.JJDM || J.JJJC || '，净值日期：' || J.JSRQ || '，净值：' || TO_CHAR(J.JJJZ, 'FM9999999990.0000') || '。与' ||
        J.LASTJZRQ || '相比涨跌幅'|| (CASE WHEN J.DFLAG='1' THEN J.TFLAG || DECODE(J.HBDR, NULL, '--', J.HBDR,
        TO_CHAR((J.HBDR * 100), 'FM9999999990.00')||'%') ELSE '计算中' END) || '。您名下共有' || N.CNT ||
        '个客户持有该产品。温馨提醒：净值可能存在误差，如对净值有疑议，请及时联系后台同事处理。涨跌幅请以官网公布数据为准。'
        AS MSGCONTENT,
        J.SYNCDT,
        TO_CHAR(J.SYNCDT, 'YYYYMMDD') AS MSGCONTENTDT
        FROM (
        SELECT T1.JJDM, T1.JJJC, T1.JSRQ, T1.JJJZ, T1.HBDR, T1.LASTJZRQ, T1.JJJZMODDT, T1.SYNCDT, (CASE WHEN T1.MODDT IS
        NOT NULL AND T1.JJJZMODDT <![CDATA[<]]> T1.MODDT THEN 1 WHEN T1.MODDT IS NOT NULL AND T1.JJJZMODDT
        <![CDATA[>=]]> T1.MODDT THEN 2 ELSE 3 END) DFLAG, (CASE WHEN T1.HBDR IS NOT NULL AND T1.HBDR <![CDATA[>]]> 0
        THEN '+' ELSE '' END) TFLAG
        FROM CM_JJJZ_MSG_REC T1
        WHERE 1=1
        <if test="timeFlag != null and timeFlag == 'AM9'">
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE - 1, 'YYYYMMDD') || '1600'
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD') || '0000'
        </if>
        <if test="timeFlag != null and timeFlag == 'PM17'">
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE, 'YYYYMMDD') || '0000'
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD') || '1600'
        </if>
        AND T1.ID IN (SELECT ID FROM CM_JJJZ_MSG_LATEST)
        ) J
        LEFT JOIN (SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
        FROM CM_HIGH_CUSTFUND T
        INNER JOIN CM_CONSCUST T1
        ON T.CUSTNO = T1.CONSCUSTNO
        INNER JOIN CM_CUSTCONSTANT B
        ON T.CUSTNO = B.CUSTNO
        INNER JOIN JJXX1 T2
        ON T.FUNDCODE = T2.JJDM
        WHERE T.BALANCEVOL > 1
        AND T.FUNDTYPE IN ('2', '3')
        GROUP BY T.FUNDCODE, B.CONSCODE) N
        ON J.JJDM = N.FUNDCODE
        LEFT JOIN JJXX1 T3
        ON T3.JJDM = J.JJDM
        WHERE 1 = 1
        AND N.CONSCODE IS NOT NULL
        ) T
        WHERE NOT EXISTS (SELECT 1
        FROM (select *
        from CM_REMIND_MSG_DAY D
        WHERE D.MSGTYPE = '6' UNION　ALL
        select *
        from CM_REMIND_MSG_HIS H
        WHERE H.MSGTYPE = '6'
        ) CC
        WHERE CC.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustShMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.SHARES,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE,
               T.REDEEMDIRECTION,
               T.ID AS preId
        FROM (
                 SELECT T.ID,T2.CONSCODE AS CONSCODE,
                        '【下单赎回通知】客户：' || T4.CUSTNAME || '，赎回产品：' || T3.JJDM||' '||T3.JJJC || '，赎回份额：' || NVL(CZO.APPVOL,0) || '，预计交易日期：' || CZO.EXPECTEDTRADEDT
                         || '，赎回至：'
                         || (case when (
                            CASE
                             WHEN T3.SFMSJG = '2' THEN
                              '1'
                             WHEN T3.SFMSJG = '3' AND CBD.HBONENO IS NOT NULL THEN
                              '1'
                             ELSE
                              '0'
                            END)='1' then '银行卡'
                            when CZO.DEALNO is not null  then hc.constdesc
                              else '' end) AS MSGCONTENT,
                        T4.CUSTNAME,
                        '200430' AS businessid,
                        T3.JJDM||' '||T3.JJJC AS PNAME,
                        NVL(CZO.APPVOL,0) AS shares,
                        CZO.EXPECTEDTRADEDT AS MSGCONTENTDT,
                         case when (
                        CASE
                         WHEN T3.SFMSJG = '2' THEN
                          '1'
                         WHEN T3.SFMSJG = '3' AND CBD.HBONENO IS NOT NULL THEN
                          '1'
                         ELSE
                          '0'
                        END)='1' then '银行卡'
                        when CZO.DEALNO is not null  then hc.constdesc
                          else '' end as REDEEMDIRECTION
          FROM CM_PREBOOKPRODUCTINFO T
          LEFT JOIN CM_ZT_ORDERINFO CZO   ON T.ID = CZO.APPOINTMENTDEALNO
          LEFT JOIN CM_CUSTCONSTANT T2    ON T2.CUSTNO = T.CONSCUSTNO
          LEFT JOIN JJXX1 T3         ON T3.JJDM = T.PCODE
          LEFT JOIN CM_CONSCUST T4   ON T.CONSCUSTNO = T4.CONSCUSTNO
        LEFT JOIN CM_BLACKLIST_DIRECT CBD  ON T4.HBONE_NO = CBD.HBONENO   AND T.PCODE = CBD.FUNDCODE
        left join hb_constant hc    on hc.typecode   ='redeemdirection'   and hc.constcode =  CZO.REDEEMDIRECTION
         WHERE T.TRADE_TYPE = '3'
           AND CZO.ORDERSTATUS IN ('1','2','3')
           AND CZO.APPDATE = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '15' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustXdgmtzMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.AMOUNT,
               T.FEE,
               T.PAYSTATE,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE
        FROM (
                 SELECT T.ID,
                        T2.CONSCODE AS CONSCODE,
                        '【下单购买通知】客户：' || T4.CUSTNAME || '，购买产品：' || T3.JJDM||' '||T3.JJJC || '，金额：' || NVL(CZO.APPAMT,0)|| '，手续费：' || NVL(CZO.FEE,0) || '，下单成功，打款状态：' || HC.CONSTDESC || '。' AS MSGCONTENT,
                        T4.CUSTNAME,
                        '200885' AS BUSINESSID,
                        T3.JJDM||' '||T3.JJJC AS PNAME,
                        NVL(CZO.APPAMT,0) AS AMOUNT,
                        NVL(CZO.FEE,0) AS FEE,
                        HC.CONSTDESC AS PAYSTATE,
                        CZO.EXPECTEDTRADEDT AS MSGCONTENTDT
          FROM CM_PREBOOKPRODUCTINFO T
          LEFT JOIN CM_ZT_ORDERINFO CZO
            ON T.ID = CZO.APPOINTMENTDEALNO
          LEFT JOIN CM_CUSTCONSTANT T2
            ON T2.CUSTNO = T.CONSCUSTNO
          LEFT JOIN JJXX1 T3
            ON T3.JJDM = T.PCODE
          LEFT JOIN CM_CONSCUST T4
            ON T.CONSCUSTNO = T4.CONSCUSTNO
          LEFT JOIN CM_BLACKLIST_DIRECT CBD
            ON T4.HBONE_NO = CBD.HBONENO
           AND T.PCODE = CBD.FUNDCODE
          LEFT JOIN HB_CONSTANT HC
            ON HC.TYPECODE = 'payStates'
           AND HC.CONSTCODE = T.PAYSTATE
         WHERE T.TRADE_TYPE IN ('1', '2')
           AND CZO.ORDERSTATUS = '1'
           AND CZO.APPDATE = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '35' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustDdqrsbgmMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.AMOUNT,
               T.FEE,
               T.PAYSTATE,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE
        FROM (
                 SELECT T.ID,
                        T2.CONSCODE AS CONSCODE,
                        '【购买确认失败】客户：' || T4.CUSTNAME || '，购买产品：' || T3.JJDM||' '||T3.JJJC || '，金额：' || NVL(CZO.APPAMT,0)|| '，手续费：' || NVL(CZO.FEE,0) || '，交易失败。' AS MSGCONTENT,
                        T4.CUSTNAME,
                        '200883' AS BUSINESSID,
                        T3.JJDM||' '||T3.JJJC AS PNAME,
                        NVL(CZO.APPAMT,0) AS AMOUNT,
                        NVL(CZO.FEE,0) AS FEE,
                        HC.CONSTDESC AS PAYSTATE,
                        CZO.EXPECTEDTRADEDT AS MSGCONTENTDT
                  FROM CM_PREBOOKPRODUCTINFO T
                  LEFT JOIN CM_ZT_ORDERINFO CZO
                    ON T.ID = CZO.APPOINTMENTDEALNO
                  LEFT JOIN CM_CUSTCONSTANT T2
                    ON T2.CUSTNO = T.CONSCUSTNO
                  LEFT JOIN JJXX1 T3
                    ON T3.JJDM = T.PCODE
                  LEFT JOIN CM_CONSCUST T4
                    ON T.CONSCUSTNO = T4.CONSCUSTNO
                  LEFT JOIN CM_BLACKLIST_DIRECT CBD
                    ON T4.HBONE_NO = CBD.HBONENO
                   AND T.PCODE = CBD.FUNDCODE
                  LEFT JOIN HB_CONSTANT HC
                    ON HC.TYPECODE = 'payStates'
                   AND HC.CONSTCODE = T.PAYSTATE
                 WHERE T.TRADE_TYPE IN ('1', '2')
                   AND CZO.ORDERSTATUS IN ('4', '6')
                   AND substr(CZO.Dealdt,0,8) = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '36' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustDdqrsbshMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.SHARES,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE
        FROM (
                 SELECT T.ID,
                        T2.CONSCODE AS CONSCODE,
                        '【赎回确认失败】客户：' || T4.CUSTNAME || '，赎回产品：' || T3.JJDM||' '||T3.JJJC || '，赎回份额：' || NVL(CZO.APPVOL,0)|| '，交易失败。' AS MSGCONTENT,
                        T4.CUSTNAME,
                        '200884' AS BUSINESSID,
                        T3.JJDM||' '||T3.JJJC AS PNAME,
                        NVL(CZO.APPVOL,0) AS SHARES,
                        CZO.EXPECTEDTRADEDT AS MSGCONTENTDT
                  FROM CM_PREBOOKPRODUCTINFO T
                  LEFT JOIN CM_ZT_ORDERINFO CZO
                    ON T.ID = CZO.APPOINTMENTDEALNO
                  LEFT JOIN CM_CUSTCONSTANT T2
                    ON T2.CUSTNO = T.CONSCUSTNO
                  LEFT JOIN JJXX1 T3
                    ON T3.JJDM = T.PCODE
                  LEFT JOIN CM_CONSCUST T4
                    ON T.CONSCUSTNO = T4.CONSCUSTNO
                  LEFT JOIN CM_BLACKLIST_DIRECT CBD
                    ON T4.HBONE_NO = CBD.HBONENO
                   AND T.PCODE = CBD.FUNDCODE
                  LEFT JOIN HB_CONSTANT HC
                    ON HC.TYPECODE = 'payStates'
                   AND HC.CONSTCODE = T.PAYSTATE
                 WHERE T.TRADE_TYPE = '3'
                   AND CZO.ORDERSTATUS IN ('4', '6')
                   AND substr(CZO.Dealdt,0,8) = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '39' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustCxgmtzMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.AMOUNT,
               T.FEE,
               T.PAYSTATE,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE
        FROM (
                 SELECT T.ID,
                        T2.CONSCODE AS CONSCODE,
                         '【撤销购买通知】客户：' || T4.CUSTNAME || '，撤销购买订单，撤销购买产品：' || T3.JJDM||' '||T3.JJJC || '，金额：' || NVL(CZO.APPAMT,0)|| '，手续费：' || NVL(CZO.FEE,0) || '，打款状态：' || HC.CONSTDESC || '。' AS MSGCONTENT,
                        T4.CUSTNAME,
                        '200886' AS BUSINESSID,
                        T3.JJDM||' '||T3.JJJC AS PNAME,
                        NVL(CZO.APPAMT,0) AS AMOUNT,
                        NVL(CZO.FEE,0) AS FEE,
                        HC.CONSTDESC AS PAYSTATE,
                        CZO.EXPECTEDTRADEDT AS MSGCONTENTDT
                  FROM CM_PREBOOKPRODUCTINFO T
                  LEFT JOIN CM_ZT_ORDERINFO CZO
                    ON T.ID = CZO.APPOINTMENTDEALNO
                  LEFT JOIN CM_CUSTCONSTANT T2
                    ON T2.CUSTNO = T.CONSCUSTNO
                  LEFT JOIN JJXX1 T3
                    ON T3.JJDM = T.PCODE
                  LEFT JOIN CM_CONSCUST T4
                    ON T.CONSCUSTNO = T4.CONSCUSTNO
                  LEFT JOIN CM_BLACKLIST_DIRECT CBD
                    ON T4.HBONE_NO = CBD.HBONENO
                   AND T.PCODE = CBD.FUNDCODE
                  LEFT JOIN HB_CONSTANT HC
                    ON HC.TYPECODE = 'payStates'
                   AND HC.CONSTCODE = T.PAYSTATE
                 WHERE T.TRADE_TYPE IN ('1', '2')
                   AND CZO.ORDERSTATUS = '5'
                   AND substr(CZO.Dealdt,0,8) = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '37' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustCxshtzMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.BUSINESSID,
               T.CUSTNAME,
               T.PNAME,
               T.SHARES,
               NVL(T.MSGCONTENTDT, TO_CHAR(SYSDATE, 'YYYYMMDD')) AS MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT || T.ID) AS MSGCODE
        FROM (
                SELECT T.ID,
                       T2.CONSCODE AS CONSCODE,
                        '【撤销赎回通知】客户：' || T4.CUSTNAME || '，撤销赎回订单，撤销赎回产品：' || T3.JJDM||' '||T3.JJJC || '，赎回份额：' || NVL(CZO.APPVOL,0) || '。' AS MSGCONTENT,
                       T4.CUSTNAME,
                       '200887' AS BUSINESSID,
                       T3.JJDM||' '||T3.JJJC AS PNAME,
                       NVL(CZO.APPVOL,0) AS SHARES,
                       NVL(CZO.FEE,0) AS FEE,
                       HC.CONSTDESC AS PAYSTATE,
                       CZO.EXPECTEDTRADEDT AS MSGCONTENTDT
                  FROM CM_PREBOOKPRODUCTINFO T
                  LEFT JOIN CM_ZT_ORDERINFO CZO
                    ON T.ID = CZO.APPOINTMENTDEALNO
                  LEFT JOIN CM_CUSTCONSTANT T2
                    ON T2.CUSTNO = T.CONSCUSTNO
                  LEFT JOIN JJXX1 T3
                    ON T3.JJDM = T.PCODE
                  LEFT JOIN CM_CONSCUST T4
                    ON T.CONSCUSTNO = T4.CONSCUSTNO
                  LEFT JOIN CM_BLACKLIST_DIRECT CBD
                    ON T4.HBONE_NO = CBD.HBONENO
                   AND T.PCODE = CBD.FUNDCODE
                  LEFT JOIN HB_CONSTANT HC
                    ON HC.TYPECODE = 'payStates'
                   AND HC.CONSTCODE = T.PAYSTATE
                 WHERE T.TRADE_TYPE = '3'
                   AND CZO.ORDERSTATUS = '5'
                   AND substr(CZO.Dealdt,0,8) = TO_CHAR(SYSDATE, 'YYYYMMDD')
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '38' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustYyMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT T.CONSCODE,
               T.MSGCONTENT,
               T.businessid,
               T.CUSTNAME,
               T.visit,
               T.starttime,
               T.endtime,
               T.schedule,
               T.MSGCONTENTDT,
               TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
        FROM (SELECT CONSCODE,
                     MSGCONTENT,
                     MSGCONTENTDT,
                     businessid,
                     CUSTNAME,
                     visit,
                     starttime,
                     endtime,
                     schedule
              FROM (SELECT T.BOOKINGCONS AS CONSCODE,
                           ('【预约事项提醒】客户：' || CUST.CUSTNAME || '，拜访方式：' ||
                            A.CONSTDESC || '，预约日期：' || T.BOOKINGDT || ' ' ||
                            T.BOOKINGSTARTTIME || ' 至 ' ||
                            NVL(T.BOOKINGENDTIME, '--') || '，预约事项：' ||
                            NVL(T.CONTENT, '--')) AS MSGCONTENT,
                           '200426' AS businessid,
                           CUST.CUSTNAME,
                           A.CONSTDESC AS visit,
                           T.BOOKINGDT AS MSGCONTENTDT,
                           T.BOOKINGSTARTTIME AS starttime,
                           NVL(T.BOOKINGENDTIME, '--') AS endtime,
                           NVL(T.CONTENT, '--') AS schedule
                    FROM CM_CONSBOOKINGCUST T
                             LEFT JOIN HB_CONSTANT A ON A.TYPECODE = 'new_visitType' AND T.VISITTYPE = A.CONSTCODE
                             LEFT JOIN CM_CONSCUST CUST
                                       ON CUST.CONSCUSTNO = T.CONSCUSTNO
                                           AND CUST.CONSCUSTSTATUS = '0'
                    WHERE T.BOOKINGDT = TO_CHAR(SYSDATE, 'YYYYMMDD')
                      AND (T.BOOKINGSTARTTIME = TO_CHAR(SYSDATE, 'HH24:MI') OR
                           T.BOOKINGSTARTTIME =
                           TO_CHAR(SYSDATE + 15 / 24 / 60, 'HH24:MI')))) T
        WHERE NOT EXISTS
            (SELECT 1
             FROM CM_REMIND_MSG_DAY D
             WHERE D.MSGTYPE = '7'
               AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryJylxqsMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
    	       T.pname,
    	       T.businessid,
           TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5) AS MSGCONTENTDT,
           TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
          SELECT * FROM (
             SELECT ROW_NUMBER() OVER(PARTITION BY tem.CONSCODE,tem.productcode ORDER BY tem.CONSCODE DESC) rn, tem.*
	         FROM (SELECT T4.CONSCODE AS CONSCODE,
	                   '【到期通知】产品' || T1.productcode||' '||T1.productname || '，已于' || TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD') || '到期。相关款项预计在产品结束后10个工作日内回款至客户受益账户，具体请以合同为准。' MSGCONTENT,
                          T1.productcode||' '||T1.productname as pname,
                          '200427' AS businessid,
	                   TO_CHAR(sysdate , 'YYYYMMDD') AS MSGCONTENTDT,T1.productcode
	              FROM CM_ZT_NOTRADETRANSFER T1
	              INNER JOIN AC_TX_HBONE T2
	                ON T1.TXACCTNO = T2.TX_ACCT_NO
	              INNER JOIN CM_CONSCUST T3
	                ON T2.hbone_no = T3.hbone_no
	              INNER JOIN CM_CUSTCONSTANT T4
	                ON T3.conscustno = T4.custno
	              INNER JOIN JJXX1 T5
	                ON T5.JJDM = T1.productcode
	             WHERE T1.tatradedt = TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD')
	             and T5.HMCPX = '2' and T1.Mbusicode = '1142' ) tem
	          ) WHERE rn = 1
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '9' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryJylxhlffMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
    	SELECT T.CONSCODE,
           T.MSGCONTENT,
               T.pname,
               T.businessid,
           TO_CHAR(SYSDATE, 'YYYY') || SUBSTR(T.MSGCONTENTDT, 5) AS MSGCONTENTDT,
           TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
           F_MD5(T.CONSCODE || T.MSGCONTENT) AS MSGCODE
      FROM (
          SELECT * FROM (
             SELECT ROW_NUMBER() OVER(PARTITION BY tem.CONSCODE,tem.productcode ORDER BY tem.CONSCODE DESC) rn, tem.*
	         FROM (
	              SELECT T4.CONSCODE AS CONSCODE,
	                   '【分红通知】产品' || T1.productcode||' '|| T1.productname || '，已于' || TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD') || '分红。相关款项预计在该日期后10个工作日内回款至客户受益账户，具体请以合同为准。' MSGCONTENT,
                         T1.productcode||' '|| T1.productname as pname,
                         '200461' AS businessid,
	                     TO_CHAR(sysdate , 'YYYYMMDD') AS MSGCONTENTDT,T1.productcode
	              FROM CM_ZT_NOTRADETRANSFER T1
	              INNER JOIN AC_TX_HBONE T2
	                ON T1.TXACCTNO = T2.TX_ACCT_NO
	              INNER JOIN CM_CONSCUST T3
	                ON T2.hbone_no = T3.hbone_no
	              INNER JOIN CM_CUSTCONSTANT T4
	                ON T3.conscustno = T4.custno
	             WHERE T1.tatradedt = TO_CHAR(sysdate - interval '24' hour, 'YYYYMMDD')
	             and (T1.ackvol is null or  T1.ackvol = 0)     and T1.Mbusicode = '1143') tem
	           ) WHERE rn = 1
         ) T
         WHERE NOT EXISTS (SELECT 1 FROM CM_REMIND_MSG_DAY D WHERE D.MSGTYPE = '10' AND D.MSGCODE = F_MD5(T.CONSCODE || T.MSGCONTENT))
    </select>

    <select id="queryCustPaymentMsgList" resultType="com.howbuy.crm.nt.remind.domain.CmRemindMsgParam">
        SELECT TT.CONSCODE,
               TT.MSGCONTENT,
               TT.CUSTNAME,
               TT.businessid,
               TT.PNAME,
               TT.deadline,
            TT.MSGCONTENTDT,
                TO_CHAR(SYSDATE, 'YYYYMMDD') AS EXPIREDT,
               F_MD5(TT.CONSCODE || TT.MSGCONTENT) AS MSGCODE
        FROM (SELECT CT.CONSCODE,
                     '【缴款通知】您名下的客户' || CC.CUSTNAME || '持有的创新产品' || A.FUNDCODE||' '||A.FUNDNAME ||
                     '，将于' || C.PAYDT || '缴款，请通知客户及时缴款' AS MSGCONTENT,
                     CC.CUSTNAME，
                     '200428' as businessid,
               A.FUNDCODE||' '||A.FUNDNAME AS PNAME,
               C.PAYDT AS deadline,
               TO_CHAR(SYSDATE, 'yyyyMMdd') AS MSGCONTENTDT
		FROM CM_BX_PREBOOKINFO T
		LEFT JOIN CM_BX_PREBOOK_BUYINFO B
		ON T.ID = B.PREID
		LEFT JOIN CM_BX_PRODUCT A
		ON B.FUNDCODE = A.FUNDCODE
		LEFT JOIN CM_BX_PREBOOK_ENDPAY_LIST C
		ON C.BUYID = B.ID
		LEFT JOIN CM_CONSCUST CC
		ON T.CONSCUSTNO = CC.CONSCUSTNO
		LEFT JOIN CM_CUSTCONSTANT CT
		ON T.CONSCUSTNO = CT.CUSTNO
		WHERE C.PAYSTATE = '3'
		AND C.ISDEL = '1'
		AND B.ISDEL = '1'
		AND T.PRESTATE = '2'
		AND T.CHECKSTATE = '2'
		AND T.INSURSTATE = '3'
		AND ((A.BUSITYPE = '1' AND
		((TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 15, 'YYYYMMDD')) OR
		(TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 5, 'YYYYMMDD')))) OR
		(A.BUSITYPE <![CDATA[<>]]> '1' AND
		((TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 15, 'YYYYMMDD')) OR
		(TO_CHAR(SYSDATE, 'YYYYMMDD') =
		TO_CHAR(TO_DATE(C.PAYDT, 'YYYYMMDD') - 25, 'YYYYMMDD')))))) TT
		WHERE NOT EXISTS (SELECT 1
          FROM (select *
                  from CM_REMIND_MSG_DAY D
                 WHERE D.MSGTYPE = '11' UNION　ALL
                  select *
				  from CM_REMIND_MSG_HIS H
				 WHERE H.MSGTYPE = '11'
                ) CC
         WHERE CC.MSGCODE = F_MD5(TT.CONSCODE || TT.MSGCONTENT))
    </select>

    <update id="batchInsertCmRemindMsgDay" parameterType="list" useGeneratedKeys="false">
        INSERT INTO CM_REMIND_MSG_DAY (ID, MSGTYPE, CONSCODE, MSGCONTENT, READFLAG, CREATOR, PUSHDT,PUSHFLAG, CREDT,
        STIMESTAMP, MSGCONTENTDT, EXPIREDT, MSGCODE)
        <foreach item="item" index="index" collection="list" open="(" close=")" separator="union all">
            SELECT
            #{item.id, jdbcType=VARCHAR} as a0,
            #{item.msgType, jdbcType=VARCHAR} as a1,
            #{item.consCode, jdbcType=VARCHAR} as a2,
            #{item.msgContent, jdbcType=VARCHAR } as a3,
            #{item.readFlag, jdbcType=VARCHAR} as a4,
            #{item.creator, jdbcType=VARCHAR} as a5,
            TO_DATE(#{item.pushDt, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') as a6,
            #{item.pushFlag, jdbcType=VARCHAR} as a12,
            TO_DATE(#{item.creDt, jdbcType=VARCHAR}, 'YYYY-MM-DD HH24:MI:SS') as a7,
            #{item.stimeStamp, jdbcType=TIMESTAMP} as a8,
            #{item.msgContentDt, jdbcType=VARCHAR } as a9,
            #{item.expireDt, jdbcType=VARCHAR } as a10,
            #{item.msgCode, jdbcType=VARCHAR } as a11
            FROM DUAL
        </foreach>
    </update>

    <update id="batchInsertCmRemindMsgHis" useGeneratedKeys="false">
        INSERT INTO CM_REMIND_MSG_HIS
		  (ID,
		   MSGTYPE,
		   CONSCODE,
		   MSGCONTENT,
		   READFLAG,
		   CREATOR,
		   PUSHDT,
		   CREDT,
		   MODDT,
		   MODIFIER,
		   STIMESTAMP,
		   MSGCONTENTDT,
		   EXPIREDT,
		   MSGCODE,
		   PUSHFLAG,
		   PUSHID,
           MSG_LINK)
		  (SELECT ID,
		          MSGTYPE,
		          CONSCODE,
		          MSGCONTENT,
		          READFLAG,
		          CREATOR,
		          PUSHDT,
		          SYSDATE,
		          MODDT,
		          MODIFIER,
		          STIMESTAMP,
		          MSGCONTENTDT,
		          EXPIREDT,
		          MSGCODE,
		          PUSHFLAG,
		          PUSHID,
                  MSG_LINK
		     FROM CM_REMIND_MSG_DAY
		    WHERE TO_CHAR(CREDT, 'YYYYMMDD') != TO_CHAR(SYSDATE, 'YYYYMMDD')
   			  AND EXPIREDT <![CDATA[<]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
   		   )
    </update>

    <delete id="deleteCmRemindMsgDay">
        DELETE FROM CM_REMIND_MSG_DAY
		 WHERE TO_CHAR(CREDT, 'YYYYMMDD') != TO_CHAR(SYSDATE, 'YYYYMMDD')
		   AND EXPIREDT <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD')
    </delete>

    <select id="countEcDoubleRecordMessage" resultType="Integer" useCache="false">
        SELECT count(*)
        from cm_double_trade t1			  
        <!-- CONDUCTOR处理人为空 -->
        where t1.needFlag = '1'  and t1.CONDUCTOR is null AND t1.recState = '0'  AND t1.taskType = '2'
          AND t1.systemFlag != 'RETAIL'
          AND NOT EXISTS (SELECT 1
                 FROM CM_DOUBLE_TRADE DT
                WHERE DT.SYSTEMFLAG IN ('ZT', 'CRM')
                  AND DT.TASKTYPE = '1'
                  AND DT.HANDLEFLAG = '1'
                  AND DT.RECSTATE = '0'
                  AND DT.CONTRACTNO = T1.CONTRACTNO)
          AND NOT EXISTS
             (SELECT 1
                 FROM CM_DOUBLE_TRADE DT
                INNER JOIN CM_ZT_ORDERINFO ZT
                   ON ZT.APPOINTMENTDEALNO = DT.CONTRACTNO
                WHERE DT.SYSTEMFLAG IN ('ZT', 'CRM')
                  AND DT.TASKTYPE = '1'
                  AND DT.HANDLEFLAG = '1'
                  AND DT.RECSTATE = '0'
                  AND ZT.DEALNO = T1.CONTRACTNO)
          AND ((t1.SYSTEMFLAG = 'CRM' AND t1.PMTDT IS NOT NULL AND
              SYSDATE >= TO_DATE(t1.PMTDT, 'YYYYMMDDHH24MISS') + 1) OR
              (t1.SYSTEMFLAG = 'ZT' AND
              TO_CHAR(SYSDATE, 'YYYYMMDDHH24MISS') >= T1.CALMENDDT))
          AND EXISTS
              (select 1 from ( select pre.prebookstate, to_char(pre.id) as doubleNo from CM_PREBOOKPRODUCTINFO pre
		  			  	       union 
		  			  	       select pre.prebookstate,ord.DEALNO as doubleNo from CM_PREBOOKPRODUCTINFO pre
		  				       inner join cm_zt_orderinfo ord on to_char(pre.id) = ord.APPOINTMENTDEALNO
		  				     ) pp
		  	   <!-- 不等于已撤销 -->
		  	    where pp.prebookstate != '4' and t1.contractno = pp.doubleNo
		  	  )
        ORDER BY T1.CREDT DESC     
    </select>
    <select id="queryCustZlgxMsgListForPush" resultType="com.howbuy.crm.nt.remind.domain.CmZlgxMsgInfo">
        SELECT T.CONSCODE as consCode,
                T.TYPE as type,
                T.STR as name,
                T.TITLE  as title,
                DECODE(T.REPORTTYPE,
                             '4',
                             '通知公告',
                             '5',
                             '运作报告',
                             '6',
                             '调研报告',
                             '7',
                             '会议纪要',
                             '8',
                             '信息披露',
                             '9',
                             '分配公告',
                             '13',
                             '项目进展',
                             '') as reportType
      FROM (
        SELECT TIT.CONSCODE,TIT.TYPE,
        TIT.REPORTTYPE,TIT.STR,TIT.TITLE
        FROM (SELECT 1 AS TYPE,
               TTT.CONSCODE,
               TTT.REPORTTYPE,
               to_char(wm_concat(TTT.FUNDCODE)) AS STR,
               TTT.TITLE
          FROM (SELECT CAL.CONSCODE, CAL.REPORTTYPE, CAL.FUNDCODE, CAL.TITLE
                  FROM (SELECT (SELECT JX.JJDM||' '||JX.JJJC
                                  FROM JJXX1 JX
                                 WHERE JX.JJDM = CR.FUNDCODE) as FUNDCODE,
                               CR.REPORTTYPE,
                               CH.CUSTNO,
                               CT.CONSCODE,
                               CR.TITLE
                          FROM (SELECT T.FUNDCODE, T.REPORTTYPE, T.TITLE
                                  FROM CM_company_report T
                                 where T.FUNDCODE IS NOT NULL
                                   AND T.STATUS = 0
                                   AND T.SENDSTATUS = '1'
                                    AND T.CHECKSTATUS = '1'
                                    AND T.REPORTTYPE IN
                                    ('4', '5', '6', '7', '8', '9', '13')
                                    <if test="timeFlag != null and timeFlag == 'PM17'">
                                        AND TO_CHAR(T.SYNCDATE, 'YYYYMMDDHH24MI') <![CDATA[=]]> to_char(sysdate,'YYYYMMDD') || '1730'
                                    </if>
                                    <if test="timeFlag != null and timeFlag == 'PM19'">
                                        AND TO_CHAR(T.SYNCDATE, 'YYYYMMDDHH24MI') <![CDATA[=]]> to_char(sysdate,'YYYYMMDD') || '1930'
                                    </if>
                              ) CR
                          LEFT JOIN CM_HIGH_CUSTFUND CH
                            ON CR.FUNDCODE = CH.FUNDCODE
                          LEFT JOIN CM_CUSTCONSTANT CT
                            ON CH.CUSTNO = CT.CUSTNO
                         WHERE CH.CUSTNO IS NOT NULL
                         AND CH.BALANCEVOL > 1) CAL
                 GROUP BY CAL.CONSCODE,
                          CAL.REPORTTYPE,
                          CAL.FUNDCODE,
                          CAL.TITLE) TTT
         GROUP BY TTT.CONSCODE, TTT.REPORTTYPE, TTT.TITLE
        UNION ALL
        SELECT 0 AS TYPE,
               CAL.CONSCODE,
               CAL.REPORTTYPE,
               (SELECT A.JGMC
                  FROM jgxx A
                 WHERE A.JGDM = CAL.COMPANYID ) AS STR,
               CAL.TITLE
          FROM (SELECT CR.COMPANYID,
                       CR.REPORTTYPE,
                       CH.CUSTNO,
                       CT.CONSCODE,
                       CR.TITLE
                  FROM (SELECT T.JJDM AS FUNDCODE,
                               R.COMPANYID,
                               R.REPORTTYPE,
                               R.TITLE
                          FROM JJXX1 T, CM_company_report R
                         where T.GLRM = R.COMPANYID
                           AND R.FUNDCODE IS NULL
                           AND R.STATUS = 0
                           AND R.SENDSTATUS = '1'
                            AND R.CHECKSTATUS = '1'
                            AND R.REPORTTYPE IN
                            ('4', '5', '6', '7', '8', '9', '13')
                            <if test="timeFlag != null and timeFlag == 'PM17'">
                                AND TO_CHAR(R.SYNCDATE, 'YYYYMMDDHH24MI') <![CDATA[=]]> to_char(sysdate,'YYYYMMDD') || '1730'
                            </if>
                            <if test="timeFlag != null and timeFlag == 'PM19'">
                                AND TO_CHAR(R.SYNCDATE, 'YYYYMMDDHH24MI') <![CDATA[=]]> to_char(sysdate,'YYYYMMDD') || '1930'
                            </if>
                      ) CR
                  LEFT JOIN CM_HIGH_CUSTFUND CH
                    ON CR.FUNDCODE = CH.FUNDCODE
                  LEFT JOIN CM_CUSTCONSTANT CT
                    ON CH.CUSTNO = CT.CUSTNO
                 WHERE CH.CUSTNO IS NOT NULL
                 AND CH.BALANCEVOL > 1) CAL
         GROUP BY CAL.CONSCODE, CAL.REPORTTYPE, CAL.COMPANYID, CAL.TITLE) TIT
         ) T
    </select>
    <select id="queryFundJzMsgListForPush" resultType="com.howbuy.crm.nt.remind.domain.CmJzMsgInfo">
        SELECT  N.CONSCODE AS consCode,
                J.JJDM as  pcode,
                J.JJJC as pname,
                J.JSRQ as "date",
                TO_CHAR(J.JJJZ, 'FM9999999990.0000') as nav ,
                J.LASTJZRQ as lastDate,
                (CASE WHEN J.DFLAG='1' THEN J.TFLAG || DECODE(J.HBDR, NULL, '--', J.HBDR, TO_CHAR((J.HBDR * 100), 'FM9999999990.00')||'%') ELSE '计算中' END) as chg,
                N.CNT as amount
        FROM (
        SELECT T1.JJDM, T1.JJJC, T1.JSRQ, T1.JJJZ, T1.HBDR, T1.LASTJZRQ, T1.JJJZMODDT, T1.SYNCDT, (CASE WHEN T1.MODDT IS
        NOT NULL AND T1.JJJZMODDT <![CDATA[<]]> T1.MODDT THEN 1 WHEN T1.MODDT IS NOT NULL AND T1.JJJZMODDT
        <![CDATA[>=]]> T1.MODDT THEN 2 ELSE 3 END) DFLAG, (CASE WHEN T1.HBDR IS NOT NULL AND T1.HBDR <![CDATA[>]]> 0
        THEN '+' ELSE '' END) TFLAG
        FROM CM_JJJZ_MSG_REC T1
        WHERE 1=1
        <if test="timeFlag != null and timeFlag == 'AM9'">
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE - 1, 'YYYYMMDD') || '1600'
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD') || '0000'
        </if>
        <if test="timeFlag != null and timeFlag == 'PM17'">
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[>]]>  TO_CHAR(SYSDATE, 'YYYYMMDD') || '0000'
            AND TO_CHAR(T1.JJJZMODDT, 'YYYYMMDDHH24MI') <![CDATA[<=]]> TO_CHAR(SYSDATE, 'YYYYMMDD') || '1600'
        </if>
        AND T1.ID IN (SELECT ID FROM CM_JJJZ_MSG_LATEST)
        ) J
        LEFT JOIN (SELECT T.FUNDCODE, COUNT(T.CUSTNO) AS CNT, B.CONSCODE
        FROM CM_HIGH_CUSTFUND T
        INNER JOIN CM_CONSCUST T1
        ON T.CUSTNO = T1.CONSCUSTNO
        INNER JOIN CM_CUSTCONSTANT B
        ON T.CUSTNO = B.CUSTNO
        INNER JOIN JJXX1 T2
        ON T.FUNDCODE = T2.JJDM
        WHERE T.BALANCEVOL > 1
        AND T.FUNDTYPE IN ('2', '3')
        GROUP BY T.FUNDCODE, B.CONSCODE) N
        ON J.JJDM = N.FUNDCODE
        LEFT JOIN JJXX1 T3
        ON T3.JJDM = J.JJDM
        WHERE 1 = 1
        AND N.CONSCODE IS NOT NULL
    </select>
    <select id="getPCExpectPushMsg" resultType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsg">
        SELECT t.pushid       as pushid,
        t.title        as title,
        t.msgtype      as msgtype,
        '【' || cmt.MSG_TYPE_NAME || '】' || t.msgcontent   as msgcontent,
        t.conscode     as conscode,
        t.orgcode      as orgcode,
        t.creator      as creator,
        t.credt        as credt,
        t.modifier     as modifier,
        t.moddt        as moddt,
        t.pushflag     as pushflag,
        t.pushdt       as pushdt,
        t.expectpushdt as expectpushdt,
        t.msgstyle     as msgstyle,
        t.msgchannel   as msgchannel,
        t.remark       as remark,
        t.MSG_LINK     as msgLink
        FROM CM_PUSH_MSG T
        left join CM_MSG_TYPE cmt on t.msgtype = cmt.msg_type_code
        WHERE T.PUSHFLAG = '0'
        AND T.MSGCHANNEL !=  '2'
        AND T.Expectpushdt >= sysdate - 500 / (24 * 60)
        AND T.Expectpushdt &lt; sysdate
    </select>
    <select id="getWechatExpectPushMsg" resultType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsg">
        SELECT t.pushid       as pushid,
        t.title        as title,
        t.msgtype      as msgtype,
        cmt.MSG_TYPE_NAME as msgtypeval,
        t.msgcontent   as msgcontent,
        t.conscode     as conscode,
        t.orgcode      as orgcode,
        t.creator      as creator,
        t.credt        as credt,
        t.modifier     as modifier,
        t.moddt        as moddt,
        t.pushflag     as pushflag,
        t.pushdt       as pushdt,
        t.expectpushdt as expectpushdt,
        t.msgstyle     as msgstyle,
        t.msgchannel   as msgchannel,
        t.remark       as remark,
        t.MSG_LINK     as msgLink
        FROM CM_PUSH_MSG T
        left join CM_MSG_TYPE cmt on t.msgtype = cmt.msg_type_code
        WHERE T.PUSHFLAG = '0'
        AND T.MSGCHANNEL =  '2'
        and T.Msgstyle='1'
        AND T.Expectpushdt >= sysdate - 500 / (24 * 60)
        AND T.Expectpushdt  &lt; sysdate
        and T.task_type is null
    </select>

    <select id="getWechatTaskPushMsg" resultType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsg">
        SELECT t.pushid       as pushid,
        t.title        as title,
        t.msgtype      as msgtype,
        cmt.MSG_TYPE_NAME as msgtypeval,
        t.msgcontent   as msgcontent,
        consult.conscode     as conscode,
        t.conscode     as wechatConsCode,
        t.orgcode      as orgcode,
        t.creator      as creator,
        t.credt        as credt,
        t.modifier     as modifier,
        t.moddt        as moddt,
        t.pushflag     as pushflag,
        t.pushdt       as pushdt,
        t.expectpushdt as expectpushdt,
        t.msgstyle     as msgstyle,
        t.msgchannel   as msgchannel,
        t.remark       as remark,
        t.task_type as taskType,
        t.conscustno as conscustNo,
        t.task_id as taskId,
        t.MSG_LINK as msgLink,
        task.card_url as cardUrl
        FROM CM_PUSH_MSG T
        left join CM_MSG_TYPE cmt on t.msgtype = cmt.msg_type_code
        left join CM_WECHAT_PUSH_TASK task on T.task_id = task.id
		LEFT JOIN CM_CONSULTANT consult
		ON T.CONSCODE = consult.WECHATCONSCODE
        WHERE
        T.task_type in ('1', '2')
        and T.PUSHFLAG = '0'
        AND T.MSGCHANNEL =  '2'
        and T.Msgstyle='1'
        AND (T.Expectpushdt is null or (T.Expectpushdt >= sysdate - 500 / (24 * 60)
              AND T.Expectpushdt  &lt; sysdate))
    </select>

    <insert id="batchUpdatePushFlag">
        DECLARE
        begin
        <foreach collection="msgList" item="item">
            update CM_PUSH_MSG a
            set
            a.PUSHFLAG = #{pushFlag}, a.PUSHDT = SYSDATE
            where a.pushid = #{item.pushid};
        </foreach>
        commit;
        END;
    </insert>
    <insert id="insertCmRemindMsgDayAndUpdatePushFlag"
            parameterType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsg">
        DECLARE
        begin
        insert into CM_REMIND_MSG_DAY
          (ID,
           MSGTYPE,
           CONSCODE,
           MSGCONTENT,
           READFLAG,
           CREATOR,
           CREDT,
           MODIFIER,
           MODDT,
           STIMESTAMP,
           PUSHDT,
           MSGCONTENTDT,
           EXPIREDT,
           MSGCODE,
           PUSHFLAG,
           PUSHID,
           MSG_LINK)
        values
          (SEQ_CM_PUSH_MSG_DAY_ID.NEXTVAL,
           #{msgtype},
            #{conscode},
            #{msgcontent},
           '0',
           'push-sys',
           sysdate,
           null,
           null,
           sysdate,
           sysdate,
           to_char(sysdate, 'YYYYmmDD'),
           to_char(sysdate, 'YYYYmmDD'),
           F_MD5( #{conscode} || #{msgcontent}),
           '0',
           #{pushid},
           #{msgLink,jdbcType=VARCHAR});

        UPDATE CM_PUSH_MSG T
           SET T.PUSHFLAG = '1', T.PUSHDT = SYSDATE
         WHERE T.PUSHID =  #{pushid};
        commit;
        END;
    </insert>

    <select id="getCmPushMsgAnnexTitle" parameterType="String" resultType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex" useCache="false">
        SELECT * FROM CM_PUSH_MSG_ANNEX T WHERE T.CONTENTTYPE = '1' AND T.PUSHID = #{pushid}
    </select>

    <select id="listPushMsgAnnex" parameterType="String" resultType="com.howbuy.crm.nt.pushmsg.dto.CmPushMsgAnnex" useCache="false">
        SELECT * FROM CM_PUSH_MSG_ANNEX T WHERE T.CONTENTTYPE = '2' AND T.PUSHID = #{pushid}
    </select>
</mapper>
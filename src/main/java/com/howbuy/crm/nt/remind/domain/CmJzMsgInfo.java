package com.howbuy.crm.nt.remind.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 净值通知消息info
 */
@Data
public class CmJzMsgInfo implements Serializable {


	private static final long serialVersionUID = 1L;

	/** 所属投顾 */
	private String consCode;

	/** 产品代码 */
	private String pcode;

	/** 产品名称 */
	private String pname;

	/** 净值日期 */
	private String date;

	/** 净值 */
	private String nav;

	/** 上一净值日期 */
	private String lastDate;

	/** 涨跌幅 */
	private String chg;

	/** 持仓客户数 */
	private String amount;
}

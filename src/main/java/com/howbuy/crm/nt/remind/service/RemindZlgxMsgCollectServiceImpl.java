package com.howbuy.crm.nt.remind.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.nt.remind.buss.CmRemindMsgDaoBuss;
import com.howbuy.crm.util.DateTimeUtil;
import com.howbuy.crm.util.MainLogUtils;
import com.howbuy.persistence.content.interview.CompanyReport;
import crm.howbuy.base.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("remindZlgxMsgCollectService")
@Slf4j
public class RemindZlgxMsgCollectServiceImpl implements RemindZlgxMsgCollectService {

    @Autowired
    private CmRemindMsgDaoBuss cmRemindMsgDaoBuss;

    @Value("${dbhttpurl}")
    private String dbhttpurl;

    @Override
    public void syncCustZlgxMsgData(String arg) {
        /*this.syncZlgxMsgData("zlgxtz");*/
        String beforeday;
        String nowday;
        String timeFlag;
        Map<String, String> paramMap = new HashMap<>();
        if(arg.contains("PM17")){
            beforeday = DateTimeUtil.getBeforeDateFmt(DateTimeUtil.DATE_PATTERN)+" 19:30:00";
            nowday = DateTimeUtil.getCurDate(DateTimeUtil.DATE_PATTERN)+" 17:30:00";
            timeFlag = "PM17";
        } else {
            beforeday = DateTimeUtil.getCurDate(DateTimeUtil.DATE_PATTERN)+" 17:30:00";
            nowday = DateTimeUtil.getCurDate(DateTimeUtil.DATE_PATTERN)+" 19:30:00";
            timeFlag = "PM19";
        }

        log.info("syncCustZlgxMsgData传参beforeday:{},nowday:{}",beforeday,nowday);

        paramMap.put("startTime",beforeday);
        paramMap.put("endTime",nowday);
        paramMap.put("timeFlag",timeFlag);

        List<CompanyReport> reportlist= new ArrayList<CompanyReport>();
        try {
            long startTime = System.currentTimeMillis();
            String responsejson = HttpUtils.post(dbhttpurl,paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(dbhttpurl, String.valueOf(HttpStatus.OK.value()), endTime - startTime);

            JSONArray jsonArray = JSONArray.parseArray(responsejson);
            if(jsonArray != null && jsonArray.size() > 0){
                for(Object obj :jsonArray){
                    JSONObject entity = (JSONObject) obj;
                    CompanyReport bean = JSON.parseObject(entity.toJSONString(), CompanyReport.class);
                    reportlist.add(bean);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        log.info("syncCustZlgxMsgData.reportlist:"+ JSON.toJSONString(reportlist));
        if(reportlist != null && reportlist.size() > 0){
            cmRemindMsgDaoBuss.batchInsertCmCompanyReport(reportlist,nowday);
        }
        this.syncZlgxMsgData(paramMap);
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncZlgxMsgData(Map paramMap){
        // 加载到账通知任务
        cmRemindMsgDaoBuss.pushCustZlgxMsg(paramMap);
    }
}

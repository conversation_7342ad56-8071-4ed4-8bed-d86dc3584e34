package com.howbuy.crm.nt.asset.dao;

import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.nt.asset.dto.AssetAdjustDomain;
import com.howbuy.crm.nt.asset.dto.AssetCombinationDomain;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CmProductInfoMapper {

    /**
     * 私募产品库是否有
     * @param pCode
     * @return
     */
    int getHaveCmProductInfo(@Param("pCode") String pCode);

    /**
     * 调整需要的基金信息字段
     * @param fundCodes
     * @return
     */
    List<AssetAdjustDomain> listJjxxByAdjust(@Param("fundCodes") List<String> fundCodes);

    /**
     * 组合查询
     * @param fundCodes
     * @return
     */
    List<AssetCombinationDomain> listJjxxByCombinationAsset(@Param("fundCodes") List<String> fundCodes);

    /**
     * 查询私募基金(调整)
     * @param param
     * @param pageBean
     * @return
     */
    List<AssetCombinationDomain> listProductByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 查询所有私募基金(组合):
     * @param
     * <AUTHOR>
     * @date 2020/8/11
     */
    List<AssetCombinationDomain> listAllPriFundByPage(@Param("param") Map<String, String> param, @Param("page") CommPageBean pageBean);

    /**
     * 收益情况需要的基金信息相关字段
     * @param fundcode
     * @return
     */
    AssetCombinationDomain getfindProduct(@Param("fundcode") String fundcode);

    /**
     * 根据产品代码查询产品部分信息
     * @param fundcode
     * @return
     */
    List<JjxxInfo> getProductByCode(@Param("fundcode") String fundcode);
}

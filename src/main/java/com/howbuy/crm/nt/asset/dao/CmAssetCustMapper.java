package com.howbuy.crm.nt.asset.dao;

import com.howbuy.crm.nt.asset.dto.CmAssectReportInfo;
import crm.howbuy.base.db.CommPageBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CmAssetCustMapper {

    /**
     * 资配报告查询完成情况报表
     * @param param
     * @return
     */
    List<CmAssectReportInfo> listCustForAssetReportByPage(@Param("param") Map param, @Param("page") CommPageBean pageBean);
}

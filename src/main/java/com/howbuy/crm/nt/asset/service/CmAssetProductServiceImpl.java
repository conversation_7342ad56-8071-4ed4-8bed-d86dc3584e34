package com.howbuy.crm.nt.asset.service;

import com.howbuy.crm.dubbo.framework.dbutil.plugins.PaginationInterceptorPlugin;
import com.howbuy.crm.nt.asset.dao.CmProductInfoMapper;
import com.howbuy.crm.nt.asset.dto.AssetAdjustDomain;
import com.howbuy.crm.nt.asset.dto.AssetCombinationDomain;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service("cmAssetProductService")
public class CmAssetProductServiceImpl implements CmAssetProductService{

    @Autowired
    private CmProductInfoMapper cmProductInfoMapper;

    @Override
    public boolean getHaveProductInfo(String pCode) {
        if(cmProductInfoMapper.getHaveCmProductInfo(pCode) > 0){
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<AssetAdjustDomain> listJjxxByAdjust(List<String> fundCodes) {
        return cmProductInfoMapper.listJjxxByAdjust(fundCodes);
    }

    @Override
    public List<AssetCombinationDomain> listJjxxByCombinationAsset(List<String> fundCodes) {
        return cmProductInfoMapper.listJjxxByCombinationAsset(fundCodes);
    }

    @Override
    public PageData<AssetCombinationDomain> listProductByPage(Map<String, String> param) {
        PageData<AssetCombinationDomain> pageData = new PageData<>();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<AssetCombinationDomain> list = cmProductInfoMapper.listProductByPage(param, pageBean);
        pageData.setListData(list);
        pageData.setPageBean(pageBean);
        return pageData;
    }

    @Override
    public PageData<AssetCombinationDomain> listAllPriFundByPage(Map<String, String> param) {
        PageData<AssetCombinationDomain> pageData = new PageData<>();
        CommPageBean pageBean = CommPageBean.getPageBeanByParam(param);
        List<AssetCombinationDomain> list = cmProductInfoMapper.listAllPriFundByPage(param, pageBean);
        pageData.setListData(list);
        pageData.setPageBean(pageBean);
        return pageData;
    }

    /**
     * 收益情况需要的基金信息相关字段
     *
     * @param fundcode
     * @return
     */
    @Override
    public AssetCombinationDomain getfindProduct(String fundcode) {
        return cmProductInfoMapper.getfindProduct(fundcode);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.asset.dao.CmAssetCustMapper">

    <select id="listCustForAssetReportByPage" parameterType="java.util.Map" resultType="com.howbuy.crm.nt.asset.dto.CmAssectReportInfo" useCache="false">
        SELECT CH.CONSCUSTNO,A.CUS<PERSON>ME,CC.CONSCODE,CC.TEAMCODE,H.ORGNAME, SC.PREMIUM_AMOUNT as premiumamount
        FROM CM_HIGH_CUSTINFO CH
        LEFT JOIN CM_CONSCUST A ON CH.CONSCUSTNO = A.CONSCUSTNO
        LEFT JOIN SYNC_CAS_BALANCE_SUMMARY SC
        ON SC.HBONE_NO = CH.HBONENO
        LEFT JOIN CM_CUSTCONSTANT CA
        ON CH.CONSCUSTNO = CA.CUSTNO
        LEFT JOIN CM_CONSULTANT CC
        ON CA.CONSCODE = CC.CONSCODE
        LEFT JOIN hb_organization H
        ON CC.OUTLETCODE = H.ORGCODE
        WHERE 1 = 1
        <if test="param.amount!=null">
            <if test="param.amount == '100'.toString()">
                AND SC.PREMIUM_AMOUNT <![CDATA[ > ]]> 1000000
            </if>
            <if test="param.amount == '200'.toString()">
                AND SC.PREMIUM_AMOUNT <![CDATA[ > ]]> 2000000
            </if>
            <if test="param.amount == '300'.toString()">
                AND SC.PREMIUM_AMOUNT <![CDATA[ > ]]> 3000000
            </if>
        </if>
        <if test="param.istrade!=null"> AND CH.GDCJLABEL = #{param.istrade} </if>
        <if test="param.teamCode != null">
            AND cc.conscode in (${param.teamCode})
        </if>
        <if test="param.consCode != null">
            AND cc.conscode = #{param.consCode}
        </if>
    </select>
</mapper>
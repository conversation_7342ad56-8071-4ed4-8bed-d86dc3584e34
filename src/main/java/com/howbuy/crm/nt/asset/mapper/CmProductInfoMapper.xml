<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.howbuy.crm.nt.asset.dao.CmProductInfoMapper">

    <select id="getHaveCmProductInfo" resultType="int" parameterType="string" useCache="false">
        select count(1) from CM_PRODUCTINFO where pcode = #{pCode}
    </select>

    <select id="listJjxxByAdjust" parameterType="string" resultType="com.howbuy.crm.nt.asset.dto.AssetAdjustDomain" useCache="false">
        select
              t1.jjdm as fundCode,
               case t1.yzqdw
                 when 1 then
                  t1.yzq  ||  '天'
                 when 2 then
                  t1.yzq  ||  '周'
                 when 3 then
                  t1.yzq  ||  '个月'
                 when 4 then
                  t1.yzq  ||  '年'
                 else
                  null
               end as INVESTMENT_HORIZON,
               t3.yjsy as BENCH_MARK,
               t2.qxsm as FUND_CXQX_STR,
               t1.plqx,
               t1.JJPY
        from jjxx1 t1
          left join smxx t2
            on t1.jjdm = t2.jjdm
          left join (select obj_value as xtgldm, target_value as xtdm
                    from s_transcode_data
                    where trans_code = 001) t4
            on t1.jjdm = t4.xtgldm
          left join xtxx t3
            on t3.xtdm = t4.xtdm
        where t1.jjdm in
        <foreach collection="fundCodes" close=")" item="item" open="(" separator=",">
          #{item}
        </foreach>
    </select>

    <resultMap id="combinationDomainMap" type="com.howbuy.crm.nt.asset.dto.AssetCombinationDomain">
        <result column="FUND_CODE" property="fundCode" jdbcType="VARCHAR"/>
        <result column="jjjc" property="fundName" jdbcType="VARCHAR"/>
        <result column="jjjz" property="newNav" jdbcType="VARCHAR"/>
        <result column="hmfx" property="riskLevel" jdbcType="VARCHAR"/>
        <result column="clrq" property="startDate" jdbcType="VARCHAR"/>
        <result column="zcfl" property="assetType" jdbcType="VARCHAR"/>
        <result column="SFHWJJ" property="isAboard" jdbcType="VARCHAR"/>
        <result column="clfl" property="strategyType" jdbcType="VARCHAR"/>
        <result column="yzq" property="investPeriod" jdbcType="VARCHAR"/>
        <result column="yjsy" property="incomeRate" jdbcType="INTEGER"/>
        <result column="qxsm" property="periodDesc" jdbcType="VARCHAR"/>
        <result column="plqx" property="plqx" jdbcType="VARCHAR"/>
        <result column="cpfl" property="cpfl" jdbcType="VARCHAR"/>
        <result column="jjpy" property="jjpy" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="jjxx1Map" type="com.howbuy.crm.jjxx.dto.JjxxInfo">
        <result column="jjdm" property="jjdm" jdbcType="VARCHAR"/>
        <result column="fccl" property="fccl" jdbcType="VARCHAR"/>
        <result column="SFMSJG" property="sfmsjg" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="listJjxxByCombinationAsset" resultMap="combinationDomainMap" parameterType="Map" useCache="false">
        select t2.jjdm as fundCode,
                t2.jjjc,
               t2.clrq,
               nvl(t2.zcfl,'5') as zcfl,
               t2.SFHWJJ,
               t2.clfl,
               t2.plqx,
               t2.JJPY,
               case t2.yzqdw
                 when 1 then
                  t2.yzq  ||  '天'
                 when 2 then
                  t2.yzq  ||  '周'
                 when 3 then
                  t2.yzq  ||  '个月'
                 when 4 then
                  t2.yzq  ||  '年'
                 else
                  null
               end as yzq,
               t4.yjsy,
               t3.qxsm,
               nvl2(t6.jzrq,to_char(t6.jjjz,'99990.9999') || '(' || t6.jzrq || ')','') as jjjz,
               nvl2(t6.jzrq,to_char(t6.jjjz,'99990.9999') || '<![CDATA[ <br/> ]]>' ||'(' || t6.jzrq || ')','') as newNav2
          from jjxx1 t2
          left join smxx t3
            on t2.jjdm = t3.jjdm
          left join (select obj_value as xtgldm, target_value as xtdm
                    from s_transcode_data
                    where trans_code = 001) t5
            on t2.jjdm = t5.xtgldm
          left join xtxx t4
            on t4.xtdm = t5.xtdm
          left join (select jjdm,jjjz,jzrq,row_number() over(partition by jjdm order by jzrq desc) as rn from jjjz) t6
            on t2.jjdm = t6.jjdm and t6.rn = 1
         where  t2.jjdm in
        <foreach collection="fundCodes" close=")" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <sql id="fundCondition">
        where t.cpfl in ('1','4','6')
        <!--好买在销-->
        <if test="param.useDefaultCondition != null and param.useDefaultCondition == '1'.toString()">
            AND t.zsbz = '1'
        </if>
        <if test="param.searchParam != null and param.searchParam != ''">
            AND (t.jjmc LIKE '%'||#{param.searchParam}||'%' OR t.JJDM  LIKE '%'||NLS_UPPER(#{param.searchParam})||'%' OR t.jjjc LIKE '%'||#{param.searchParam}||'%')
        </if>
        <!--好买FOF-->
        <if test="param.sffof != null and param.sffof == '1'.toString()">
            AND t.GLRM='90001228' AND t.SFFOF='1'
        </if>
    </sql>

    <select id="listProductByPage" parameterType="java.util.Map" resultMap="combinationDomainMap" useCache="false">
        SELECT t.JJDM fund_code,
        t.jjjc,
        t.clrq,
        t.hmfx,
        t.plqx,
        t.JJPY,
        nvl(t.zcfl,'5') as zcfl,
        t.SFHWJJ,
        case t.yzqdw
        when 1 then
        t.yzq || '天'
        when 2 then
        t.yzq || '周'
        when 3 then
        t.yzq || '个月'
        when 4 then
        t.yzq || '年'
        else
        null
        end as yzq,
        t4.yjsy,
        t3.qxsm,
        nvl2(t6.jzrq,to_char(t6.jjjz,'99990.9999') || '(' || t6.jzrq || ')','') as jjjz,
        t.cpfl,
        case
        when t.GLRM='90001228' AND t.SFFOF='1' then '是'
        else '否' END as sffof
        FROM JJXX1 t
        left join smxx t3
        on t.JJDM = t3.jjdm
        left join (select obj_value as xtgldm, target_value as xtdm
        from s_transcode_data
        where trans_code = 001) t5
        on t.JJDM = t5.xtgldm
        left join xtxx t4
        on t4.xtdm = t5.xtdm
        left join (select jjdm,
        jjjz,
        jzrq,
        row_number() over(partition by jjdm order by jzrq desc) as rn
        from jjjz
        where jjdm in (select jjdm
        from jjxx1 t
        <include refid="fundCondition"/>)
        ) t6
        on t.JJDM  = t6.jjdm and t6.rn = 1
        <include refid="fundCondition"/>
        order by t.clrq desc nulls last
    </select>

    <select id="listAllPriFundByPage" parameterType="java.util.Map" resultMap="combinationDomainMap" useCache="false">
        SELECT
        t1.jjdm as fund_code,
        t1.jjjc,
        t1.clrq,
        t1.hmfx,
        nvl(t1.zcfl, '5') as zcfl,
        t1.SFHWJJ,
        t1.clfl,
        t1.zsbz,
        case t1.yzqdw
        when 1 then
        t1.yzq || '天'
        when 2 then
        t1.yzq || '周'
        when 3 then
        t1.yzq || '个月'
        when 4 then
        t1.yzq || '年'
        else
        null
        end as yzq,
        t4.yjsy,
        t3.qxsm,
        t1.plqx,
        t1.JJPY,
        nvl2(t6.jzrq, to_char(t6.jjjz, '99990.9999') || '(' || t6.jzrq || ')', '') as jjjz,
        t1.cpfl
        FROM jjxx1 t1
        inner join jgxx t2
        on t1.glrm = t2.jgdm
        left join smxx t3
        on t1.JJDM = t3.jjdm
        left join (select obj_value as xtgldm, target_value as xtdm
        from s_transcode_data
        where trans_code = 001) t5
        on t1.jjdm = t5.xtgldm
        left join xtxx t4
        on t4.xtdm = t5.xtdm
        left join (select jjdm,
        jjjz,
        jzrq,
        row_number() over(partition by jjdm order by jzrq desc) as rn
        from jjjz) t6
        on t1.jjdm = t6.jjdm
        and t6.rn = 1
        WHERE
        <if test='param.useDefaultCondition=="1"'>
            t1.zsbz =1 and t1.cpfl in ('1', '4') and
        </if>
        t1.glrm = t2.jgdm
        <if test='param.sunPriFund =="1"'>
            and t1.cpfl='4'
        </if>
        <if test='param.sunPriFund == null'>
            and ((t1.cpfl='4' and (t2.sfsmjjglr = '1' or t2.sfsmjjglr is null))
            or (t1.cpfl='6' and (t2.plqx='1' or  t2.plqx is null))
            or t1.cpfl='1')
        </if>
        <if test="param.searchParam != null and param.searchParam != ''">
            AND (t1.jjdm LIKE '%'||NLS_UPPER(#{param.searchParam})||'%' OR t1.jjmc LIKE '%'||#{param.searchParam}||'%' OR t1.jjjc LIKE '%'||#{param.searchParam}||'%')
        </if>
    </select>

    <select id="getProductByCode" parameterType="String" resultMap="jjxx1Map">
        SELECT T.JJDM,T.FCCL,T.SFMSJG FROM JJXX1 T WHERE T.JJDM = #{fundcode}
    </select>

    <select id="getfindProduct" parameterType="String" resultMap="combinationDomainMap" useCache="false">
        SELECT t.jjdm fund_code,
        t.jjjc,
        t.clrq,
        t.hmfx,
        nvl(t.zcfl,'5') as zcfl,
        t.SFHWJJ,
        case t.yzqdw
        when 1 then
        t.yzq || '天'
        when 2 then
        t.yzq || '周'
        when 3 then
        t.yzq || '个月'
        when 4 then
        t.yzq || '年'
        else
        null
        end as yzq,
        t4.yjsy,
        t3.qxsm,
        nvl2(t6.jzrq,to_char(t6.jjjz,'99990.9999') || '(' || t6.jzrq || ')','') as jjjz
        FROM JJXX1 t
        left join smxx t3
        on t.jjdm = t3.jjdm
        left join (select obj_value as xtgldm, target_value as xtdm
        from s_transcode_data
        where trans_code = 001) t5
        on t.jjdm = t5.xtgldm
        left join xtxx t4
        on t4.xtdm = t5.xtdm
        left join (select jjdm,jjjz,jzrq,row_number() over(partition by jjdm order by jzrq desc) as rn from jjjz) t6
        on t.jjdm = t6.jjdm and t6.rn = 1
        WHERE t.cpfl != '10'
        AND t.JJDM = #{fundcode}
    </select>
</mapper>
package com.howbuy.crm.nt.asset.service;

import com.howbuy.crm.nt.asset.dao.CmAssetCustMapper;
import com.howbuy.crm.nt.asset.dto.CmAssectReportInfo;
import com.howbuy.crm.nt.asset.request.AssetCustRequest;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("cmAssetCustService")
public class CmAssetCustServiceImpl implements CmAssetCustService{

    @Autowired
    private CmAssetCustMapper cmAssetCustMapper;

    @Override
    public PageData<CmAssectReportInfo> queryCustByAssetReport(AssetCustRequest assetCustRequest) {
        Map param = new HashMap();
        param.put("amount", assetCustRequest.getAmount());
        param.put("consCode", assetCustRequest.getConsCode());
        param.put("teamCode", assetCustRequest.getTeamCode());
        param.put("istrade", assetCustRequest.getIsTrade());
        CommPageBean pageBean = assetCustRequest.getCommPageBean();
        List<CmAssectReportInfo> list = cmAssetCustMapper.listCustForAssetReportByPage(param, pageBean);
        PageData<CmAssectReportInfo> pageData = new PageData<>();
        pageData.setListData(list);
        pageData.setPageBean(pageBean);
        return pageData;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.trade.dao.CmCustPrivateFundMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.trade.domian.CmCustPrivateFund">
    <!--@mbg.generated-->
    <!--@Table CM_CUSTPRIVATEFUND-->
    <result column="CUSTNO" jdbcType="VARCHAR" property="custno" />
    <result column="FUNDCODE" jdbcType="VARCHAR" property="fundcode" />
    <result column="BALANCEVOL" jdbcType="DECIMAL" property="balancevol" />
    <result column="TOTALACKAMT" jdbcType="DECIMAL" property="totalackamt" />
    <result column="TRADEDT" jdbcType="VARCHAR" property="tradedt" />
    <result column="NAV" jdbcType="DECIMAL" property="nav" />
    <result column="TOTALCOST" jdbcType="DECIMAL" property="totalcost" />
    <result column="PROFIT" jdbcType="DECIMAL" property="profit" />
    <result column="PROFITPER" jdbcType="DECIMAL" property="profitper" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="CONSCUSTRID" jdbcType="VARCHAR" property="conscustrid" />
    <result column="MGRCODE" jdbcType="VARCHAR" property="mgrcode" />
    <result column="PROFITED" jdbcType="DECIMAL" property="profited" />
    <result column="DIVIDEAMT" jdbcType="DECIMAL" property="divideamt" />
    <result column="DIVAMT" jdbcType="DECIMAL" property="divamt" />
    <result column="CRETIME" jdbcType="TIMESTAMP" property="cretime" />
    <result column="MODTIME" jdbcType="TIMESTAMP" property="modtime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CUSTNO, FUNDCODE, BALANCEVOL, TOTALACKAMT, TRADEDT, NAV, TOTALCOST, PROFIT, PROFITPER, 
    RECSTAT, CHECKFLAG, CREATOR, MODIFIER, CHECKER, CREDT, MODDT, CONSCUSTRID, MGRCODE, 
    PROFITED, DIVIDEAMT, DIVAMT, CRETIME, MODTIME
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.nt.trade.domian.CmCustPrivateFund">
    <!--@mbg.generated-->
    insert into CM_CUSTPRIVATEFUND (CUSTNO, FUNDCODE, BALANCEVOL, 
      TOTALACKAMT, TRADEDT, NAV, 
      TOTALCOST, PROFIT, PROFITPER, 
      RECSTAT, CHECKFLAG, CREATOR, 
      MODIFIER, CHECKER, CREDT, 
      MODDT, CONSCUSTRID, MGRCODE, 
      PROFITED, DIVIDEAMT, DIVAMT, 
      CRETIME, MODTIME)
    values (#{custno,jdbcType=VARCHAR}, #{fundcode,jdbcType=VARCHAR}, #{balancevol,jdbcType=DECIMAL}, 
      #{totalackamt,jdbcType=DECIMAL}, #{tradedt,jdbcType=VARCHAR}, #{nav,jdbcType=DECIMAL}, 
      #{totalcost,jdbcType=DECIMAL}, #{profit,jdbcType=DECIMAL}, #{profitper,jdbcType=DECIMAL}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=VARCHAR}, #{conscustrid,jdbcType=VARCHAR}, #{mgrcode,jdbcType=VARCHAR}, 
      #{profited,jdbcType=DECIMAL}, #{divideamt,jdbcType=DECIMAL}, #{divamt,jdbcType=DECIMAL}, 
      #{cretime,jdbcType=TIMESTAMP}, #{modtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.trade.domian.CmCustPrivateFund">
    <!--@mbg.generated-->
    insert into CM_CUSTPRIVATEFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="custno != null">
        CUSTNO,
      </if>
      <if test="fundcode != null">
        FUNDCODE,
      </if>
      <if test="balancevol != null">
        BALANCEVOL,
      </if>
      <if test="totalackamt != null">
        TOTALACKAMT,
      </if>
      <if test="tradedt != null">
        TRADEDT,
      </if>
      <if test="nav != null">
        NAV,
      </if>
      <if test="totalcost != null">
        TOTALCOST,
      </if>
      <if test="profit != null">
        PROFIT,
      </if>
      <if test="profitper != null">
        PROFITPER,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="conscustrid != null">
        CONSCUSTRID,
      </if>
      <if test="mgrcode != null">
        MGRCODE,
      </if>
      <if test="profited != null">
        PROFITED,
      </if>
      <if test="divideamt != null">
        DIVIDEAMT,
      </if>
      <if test="divamt != null">
        DIVAMT,
      </if>
      <if test="cretime != null">
        CRETIME,
      </if>
      <if test="modtime != null">
        MODTIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="custno != null">
        #{custno,jdbcType=VARCHAR},
      </if>
      <if test="fundcode != null">
        #{fundcode,jdbcType=VARCHAR},
      </if>
      <if test="balancevol != null">
        #{balancevol,jdbcType=DECIMAL},
      </if>
      <if test="totalackamt != null">
        #{totalackamt,jdbcType=DECIMAL},
      </if>
      <if test="tradedt != null">
        #{tradedt,jdbcType=VARCHAR},
      </if>
      <if test="nav != null">
        #{nav,jdbcType=DECIMAL},
      </if>
      <if test="totalcost != null">
        #{totalcost,jdbcType=DECIMAL},
      </if>
      <if test="profit != null">
        #{profit,jdbcType=DECIMAL},
      </if>
      <if test="profitper != null">
        #{profitper,jdbcType=DECIMAL},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="conscustrid != null">
        #{conscustrid,jdbcType=VARCHAR},
      </if>
      <if test="mgrcode != null">
        #{mgrcode,jdbcType=VARCHAR},
      </if>
      <if test="profited != null">
        #{profited,jdbcType=DECIMAL},
      </if>
      <if test="divideamt != null">
        #{divideamt,jdbcType=DECIMAL},
      </if>
      <if test="divamt != null">
        #{divamt,jdbcType=DECIMAL},
      </if>
      <if test="cretime != null">
        #{cretime,jdbcType=TIMESTAMP},
      </if>
      <if test="modtime != null">
        #{modtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="listBuyingCustNoByFundCodePrefixes" resultType="java.lang.String">
    SELECT DISTINCT CUSTNO
    FROM CM_CUSTPRIVATEFUND
    WHERE BALANCEVOL > 0
      AND
    <foreach collection="fundCodePrefixes" item="fundCodePrefix" separator="OR" open="(" close=")">
      FUNDCODE LIKE #{fundCodePrefix,jdbcType=VARCHAR} || '%'
    </foreach>
  </select>

  <select id="listBuyedCustNoByFundCodePrefixes" resultType="java.lang.String">
    SELECT DISTINCT TRADE.CUSTNO
    FROM CM_CUSTPRIVATEFUND TRADE
    WHERE TRADE.BALANCEVOL <![CDATA[<=]]> 0
      AND
    <foreach collection="fundCodePrefixes" item="fundCodePrefix" separator="OR" open="(" close=")">
      TRADE.FUNDCODE LIKE #{fundCodePrefix,jdbcType=VARCHAR} || '%'
    </foreach>
  </select>

  <select id="listBuyingCustNoForPrivate" resultType="java.lang.String">
    SELECT DISTINCT TRADE.CUSTNO
    FROM CM_CUSTPRIVATEFUND TRADE
    WHERE (TRADE.FUNDCODE LIKE 'C%' OR TRADE.FUNDCODE LIKE 'S%' OR
           (TRADE.FUNDCODE LIKE 'P%' AND TRADE.FUNDCODE NOT LIKE 'PE%'))
      AND TRADE.BALANCEVOL > 0
  </select>

  <select id="listBuyedCustNoForPrivate" resultType="java.lang.String">
    SELECT DISTINCT TRADE.CUSTNO
    FROM CM_CUSTPRIVATEFUND TRADE
    WHERE (TRADE.FUNDCODE LIKE 'C%' OR TRADE.FUNDCODE LIKE 'S%' OR
           (TRADE.FUNDCODE LIKE 'P%' AND TRADE.FUNDCODE NOT LIKE 'PE%'))
      AND TRADE.BALANCEVOL <![CDATA[<=]]> 0
  </select>
</mapper>
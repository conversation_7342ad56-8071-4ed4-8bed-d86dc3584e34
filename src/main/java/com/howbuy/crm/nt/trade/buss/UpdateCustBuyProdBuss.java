/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.buss;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description: 统计购买产品和曾购买产品业务处理类
 * <AUTHOR>
 * @date 2023/11/20 14:21
 * @since JDK 1.8
 */

@Slf4j
@Component
public class UpdateCustBuyProdBuss {

    @Autowired
    private ConscustMapper conscustMapper;

    /**
     * 批量处理的数量
     */
    private static final int BATCH_SIZE = 200;


    /**
     * @description: 更新购买产品
     * @param index 购买产品重置为16位 '0000000000000000' 后，将指定下标的位置改为1，表示购买了该类产品
     * @param custNoList 客户号列表
     * @return int 更新条数
     * @author: jin.wang03
     * @date: 2023/11/20 13:25
     * @since JDK 1.8
     */
    public int updateBuyingProdByCustNoList(int index, List<String> custNoList) {
        if (CollectionUtils.isEmpty(custNoList)) {
            return 0;
        }
        AtomicInteger totalUpdateNum = new AtomicInteger();
        List<List<String>> partitionCustNoList = Lists.partition(custNoList, BATCH_SIZE);

        for (List<String> partitionList : partitionCustNoList) {
            int updateNum = conscustMapper.updateBuyingProdByCustNoList(index, partitionList);
            totalUpdateNum.addAndGet(updateNum);
            log.info("updateNum:{}", updateNum);
        }

        return totalUpdateNum.get();
    }


    /**
     * @description: 更新曾购买产品
     * @param index 曾购买产品重置为16位 '0000000000000000' 后，将指定下标的位置改为1，表示曾购买了该类产品
     * @param custNoList 客户号列表
     * @return int 更新条数
     * @author: jin.wang03
     * @date: 2023/11/20 13:26
     * @since JDK 1.8
     */
    public int updateBuyedProdByCustNoList(int index, List<String> custNoList) {
        if (CollectionUtils.isEmpty(custNoList)) {
            return 0;
        }
        AtomicInteger totalUpdateNum = new AtomicInteger();
        List<List<String>> partitionCustNoList = Lists.partition(custNoList, BATCH_SIZE);

        for (List<String> partitionList : partitionCustNoList) {
            int updateNum = conscustMapper.updateBuyedProdByCustNoList(index, partitionList);
            totalUpdateNum.addAndGet(updateNum);
            log.info("updateNum:{}", updateNum);
        }

        return totalUpdateNum.get();
    }

}
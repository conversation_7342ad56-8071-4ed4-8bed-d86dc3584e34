/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.dao;

import com.howbuy.crm.nt.trade.domian.CmCustPrivateFund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 客户持有私募dao层接口类
 * <AUTHOR>
 * @date 2023/11/20 11:22
 * @since JDK 1.8
 */
@Mapper
public interface CmCustPrivateFundMapper {

    /**
     * @param cmCustPrivateFund 私募客户信息
     * @return int 插入条数
     * @description: 插入私募客户信息
     * @author: jin.wang03
     * @date: 2023/11/20 14:43
     * @since JDK 1.8
     */
    int insert(CmCustPrivateFund cmCustPrivateFund);

    /**
     * @param cmCustPrivateFund 私募客户信息
     * @return int 插入条数
     * @description: 插入私募客户信息
     * @author: jin.wang03
     * @date: 2023/11/20 14:43
     * @since JDK 1.8
     */
    int insertSelective(CmCustPrivateFund cmCustPrivateFund);

    /**
     * @description: 根据基金代码查询持有的客户号
     * @param fundCodePrefixes 基金代码
     * @return java.util.List<java.lang.String> 持有的客户号列表
     * @author: jin.wang03
     * @date: 2023/11/20 14:55
     * @since JDK 1.8
     */
    List<String> listBuyingCustNoByFundCodePrefixes(@Param("fundCodePrefixes") List<String> fundCodePrefixes);

    /**
     * @description: 根据基金代码查询曾经持有的客户号
     * @param fundCodePrefixes 基金代码
     * @return java.util.List<java.lang.String> 曾经持有的客户号列表
     * @author: jin.wang03
     * @date: 2023/11/20 14:55
     * @since JDK 1.8
     */
    List<String> listBuyedCustNoByFundCodePrefixes(@Param("fundCodePrefixes") List<String> fundCodePrefixes);


    /**
     * @description: 查询所有购买[私募]产品的客户号
     * @return java.util.List<java.lang.String> 所有购买[私募]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 13:50
     * @since JDK 1.8
     */
    List<String> listBuyingCustNoForPrivate();


    /**
     * @description: 查询所有曾经购买[私募]产品的客户号
     * @return java.util.List<java.lang.String> 所有曾经购买[私募]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 14:15
     * @since JDK 1.8
     */
    List<String> listBuyedCustNoForPrivate();

}
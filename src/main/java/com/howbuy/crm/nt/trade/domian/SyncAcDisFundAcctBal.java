/**
 * Copyright (c) 2023, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.domian;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:  分销账户基金余额表
 * <AUTHOR>
 * @date 2023/11/20 10:16
 * @since JDK 1.8
 */

@Data
public class SyncAcDisFundAcctBal {
    /**
    * 分销交易账号
    */
    private String disFundTxAcctNo;

    /**
    * 协议号
    */
    private String protocalNo;

    /**
    * 基金代码
    */
    private String fundCode;

    /**
    * 基金账号
    */
    private String fundAcctNo;

    /**
    * 份额类型
A-前收费；B-后收费
    */
    private String shareClass;

    /**
    * 客户银行编号
    */
    private String custBankId;

    /**
    * 分销机构代码
    */
    private String disCode;

    /**
    * 交易账号
    */
    private String fundTxAcctNo;

    /**
    * 总余额
    */
    private BigDecimal balanceVol;

    /**
    * 可用余额
    */
    private BigDecimal availVol;

    /**
    * 冻结余额
    */
    private BigDecimal frznVol;

    /**
    * 司法冻结份额份额
    */
    private BigDecimal justFrznVol;

    /**
    * TA代码
    */
    private String taCode;

    /**
    * 登记日期
    */
    private String regDt;

    /**
    * 最近修改日期
    */
    private String udDt;

    /**
    * 最近申购日期
    */
    private String lastSubsDt;

    /**
    * 记录创建日期
    */
    private String creDt;

    /**
    * 记录修改日期
    */
    private String modDt;

    /**
    * 同步时间
    */
    private Date syncDate;

}
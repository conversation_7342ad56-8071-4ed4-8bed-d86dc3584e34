/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.dao;

import com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 分销账户基金余额 dao层接口类
 * <AUTHOR>
 * @date 2023/11/20 11:22
 * @since JDK 1.8
 */
@Mapper
public interface SyncAcDisFundAcctBalMapper {
    int deleteByPrimaryKey(@Param("disFundTxAcctNo") String disFundTxAcctNo, @Param("protocalNo") String protocalNo, @Param("fundCode") String fundCode, @Param("fundAcctNo") String fundAcctNo, @Param("shareClass") String shareClass, @Param("custBankId") String custBankId);

    int insert(SyncAcDisFundAcctBal record);

    int insertSelective(SyncAcDisFundAcctBal record);

    SyncAcDisFundAcctBal selectByPrimaryKey(@Param("disFundTxAcctNo") String disFundTxAcctNo, @Param("protocalNo") String protocalNo, @Param("fundCode") String fundCode, @Param("fundAcctNo") String fundAcctNo, @Param("shareClass") String shareClass, @Param("custBankId") String custBankId);

    int updateByPrimaryKeySelective(SyncAcDisFundAcctBal record);

    int updateByPrimaryKey(SyncAcDisFundAcctBal record);

    /**
     * @description: 查询所有购买[公募]产品的客户号
     * @return java.util.List<java.lang.String> 所有购买[公募]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 13:01
     * @since JDK 1.8
     */
    List<String> listBuyingCustNoForPublicFund();



    /**
     * @description: 查询所有曾经购买[公募]产品的客户号
     * @return java.util.List<java.lang.String> 所有曾经购买[公募]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 13:55
     * @since JDK 1.8
     */
    List<String> listBuyedCustNoForPublicFund();

    /**
     * @description: 查询所有购买[公募专户]产品的客户号
     * @return java.util.List<java.lang.String> 所有购买[公募专户]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 19:18
     * @since JDK 1.8
     */
    List<String> listBuyingCustNoForPublicFundSpecial();

    /**
     * @description: 查询所有曾经购买[公募专户]产品的客户号
     * @return java.util.List<java.lang.String> 所有曾经购买[公募专户]产品的客户号
     * @author: jin.wang03
     * @date: 2023/11/20 19:21
     * @since JDK 1.8
     */
    List<String> listBuyedCustNoForPublicFundSpecial();

}
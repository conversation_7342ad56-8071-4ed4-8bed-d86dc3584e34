/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.domian;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:  客户持有私募
 * <AUTHOR>
 * @date 2023/11/20 10:16
 * @since JDK 1.8
 */
@Data
public class CmCustPrivateFund {
    /**
    * 客户号
    */
    private String custno;

    /**
    * 基金代码
    */
    private String fundcode;

    /**
    * 总余额
    */
    private BigDecimal balancevol;

    /**
    * 累计投资金额
    */
    private BigDecimal totalackamt;

    /**
    * 交易日期
    */
    private String tradedt;

    /**
    * 基金净值
    */
    private BigDecimal nav;

    /**
    * 总成本
    */
    private BigDecimal totalcost;

    /**
    * 盈亏金额
    */
    private BigDecimal profit;

    /**
    * 盈亏比例
    */
    private BigDecimal profitper;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 关联人ID
    */
    private String conscustrid;

    /**
    * 管理人代码
    */
    private String mgrcode;

    /**
    * 已实现收益
    */
    private BigDecimal profited;

    /**
    * 已实现分红
    */
    private BigDecimal divideamt;

    /**
     * 当前份额已实现分红
     */
    private BigDecimal divamt;

    /**
    * 创建时间
    */
    private Date cretime;

    /**
    * 修改时间
    */
    private Date modtime;
}
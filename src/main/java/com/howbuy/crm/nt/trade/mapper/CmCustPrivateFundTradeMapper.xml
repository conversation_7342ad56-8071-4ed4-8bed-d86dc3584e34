<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.trade.dao.CmCustPrivateFundTradeMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.trade.domian.CmCustPrivateFundTrade">
    <!--@mbg.generated-->
    <!--@Table CM_CUSTPRIVATEFUNDTRADE-->
    <result column="APPSERIALNO" jdbcType="VARCHAR" property="appserialno" />
    <result column="TRADEDT" jdbcType="VARCHAR" property="tradedt" />
    <result column="CUSTNO" jdbcType="VARCHAR" property="custno" />
    <result column="FUNDCODE" jdbcType="VARCHAR" property="fundcode" />
    <result column="BUSICODE" jdbcType="VARCHAR" property="busicode" />
    <result column="APPAMT" jdbcType="DECIMAL" property="appamt" />
    <result column="APPVOL" jdbcType="DECIMAL" property="appvol" />
    <result column="ACKAMT" jdbcType="DECIMAL" property="ackamt" />
    <result column="ACKVOL" jdbcType="DECIMAL" property="ackvol" />
    <result column="DISCRATEOFCOMM" jdbcType="DECIMAL" property="discrateofcomm" />
    <result column="NAV" jdbcType="DECIMAL" property="nav" />
    <result column="DIVMODE" jdbcType="VARCHAR" property="divmode" />
    <result column="RECSTAT" jdbcType="VARCHAR" property="recstat" />
    <result column="CHECKFLAG" jdbcType="VARCHAR" property="checkflag" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="CHECKER" jdbcType="VARCHAR" property="checker" />
    <result column="CREDT" jdbcType="VARCHAR" property="credt" />
    <result column="MODDT" jdbcType="VARCHAR" property="moddt" />
    <result column="CONSCUSTRID" jdbcType="VARCHAR" property="conscustrid" />
    <result column="MGRCODE" jdbcType="VARCHAR" property="mgrcode" />
    <result column="TRADERATIO" jdbcType="DECIMAL" property="traderatio" />
    <result column="DISCAMT" jdbcType="DECIMAL" property="discamt" />
    <result column="DISCSUMMARY" jdbcType="VARCHAR" property="discsummary" />
    <result column="MGMAMT" jdbcType="DECIMAL" property="mgmamt" />
    <result column="VERIFYRATIO" jdbcType="DECIMAL" property="verifyratio" />
    <result column="ACCESSORY" jdbcType="VARCHAR" property="accessory" />
    <result column="ORIAPPSERIALNO" jdbcType="VARCHAR" property="oriappserialno" />
    <result column="CONSCODE" jdbcType="VARCHAR" property="conscode" />
    <result column="FEE" jdbcType="DECIMAL" property="fee" />
    <result column="APPLY_APPSERIALNO" jdbcType="VARCHAR" property="applyAppserialno" />
    <result column="DISCODE" jdbcType="VARCHAR" property="discode" />
    <result column="TRADACKFLOWID" jdbcType="DECIMAL" property="tradackflowid" />
    <result column="ISMESSAGE" jdbcType="VARCHAR" property="ismessage" />
    <result column="CRETIME" jdbcType="TIMESTAMP" property="cretime" />
    <result column="MODTIME" jdbcType="TIMESTAMP" property="modtime" />
    <result column="SNO" jdbcType="DECIMAL" property="sno" />
    <result column="ISHAIWAI" jdbcType="VARCHAR" property="ishaiwai" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    APPSERIALNO, TRADEDT, CUSTNO, FUNDCODE, BUSICODE, APPAMT, APPVOL, ACKAMT, ACKVOL, 
    DISCRATEOFCOMM, NAV, DIVMODE, RECSTAT, CHECKFLAG, CREATOR, MODIFIER, CHECKER, CREDT, 
    MODDT, CONSCUSTRID, MGRCODE, TRADERATIO, DISCAMT, DISCSUMMARY, MGMAMT, VERIFYRATIO, 
    ACCESSORY, ORIAPPSERIALNO, CONSCODE, FEE, APPLY_APPSERIALNO, DISCODE, TRADACKFLOWID, 
    ISMESSAGE, CRETIME, MODTIME, SNO, ISHAIWAI
  </sql>
  <insert id="insert" parameterType="com.howbuy.crm.nt.trade.domian.CmCustPrivateFundTrade">
    <!--@mbg.generated-->
    insert into CM_CUSTPRIVATEFUNDTRADE (APPSERIALNO, TRADEDT, CUSTNO, 
      FUNDCODE, BUSICODE, APPAMT, 
      APPVOL, ACKAMT, ACKVOL, 
      DISCRATEOFCOMM, NAV, DIVMODE, 
      RECSTAT, CHECKFLAG, CREATOR, 
      MODIFIER, CHECKER, CREDT, 
      MODDT, CONSCUSTRID, MGRCODE, 
      TRADERATIO, DISCAMT, DISCSUMMARY, 
      MGMAMT, VERIFYRATIO, ACCESSORY, 
      ORIAPPSERIALNO, CONSCODE, FEE, 
      APPLY_APPSERIALNO, DISCODE, TRADACKFLOWID, 
      ISMESSAGE, CRETIME, MODTIME, 
      SNO, ISHAIWAI)
    values (#{appserialno,jdbcType=VARCHAR}, #{tradedt,jdbcType=VARCHAR}, #{custno,jdbcType=VARCHAR}, 
      #{fundcode,jdbcType=VARCHAR}, #{busicode,jdbcType=VARCHAR}, #{appamt,jdbcType=DECIMAL}, 
      #{appvol,jdbcType=DECIMAL}, #{ackamt,jdbcType=DECIMAL}, #{ackvol,jdbcType=DECIMAL}, 
      #{discrateofcomm,jdbcType=DECIMAL}, #{nav,jdbcType=DECIMAL}, #{divmode,jdbcType=VARCHAR}, 
      #{recstat,jdbcType=VARCHAR}, #{checkflag,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{checker,jdbcType=VARCHAR}, #{credt,jdbcType=VARCHAR}, 
      #{moddt,jdbcType=VARCHAR}, #{conscustrid,jdbcType=VARCHAR}, #{mgrcode,jdbcType=VARCHAR}, 
      #{traderatio,jdbcType=DECIMAL}, #{discamt,jdbcType=DECIMAL}, #{discsummary,jdbcType=VARCHAR}, 
      #{mgmamt,jdbcType=DECIMAL}, #{verifyratio,jdbcType=DECIMAL}, #{accessory,jdbcType=VARCHAR}, 
      #{oriappserialno,jdbcType=VARCHAR}, #{conscode,jdbcType=VARCHAR}, #{fee,jdbcType=DECIMAL}, 
      #{applyAppserialno,jdbcType=VARCHAR}, #{discode,jdbcType=VARCHAR}, #{tradackflowid,jdbcType=DECIMAL}, 
      #{ismessage,jdbcType=VARCHAR}, #{cretime,jdbcType=TIMESTAMP}, #{modtime,jdbcType=TIMESTAMP}, 
      #{sno,jdbcType=DECIMAL}, #{ishaiwai,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.trade.domian.CmCustPrivateFundTrade">
    <!--@mbg.generated-->
    insert into CM_CUSTPRIVATEFUNDTRADE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="appserialno != null">
        APPSERIALNO,
      </if>
      <if test="tradedt != null">
        TRADEDT,
      </if>
      <if test="custno != null">
        CUSTNO,
      </if>
      <if test="fundcode != null">
        FUNDCODE,
      </if>
      <if test="busicode != null">
        BUSICODE,
      </if>
      <if test="appamt != null">
        APPAMT,
      </if>
      <if test="appvol != null">
        APPVOL,
      </if>
      <if test="ackamt != null">
        ACKAMT,
      </if>
      <if test="ackvol != null">
        ACKVOL,
      </if>
      <if test="discrateofcomm != null">
        DISCRATEOFCOMM,
      </if>
      <if test="nav != null">
        NAV,
      </if>
      <if test="divmode != null">
        DIVMODE,
      </if>
      <if test="recstat != null">
        RECSTAT,
      </if>
      <if test="checkflag != null">
        CHECKFLAG,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="checker != null">
        CHECKER,
      </if>
      <if test="credt != null">
        CREDT,
      </if>
      <if test="moddt != null">
        MODDT,
      </if>
      <if test="conscustrid != null">
        CONSCUSTRID,
      </if>
      <if test="mgrcode != null">
        MGRCODE,
      </if>
      <if test="traderatio != null">
        TRADERATIO,
      </if>
      <if test="discamt != null">
        DISCAMT,
      </if>
      <if test="discsummary != null">
        DISCSUMMARY,
      </if>
      <if test="mgmamt != null">
        MGMAMT,
      </if>
      <if test="verifyratio != null">
        VERIFYRATIO,
      </if>
      <if test="accessory != null">
        ACCESSORY,
      </if>
      <if test="oriappserialno != null">
        ORIAPPSERIALNO,
      </if>
      <if test="conscode != null">
        CONSCODE,
      </if>
      <if test="fee != null">
        FEE,
      </if>
      <if test="applyAppserialno != null">
        APPLY_APPSERIALNO,
      </if>
      <if test="discode != null">
        DISCODE,
      </if>
      <if test="tradackflowid != null">
        TRADACKFLOWID,
      </if>
      <if test="ismessage != null">
        ISMESSAGE,
      </if>
      <if test="cretime != null">
        CRETIME,
      </if>
      <if test="modtime != null">
        MODTIME,
      </if>
      <if test="sno != null">
        SNO,
      </if>
      <if test="ishaiwai != null">
        ISHAIWAI,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="appserialno != null">
        #{appserialno,jdbcType=VARCHAR},
      </if>
      <if test="tradedt != null">
        #{tradedt,jdbcType=VARCHAR},
      </if>
      <if test="custno != null">
        #{custno,jdbcType=VARCHAR},
      </if>
      <if test="fundcode != null">
        #{fundcode,jdbcType=VARCHAR},
      </if>
      <if test="busicode != null">
        #{busicode,jdbcType=VARCHAR},
      </if>
      <if test="appamt != null">
        #{appamt,jdbcType=DECIMAL},
      </if>
      <if test="appvol != null">
        #{appvol,jdbcType=DECIMAL},
      </if>
      <if test="ackamt != null">
        #{ackamt,jdbcType=DECIMAL},
      </if>
      <if test="ackvol != null">
        #{ackvol,jdbcType=DECIMAL},
      </if>
      <if test="discrateofcomm != null">
        #{discrateofcomm,jdbcType=DECIMAL},
      </if>
      <if test="nav != null">
        #{nav,jdbcType=DECIMAL},
      </if>
      <if test="divmode != null">
        #{divmode,jdbcType=VARCHAR},
      </if>
      <if test="recstat != null">
        #{recstat,jdbcType=VARCHAR},
      </if>
      <if test="checkflag != null">
        #{checkflag,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="checker != null">
        #{checker,jdbcType=VARCHAR},
      </if>
      <if test="credt != null">
        #{credt,jdbcType=VARCHAR},
      </if>
      <if test="moddt != null">
        #{moddt,jdbcType=VARCHAR},
      </if>
      <if test="conscustrid != null">
        #{conscustrid,jdbcType=VARCHAR},
      </if>
      <if test="mgrcode != null">
        #{mgrcode,jdbcType=VARCHAR},
      </if>
      <if test="traderatio != null">
        #{traderatio,jdbcType=DECIMAL},
      </if>
      <if test="discamt != null">
        #{discamt,jdbcType=DECIMAL},
      </if>
      <if test="discsummary != null">
        #{discsummary,jdbcType=VARCHAR},
      </if>
      <if test="mgmamt != null">
        #{mgmamt,jdbcType=DECIMAL},
      </if>
      <if test="verifyratio != null">
        #{verifyratio,jdbcType=DECIMAL},
      </if>
      <if test="accessory != null">
        #{accessory,jdbcType=VARCHAR},
      </if>
      <if test="oriappserialno != null">
        #{oriappserialno,jdbcType=VARCHAR},
      </if>
      <if test="conscode != null">
        #{conscode,jdbcType=VARCHAR},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="applyAppserialno != null">
        #{applyAppserialno,jdbcType=VARCHAR},
      </if>
      <if test="discode != null">
        #{discode,jdbcType=VARCHAR},
      </if>
      <if test="tradackflowid != null">
        #{tradackflowid,jdbcType=DECIMAL},
      </if>
      <if test="ismessage != null">
        #{ismessage,jdbcType=VARCHAR},
      </if>
      <if test="cretime != null">
        #{cretime,jdbcType=TIMESTAMP},
      </if>
      <if test="modtime != null">
        #{modtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sno != null">
        #{sno,jdbcType=DECIMAL},
      </if>
      <if test="ishaiwai != null">
        #{ishaiwai,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="listCustNoAndLatestTradeDt" resultType="com.howbuy.crm.nt.conscust.domain.ConscustInfo">
    SELECT T.CUSTNO CONSCUSTNO, MAX(T.TRADEDT) LATESTTRADEDT
    FROM V_TRADE_HIGH T
    GROUP BY T.CUSTNO
  </select>

</mapper>
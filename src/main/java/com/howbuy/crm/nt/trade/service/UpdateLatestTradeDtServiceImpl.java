/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.service;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.trade.dao.CmCustPrivateFundTradeMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 更新所有客户最近一次高净值交易日期
 * @date 2023/11/21 13:05
 * @since JDK 1.8
 */
@Slf4j
@Service("updateLatestTradeDtService")
public class UpdateLatestTradeDtServiceImpl implements UpdateLatestTradeDtService {

    @Autowired
    private ConscustMapper conscustMapper;

    @Autowired
    private CmCustPrivateFundTradeMapper cmCustPrivateFundTradeMapper;

    /**
     * NOTICE : 更新 CM_CONSCUST 表 LATESTTRADEDT
     * 实际首次交易日期、最近一次交易日期 ，已有统计 ： CM_HIGH_CUSTINFO 表
     *
     * 汇总说明：关于  最近一次交易日期，统计说明：
     * 已查到的有 ： CM_HIGH_CUSTFUND 、CM_HIGH_CUSTINFO 、CM_CONSCUST
     * 实际建议，目标是，只保留： CM_HIGH_CUSTINFO 表。
     */
    @Override
    public void syncProdData(String s) {
        long currentTimeMillis = System.currentTimeMillis();
        log.info("开始读取客户号和最近一次高端交易日期");
        List<ConscustInfo> conscustInfoList = cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt();

        if (CollectionUtils.isEmpty(conscustInfoList)) {
            log.info("没有需要更新的数据");
            return;
        }
        log.info("读取客户号和最近一次高端交易日期结束，总条数：{}，耗时：{}ms", conscustInfoList.size(), System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        List<List<ConscustInfo>> partitionConscustInfoList = Lists.partition(conscustInfoList, 200);
        for (List<ConscustInfo> partitionList : partitionConscustInfoList) {
            conscustMapper.batchUpdateLatestTradeDtByConscustNo(partitionList);
        }
        log.info("更新客户最近一次高端交易日期结束，耗时：{}ms", System.currentTimeMillis() - currentTimeMillis);
    }
}
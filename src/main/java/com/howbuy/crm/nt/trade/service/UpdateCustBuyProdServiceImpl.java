/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.service;

import com.google.common.collect.Lists;
import com.howbuy.crm.nt.base.model.ProductionIndexEnum;
import com.howbuy.crm.nt.base.model.ProductionPrefixEnum;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.trade.buss.UpdateCustBuyProdBuss;
import com.howbuy.crm.nt.trade.dao.CmCustPrivateFundMapper;
import com.howbuy.crm.nt.trade.dao.SyncAcDisFundAcctBalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 统计购买产品和曾购买产品
 * @date 2023/11/20 10:16
 * @since JDK 1.8
 */

@Slf4j
@Service("updateCustBuyProdService")
public class UpdateCustBuyProdServiceImpl implements UpdateCustBuyProdService {


    @Autowired
    private ConscustMapper conscustMapper;

    @Autowired
    private SyncAcDisFundAcctBalMapper syncAcDisFundAcctBalMapper;

    @Autowired
    private CmCustPrivateFundMapper cmCustPrivateFundMapper;

    @Autowired
    private UpdateCustBuyProdBuss updateCustBuyProdBuss;

    private static final int UPDATE_CONSCUST_SIZE = 100000;


    /**
     * 其他产品代码前缀
     */
    private static final List<String> otherFundCodePrefixes = new ArrayList<>();

    static {
        otherFundCodePrefixes.add("F");
        otherFundCodePrefixes.add("H");
        otherFundCodePrefixes.add("X");
        otherFundCodePrefixes.add("Z");
        otherFundCodePrefixes.add("0");
        otherFundCodePrefixes.add("1");
        otherFundCodePrefixes.add("2");
        otherFundCodePrefixes.add("3");
        otherFundCodePrefixes.add("4");
        otherFundCodePrefixes.add("5");
        otherFundCodePrefixes.add("6");
        otherFundCodePrefixes.add("7");
        otherFundCodePrefixes.add("8");
        otherFundCodePrefixes.add("9");
    }

    @Override
    public void syncProdData(String arg) {
        // 查询CM_CONSCUST表所有的记录数
        int count = conscustMapper.countConscust();
        // 查询CM_CONSCUST表中最小的custscustNo
        String minCustscustNo = conscustMapper.getMinCustNo();

        int updateC = count / UPDATE_CONSCUST_SIZE;
        int updateM = count % UPDATE_CONSCUST_SIZE;
        if (updateM > 0) {
            updateC++;
        }
        long zeroStartTimeMillis = System.currentTimeMillis();
        long currentTimeMillis;
        // 每次根据between startCustscustNo and endCustscustNo，更新CM_CONSCUST表中10万条记录
        for (int i = 1; i <= updateC; i++) {
            String startCustscustNo = minCustscustNo;

            currentTimeMillis = System.currentTimeMillis();
            String endCustscustNo = conscustMapper.getCustNoByAscIndex(Math.min(i * UPDATE_CONSCUST_SIZE, count));
            log.info("查询第{}页的custNo最大值，耗时：{}", i, System.currentTimeMillis() - currentTimeMillis);

            // 更新 BUYINGPROD = '0000000000000000', BUYEDPROD  = '0000000000000000'
            currentTimeMillis = System.currentTimeMillis();
            int num = conscustMapper.zeroingBuyProdBetweenCustNo(startCustscustNo, endCustscustNo);
            log.info("更新CM_CONSCUST表中第{}页的{}条记录，耗时：{}", i, num, System.currentTimeMillis() - currentTimeMillis);

            minCustscustNo = endCustscustNo;
        }
        log.info("BUYINGPROD和0000000000000000全部清0，耗时：{}", System.currentTimeMillis() - zeroStartTimeMillis);

        // 公募
        currentTimeMillis = System.currentTimeMillis();
        List<String> custNoList = syncAcDisFundAcctBalMapper.listBuyingCustNoForPublicFund();
        int num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.PUBLIC_FUND.getCode(), custNoList);
        log.info("更新公募购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = syncAcDisFundAcctBalMapper.listBuyedCustNoForPublicFund();
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.PUBLIC_FUND.getCode(), custNoList);
        log.info("更新公募曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 公募专户
        currentTimeMillis = System.currentTimeMillis();
        custNoList = syncAcDisFundAcctBalMapper.listBuyingCustNoForPublicFundSpecial();
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.PUBLIC_FUND_SPECIAL.getCode(), custNoList);
        log.info("更新公募专户购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = syncAcDisFundAcctBalMapper.listBuyedCustNoForPublicFundSpecial();
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.PUBLIC_FUND_SPECIAL.getCode(), custNoList);
        log.info("更新公募专户曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 私募
        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyingCustNoForPrivate();
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.PRIVATE_FUND.getCode(), custNoList);
        log.info("更新私募购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoForPrivate();
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.PRIVATE_FUND.getCode(), custNoList);
        log.info("更新私募曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 固定收益
        currentTimeMillis = System.currentTimeMillis();
        custNoList= cmCustPrivateFundMapper.listBuyingCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.FIXED_INCOME.getCode()));
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.FIXED_INCOME.getCode(), custNoList);
        log.info("更新固定收益购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.FIXED_INCOME.getCode()));
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.FIXED_INCOME.getCode(), custNoList);
        log.info("更新固定收益曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // TOT/FOF
        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyingCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.TOT_FOF.getCode()));
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.TOT_FOF.getCode(), custNoList);
        log.info("更新TOT/FOF购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.TOT_FOF.getCode()));
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.TOT_FOF.getCode(), custNoList);
        log.info("更新TOT/FOF曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // VC/PE
        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper
                .listBuyingCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.VC.getCode(), ProductionPrefixEnum.PE.getCode()));
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.VC_PE.getCode(), custNoList);
        log.info("更新VC/PE购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper
                .listBuyedCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.VC.getCode(), ProductionPrefixEnum.PE.getCode()));
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.VC_PE.getCode(), custNoList);
        log.info("更新VC/PE曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 券商集合
        currentTimeMillis = System.currentTimeMillis();
        custNoList= cmCustPrivateFundMapper.listBuyingCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.BROKER_COLLECTION.getCode()));
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.BROKER_COLLECTION.getCode(), custNoList);
        log.info("更新券商集合购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.BROKER_COLLECTION.getCode()));
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.BROKER_COLLECTION.getCode(), custNoList);
        log.info("更新券商集合曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 一对多
        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyingCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.ONE_TO_MANY.getCode()));
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.ONE_TO_MANY.getCode(), custNoList);
        log.info("更新一对多购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoByFundCodePrefixes(Lists.newArrayList(ProductionPrefixEnum.ONE_TO_MANY.getCode()));
        num = updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.ONE_TO_MANY.getCode(), custNoList);
        log.info("更新一对多曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        // 其他
        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyingCustNoByFundCodePrefixes(otherFundCodePrefixes);
        num = updateCustBuyProdBuss.updateBuyingProdByCustNoList(ProductionIndexEnum.OTHER.getCode(), custNoList);
        log.info("更新其他购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);

        currentTimeMillis = System.currentTimeMillis();
        custNoList = cmCustPrivateFundMapper.listBuyedCustNoByFundCodePrefixes(otherFundCodePrefixes);
        updateCustBuyProdBuss.updateBuyedProdByCustNoList(ProductionIndexEnum.OTHER.getCode(), custNoList);
        log.info("更新其他曾购买产品，更新条数:{}，耗时：{}", num, System.currentTimeMillis() - currentTimeMillis);
    }

}
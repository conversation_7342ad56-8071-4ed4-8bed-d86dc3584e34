<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.trade.dao.SyncAcDisFundAcctBalMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal">
    <!--@mbg.generated-->
    <!--@Table SYNC_AC_DIS_FUND_ACCT_BAL-->
    <id column="DIS_FUND_TX_ACCT_NO" jdbcType="VARCHAR" property="disFundTxAcctNo" />
    <id column="PROTOCAL_NO" jdbcType="VARCHAR" property="protocalNo" />
    <id column="FUND_CODE" jdbcType="VARCHAR" property="fundCode" />
    <id column="FUND_ACCT_NO" jdbcType="VARCHAR" property="fundAcctNo" />
    <id column="SHARE_CLASS" jdbcType="VARCHAR" property="shareClass" />
    <id column="CUST_BANK_ID" jdbcType="VARCHAR" property="custBankId" />
    <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
    <result column="FUND_TX_ACCT_NO" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="BALANCE_VOL" jdbcType="DECIMAL" property="balanceVol" />
    <result column="AVAIL_VOL" jdbcType="DECIMAL" property="availVol" />
    <result column="FRZN_VOL" jdbcType="DECIMAL" property="frznVol" />
    <result column="JUST_FRZN_VOL" jdbcType="DECIMAL" property="justFrznVol" />
    <result column="TA_CODE" jdbcType="VARCHAR" property="taCode" />
    <result column="REG_DT" jdbcType="VARCHAR" property="regDt" />
    <result column="UD_DT" jdbcType="VARCHAR" property="udDt" />
    <result column="LAST_SUBS_DT" jdbcType="VARCHAR" property="lastSubsDt" />
    <result column="CRE_DT" jdbcType="VARCHAR" property="creDt" />
    <result column="MOD_DT" jdbcType="VARCHAR" property="modDt" />
    <result column="SYNC_DATE" jdbcType="TIMESTAMP" property="syncDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    DIS_FUND_TX_ACCT_NO, PROTOCAL_NO, FUND_CODE, FUND_ACCT_NO, SHARE_CLASS, CUST_BANK_ID, 
    DIS_CODE, FUND_TX_ACCT_NO, BALANCE_VOL, AVAIL_VOL, FRZN_VOL, JUST_FRZN_VOL, TA_CODE, 
    REG_DT, UD_DT, LAST_SUBS_DT, CRE_DT, MOD_DT, SYNC_DATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="map" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from SYNC_AC_DIS_FUND_ACCT_BAL
    where DIS_FUND_TX_ACCT_NO = #{disFundTxAcctNo,jdbcType=VARCHAR}
      and PROTOCAL_NO = #{protocalNo,jdbcType=VARCHAR}
      and FUND_CODE = #{fundCode,jdbcType=VARCHAR}
      and FUND_ACCT_NO = #{fundAcctNo,jdbcType=VARCHAR}
      and SHARE_CLASS = #{shareClass,jdbcType=VARCHAR}
      and CUST_BANK_ID = #{custBankId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="map">
    <!--@mbg.generated-->
    delete from SYNC_AC_DIS_FUND_ACCT_BAL
    where DIS_FUND_TX_ACCT_NO = #{disFundTxAcctNo,jdbcType=VARCHAR}
      and PROTOCAL_NO = #{protocalNo,jdbcType=VARCHAR}
      and FUND_CODE = #{fundCode,jdbcType=VARCHAR}
      and FUND_ACCT_NO = #{fundAcctNo,jdbcType=VARCHAR}
      and SHARE_CLASS = #{shareClass,jdbcType=VARCHAR}
      and CUST_BANK_ID = #{custBankId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal">
    <!--@mbg.generated-->
    insert into SYNC_AC_DIS_FUND_ACCT_BAL (DIS_FUND_TX_ACCT_NO, PROTOCAL_NO, FUND_CODE, 
      FUND_ACCT_NO, SHARE_CLASS, CUST_BANK_ID, 
      DIS_CODE, FUND_TX_ACCT_NO, BALANCE_VOL, 
      AVAIL_VOL, FRZN_VOL, JUST_FRZN_VOL, 
      TA_CODE, REG_DT, UD_DT, 
      LAST_SUBS_DT, CRE_DT, MOD_DT, 
      SYNC_DATE)
    values (#{disFundTxAcctNo,jdbcType=VARCHAR}, #{protocalNo,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, 
      #{fundAcctNo,jdbcType=VARCHAR}, #{shareClass,jdbcType=VARCHAR}, #{custBankId,jdbcType=VARCHAR}, 
      #{disCode,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, #{balanceVol,jdbcType=DECIMAL}, 
      #{availVol,jdbcType=DECIMAL}, #{frznVol,jdbcType=DECIMAL}, #{justFrznVol,jdbcType=DECIMAL}, 
      #{taCode,jdbcType=VARCHAR}, #{regDt,jdbcType=VARCHAR}, #{udDt,jdbcType=VARCHAR}, 
      #{lastSubsDt,jdbcType=VARCHAR}, #{creDt,jdbcType=VARCHAR}, #{modDt,jdbcType=VARCHAR}, 
      #{syncDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal">
    <!--@mbg.generated-->
    insert into SYNC_AC_DIS_FUND_ACCT_BAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="disFundTxAcctNo != null">
        DIS_FUND_TX_ACCT_NO,
      </if>
      <if test="protocalNo != null">
        PROTOCAL_NO,
      </if>
      <if test="fundCode != null">
        FUND_CODE,
      </if>
      <if test="fundAcctNo != null">
        FUND_ACCT_NO,
      </if>
      <if test="shareClass != null">
        SHARE_CLASS,
      </if>
      <if test="custBankId != null">
        CUST_BANK_ID,
      </if>
      <if test="disCode != null">
        DIS_CODE,
      </if>
      <if test="fundTxAcctNo != null">
        FUND_TX_ACCT_NO,
      </if>
      <if test="balanceVol != null">
        BALANCE_VOL,
      </if>
      <if test="availVol != null">
        AVAIL_VOL,
      </if>
      <if test="frznVol != null">
        FRZN_VOL,
      </if>
      <if test="justFrznVol != null">
        JUST_FRZN_VOL,
      </if>
      <if test="taCode != null">
        TA_CODE,
      </if>
      <if test="regDt != null">
        REG_DT,
      </if>
      <if test="udDt != null">
        UD_DT,
      </if>
      <if test="lastSubsDt != null">
        LAST_SUBS_DT,
      </if>
      <if test="creDt != null">
        CRE_DT,
      </if>
      <if test="modDt != null">
        MOD_DT,
      </if>
      <if test="syncDate != null">
        SYNC_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="disFundTxAcctNo != null">
        #{disFundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="protocalNo != null">
        #{protocalNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAcctNo != null">
        #{fundAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="shareClass != null">
        #{shareClass,jdbcType=VARCHAR},
      </if>
      <if test="custBankId != null">
        #{custBankId,jdbcType=VARCHAR},
      </if>
      <if test="disCode != null">
        #{disCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="balanceVol != null">
        #{balanceVol,jdbcType=DECIMAL},
      </if>
      <if test="availVol != null">
        #{availVol,jdbcType=DECIMAL},
      </if>
      <if test="frznVol != null">
        #{frznVol,jdbcType=DECIMAL},
      </if>
      <if test="justFrznVol != null">
        #{justFrznVol,jdbcType=DECIMAL},
      </if>
      <if test="taCode != null">
        #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="regDt != null">
        #{regDt,jdbcType=VARCHAR},
      </if>
      <if test="udDt != null">
        #{udDt,jdbcType=VARCHAR},
      </if>
      <if test="lastSubsDt != null">
        #{lastSubsDt,jdbcType=VARCHAR},
      </if>
      <if test="creDt != null">
        #{creDt,jdbcType=VARCHAR},
      </if>
      <if test="modDt != null">
        #{modDt,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal">
    <!--@mbg.generated-->
    update SYNC_AC_DIS_FUND_ACCT_BAL
    <set>
      <if test="disCode != null">
        DIS_CODE = #{disCode,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        FUND_TX_ACCT_NO = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="balanceVol != null">
        BALANCE_VOL = #{balanceVol,jdbcType=DECIMAL},
      </if>
      <if test="availVol != null">
        AVAIL_VOL = #{availVol,jdbcType=DECIMAL},
      </if>
      <if test="frznVol != null">
        FRZN_VOL = #{frznVol,jdbcType=DECIMAL},
      </if>
      <if test="justFrznVol != null">
        JUST_FRZN_VOL = #{justFrznVol,jdbcType=DECIMAL},
      </if>
      <if test="taCode != null">
        TA_CODE = #{taCode,jdbcType=VARCHAR},
      </if>
      <if test="regDt != null">
        REG_DT = #{regDt,jdbcType=VARCHAR},
      </if>
      <if test="udDt != null">
        UD_DT = #{udDt,jdbcType=VARCHAR},
      </if>
      <if test="lastSubsDt != null">
        LAST_SUBS_DT = #{lastSubsDt,jdbcType=VARCHAR},
      </if>
      <if test="creDt != null">
        CRE_DT = #{creDt,jdbcType=VARCHAR},
      </if>
      <if test="modDt != null">
        MOD_DT = #{modDt,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where DIS_FUND_TX_ACCT_NO = #{disFundTxAcctNo,jdbcType=VARCHAR}
      and PROTOCAL_NO = #{protocalNo,jdbcType=VARCHAR}
      and FUND_CODE = #{fundCode,jdbcType=VARCHAR}
      and FUND_ACCT_NO = #{fundAcctNo,jdbcType=VARCHAR}
      and SHARE_CLASS = #{shareClass,jdbcType=VARCHAR}
      and CUST_BANK_ID = #{custBankId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.nt.trade.domian.SyncAcDisFundAcctBal">
    <!--@mbg.generated-->
    update SYNC_AC_DIS_FUND_ACCT_BAL
    set DIS_CODE = #{disCode,jdbcType=VARCHAR},
      FUND_TX_ACCT_NO = #{fundTxAcctNo,jdbcType=VARCHAR},
      BALANCE_VOL = #{balanceVol,jdbcType=DECIMAL},
      AVAIL_VOL = #{availVol,jdbcType=DECIMAL},
      FRZN_VOL = #{frznVol,jdbcType=DECIMAL},
      JUST_FRZN_VOL = #{justFrznVol,jdbcType=DECIMAL},
      TA_CODE = #{taCode,jdbcType=VARCHAR},
      REG_DT = #{regDt,jdbcType=VARCHAR},
      UD_DT = #{udDt,jdbcType=VARCHAR},
      LAST_SUBS_DT = #{lastSubsDt,jdbcType=VARCHAR},
      CRE_DT = #{creDt,jdbcType=VARCHAR},
      MOD_DT = #{modDt,jdbcType=VARCHAR},
      SYNC_DATE = #{syncDate,jdbcType=TIMESTAMP}
    where DIS_FUND_TX_ACCT_NO = #{disFundTxAcctNo,jdbcType=VARCHAR}
      and PROTOCAL_NO = #{protocalNo,jdbcType=VARCHAR}
      and FUND_CODE = #{fundCode,jdbcType=VARCHAR}
      and FUND_ACCT_NO = #{fundAcctNo,jdbcType=VARCHAR}
      and SHARE_CLASS = #{shareClass,jdbcType=VARCHAR}
      and CUST_BANK_ID = #{custBankId,jdbcType=VARCHAR}
  </update>

  <select id="listBuyingCustNoForPublicFund" resultType="java.lang.String">
    SELECT DISTINCT RR.CONSCUSTNO
    FROM (SELECT ACCT.CUST_NO
          FROM SYNC_AC_DIS_FUND_ACCT_BAL BAL
                 LEFT JOIN SYNC_BP_FUND_BASIC_INFO FUND
                           ON BAL.FUND_CODE = FUND.FUND_CODE
                 LEFT JOIN SYNC_AC_FUND_TX_ACCT ACCT
                           ON BAL.FUND_TX_ACCT_NO = ACCT.FUND_TX_ACCT_NO
          WHERE FUND.FUND_TYPE <![CDATA[<>]]> '7'
            AND BAL.BALANCE_VOL > 0
            AND ACCT.FUND_TX_ACCT_STAT = '0'
          UNION
          SELECT T2.CUST_NO
          FROM SYNC_AC_PIGGY_VOL T1
                 LEFT JOIN SYNC_AC_DIS_FUND_TX_ACCT T2
                           ON T1.FUND_TX_ACCT_NO = T2.FUND_TX_ACCT_NO
          WHERE T1.TOTAL_AMT > 0) A
           LEFT JOIN CM_CONSCUSTRPUBCUST RR
                     ON A.CUST_NO = RR.PUBCUSTNO
                       AND RR.ISRELATED = '0'
    WHERE CUST_NO IS NOT NULL
      and RR.CONSCUSTNO is not null
  </select>


  <select id="listBuyedCustNoForPublicFund" resultType="java.lang.String">
    SELECT DISTINCT RR.CONSCUSTNO
    FROM (SELECT ACCT.CUST_NO
          FROM SYNC_AC_DIS_FUND_ACCT_BAL BAL
                 LEFT JOIN SYNC_BP_FUND_BASIC_INFO FUND
                           ON BAL.FUND_CODE = FUND.FUND_CODE
                 LEFT JOIN SYNC_AC_FUND_TX_ACCT ACCT
                           ON BAL.FUND_TX_ACCT_NO = ACCT.FUND_TX_ACCT_NO
          WHERE FUND.FUND_TYPE <![CDATA[<>]]> '7'
            AND BAL.BALANCE_VOL <![CDATA[<=]]> 0
            AND ACCT.FUND_TX_ACCT_STAT = '0'
          UNION
          SELECT T2.CUST_NO
          FROM SYNC_AC_PIGGY_VOL T1
                 LEFT JOIN SYNC_AC_DIS_FUND_TX_ACCT T2
                           ON T1.FUND_TX_ACCT_NO = T2.FUND_TX_ACCT_NO
          WHERE T1.TOTAL_AMT <![CDATA[<=]]> 0) A
           LEFT JOIN CM_CONSCUSTRPUBCUST RR
                     ON A.CUST_NO = RR.PUBCUSTNO
                       AND RR.ISRELATED = '0'
    WHERE CUST_NO IS NOT NULL
      and RR.CONSCUSTNO is not null
  </select>

  <select id="listBuyingCustNoForPublicFundSpecial" resultType="java.lang.String">
    SELECT DISTINCT RR.CONSCUSTNO
    FROM SYNC_AC_DIS_FUND_ACCT_BAL BAL
           LEFT JOIN SYNC_BP_FUND_BASIC_INFO FUND
                     ON BAL.FUND_CODE = FUND.FUND_CODE
           LEFT JOIN SYNC_AC_FUND_TX_ACCT ACCT
                     ON BAL.FUND_TX_ACCT_NO = ACCT.FUND_TX_ACCT_NO
           LEFT JOIN CM_CONSCUSTRPUBCUST RR
                     ON ACCT.CUST_NO = RR.PUBCUSTNO
                       AND RR.ISRELATED = '0'
    WHERE FUND.FUND_TYPE = '7'
      AND BAL.BALANCE_VOL > 0
      AND ACCT.FUND_TX_ACCT_STAT = '0'
      and RR.CONSCUSTNO is not null
  </select>

  <select id="listBuyedCustNoForPublicFundSpecial" resultType="java.lang.String">
    SELECT DISTINCT RR.CONSCUSTNO
    FROM SYNC_AC_DIS_FUND_ACCT_BAL BAL
           LEFT JOIN SYNC_BP_FUND_BASIC_INFO FUND
                     ON BAL.FUND_CODE = FUND.FUND_CODE
           LEFT JOIN SYNC_AC_FUND_TX_ACCT ACCT
                     ON BAL.FUND_TX_ACCT_NO = ACCT.FUND_TX_ACCT_NO
           LEFT JOIN CM_CONSCUSTRPUBCUST RR
                     ON ACCT.CUST_NO = RR.PUBCUSTNO
                       AND RR.ISRELATED = '0'
    WHERE FUND.FUND_TYPE = '7'
      AND BAL.BALANCE_VOL <![CDATA[<=]]> 0
      AND ACCT.FUND_TX_ACCT_STAT = '0'
      and RR.CONSCUSTNO is not null
  </select>
</mapper>
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.dao;

import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.trade.domian.CmCustPrivateFundTrade;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @description: 客户私募交易明细dao层接口类
 * <AUTHOR>
 * @date 2023/11/21 13:27
 * @since JDK 1.8
 */
@Mapper
public interface CmCustPrivateFundTradeMapper {
    int insert(CmCustPrivateFundTrade record);

    int insertSelective(CmCustPrivateFundTrade record);


    /**
     * @description: 查询客户号和最近一次交易日期
     * @return java.util.List<com.howbuy.crm.nt.conscust.domain.ConscustInfo> 客户号和最近一次交易日期列表
     * @author: jin.wang03
     * @date: 2023/11/21 13:40
     * @since JDK 1.8
     */
    List<ConscustInfo> listCustNoAndLatestTradeDt();

}
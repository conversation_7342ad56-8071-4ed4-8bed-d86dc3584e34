/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.trade.domian;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:  客户私募交易明细
 * <AUTHOR>
 * @date 2023/11/21 13:35
 * @since JDK 1.8
 */
@Data
public class CmCustPrivateFundTrade {
    /**
    * 交易流水号
    */
    private String appserialno;

    /**
    * 交易日期
    */
    private String tradedt;

    /**
    * 客户号
    */
    private String custno;

    /**
    * 基金代码
    */
    private String fundcode;

    /**
    * 基金业务代码
    */
    private String busicode;

    /**
    * 申请金额
    */
    private BigDecimal appamt;

    /**
    * 申请份额
    */
    private BigDecimal appvol;

    /**
    * 确认金额
    */
    private BigDecimal ackamt;

    /**
    * 确认份额
    */
    private BigDecimal ackvol;

    /**
    * 销售佣金折扣率
    */
    private BigDecimal discrateofcomm;

    /**
    * 基金净值
    */
    private BigDecimal nav;

    /**
    * 分红方式
    */
    private String divmode;

    /**
    * 记录状态
    */
    private String recstat;

    /**
    * 复核标志
    */
    private String checkflag;

    /**
    * 创建人
    */
    private String creator;

    /**
    * 修改人
    */
    private String modifier;

    /**
    * 复核人
    */
    private String checker;

    /**
    * 记录创建日期
    */
    private String credt;

    /**
    * 记录修改日期
    */
    private String moddt;

    /**
    * 关联人ID
    */
    private String conscustrid;

    /**
    * 管理人代码
    */
    private String mgrcode;

    /**
    * 成交系数
    */
    private BigDecimal traderatio;

    /**
    * 手续费折扣金额
    */
    private BigDecimal discamt;

    /**
    * 折扣情况摘要
    */
    private String discsummary;

    /**
    * MGM结算金额
    */
    private BigDecimal mgmamt;

    /**
    * 核算增加系数
    */
    private BigDecimal verifyratio;

    /**
    * 附加信息
    */
    private String accessory;

    /**
    * 原交易流水号
    */
    private String oriappserialno;

    private String conscode;

    /**
    * 手续费
    */
    private BigDecimal fee;

    /**
    * 交易申请流水号
    */
    private String applyAppserialno;

    /**
    * 分销机构代码
    */
    private String discode;

    /**
    * 电子成单同步成交记录id
    */
    private BigDecimal tradackflowid;

    /**
    * 0:未发送；1：已发送
    */
    private String ismessage;

    /**
    * 创建时间
    */
    private Date cretime;

    /**
    * 修改时间
    */
    private Date modtime;

    /**
    * 固收产品批次标识主键
    */
    private BigDecimal sno;

    /**
    * 1:是；0：否
    */
    private String ishaiwai;

}
package com.howbuy.crm.nt.synchbone.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.bean.CustInfoBean;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.bean.DisCustInfoBean;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoFacade;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoRequest;
import com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoResponse;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.UpdateConscustInfoRequest;
import com.howbuy.crm.conscust.service.UpdateConscustInfoService;
import com.howbuy.crm.nt.conscust.dao.ConscustMapper;
import com.howbuy.crm.nt.conscust.domain.ConscustInfo;
import com.howbuy.crm.nt.conscust.domain.CustInfo;
import com.howbuy.crm.util.CrmNtConstant;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SyncHboneBuss {

	@Autowired
	private QueryAccHboneInfoFacade queryAccHboneInfoFacade;

	@Autowired
	private ConscustMapper conscustMapper;

	@Autowired
	private UpdateConscustInfoService updateConscustInfoService;

	@Autowired
	private QueryAllCustInfoAndDisCustInfoFacade queryAllCustInfoAndDisCustInfoFacade;

	@Autowired
	private QueryDisCustSensitiveInfoFacade queryDisCustSensitiveInfoFacade;

	/** 证件是否长期有效：0-否,1-是 */
	public static final String ID_ALWAYS_VALID_FLAG_FALSE = "0";
	public static final String ID_ALWAYS_VALID_FLAG_TRUE = "1";

	/**
	 * 同步一账通数据
	 */
	public void syncFundFhMsg(String arg) {
		log.info("同步一账通数据方法开始执行。。。");

		//查询符合条件的一账通和公募客户号
		List<CustInfo> custlist = conscustMapper.listCustInfoForSync();

		if (custlist != null && custlist.size() > 0) {
			for (CustInfo custinfo : custlist) {

				if (StringUtils.isNotBlank(custinfo.getHboneNo()) && StringUtils.isNotBlank(custinfo.getPubcustNo())) {
					synchbone(custinfo.getHboneNo(), custinfo.getPubcustNo());
				}
			}
		}
	}

	public void synchbone(String hboneno,String pubcustno) {
		try {
			//查询一账通数据
			QueryAccHboneInfoRequest queryHboneInfo = new QueryAccHboneInfoRequest();
			queryHboneInfo.setHboneNo(hboneno);

			log.info("queryAccHboneInfoFacade synchbone:" + JSON.toJSONString(queryHboneInfo));
			QueryAccHboneInfoResponse baseHboneInfo = queryAccHboneInfoFacade.execute(queryHboneInfo);
			log.info("queryAccHboneInfoFacade synchbone:" + JSON.toJSONString(baseHboneInfo));

			if (CrmNtConstant.RMISuccNew.equals(baseHboneInfo.getReturnCode())) {
				//一账通返回成功后，查询匹配的投顾客户数据
				ConscustInfo cunscust = null;
				Map<String, String> params = new HashMap<>();
				params.put("hboneno", hboneno);
				List<ConscustInfo> listCustInfo = conscustMapper.listConsCustInfoByHboneNo(params);
				if (listCustInfo != null && listCustInfo.size() > 0) {
					cunscust = listCustInfo.get(0);

					//判断基础参数不为空并且相等
					if (StringUtils.isNotBlank(baseHboneInfo.getCustName()) && StringUtils.isNotBlank(baseHboneInfo.getUserType()) && StringUtils.isNotBlank(baseHboneInfo.getIdType()) &&
							StringUtils.isNotBlank(baseHboneInfo.getIdNoDigest()) && StringUtils.isNotBlank(baseHboneInfo.getMobileDigest()) && StringUtils.isNotBlank(cunscust.getCustname()) &&
							StringUtils.isNotBlank(cunscust.getInvsttype()) && StringUtils.isNotBlank(cunscust.getIdtype()) && StringUtils.isNotBlank(cunscust.getIdnoDigest()) &&
							StringUtils.isNotBlank(cunscust.getMobileDigest()) && baseHboneInfo.getCustName().equals(cunscust.getCustname()) && baseHboneInfo.getUserType().equals(cunscust.getInvsttype())
							&& baseHboneInfo.getIdType().equals(cunscust.getIdtype()) && baseHboneInfo.getIdNoDigest().equals(cunscust.getIdnoDigest())
							&& baseHboneInfo.getMobileDigest().equals(cunscust.getMobileDigest())) {

						//查询对应公募客户号
						QueryAllCustInfoAndDisCustInfoRequest dubboReq = new QueryAllCustInfoAndDisCustInfoRequest();
						dubboReq.setTxAcctNo(pubcustno);
						log.info(dubboReq.getClass().getSimpleName() + "|" + JSON.toJSON(dubboReq));
						QueryAllCustInfoAndDisCustInfoResponse dubboRsp = queryAllCustInfoAndDisCustInfoFacade.execute(dubboReq);
						log.info(dubboRsp.getClass().getSimpleName() + "|" + JSON.toJSON(dubboRsp));

						if (CrmNtConstant.RMISuccNew.equals(dubboRsp.getReturnCode())) {
							// 获取客户基本信息

							ConscustInfoDomain conscustinfo = new ConscustInfoDomain();
							BeanUtils.copyProperties(cunscust, conscustinfo);

							if (dubboRsp.getCustInfo() != null) {
								CustInfoBean custInfoBean = dubboRsp.getCustInfo();

								Boolean check = false;
								// 客户性别
								if (StringUtils.isBlank(conscustinfo.getGender()) && StringUtils.isNotBlank(custInfoBean.getGender())) {
									conscustinfo.setGender(custInfoBean.getGender());
									check = true;
								}

								if (StringUtils.isBlank(conscustinfo.getBirthday()) && StringUtils.isNotBlank(custInfoBean.getBirthday())) {
									conscustinfo.setBirthday(custInfoBean.getBirthday());
									check = true;
								}

								// 证件是否长期有效：0-否,1-是
								if (StringUtils.isBlank(conscustinfo.getValidity()) && StringUtils.isNotBlank(custInfoBean.getIdAlwaysValidFlag())) {
									// 1-是
									if (ID_ALWAYS_VALID_FLAG_TRUE.equals(custInfoBean.getIdAlwaysValidFlag())) {
										conscustinfo.setValidity(custInfoBean.getIdAlwaysValidFlag());
									} else {
										conscustinfo.setValidity("2");
										conscustinfo.setValiditydt(custInfoBean.getIdValidityEnd());
									}
									check = true;
								}


								// 获取客户分销信息
								List<DisCustInfoBean> disList = dubboRsp.getDisCustInfoList();

								if (disList != null) {

									QueryDisCustSensitiveInfoRequest req = new QueryDisCustSensitiveInfoRequest();
									req.setHboneNo(hboneno);
									req.setDisCode(StaticVar.HB_DISCODE);
									QueryDisCustSensitiveInfoResponse rep = queryDisCustSensitiveInfoFacade.execute(req);

									for (DisCustInfoBean disCustInfoBean : disList) {
										// 转义客户职业
										if (StringUtils.isNotBlank(disCustInfoBean.getVocation()) && "好买".equals(disCustInfoBean.getDisName())) {
											if (StringUtils.isBlank(conscustinfo.getVocation())) {
												conscustinfo.setVocation(disCustInfoBean.getVocation());
												check = true;
											}
										}

										// 获取客户地址
										if (StringUtils.isNotBlank(rep.getDisCust().getAddr()) && "好买".equals(disCustInfoBean.getDisName())) {
											if (StringUtils.isBlank(conscustinfo.getAddr())) {
												conscustinfo.setAddr(rep.getDisCust().getAddr());
												check = true;
											}
										}

										// 获取客户地址
										if (StringUtils.isNotBlank(rep.getDisCust().getEmail()) && "好买".equals(disCustInfoBean.getDisName())) {
											if (StringUtils.isBlank(conscustinfo.getEmail())) {
												conscustinfo.setEmail(rep.getDisCust().getEmail());
												check = true;
											}
										}
									}
								}

								if (check) {
									//对比
									UpdateConscustInfoRequest updaterequest = new UpdateConscustInfoRequest();

									updaterequest.setConscustinfo(conscustinfo);
									updateConscustInfoService.updateConsCustInfo(updaterequest);
								}
							}
						}
					}
				}
			}
		}catch(Exception e) {
			log.error("查询接口querySingleService错误："+e.getMessage());
			log.error(e.getMessage(),e);
		}
		log.info("同步一账通数据方法执行结束。。。");
	}

}



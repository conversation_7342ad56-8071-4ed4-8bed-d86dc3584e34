package com.howbuy.crm.nt.synchbone.service;

import com.howbuy.crm.nt.synchbone.buss.SyncHboneBuss;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: ${user}
 * @Date: create on ${date} ${time}
 * @Description:
 */
@Service("syncHboneService")
@Slf4j
public class SyncHboneServiceImpl implements SyncHboneService {

    @Autowired
    private SyncHboneBuss syncHboneBuss;

    @Override
    public void syncHboneData(String arg) {
        syncHboneBuss.syncFundFhMsg(arg);
    }
}

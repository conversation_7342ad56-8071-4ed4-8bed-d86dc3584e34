package com.howbuy.crm.nt.joinclub.buss;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto;
import com.howbuy.crm.nt.joinclub.service.CmJoinClubService;
import com.howbuy.crm.util.CrmNtConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import com.howbuy.cc.center.feature.answer.request.QueryLastAnswerRequest;
import com.howbuy.cc.center.feature.answer.response.QueryLastAnswerResponse;
import com.howbuy.cc.center.feature.answer.service.QueryLastAnswerService;
import com.howbuy.cc.center.feature.question.domain.OptionInfoDomain;
import com.howbuy.cc.center.feature.question.domain.QuestionInfoDomain;
import com.howbuy.crm.nt.joinclub.dao.CmConscustsurveyanswerMapper;

@Component
@Transactional(rollbackFor = { Exception.class })
public class JoinClubByHboneBuss {
	private static final Logger log = LoggerFactory.getLogger(JoinClubByHboneBuss.class);

	@Autowired
	private KycInfoFacade kycInfoFacade;

	@Autowired
	private QueryLastAnswerService queryLastAnswerService;

	@Autowired
	private CmConscustsurveyanswerMapper cmConscustsurveyanswer;

	@Autowired
	private CmJoinClubService cmJoinClubService;

	/**
	 * 客户入会方法
	 */
	@SuppressWarnings("deprecation")
	@Transactional(rollbackFor = Exception.class)
	public void joinClubByHbone(String hboneNo) {
		try {
			log.info("-----入会调用开始-----hboneNo:" + hboneNo);
			KycInfoRequest userRequest = new KycInfoRequest();
			userRequest.setHboneNo(hboneNo);

			KycInfoResponse kycInfo = kycInfoFacade.execute(userRequest);
			log.info("-----入会调用开始-----kycInfoFacade:" + JSON.toJSON(kycInfo));
			QueryLastAnswerRequest lastAnswerRequest = new QueryLastAnswerRequest();
			lastAnswerRequest.setHboneNo(hboneNo);
			lastAnswerRequest.setShowExam("Y");
			QueryLastAnswerResponse lastAnswerResponse = queryLastAnswerService.queryLastAnswer(lastAnswerRequest);

			log.info("-----入会调用开始-----queryLastAnswerService:" + JSON.toJSON(lastAnswerResponse));
			// 风险测评等级
			String risklevel = kycInfo.getRiskToleranceLevel();
			// 投资金额
			String inverstamt = "";
			// 投资金额
			String inverstamtvalue = "";
			// 投资期限
			String inverstterm = "";
			// 投资目的
			String inverstaim = "";
			String examid = "";
			String ip = "";

			if (CrmNtConstant.RMISucc.equals(lastAnswerResponse.getReturnCode())) {
				if (lastAnswerResponse.getExamInfoDomain() != null && lastAnswerResponse.getAnswerHisInfoDomain() != null) {
					Map<String, OptionInfoDomain> answermap = lastAnswerResponse.getAnswerHisInfoDomain().getAnswers();
					List<QuestionInfoDomain> questionInfoList = lastAnswerResponse.getExamInfoDomain().getQuestions();

					ip = lastAnswerResponse.getAnswerHisInfoDomain().getIp();
					examid = lastAnswerResponse.getAnswerHisInfoDomain().getExamId();
					// 投资金额
					OptionInfoDomain answer7 = null;
					// 投资期限
					OptionInfoDomain answer12 = null;
					// 投资目的
					OptionInfoDomain answer13 = null;
					if (questionInfoList != null && questionInfoList.size() > 0) {
						for (int i = 0; i < questionInfoList.size(); i++) {
							QuestionInfoDomain infoDomain = questionInfoList.get(i);

							if (infoDomain.getSortNum() == 7) {
								answer7 = answermap.get(infoDomain.getQuestionId());
								log.info("-----入会调用开始----infoDomain:" + infoDomain.getQuestion() + "-answer7:" + (null==answer7?"空": answer7.getOptionId()));
								if (answer7 != null) {
									if (StringUtils.isNotBlank(answer7.getOptionChar())) {
										inverstamt = answer7.getOptionChar();
										inverstamtvalue = answer7.getOptionDesc();
									}
								}
							}

							if (infoDomain.getSortNum() == 12) {
								answer12 = answermap.get(infoDomain.getQuestionId());
								log.info("-----入会调用开始----infoDomain:" + infoDomain.getQuestion() + "-answer12:" + (null==answer12?"空": answer12.getOptionId()));
								if (answer12 != null && StringUtils.isNotBlank(answer12.getOptionChar())) {
									inverstterm = answer12.getOptionDesc();
								}
							}

							if (infoDomain.getSortNum() == 14) {
								answer13 = answermap.get(infoDomain.getQuestionId());
								log.info("-----入会调用开始----infoDomain:" + infoDomain.getQuestion() + "-answer13:" + (null==answer13?"空": answer13.getOptionId()));
								if (answer13 != null && StringUtils.isNotBlank(answer13.getOptionChar())) {
									inverstaim = answer13.getOptionDesc();
								}
							}
						}
					}
				}
			}

			if (StringUtils.isNotBlank(risklevel) && StringUtils.isNotBlank(inverstamt) && StringUtils.isNotBlank(hboneNo)
					&& StringUtils.isNotBlank(inverstaim) && StringUtils.isNotBlank(examid)
					&& StringUtils.isNotBlank(inverstterm)) {
				Map<String, String> resultMap = new HashMap<String, String>();
				resultMap.put("hboneno", hboneNo);
				resultMap.put("gpsrisklevel", risklevel);
				resultMap.put("gpsinvestlevel", inverstamt);
				resultMap.put("gpslevelvalue", inverstamtvalue);
				resultMap.put("inverstterm", inverstterm);
				resultMap.put("inverstaim", inverstaim);
				resultMap.put("examid", examid);
				resultMap.put("ip", ip);

				log.info("-----入会调用开始-----hboneno:" + hboneNo + "gpsrisklevel:" + risklevel + "gpsinvestlevel:" + inverstamt + "inverstterm:" + inverstterm + "inverstaim:" + inverstaim + "examid:" + examid);
				//cmConscustsurveyanswer.hboneJoinClub(resultMap);
				// PRO_HBONE_JOINCLUB 存储过程的改造
				CmConscustSurveyAnswerDto cmConscustSurveyAnswerDto = new CmConscustSurveyAnswerDto();
				cmConscustSurveyAnswerDto.setHboneNo(hboneNo);
				cmConscustSurveyAnswerDto.setGpsRiskLevel(risklevel);
				cmConscustSurveyAnswerDto.setGpsInvestLevel(inverstamt);
				cmConscustSurveyAnswerDto.setGpsLevelValue(inverstamtvalue);
				cmConscustSurveyAnswerDto.setInverstTerm(inverstterm);
				cmConscustSurveyAnswerDto.setInverstAim(inverstaim);
				cmConscustSurveyAnswerDto.setExamId(examid);
				cmConscustSurveyAnswerDto.setIp(ip);
				cmJoinClubService.proHboneJoinClub(cmConscustSurveyAnswerDto);
				log.info("-----入会调用结束-----PRO_HBONE_JOINCLUB");
			}
		} catch (Exception e) {
			log.info("-----入会异常-----hboneNo:" + hboneNo);
			e.printStackTrace();
		}

	}

	/*public String getPerAmtStr(String ans) {
		String result = "";
		if ("A".equals(ans)) {
			result = "300-500万元";
		}
		if ("B".equals(ans)) {
			result = "500-1000万元";
		}
		if ("C".equals(ans)) {
			result = "1000万元以上";
		}
		return result;
	}

	public String getPerTermStr(String ans) {
		String result = "";
		if ("A".equals(ans)) {
			result = "1年以下";
		}
		if ("B".equals(ans)) {
			result = "1-3年";
		}
		if ("C".equals(ans)) {
			result = "3-5年左右";
		}
		if ("D".equals(ans)) {
			result = "5年以上";
		}
		return result;
	}

	public String getPerAimStr(String ans) {
		String result = "";
		if ("A".equals(ans)) {
			result = "资产保值";
		}
		if ("B".equals(ans)) {
			result = "资产稳健增长";
		}
		if ("C".equals(ans)) {
			result = "资产迅速增长";
		}
		return result;
	}*/

}

package com.howbuy.crm.nt.joinclub.dao;

import java.util.List;
import java.util.Map;

import com.howbuy.crm.nt.hbjoincust.dto.CmConscustDto;
import com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.security.access.method.P;

@MapperScan
public interface CmConscustsurveyanswerMapper {
	
	/**
	 * 处理一账通预约申请入会
	 * @param param
	 * @return
	 */
	Map<String,String> hboneJoinClub(Map<String,String> param);

	/**
	 * @description:(查询一账通预约申请入会的数据)
	 * @return java.util.List<com.howbuy.crm.nt.hbjoincust.dto.CmConscustDto>
	 * @author: xufanchao
	 * @date: 2023/8/31 11:17
	 * @since JDK 1.8
	 */
	List<CmConscustSurveyAnswerDto> selectCmConsucstList(CmConscustSurveyAnswerDto cmConscustDto);

	/**
	 * @description:(查询出问卷总数)
	 * @param conscustNo
	 * @return java.lang.Integer
	 * @author: xufanchao
	 * @date: 2023/8/31 11:19
	 * @since JDK 1.8
	 */
	Integer selectCountByCustNo(@Param("conscustNo") String conscustNo);
	
	/**
	 * @description:(请在此添加描述)
	 * @param cmConscustSurveyAnswerDto	
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 13:56
	 * @since JDK 1.8
	 */
	int insertCustSuryeyRec(CmConscustSurveyAnswerDto cmConscustSurveyAnswerDto);

	/**
	 * @description:(查询appserialno)
	 * @param
	 * @return java.lang.String
	 * @author: xufanchao
	 * @date: 2023/8/31 13:57
	 * @since JDK 1.8
	 */
	String selectAppserialNo();

	/**
	 * @description:(更新客户gps测试等级)
	 * @param cmConscustSurveyAnswerDto
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 13:57
	 * @since JDK 1.8
	 */
	int updateGpsLevelByCustNo(CmConscustSurveyAnswerDto cmConscustSurveyAnswerDto);

	/**
	 * @description:(插入投顾客户历史表)
	 * @param appserialNo
	 * @param conscustNo
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 13:58
	 * @since JDK 1.8
	 */
	int insertIntoCmscustHis(@Param("appserialNo") String appserialNo, @Param("conscustNo") String conscustNo);

	/**
	 * @description:(插入投顾客户历史表)
	 * @param appserialNo
	 * @param conscustNo
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 13:59
	 * @since JDK 1.8
	 */
	int insertIntoCmScustHisCipher(@Param("appserialNo") String appserialNo, @Param("conscustNo") String conscustNo);

	/**
	 * @description:(查询记录表中存在流水问卷的数据)
	 * @param conscustNo
	 * @return java.util.List<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/8/31 14:01
	 * @since JDK 1.8
	 */
	List<String> selectSurveyList(@Param("conscustNo") String conscustNo);


	/**
	 * @description:(流水问卷记录迁入历史表)
	 * @param appserialNos
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 14:01
	 * @since JDK 1.8
	 */
	int insertIntoSurveyHis(@Param("appserialNos") List<String> appserialNos);
	
	/**
	 * @description:(清除对应数据，表中只保留一条申请的正确数据)
	 * @param appserialNos
	 * @return int
	 * @author: xufanchao
	 * @date: 2023/8/31 14:01
	 * @since JDK 1.8
	 */
	int deleteSurVeyRec(@Param("appserialNos") List<String> appserialNos);

}

package com.howbuy.crm.nt.joinclub.service;

import com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto;
import com.howbuy.crm.nt.joinclub.dao.CmConscustsurveyanswerMapper;
import com.howbuy.crm.nt.joinclub.dao.CmConscustsurveyrecMapper;
import com.howbuy.crm.nt.joinclub.dto.CmConscustsurveyrec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: shuai.zhang
 * @Date: 2022/9/8
 * @Description:问卷处理
 */
@Service("cmJoinClubService")
public class CmJoinClubServiceImpl implements CmJoinClubService {
    @Autowired
    private CmConscustsurveyrecMapper mapper;

    @Autowired
    private CmConscustsurveyanswerMapper cmConscustsurveyanswerMapper;
    @Override
    public void updateCmConscustsurveyrec(CmConscustsurveyrec dto) {
        mapper.updateCmConscustsurveyrec(dto);
    }


    /**
     * @api {DUBBO} com.howbuy.crm.nt.joinclub.service.CmJoinClubService.proHboneJoinClub(cmConscustDto)
     * @apiVersion 0.0.1
     * @apiGroup custinfo
     * @apiName proHboneJoinClub()
     * @apiDescription 根据一账通高端问卷入会申请
     * @apiParam (请求参数) {String} conscustNo
     * @apiParam (请求参数) {String} custName
     * @apiParam (请求参数) {String} hboneNo
     * @apiParam (请求参数) {String} inverstTerm
     * @apiParam (请求参数) {String} inverstAim
     * @apiParam (请求参数) {String} gpsInvestLevel
     * @apiParam (请求参数) {String} gpsLevelValue
     * @apiParam (请求参数) {String} gpsRiskLevel
     * @apiParam (请求参数) {String} examId
     * @apiParam (请求参数) {String} ip
     * @apiParamExample 请求参数示例
     * inverstTerm=z&conscustNo=pLu&gpsInvestLevel=Ryv&gpsRiskLevel=w3iF&ip=LwC2T3QNFE&examId=b2DSR&gpsLevelValue=6RuYLLNAG&custName=D0&hboneNo=R&inverstAim=2ZqqApv
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @Override
    public void proHboneJoinClub(CmConscustSurveyAnswerDto cmConscustDto) {
        try {
            // 1.查询满足条件的投顾客户信息的
            List<CmConscustSurveyAnswerDto> cmConscustDtos = cmConscustsurveyanswerMapper.selectCmConsucstList(cmConscustDto);
            // 1.1 查询当前客户流水问卷流水表信息
            cmConscustDtos.forEach(it -> {
                String conscustNo = it.getConscustNo();
                Integer count = cmConscustsurveyanswerMapper.selectCountByCustNo(conscustNo);
                cmConscustDto.setConscustNo(conscustNo);
                // 第一次填写问卷
                if (count == 0) {
                    // 新增流水问卷表
                    cmConscustsurveyanswerMapper.insertCustSuryeyRec(cmConscustDto);
                    // 更新客户gps测试等级
                    cmConscustsurveyanswerMapper.updateGpsLevelByCustNo(cmConscustDto);
                    // 插入投顾客户历史表
                    String appserialNo = cmConscustsurveyanswerMapper.selectAppserialNo();
                    cmConscustsurveyanswerMapper.insertIntoCmscustHis(appserialNo, conscustNo);
                    cmConscustsurveyanswerMapper.insertIntoCmScustHisCipher(appserialNo, conscustNo);
                } else if (count > 0) {
                    // 存在流水问卷表
                    List<String> appersialNos = cmConscustsurveyanswerMapper.selectSurveyList(conscustNo);
                    // 流水问卷记录迁入历史表
                    cmConscustsurveyanswerMapper.insertIntoSurveyHis(appersialNos);
                    // 清除对应数据
                    cmConscustsurveyanswerMapper.deleteSurVeyRec(appersialNos);
                    // 新增流水问卷表
                    cmConscustsurveyanswerMapper.insertCustSuryeyRec(cmConscustDto);
                    // 更新客户gps测试等级
                    cmConscustsurveyanswerMapper.updateGpsLevelByCustNo(cmConscustDto);
                    // 插入投顾客户历史表
                    String appserialNo = cmConscustsurveyanswerMapper.selectAppserialNo();
                    cmConscustsurveyanswerMapper.insertIntoCmscustHis(appserialNo, conscustNo);
                    cmConscustsurveyanswerMapper.insertIntoCmScustHisCipher(appserialNo, conscustNo);
                }
            });
        } catch (Exception e) {
            System.out.println(e);
        }
    }

}

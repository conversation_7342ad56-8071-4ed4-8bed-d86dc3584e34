<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.howbuy.crm.nt.joinclub.dao.CmConscustsurveyanswerMapper">
    <select id="hboneJoinClub" statementType="CALLABLE" parameterType="Map" resultType="Map" useCache="false">
        <![CDATA[
        {call PRO_HBONE_JOINCLUB(
                #{hboneno,mode=IN,jdbcType=VARCHAR},
                #{inverstterm,mode=IN,jdbcType=VARCHAR},
                #{inverstaim,mode=IN,jdbcType=VARCHAR},
                #{gpsinvestlevel,mode=IN,jdbcType=VARCHAR},
                #{gpslevelvalue,mode=IN,jdbcType=VARCHAR},
                #{gpsrisklevel,mode=IN,jdbcType=VARCHAR},
                #{examid,mode=IN,jdbcType=VARCHAR},
                #{ip,mode=IN,jdbcType=VARCHAR},
                #{PO_RETCODE,mode=OUT,jdbcType=VARCHAR},
                #{PO_RETMSG,mode=OUT,jdbcType=VARCHAR}
            )}
        ]]>
    </select>

    <select id="selectCmConsucstList" parameterType="String"
            resultType="com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto">
        SELECT T.CONSCUSTNO, T.CUSTNAME
        FROM CM_CONSCUST T,
             CM_CUSTCONSTANT A1
        WHERE T.CONSCUSTSTATUS = '0'
          AND (T.SPECIALFLAG IS NULL OR T.SPECIALFLAG = '0')
          AND T.CONSCUSTNO = A1.CUSTNO
          AND A1.CONSCODE != 'abnormal'
          AND T.HBONE_NO = #{hboneNo}
          AND (T.GPSRISKLEVEL != #{gpsRiskLevel} OR
               T.GPSINVESTLEVEL != #{gpsInvestLevel} OR
               T.GPSRISKLEVEL IS NULL OR T.GPSINVESTLEVEL IS NULL OR
               T.ISJOINCLUB IS NULL)
    </select>

    <select id="selectCountByCustNo" resultType="integer" parameterType="string">
        SELECT COUNT(*)
        FROM CM_CONSCUSTSURVEYREC A
        WHERE A.CONSCUSTNO = #{conscustNo}
          AND A.SURVEYID = '012'
    </select>

    <insert id="insertCustSuryeyRec" parameterType="com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto">
        INSERT INTO CM_CONSCUSTSURVEYREC
        (APPSERIALNO,
         TRADEDT,
         APPCODE,
         TXCODE,
         TXAPPFLAG,
         TXCHKFLAG,
         TRADECHAN,
         REGIONCODE,
         OUTLETCODE,
         APPDT,
         APPTM,
         CONSCUSTNO,
         SURVEYID,
         SETID,
         TOTALWP,
         PRIRISKLEVEL,
         SUGGESTIONID,
         MEMO,
         RETCODE,
         RETMSG,
         CREATOR,
         CHECKER,
         STIMESTAMP,
         GPSINVESTLEVEL,
         GPSRISKLEVEL,
         CHECKDT,
         CHECKTM,
         CUSTNAME,
         IP,
         INVERSTAMT,
         INVERSTTERM,
         INVERSTAIM)
        VALUES (TO_CHAR(SEQ_PCUSTREC.NEXTVAL),
                TO_CHAR(SYSDATE, 'yyyymmdd'),
                '40',
                '423087',
                '0',
                '1',
                NULL,
                NULL,
                NULL,
                TO_CHAR(SYSDATE, 'yyyymmdd'),
                TO_CHAR(SYSDATE, 'hh24miss'),
                #{conscustNo,jdbcType=VARCHAR},
                '012',
                #{examId,jdbcType=VARCHAR},
                0,
                ' ',
                ' ',
                ' ',
                ' ',
                ' ',
                'WEB',
                'WEB',
                SYSDATE,
                #{gpsInvestLevel,jdbcType=VARCHAR},
                #{gpsRiskLevel,jdbcType=VARCHAR},
                TO_CHAR(SYSDATE, 'yyyymmdd'),
                TO_CHAR(SYSDATE, 'hh24miss'),
                #{custName,jdbcType=VARCHAR},
                #{ip,jdbcType=VARCHAR},
                #{gpsLevelValue,jdbcType=VARCHAR},
                #{inverstTerm,jdbcType=VARCHAR},
                #{inverstAim,jdbcType=VARCHAR})
    </insert>

    <update id="updateGpsLevelByCustNo" parameterType="com.howbuy.crm.nt.hbjoincust.dto.CmConscustSurveyAnswerDto">
        UPDATE CM_CONSCUST B
        SET B.GPSINVESTLEVEL = #{gpsInvestLevel,jdbcType=VARCHAR},
            B.GPSRISKLEVEL   = #{gpsRiskLevel,jdbcType=VARCHAR},
            B.ISJOINCLUB     = '1',
            B.JOINCLUBDT     = TO_CHAR(SYSDATE, 'yyyyMMdd')
        WHERE B.CONSCUSTNO = #{conscustNo,jdbcType=VARCHAR}
          AND B.JOINCLUBDT IS NULL
    </update>

    <select id="selectAppserialNo" resultType="String">
        SELECT SEQ_CUSTREC.NEXTVAL
        FROM DUAL
    </select>

    <insert id="insertIntoCmscustHis" parameterType="String">
        INSERT INTO CM_CONSCUSTHIS
        (APPSERIALNO,
         TXCODE,
         TXCHKFLAG,
         CONSCUSTNO,
         CONSCUSTLVL,
         CONSCUSTGRADE,
         CONSCUSTSTATUS,
         IDTYPE,
         CUSTNAME,
         PROVCODE,
         CITYCODE,
         EDULEVEL,
         VOCATION,
         INCLEVEL,
         BIRTHDAY,
         GENDER,
         MARRIED,
         PINCOME,
         FINCOME,
         DECISIONFLAG,
         INTERESTS,
         FAMILYCONDITION,
         CONTACTTIME,
         SENDINFOFLAG,
         RECVTELFLAG,
         RECVEMAILFLAG,
         RECVMSGFLAG,
         COMPANY,
         RISKLEVEL,
         SELFRISKLEVEL,
         POSTCODE,
         FAX,
         OFFICETELNO,
         SOURCE,
         KNOWCHAN,
         OTHERCHAN,
         OTHERINVEST,
         SALON,
         BEFOREINVEST,
         IM2,
         SELFDEFFLAG,
         VISITFQCY,
         SALEDIRECTION,
         DEVDIRECTION,
         SUBSOURCE,
         SUBSOURCETYPE,
         REMARK,
         POSTCODE2,
         KNOWHOWBUY,
         SUBKNOW,
         SUBKNOWTYPE,
         BUYINGPROD,
         BUYEDPROD,
         SPECIALFLAG,
         DLVYMODE,
         SPECIALREASON,
         CREATOR,
         CHECKER,
         STIMESTAMP,
         PRIRISKLEVEL,
         LINKMAN,
         LINKPOSTCODE,
         CAPACITY,
         GPSINVESTLEVEL,
         GPSRISKLEVEL,
         ISBOSS,
         FINANCENEED,
         ISJOINCLUB,
         EXPLANATION,
         ISWRITEBOOK,
         ISRISKTIP,
         CUSTSOURCEREMARK,
         HBONE_NO,
         NEWSOURCENO,
         NEWSOURCENO2,
         HOPETRADETYPE,
         VALIDITY,
         VALIDITYDT,
         NATURE,
         APTITUDE,
         SCOPEBUSINESS,
         PMARKETAMT,
         IDNO_DIGEST,
         IDNO_MASK,
         ADDR_DIGEST,
         ADDR_MASK,
         MOBILE_DIGEST,
         MOBILE_MASK,
         TELNO_DIGEST,
         TELNO_MASK,
         EMAIL_DIGEST,
         EMAIL_MASK,
         ADDR2_DIGEST,
         ADDR2_MASK,
         MOBILE2_DIGEST,
         MOBILE2_MASK,
         EMAIL2_DIGEST,
         EMAIL2_MASK,
         LINKADDR_DIGEST,
         LINKADDR_MASK,
         LINKMOBILE_DIGEST,
         LINKMOBILE_MASK,
         LINKTEL_DIGEST,
         LINKTEL_MASK,
         LINKEMAIL_DIGEST,
         LINKEMAIL_MASK)
        SELECT #{appserialNo},
               '421011',
               NULL,
               CONSCUSTNO,
               CONSCUSTLVL,
               CONSCUSTGRADE,
               CONSCUSTSTATUS,
               IDTYPE,
               CUSTNAME,
               PROVCODE,
               CITYCODE,
               EDULEVEL,
               VOCATION,
               INCLEVEL,
               BIRTHDAY,
               GENDER,
               MARRIED,
               PINCOME,
               FINCOME,
               DECISIONFLAG,
               INTERESTS,
               FAMILYCONDITION,
               CONTACTTIME,
               SENDINFOFLAG,
               RECVTELFLAG,
               RECVEMAILFLAG,
               RECVMSGFLAG,
               COMPANY,
               RISKLEVEL,
               SELFRISKLEVEL,
               POSTCODE,
               FAX,
               OFFICETELNO,
               SOURCE,
               KNOWCHAN,
               OTHERCHAN,
               OTHERINVEST,
               SALON,
               BEFOREINVEST,
               NULL,
               SELFDEFFLAG,
               VISITFQCY,
               SALEDIRECTION,
               DEVDIRECTION,
               SUBSOURCE,
               SUBSOURCETYPE,
               REMARK,
               POSTCODE2,
               KNOWHOWBUY,
               SUBKNOW,
               SUBKNOWTYPE,
               BUYINGPROD,
               BUYEDPROD,
               SPECIALFLAG,
               DLVYMODE,
               NULL,
               'SYSJIONCLUB',
               NULL,
               SYSDATE,
               PRIRISKLEVEL,
               LINKMAN,
               LINKPOSTCODE,
               CAPACITY,
               GPSINVESTLEVEL,
               GPSRISKLEVEL,
               ISBOSS,
               FINANCENEED,
               ISJOINCLUB,
               '一账通入会申请',
               ISWRITEBOOK,
               ISRISKTIP,
               CUSTSOURCEREMARK,
               HBONE_NO,
               NEWSOURCENO,
               NEWSOURCENO2,
               HOPETRADETYPE,
               VALIDITY,
               VALIDITYDT,
               NATURE,
               APTITUDE,
               SCOPEBUSINESS,
               PMARKETAMT,
               IDNO_DIGEST,
               IDNO_MASK,
               ADDR_DIGEST,
               ADDR_MASK,
               MOBILE_DIGEST,
               MOBILE_MASK,
               TELNO_DIGEST,
               TELNO_MASK,
               EMAIL_DIGEST,
               EMAIL_MASK,
               ADDR2_DIGEST,
               ADDR2_MASK,
               MOBILE2_DIGEST,
               MOBILE2_MASK,
               EMAIL2_DIGEST,
               EMAIL2_MASK,
               LINKADDR_DIGEST,
               LINKADDR_MASK,
               LINKMOBILE_DIGEST,
               LINKMOBILE_MASK,
               LINKTEL_DIGEST,
               LINKTEL_MASK,
               LINKEMAIL_DIGEST,
               LINKEMAIL_MASK
        FROM CM_CONSCUST T
        WHERE T.CONSCUSTNO = #{conscustNo,jdbcType=VARCHAR}
    </insert>

    <insert id="insertIntoCmScustHisCipher" parameterType="String">
        INSERT INTO CM_CONSCUSTHIS_CIPHER
        (APPSERIALNO,
         CONSCUSTNO,
         IDNO_CIPHER,
         ADDR_CIPHER,
         MOBILE_CIPHER,
         TELNO_CIPHER,
         EMAIL_CIPHER,
         ADDR2_CIPHER,
         MOBILE2_CIPHER,
         EMAIL2_CIPHER,
         LINKADDR_CIPHER,
         LINKMOBILE_CIPHER,
         LINKTEL_CIPHER,
         LINKEMAIL_CIPHER)
        SELECT #{appserialNo},
               CONSCUSTNO,
               IDNO_CIPHER,
               ADDR_CIPHER,
               MOBILE_CIPHER,
               TELNO_CIPHER,
               EMAIL_CIPHER,
               ADDR2_CIPHER,
               MOBILE2_CIPHER,
               EMAIL2_CIPHER,
               LINKADDR_CIPHER,
               LINKMOBILE_CIPHER,
               LINKTEL_CIPHER,
               LINKEMAIL_CIPHER
        FROM CM_CONSCUST_CIPHER BC
        WHERE BC.CONSCUSTNO = #{conscustNo}
    </insert>

    <select id="selectSurveyList" parameterType="String" resultType="java.lang.String">
        SELECT APPSERIALNO
        FROM CM_CONSCUSTSURVEYREC A
        WHERE A.CONSCUSTNO = #{conscustNo}
          AND A.SURVEYID = '012'
    </select>

    <insert id="insertIntoSurveyHis" parameterType="List">
        INSERT INTO CM_CONSCUSTSURVEYRECHIS
        (APPSERIALNO,
         TRADEDT,
         APPCODE,
         TXCODE,
         TXAPPFLAG,
         TXCHKFLAG,
         TRADECHAN,
         REGIONCODE,
         OUTLETCODE,
         APPDT,
         APPTM,
         CONSCUSTNO,
         SURVEYID,
         SETID,
         TOTALWP,
         PRIRISKLEVEL,
         SUGGESTIONID,
         MEMO,
         RETCODE,
         RETMSG,
         CREATOR,
         CHECKER,
         STIMESTAMP,
         GPSINVESTLEVEL,
         GPSRISKLEVEL,
         CHECKDT,
         CHECKTM,
         CUSTNAME,
         IP,
         INVERSTAMT,
         INVERSTTERM,
         INVERSTAIM)
        SELECT APPSERIALNO,
               TRADEDT,
               APPCODE,
               TXCODE,
               TXAPPFLAG,
               TXCHKFLAG,
               TRADECHAN,
               REGIONCODE,
               OUTLETCODE,
               APPDT,
               APPTM,
               CONSCUSTNO,
               SURVEYID,
               SETID,
               TOTALWP,
               PRIRISKLEVEL,
               SUGGESTIONID,
               MEMO,
               RETCODE,
               RETMSG,
               CREATOR,
               CHECKER,
               STIMESTAMP,
               GPSINVESTLEVEL,
               GPSRISKLEVEL,
               CHECKDT,
               CHECKTM,
               CUSTNAME,
               IP,
               INVERSTAMT,
               INVERSTTERM,
               INVERSTAIM
        FROM CM_CONSCUSTSURVEYREC A
        WHERE A.APPSERIALNO in
        <foreach collection="appserialNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>

    <delete id="deleteSurVeyRec" parameterType="String">
        DELETE
        FROM CM_CONSCUSTSURVEYREC A
        WHERE A.APPSERIALNO
            in
        <foreach collection="appserialNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

</mapper>
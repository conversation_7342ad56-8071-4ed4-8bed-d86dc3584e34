<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.howbuy.crm.nt.joinclub.dao.CmConscustsurveyrecMapper">
    <update id="updateCmConscustsurveyrec" parameterType="com.howbuy.crm.nt.joinclub.dto.CmConscustsurveyrec" >
        UPDATE CM_CONSCUSTSURVEYREC
        <set>
            <if test="appserialno != null"> appserialno = #{appserialno}, </if>
            <if test="tradedt != null"> tradedt = #{tradedt}, </if>
            <if test="appcode != null"> appcode = #{appcode}, </if>
            <if test="txcode != null"> txcode = #{txcode}, </if>
            <if test="txappflag != null"> txappflag = #{txappflag}, </if>
            <if test="txchkflag != null"> txchkflag = #{txchkflag}, </if>
            <if test="tradechan != null"> tradechan = #{tradechan}, </if>
            <if test="regioncode != null"> regioncode = #{regioncode}, </if>
            <if test="outletcode != null"> outletcode = #{outletcode}, </if>
            <if test="appdt != null"> appdt = #{appdt}, </if>
            <if test="apptm != null"> apptm = #{apptm}, </if>
            <if test="conscustno != null"> conscustno = #{conscustno}, </if>
            <if test="surveyid != null"> surveyid = #{surveyid}, </if>
            <if test="setid != null"> setid = #{setid}, </if>
            <if test="totalwp != null"> totalwp = #{totalwp}, </if>
            <if test="pririsklevel != null"> pririsklevel = #{pririsklevel}, </if>
            <if test="suggestionid != null"> suggestionid = #{suggestionid}, </if>
            <if test="memo != null"> memo = #{memo}, </if>
            <if test="retcode != null"> retcode = #{retcode}, </if>
            <if test="retmsg != null"> retmsg = #{retmsg}, </if>
            <if test="creator != null"> creator = #{creator}, </if>
            <if test="checker != null"> checker = #{checker}, </if>
            <if test="stimestamp != null"> stimestamp = #{stimestamp}, </if>
            <if test="gpsinvestlevel != null"> gpsinvestlevel = #{gpsinvestlevel}, </if>
            <if test="gpsrisklevel != null"> gpsrisklevel = #{gpsrisklevel}, </if>
            <if test="checkdt != null"> checkdt = #{checkdt}, </if>
            <if test="checktm != null"> checktm = #{checktm}, </if>
            <if test="custname != null"> custname = #{custname}, </if>
            <if test="inverstamt != null"> inverstamt = #{inverstamt}, </if>
            <if test="inverstterm != null"> inverstterm = #{inverstterm}, </if>
            <if test="singdate != null"> singdate = #{singdate}, </if>
            <if test="inverstaim != null"> inverstaim = #{inverstaim}, </if>
            <if test="upcheckadvice == '1'.toString() and checkadvice == null"> checkadvice = null, </if>
            <if test="checkadvice != null"> checkadvice = #{checkadvice}, </if>
        </set>
        where appserialno = #{appserialno}
    </update>


</mapper>




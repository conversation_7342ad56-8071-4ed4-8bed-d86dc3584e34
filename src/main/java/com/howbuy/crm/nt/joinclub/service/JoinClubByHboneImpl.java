package com.howbuy.crm.nt.joinclub.service;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.howbuy.crm.nt.joinclub.buss.JoinClubByHboneBuss;
import com.howbuy.crm.nt.joinclub.request.JoinClubByHboneRequest;
import com.howbuy.crm.nt.joinclub.response.JoinClubByHboneResponse;

/**
 * 客户入会服务
 */
@Service("joinClubByHboneService")
public class JoinClubByHboneImpl implements JoinClubByHboneService {
	private Logger logger= LoggerFactory.getLogger(JoinClubByHboneImpl.class);

	@Autowired
	private JoinClubByHboneBuss joinClubByHboneBuss;

	@Override
	public JoinClubByHboneResponse joinClubByHbone(JoinClubByHboneRequest request) {
		JoinClubByHboneResponse response = new JoinClubByHboneResponse();
		try {
			joinClubByHboneBuss.joinClubByHbone(request.getHboneNo());
			response.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			response.processedFail();
		}

		return response;
	}

	public boolean validate(JoinClubByHboneRequest request) {
		boolean result = false;
		if (StringUtils.isNotBlank(request.getHboneNo())) {
			result = true;
		}
		return result;
	}

}

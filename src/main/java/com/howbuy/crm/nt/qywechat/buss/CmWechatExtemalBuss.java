package com.howbuy.crm.nt.qywechat.buss;

import com.howbuy.crm.nt.qywechat.dao.CmWechatExtemalInfoDao;
import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @classname: CmWechatExtemalBuss
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-02-22 14:16
 * @since: JDK1.8
 */
@Component
@Slf4j
public class CmWechatExtemalBuss {

    @Autowired
    private CmWechatExtemalInfoDao cmWechatExtemalInfoDao;

    public void insertCmWechatExtemal(CmWechatExtemalInfo cmWechatExtemal){
        cmWechatExtemalInfoDao.insertSelective(cmWechatExtemal);
    }

    public CmWechatExtemalInfo selectCmWechatExtemal(CmWechatExtemalInfo cmWechatExtemal){
        return cmWechatExtemalInfoDao.selectCmWechatExtemal(cmWechatExtemal);
    }
}

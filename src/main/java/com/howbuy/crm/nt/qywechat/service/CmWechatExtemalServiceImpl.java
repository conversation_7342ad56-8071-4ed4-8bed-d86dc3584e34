package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWechatExtemalBuss;
import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo;
import com.howbuy.crm.nt.qywechat.request.CmWechatExtemalRequest;
import com.howbuy.crm.nt.qywechat.response.CmWechatExtemalResponse;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @classname: CmWechatExtemalServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-04-26 15:45
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWechatExtemalService")
public class CmWechatExtemalServiceImpl implements CmWechatExtemalService {

	@Autowired
	private CmWechatExtemalBuss cmWechatExtemalBuss;

	@Override
	public BaseResponse insertCmWechatExtemal(CmWechatExtemalRequest req) {

		BaseResponse response = new BaseResponse();

		try{
			cmWechatExtemalBuss.insertCmWechatExtemal(req.getCmWechatExtemal());
		}catch(Exception e){
			response.paramError("CmWechatExtemalService接口异常"+e.getMessage());
			log.error(e.getMessage(),e);
		}

		response.success();
		return response;
	}

	@Override
	public CmWechatExtemalResponse cmWechatExtemalIsExist(CmWechatExtemalRequest req) {
		CmWechatExtemalResponse rep = new CmWechatExtemalResponse();
		CmWechatExtemalInfo wechatinfo = cmWechatExtemalBuss.selectCmWechatExtemal(req.getCmWechatExtemal());
		if(wechatinfo != null){
			rep.setIsexist(true);
		}else{
			rep.setIsexist(false);
		}
		rep.success();
		return rep;
	}
}

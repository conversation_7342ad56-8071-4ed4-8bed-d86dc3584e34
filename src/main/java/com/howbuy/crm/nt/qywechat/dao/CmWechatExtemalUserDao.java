package com.howbuy.crm.nt.qywechat.dao;

import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalUserInfo;
import org.mybatis.spring.annotation.MapperScan;

/**
 * @classname: 外部联系人
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-02-22 14:16
 * @since: JDK1.8
 */
@MapperScan
public interface CmWechatExtemalUserDao {

    /**
     * updateAllExtemalUser
     * @param
     * @return int
     * @Author: yu.zhang on 2021/2/25 15:38
     */
    void updateAllExtemalUser(String channel);

    /**
     * insertExtemalUser
     * @param record
     * @return int
     * @Author: yu.zhang on 2021/2/25 15:38
     */
    int insertExtemalUser(CmWechatExtemalUserInfo record);

}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWechatCustInfoBuss;
import com.howbuy.crm.nt.qywechat.response.CmWechatCustInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: (CmWechatCustInfoServiveImpl)
 * <AUTHOR>
 * @date 2023/3/17 9:12
 * @since JDK 1.8
 */
@Slf4j
@Service("CmWechatCustInfoServive")
public class CmWechatCustInfoServiveImpl implements CmWechatCustInfoService{

    @Autowired
    private CmWechatCustInfoBuss cmWechatCustInfoBuss;

    @Override
    public CmWechatCustInfoResponse selectnicknamebyunionid(String unionId) {
        CmWechatCustInfoResponse response = new CmWechatCustInfoResponse();
        try {
            String nickName = cmWechatCustInfoBuss.selectnicknamebyunionid(unionId);
            if (null != nickName) {
                response.setNickName(nickName);
                response.success();
            } else {
                response.paramError("未查询到用户昵称");
            }

            return response;
        } catch (Exception e) {
            log.error("error in invoke selectnicknamebyunionid,{}", e.getMessage());
            response.paramError("调用selectnicknamebyunionid接口异常:" + e.getMessage());
            return response;
        }
    }


}
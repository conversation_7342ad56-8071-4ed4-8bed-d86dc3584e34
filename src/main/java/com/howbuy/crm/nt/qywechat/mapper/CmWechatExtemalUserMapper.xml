<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.qywechat.dao.CmWechatExtemalUserDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalUserInfo">
    <id column="USERID" jdbcType="VARCHAR" property="userid" />
    <id column="EXTERNALUSERID" jdbcType="VARCHAR" property="externaluserid" />
    <id column="CHANNEL" jdbcType="VARCHAR" property="channel" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="HBONENO" jdbcType="VARCHAR" property="hboneno" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="USERFLAG" jdbcType="VARCHAR" property="userflag" />
    <result column="CREATEDT" jdbcType="TIMESTAMP" property="createdt" />
  </resultMap>
  <sql id="Base_Column_List">
    USERID, EXTERNALUSERID, CHANNEL, NAME, TYPE, HBONENO, UNIONID, USERFLAG, CREATEDT
  </sql>

  <update id="updateAllExtemalUser" parameterType="String">
    UPDATE CM_WECHAT_EXTEMAL_USER T SET T.USERFLAG = '1' WHERE T.CHANNEL = #{channel}
  </update>

  <insert id="insertExtemalUser" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalUserInfo">
    merge into CM_WECHAT_EXTEMAL_USER t
    using (select #{userid} USERID,#{externaluserid} EXTERNALUSERID, #{channel} CHANNEL from dual) t1
    on (t.USERID = t1.USERID and t.EXTERNALUSERID = t1.EXTERNALUSERID and t.CHANNEL = t1.CHANNEL)
    when not matched then insert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        USERID,
      </if>
      <if test="externaluserid != null">
        EXTERNALUSERID,
      </if>
      <if test="channel != null">
        CHANNEL,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="hboneno != null">
        HBONENO,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="userflag != null">
        USERFLAG,
      </if>
      <if test="createdt != null">
        CREATEDT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="externaluserid != null">
        #{externaluserid,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="hboneno != null">
        #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="userflag != null">
        #{userflag,jdbcType=VARCHAR},
      </if>
      <if test="createdt != null">
        #{createdt,jdbcType=TIMESTAMP},
      </if>
    </trim>
    when matched then
    update
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="hboneno != null">
        HBONENO = #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="userflag != null">
        USERFLAG = #{userflag,jdbcType=VARCHAR},
      </if>
      <if test="createdt != null">
        CREATEDT = #{createdt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where USERID = #{userid,jdbcType=VARCHAR}
    and EXTERNALUSERID = #{externaluserid,jdbcType=VARCHAR}
    and CHANNEL = #{channel,jdbcType=VARCHAR}
  </insert>

</mapper>
package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWechatChooseuserGroupBuss;
import com.howbuy.crm.nt.qywechat.request.CmWechatChooseuserGroupRequest;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @classname: CmWechatChooseuserGroupServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-08-13 17:39
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWechatChooseuserGroupService")
public class CmWechatChooseuserGroupServiceImpl implements CmWechatChooseuserGroupService{

    @Autowired
    private CmWechatChooseuserGroupBuss cmWechatChooseuserGroupBuss;

    @Override
    public BaseResponse insertCmWechatChooseUserGroup(CmWechatChooseuserGroupRequest req) {
        BaseResponse response = new BaseResponse();

        try{
            cmWechatChooseuserGroupBuss.insertCmWechatChooseUserGroup(req.getCmWechatChooseuserGroupList());
            response.success();
        }catch(Exception e){
            response.paramError("CmWechatChooseuserGroupService接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }

        return response;
    }

    @Override
    public BaseResponse updateAllChooseuserGroup() {
        BaseResponse response = new BaseResponse();
        try{
            cmWechatChooseuserGroupBuss.updateAllChooseuserGroup();
            response.success();
        }catch(Exception e){
            response.paramError("CmWechatChooseuserGroupService updateAllChooseuserGroup接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }
        return response;
    }
}

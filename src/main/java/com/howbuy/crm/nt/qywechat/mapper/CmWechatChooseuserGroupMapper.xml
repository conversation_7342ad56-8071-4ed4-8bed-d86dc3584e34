<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.qywechat.dao.CmWechatChooseuserGroupDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.qywechat.dto.CmWechatChooseuserGroup">
    <result column="CHATID" jdbcType="VARCHAR" property="chatid" />
    <result column="CHATNAME" jdbcType="VARCHAR" property="chatname" />
    <result column="CHATOWNER" jdbcType="VARCHAR" property="chatowner" />
    <result column="CHATFLAG" jdbcType="VARCHAR" property="chatflag" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="GROUP_NICKNAME" jdbcType="VARCHAR" property="groupNickname" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="HBONENO" jdbcType="VARCHAR" property="hboneno" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="JOIN_SCENE" jdbcType="VARCHAR" property="joinScene" />
    <result column="USERCHATFLAG" jdbcType="VARCHAR" property="userchatflag" />
    <result column="CREATEDT" jdbcType="TIMESTAMP" property="createdt" />
    <result column="JOIN_TIME" jdbcType="TIMESTAMP" property="joinTime" />
  </resultMap>

  <update id="updateAllChooseuserGroup" >
    UPDATE CM_WECHAT_CHOOSEUSER_GROUP T SET T.CHATFLAG = '1',T.USERCHATFLAG = '1'
  </update>

  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatChooseuserGroup">
    merge into CM_WECHAT_CHOOSEUSER_GROUP t
    using (select #{userid} USERID,#{chatid} CHATID from dual) t1
    on (t.CHATID = t1.CHATID and t.USERID = t1.USERID)
    when not matched then
    insert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="chatid != null">
        CHATID,
      </if>
      <if test="chatname != null">
        CHATNAME,
      </if>
      <if test="chatowner != null">
        CHATOWNER,
      </if>
      <if test="chatflag != null">
        CHATFLAG,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="groupNickname != null">
        GROUP_NICKNAME,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="hboneno != null">
        HBONENO,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="joinScene != null">
        JOIN_SCENE,
      </if>
      <if test="userchatflag != null">
        USERCHATFLAG,
      </if>
      <if test="createdt != null">
        CREATEDT,
      </if>
      <if test="joinTime != null">
        JOIN_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="chatid != null">
        #{chatid,jdbcType=VARCHAR},
      </if>
      <if test="chatname != null">
        #{chatname,jdbcType=VARCHAR},
      </if>
      <if test="chatowner != null">
        #{chatowner,jdbcType=VARCHAR},
      </if>
      <if test="chatflag != null">
        #{chatflag,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="groupNickname != null">
        #{groupNickname,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="hboneno != null">
        #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="joinScene != null">
        #{joinScene,jdbcType=VARCHAR},
      </if>
      <if test="userchatflag != null">
        #{userchatflag,jdbcType=VARCHAR},
      </if>
      <if test="createdt != null">
        #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="joinTime != null">
        #{joinTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    when matched then
    update
    <set>
      <if test="chatname != null">
        CHATNAME = #{chatname,jdbcType=VARCHAR},
      </if>
      <if test="chatowner != null">
        CHATOWNER = #{chatowner,jdbcType=VARCHAR},
      </if>
      <if test="chatflag != null">
        CHATFLAG = #{chatflag,jdbcType=VARCHAR},
      </if>
      <if test="groupNickname != null">
        GROUP_NICKNAME = #{groupNickname,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=VARCHAR},
      </if>
      <if test="hboneno != null">
        HBONENO = #{hboneno,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="joinScene != null">
        JOIN_SCENE = #{joinScene,jdbcType=VARCHAR},
      </if>
      <if test="userchatflag != null">
        USERCHATFLAG = #{userchatflag,jdbcType=VARCHAR},
      </if>
    </set>
    where  USERID=#{userid} AND CHATID=#{chatid}
  </insert>
</mapper>
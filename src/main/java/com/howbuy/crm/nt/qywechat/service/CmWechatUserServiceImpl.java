package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWechatUserBuss;
import com.howbuy.crm.nt.qywechat.dto.CmWechatUser;
import com.howbuy.crm.nt.qywechat.response.CmWechatUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @classname: CmWechatUserServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-08-13 13:43
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWechatUserService")
public class CmWechatUserServiceImpl implements CmWechatUserService{

    @Autowired
    private CmWechatUserBuss cmWechatUserBuss;

    @Override
    public CmWechatUserResponse selectWechatUList() {
        CmWechatUserResponse rep = new CmWechatUserResponse();
        List<CmWechatUser> cmWechatUserList = cmWechatUserBuss.selectWechatUList();
        rep.setCmWechatUserList(cmWechatUserList);
        rep.success();
        return rep;
    }
}

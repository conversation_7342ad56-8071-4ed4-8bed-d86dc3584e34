<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.qywechat.dao.CmWechatExtemalInfoDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo">
    <result column="EXTERNALUSERID" jdbcType="VARCHAR" property="externalUserID" />
    <result column="MSGTYPE" jdbcType="VARCHAR" property="msgType" />
    <result column="EVENT" jdbcType="VARCHAR" property="event" />
    <result column="CHANGETYPE" jdbcType="VARCHAR" property="changeType" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
  </resultMap>
  <update id="insertSelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo">
    merge into CM_WECHAT_EXTEMAL_INFO t
    using (select #{externalUserID} EXTERNALUSERID from dual) t1
    on (t.EXTERNALUSERID = t1.EXTERNALUSERID)
    when not matched then
    insert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="externalUserID != null">
        EXTERNALUSERID,
      </if>
      <if test="msgType != null">
        MSGTYPE,
      </if>
      <if test="event != null">
        EVENT,
      </if>
      <if test="changeType != null">
        CHANGETYPE,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="externalUserID != null">
        #{externalUserID,jdbcType=VARCHAR},
      </if>
      <if test="msgType != null">
        #{msgType,jdbcType=VARCHAR},
      </if>
      <if test="event != null">
        #{event,jdbcType=VARCHAR},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
    </trim>
    when matched then
    update
    <set>
      <if test="msgType != null">
        CREATEDT = sysdate,
        msgtype=#{msgType,jdbcType=VARCHAR},
      </if>
      <if test="event != null">
        event=#{event,jdbcType=VARCHAR},
      </if>
      <if test="changeType != null">
        changetype=#{changeType,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid=#{unionid,jdbcType=VARCHAR},
      </if>
    </set>
    where  externaluserid=#{externalUserID}
  </update>


  <select id="selectCmWechatExtemal" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo" resultType="com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo" useCache="false">
        SELECT T.*
		  FROM CM_WECHAT_EXTEMAL_INFO T
		 WHERE T.externaluserid=#{externalUserID}
		   AND T.msgtype=#{msgType} AND T.event=#{event} AND changetype=#{changeType} AND T.CREATEDT > SYSDATE-3/(24*60)
    </select>
</mapper>
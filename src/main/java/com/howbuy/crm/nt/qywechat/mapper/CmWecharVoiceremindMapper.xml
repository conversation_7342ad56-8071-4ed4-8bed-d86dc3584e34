<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.qywechat.dao.CmWecharVoiceremindDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.qywechat.dto.CmWecharVoiceremindInfo">
    <id column="VOICEID" jdbcType="VARCHAR" property="voiceid" />
    <result column="ACCEPTUSER" jdbcType="VARCHAR" property="acceptuser" />
    <result column="MOBILE" jdbcType="VARCHAR" property="mobile" />
    <result column="REMINDDATE" jdbcType="TIMESTAMP" property="reminddate" />
    <result column="CREATEDT" jdbcType="TIMESTAMP" property="createdt" />
    <result column="REMINDTYPE" jdbcType="VARCHAR" property="remindtype" />
  </resultMap>
  <sql id="Base_Column_List">
    VOICEID, ACCEPTUSER, MOBILE, REMINDDATE, CREATEDT, REMINDTYPE
  </sql>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CM_WECHAR_VOICEREMIND
    where VOICEID = #{voiceid,jdbcType=VARCHAR}
  </delete>

  <update id="insertSelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWecharVoiceremindInfo">
    merge into CM_WECHAR_VOICEREMIND t
    using (select #{voiceid} VOICEID from dual) t1
    on (t.VOICEID = t1.VOICEID)
    when not matched then
    insert
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="voiceid != null">
        VOICEID,
      </if>
      <if test="acceptuser != null">
        ACCEPTUSER,
      </if>
      <if test="mobile != null">
        MOBILE,
      </if>
      <if test="reminddate != null">
        REMINDDATE,
      </if>
      <if test="createdt != null">
        CREATEDT,
      </if>
      <if test="remindtype != null">
        REMINDTYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="voiceid != null">
        #{voiceid,jdbcType=VARCHAR},
      </if>
      <if test="acceptuser != null">
        #{acceptuser,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="reminddate != null">
        #{reminddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdt != null">
        #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="remindtype != null">
        #{remindtype,jdbcType=VARCHAR},
      </if>
    </trim>
    when matched then
    update
    <set>
      <if test="acceptuser != null">
        ACCEPTUSER = #{acceptuser,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="reminddate != null">
        REMINDDATE = #{reminddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdt != null">
        CREATEDT = #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="remindtype != null">
        REMINDTYPE = #{remindtype,jdbcType=VARCHAR},
      </if>
    </set>
    where  VOICEID=#{voiceid}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWecharVoiceremindInfo">
    update CM_WECHAR_VOICEREMIND
    <set>
      <if test="acceptuser != null">
        ACCEPTUSER = #{acceptuser,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="reminddate != null">
        REMINDDATE = #{reminddate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdt != null">
        CREATEDT = #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="remindtype != null">
        REMINDTYPE = #{remindtype,jdbcType=VARCHAR},
      </if>
    </set>
    where VOICEID = #{voiceid,jdbcType=VARCHAR}
  </update>

</mapper>
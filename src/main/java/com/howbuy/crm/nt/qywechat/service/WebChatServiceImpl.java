package com.howbuy.crm.nt.qywechat.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.howbuy.crm.util.MainLogUtils;
import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import crm.howbuy.base.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 微信   接入 service
 */
@Service("webChatService")
@Slf4j
public class WebChatServiceImpl implements WebChatService {

    /**
     * 微信 -中台服务 url
     */
    @Value("${WX_OPEN_URL}")
    private String WX_OPEN_URL;

    /**
     * 微信 小程序id
     */
    @Value("${APPLET_APP_ID}")
    private String APPLET_APP_ID;

    private static final String GENERATE_URL = "/generateUrlLink.htm";

    private static final String SUCCESS_CODE = "0000";


    /**
     * 生成小程序url链接
     * 生成小程序url链接
     *
     * @return
     */
    @Override
    public String getGeneratedUrl(String path, Map<String, String> queryMap) {
        Map<String, String> paramMap = Maps.newHashMap();
//        appId、path、query

        paramMap.put("appId", APPLET_APP_ID);
        // /pageD/authLogin/authLogin
        paramMap.put("path", path);

        if (!queryMap.isEmpty()) {
            List<String> list = Lists.newArrayList();
            queryMap.forEach((k, v) -> {
                list.add(k + "=" + v);
            });
            //k1=v1&k2=v2
            paramMap.put("query", String.join("&", list));
        }

        return interactForString(paramMap, GENERATE_URL, "urlLink");
    }


    /**
     * 中台返回格式： {"data":{"accessToken":"666666666"},"respMsg":"","respCode":"0000"}
     *
     * @param paramMap
     * @param shortUrl
     * @param tClass   data-->属性的对象
     * @param <T>
     * @return
     */
    private <T> ReturnMessageDto<T> interactForObject(Map<String, String> paramMap, String shortUrl, Class<T> tClass) {
        String response = getResp(paramMap, shortUrl);

        if (StringUtils.isBlank(response)) {
            return ReturnMessageDto.fail("无返回信息！");
        }
        JSONObject jsonObject = JSON.parseObject(response);
        if (!SUCCESS_CODE.equals(jsonObject.getString("respCode"))) {
            return ReturnMessageDto.fail(jsonObject.getString("respMsg"));
        }

        T object = JSONObject.parseObject(jsonObject.getString("data"), tClass);
        return ReturnMessageDto.ok("", object);
    }

    /**
     * 中台返回格式： {"data":{"accessToken":"666666666"},"respMsg":"","respCode":"0000"}
     *
     * @param paramMap
     * @param shortUrl
     * @param attribute data中 属性名称
     * @return
     */
    private String interactForString(Map<String, String> paramMap, String shortUrl, String attribute) {
        String response = getResp(paramMap, shortUrl);

        if (StringUtils.isBlank(response)) {
            return null;
        }
        JSONObject jsonObject = JSON.parseObject(response);
        if (!SUCCESS_CODE.equals(jsonObject.getString("respCode"))) {
            return null;
        }
        return jsonObject.getJSONObject("data").getString(attribute);
    }


    private String getResp(Map<String, String> paramMap, String shortUrl) {
        String response = null;
        try {
            String path = WX_OPEN_URL + shortUrl;
            long startTime = System.currentTimeMillis();
            response = HttpUtils.get(path, paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut(path, String.valueOf(HttpStatus.OK.value()), endTime - startTime);
            log.info("请求微信[中台]接口，path:{} . request ：{} ，response：{}", path, JSONObject.toJSONString(paramMap), JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("请求微信[中台]接口异常！", e);
        }
        return response;
    }


}

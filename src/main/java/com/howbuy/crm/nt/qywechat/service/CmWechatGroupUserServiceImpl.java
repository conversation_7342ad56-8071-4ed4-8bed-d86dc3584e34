package com.howbuy.crm.nt.qywechat.service;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.qywechat.buss.CmWechatGroupUserBuss;
import com.howbuy.crm.nt.qywechat.request.CmWechatGroupUserRequest;
import com.howbuy.crm.nt.qywechat.request.CmWechatUserStateRequest;
import com.howbuy.crm.nt.qywechat.response.CmWechatUserStateResponse;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @classname: CmWechatGroupUserServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-04-26 15:45
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWechatGroupUserService")
public class CmWechatGroupUserServiceImpl implements CmWechatGroupUserService {

    @Autowired
    private CmWechatGroupUserBuss cmWechatGroupUserBuss;

    @Override
    public BaseResponse insertCmWechatGroupUser(CmWechatGroupUserRequest req) {
        BaseResponse response = new BaseResponse();

        try{
            cmWechatGroupUserBuss.insertCmWechatGroupUser(req.getCmChatGroupUserList());
            response.success();
        }catch(Exception e){
            response.paramError("cmWechatGroupUserService接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }

        return response;
    }

    @Override
    public BaseResponse deleteCmChatGroupUser() {
        BaseResponse response = new BaseResponse();

        try{
            cmWechatGroupUserBuss.deleteCmChatGroupUser();
            response.success();
        }catch(Exception e){
            response.paramError("cmWechatGroupUserService deleteCmChatGroupUser接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }

        return response;
    }

    @Override
    public BaseResponse updateCmChatGroupUser() {
        BaseResponse response = new BaseResponse();

        try{
            cmWechatGroupUserBuss.updateCmChatGroupUser();
            response.success();
        }catch(Exception e){
            response.paramError("cmWechatGroupUserService updateCmChatGroupUser接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }

        return response;
    }

    @Override
    public CmWechatUserStateResponse queryHaveWechatGroupUser(CmWechatUserStateRequest req) {
        log.info("CreateCmConferenceScanService.execute:{}", JSON.toJSONString(req));
        CmWechatUserStateResponse rep = new CmWechatUserStateResponse();

        try {
            if (validate(req, rep)) {
                rep.setUserState(cmWechatGroupUserBuss.getCmChatGroupUserState(req.getHboneno()));
                rep.success();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            rep.putBaseResult(BaseConstantEnum.FAIL, "操作失败");
        }
        log.info(JSON.toJSONString(rep));
        return rep;
    }

    private boolean validate(CmWechatUserStateRequest request, CmWechatUserStateResponse response) {
        boolean flag = true;
        String hboneno = request.getHboneno();
        if (StringUtils.isBlank(hboneno)) {
            flag = false;
            response.invalidReqParams("一账通账号不允许为空");
        }

        return flag;
    }
}

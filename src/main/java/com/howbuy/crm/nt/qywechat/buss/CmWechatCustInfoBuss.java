/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.qywechat.buss;

import com.howbuy.crm.nt.qywechat.dao.CmWechatCustInfoDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: (CmWechatCustInfoBuss)
 * <AUTHOR>
 * @date 2023/3/17 9:10
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmWechatCustInfoBuss {

    @Autowired
    private CmWechatCustInfoDao cmWechatCustInfoDao;

    public String selectnicknamebyunionid(String unionId) {
        return cmWechatCustInfoDao.selectnamebyunionid(unionId);
    }
}
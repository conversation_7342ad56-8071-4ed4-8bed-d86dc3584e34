package com.howbuy.crm.nt.qywechat.buss;

import com.howbuy.crm.nt.qywechat.dao.CmWechatChooseuserGroupDao;
import com.howbuy.crm.nt.qywechat.dao.CmWechatExtemalUserDao;
import com.howbuy.crm.nt.qywechat.dto.CmWechatChooseuserGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @classname: CmWechatChooseuserGroupBuss
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-08-13 17:39
 * @since: JDK1.8
 */
@Component
@Slf4j
public class CmWechatChooseuserGroupBuss {

    @Autowired
    private CmWechatChooseuserGroupDao cmWechatChooseuserGroupDao;

    @Autowired
    private CmWechatExtemalUserDao cmWechatExtemalUserDao;

    public void insertCmWechatChooseUserGroup(List<CmWechatChooseuserGroup> cmChatGroupUserList){
        for(CmWechatChooseuserGroup cmWechatChooseuser:cmChatGroupUserList){
            cmWechatChooseuserGroupDao.insertSelective(cmWechatChooseuser);
        }
    }

    public void updateAllChooseuserGroup(){
        cmWechatChooseuserGroupDao.updateAllChooseuserGroup();
        cmWechatExtemalUserDao.updateAllExtemalUser("1");
    }
}

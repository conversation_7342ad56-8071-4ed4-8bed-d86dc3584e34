package com.howbuy.crm.nt.qywechat.buss;

import com.howbuy.crm.nt.qywechat.dao.CmWechatUserDao;
import com.howbuy.crm.nt.qywechat.dto.CmWechatUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @classname: CmWechatUserBuss
 * @author: yu.zhang
 * @description: 配置企业微信监控用户
 * @creatdate: 2021-08-13 13:33
 * @since: JDK1.8
 */
@Component
@Slf4j
public class CmWechatUserBuss {

    @Autowired
    private CmWechatUserDao cmWechatUserDao;

    public List<CmWechatUser> selectWechatUList(){
        return cmWechatUserDao.selectWechatUList();
    }
}

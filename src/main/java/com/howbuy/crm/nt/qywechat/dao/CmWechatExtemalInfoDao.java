package com.howbuy.crm.nt.qywechat.dao;

import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo;
import org.mybatis.spring.annotation.MapperScan;

/**
 * @classname: CmWechatExtemalInfoDao
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-02-22 14:16
 * @since: JDK1.8
 */
@MapperScan
public interface CmWechatExtemalInfoDao {

    /**
     * insertSelective
     * @param record
     * @return int
     * @Author: yu.zhang on 2021/2/25 15:38
     */
    int insertSelective(CmWechatExtemalInfo record);

    /**
     * selectCmWechatExtemal
     * @param cmWechatExtemal
     * @return com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo
     * @Author: yu.zhang on 2021/2/25 15:38
     */
    CmWechatExtemalInfo selectCmWechatExtemal(CmWechatExtemalInfo cmWechatExtemal);
}
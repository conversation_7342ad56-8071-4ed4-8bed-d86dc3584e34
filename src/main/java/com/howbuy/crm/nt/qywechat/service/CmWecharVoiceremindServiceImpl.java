package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWecharVoiceremindBuss;
import com.howbuy.crm.nt.qywechat.buss.CmWechatExtemalBuss;
import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalInfo;
import com.howbuy.crm.nt.qywechat.request.CmWecharVoiceremindRequest;
import com.howbuy.crm.nt.qywechat.request.CmWechatExtemalRequest;
import com.howbuy.crm.nt.qywechat.response.CmWechatExtemalResponse;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @classname: CmWecharVoiceremindServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-04-26 15:45
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWecharVoiceremindService")
public class CmWecharVoiceremindServiceImpl implements CmWecharVoiceremindService {

	@Autowired
	private CmWecharVoiceremindBuss cmWecharVoiceremindBuss;

	@Override
	public BaseResponse insertCmWecharVoiceremind(CmWecharVoiceremindRequest req) {

		BaseResponse response = new BaseResponse();

		try{
			cmWecharVoiceremindBuss.insertCmWecharVoiceremind(req.getCmWecharVoiceremind());
		}catch(Exception e){
			response.paramError("CmWecharVoiceremindService接口异常"+e.getMessage());
			log.error(e.getMessage(),e);
		}

		response.success();
		return response;
	}

}

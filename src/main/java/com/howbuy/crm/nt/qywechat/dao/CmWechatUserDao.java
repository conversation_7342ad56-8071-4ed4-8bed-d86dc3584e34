package com.howbuy.crm.nt.qywechat.dao;

import com.howbuy.crm.nt.qywechat.dto.CmWechatUser;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CmWechatUserDao {
    int deleteByPrimaryKey(String userid);

    int insertSelective(CmWechatUser record);

    CmWechatUser selectByPrimaryKey(String userid);

    List<CmWechatUser> selectWechatUList();

    int updateByPrimaryKeySelective(CmWechatUser record);

}
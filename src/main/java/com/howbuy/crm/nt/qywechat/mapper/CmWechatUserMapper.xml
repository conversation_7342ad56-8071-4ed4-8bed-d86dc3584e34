<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.nt.qywechat.dao.CmWechatUserDao">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.nt.qywechat.dto.CmWechatUser">
    <id column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="FLAG" jdbcType="VARCHAR" property="flag" />
    <result column="CREATEDT" jdbcType="TIMESTAMP" property="createdt" />
    <result column="CREATER" jdbcType="VARCHAR" property="creater" />
    <result column="UPDATEDT" jdbcType="TIMESTAMP" property="updatedt" />
    <result column="UPDATER" jdbcType="VARCHAR" property="updater" />
  </resultMap>

  <sql id="Base_Column_List">
    USERID, FLAG, CREATEDT, CREATER, UPDATEDT, UPDATER
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CM_WECHAT_USER
    where USERID = #{userid,jdbcType=VARCHAR}
  </select>

  <select id="selectWechatUList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CM_WECHAT_USER
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CM_WECHAT_USER
    where USERID = #{userid,jdbcType=VARCHAR}
  </delete>

  <insert id="insertSelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatUser">
    insert into CM_WECHAT_USER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        USERID,
      </if>
      <if test="flag != null">
        FLAG,
      </if>
      <if test="createdt != null">
        CREATEDT,
      </if>
      <if test="creater != null">
        CREATER,
      </if>
      <if test="updatedt != null">
        UPDATEDT,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=VARCHAR},
      </if>
      <if test="createdt != null">
        #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="creater != null">
        #{creater,jdbcType=VARCHAR},
      </if>
      <if test="updatedt != null">
        #{updatedt,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.nt.qywechat.dto.CmWechatUser">
    update CM_WECHAT_USER
    <set>
      <if test="flag != null">
        FLAG = #{flag,jdbcType=VARCHAR},
      </if>
      <if test="createdt != null">
        CREATEDT = #{createdt,jdbcType=TIMESTAMP},
      </if>
      <if test="creater != null">
        CREATER = #{creater,jdbcType=VARCHAR},
      </if>
      <if test="updatedt != null">
        UPDATEDT = #{updatedt,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where USERID = #{userid,jdbcType=VARCHAR}
  </update>

</mapper>
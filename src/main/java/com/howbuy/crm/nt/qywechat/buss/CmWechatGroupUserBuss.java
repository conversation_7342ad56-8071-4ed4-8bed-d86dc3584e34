package com.howbuy.crm.nt.qywechat.buss;

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.enums.OuterSysTypeEnum;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindFacade;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindRequest;
import com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindRequest;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindResponse;
import com.howbuy.acccenter.facade.query.querywechatbindinfo.WechatAcctBindInfo;
import com.howbuy.crm.nt.qywechat.dao.CmWechatGroupUserDao;
import com.howbuy.crm.nt.qywechat.dto.CmChatGroupUserInfo;
import com.howbuy.crm.nt.qywechat.dto.WechatUserState;
import com.howbuy.crm.util.CrmNtConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @classname: CmWechatGroupUserBuss
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-04-26 15:46
 * @since: JDK1.8
 */
@Component
@Slf4j
public class CmWechatGroupUserBuss {

    @Autowired
    private CmWechatGroupUserDao cmWechatGroupUserDao;

    @Autowired
    private QueryWechatAcctBindFacade queryWechatAcctBindFacade;

    @Autowired
    private QueryOuterAcctLoginBindFacade queryOuterAcctLoginBindFacade;

    /** 群成员状态 0在群 1退群 */
    public static final String USER_CHAT_FLAG_IN_GROUP = "0";
    public static final String USER_CHAT_FLAG_LEAVE_GROUP = "1";

    /** 群状态 0 正常 1删除 */
    public static final String CHAT_FLAG_NORMAL = "0";
    public static final String CHAT_FLAG_DEL = "1";

    /**
     * 新增企业微信客户群客户
     * <AUTHOR>
     * @updateTime 2022/2/15 14:40
     * @throws 
     */
    public void insertCmWechatGroupUser(List<CmChatGroupUserInfo> cmChatGroupUserList){
        for(CmChatGroupUserInfo cmchatgroupuser:cmChatGroupUserList){
            cmWechatGroupUserDao.insertCmChatGroupUser(cmchatgroupuser);
        }
    }

    public void deleteCmChatGroupUser(){
        cmWechatGroupUserDao.deleteCmChatGroupUser();
    }

    public void updateCmChatGroupUser(){
        cmWechatGroupUserDao.updateCmChatGroupUser();
    }

    /**
     * 查询客户在群中状态
     * <AUTHOR>
     * @updateTime 2022/2/15 14:41
     * @throws 
     */
    public String getCmChatGroupUserState(String hboneno){

        log.info("getCmChatGroupUserState.hboneno:{}", hboneno);
        String result = WechatUserState.NO_USER.getValue();
        //调用接口查询unionid
        String unionid = "";
        unionid = this.getUnionidByAccCenter(hboneno);
        log.info("getCmChatGroupUserState.unionid:{}", unionid);
        if(StringUtils.isNotBlank(unionid)){
            //根据unionid查询客户群信息
            List<CmChatGroupUserInfo> cmChatGroupUserlist = cmWechatGroupUserDao.queryCmChatGroupUserList(unionid);
            List<CmChatGroupUserInfo> newlist = new ArrayList<>();

            log.info("cmChatGroupUserlist.size:{}", JSON.toJSONString(cmChatGroupUserlist));

            if(!cmChatGroupUserlist.isEmpty()){
                Optional.ofNullable(cmChatGroupUserlist).orElse(new ArrayList<>()).forEach(groupuser -> {
                    if (USER_CHAT_FLAG_IN_GROUP.equals(groupuser.getUserchatflag()) && CHAT_FLAG_NORMAL.equals(groupuser.getChatflag())) {
                        newlist.add(groupuser);
                    }
                });

                if(cmChatGroupUserlist.size() >= newlist.size() && newlist.size() != 0){
                    result = WechatUserState.EXIST_USER.getValue();
                }else{
                    result = WechatUserState.MOVE_USER.getValue();
                }
            }
        }

        return result;
    }

    /**
     * getUnionidByAccCenter
     * @param hboneno
     * @return java.lang.String
     * @Author: yu.zhang on 2021/2/23 11:39
     */
    public String getUnionidByAccCenter(String hboneno){

        String unionid = "";
        //通过unionid调用账户中心接口获取一账通信息
        QueryOuterAcctLoginBindRequest req = new QueryOuterAcctLoginBindRequest();
        //unionid
        req.setHboneNo(hboneno);
        req.setOuterSysType(OuterSysTypeEnum.WeChat);
        QueryOuterAcctLoginBindResponse rep = queryOuterAcctLoginBindFacade.execute(req);
        if(rep != null && CrmNtConstant.RMISuccNew.equals(rep.getReturnCode())){
            unionid = rep.getOuterAcctLoginBeanList().get(0).getOuterAcct();
        }

        if(StringUtils.isBlank(unionid)){
            QueryWechatAcctBindRequest wechatreq = new QueryWechatAcctBindRequest();
            wechatreq.setHboneNo(hboneno);
            QueryWechatAcctBindResponse wechatrep = queryWechatAcctBindFacade.execute(wechatreq);
            if(wechatrep != null && CrmNtConstant.RMISuccNew.equals(wechatrep.getReturnCode())){
                for(WechatAcctBindInfo info:wechatrep.getWechatList()){
                    if(StringUtils.isNotBlank(info.getUnionId())){
                        unionid = info.getUnionId();
                        break;
                    }
                }
            }
        }
        return unionid;
    }
}

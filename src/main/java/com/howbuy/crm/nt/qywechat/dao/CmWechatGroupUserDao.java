package com.howbuy.crm.nt.qywechat.dao;

import com.howbuy.crm.nt.qywechat.dto.CmChatGroupUserInfo;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface CmWechatGroupUserDao {

    void insertCmChatGroupUser(CmChatGroupUserInfo record);

    void deleteCmChatGroupUser();

    void updateCmChatGroupUser();

    List<CmChatGroupUserInfo> queryCmChatGroupUserList(String unionid);
}
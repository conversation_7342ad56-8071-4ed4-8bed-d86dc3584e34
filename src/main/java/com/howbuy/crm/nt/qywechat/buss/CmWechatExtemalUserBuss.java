package com.howbuy.crm.nt.qywechat.buss;

import com.howbuy.crm.nt.qywechat.dao.CmWechatExtemalUserDao;
import com.howbuy.crm.nt.qywechat.dto.CmWechatExtemalUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @classname: CmWechatExtemalUserBuss
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-08-20 17:44
 * @since: JDK1.8
 */
@Component
@Slf4j
public class CmWechatExtemalUserBuss {

    @Autowired
    private CmWechatExtemalUserDao cmWechatExtemalUserDao;

    public void insertExtemalUserList(List<CmWechatExtemalUserInfo> cmWechatExtemalUserInfoList){
        for(CmWechatExtemalUserInfo extemaluserinfo:cmWechatExtemalUserInfoList){
            cmWechatExtemalUserDao.insertExtemalUser(extemaluserinfo);
        }
    }

    public void updateAllExtemalUser(String channel){
        cmWechatExtemalUserDao.updateAllExtemalUser(channel);
    }
}

package com.howbuy.crm.nt.qywechat.service;

import com.howbuy.crm.nt.qywechat.buss.CmWechatExtemalUserBuss;
import com.howbuy.crm.nt.qywechat.request.CmWechatExtemalUserRequest;
import crm.howbuy.base.dubbo.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @classname: CmWechatExtemalUserServiceImpl
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-08-20 17:44
 * @since: JDK1.8
 */
@Slf4j
@Service("CmWechatExtemalUserService")
public class CmWechatExtemalUserServiceImpl implements CmWechatExtemalUserService {

    @Autowired
    private CmWechatExtemalUserBuss cmWechatExtemalUserBuss;

    @Override
    public BaseResponse insertCmWechatExtemalUser(CmWechatExtemalUserRequest req) {
        BaseResponse response = new BaseResponse();

        try{
            cmWechatExtemalUserBuss.insertExtemalUserList(req.getCmWechatExtemalUserInfoList());
            response.success();
        }catch(Exception e){
            response.paramError("cmWechatExtemalUserService接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }

        return response;
    }

    @Override
    public BaseResponse updateAllExtemalUser(String channel) {
        BaseResponse response = new BaseResponse();
        try{
            cmWechatExtemalUserBuss.updateAllExtemalUser(channel);
            response.success();
        }catch(Exception e){
            response.paramError("cmWechatExtemalUserBuss updateAllExtemalUser接口异常"+e.getMessage());
            log.error(e.getMessage(),e);
        }
        return response;
    }
}

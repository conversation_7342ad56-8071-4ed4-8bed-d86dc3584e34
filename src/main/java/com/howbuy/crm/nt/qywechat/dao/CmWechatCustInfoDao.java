/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.nt.qywechat.dao;

import org.mybatis.spring.annotation.MapperScan;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/3/17 9:05
 * @since JDK 1.8
 */
@MapperScan
public interface CmWechatCustInfoDao {

    /**
     * 根据微信用户unionId 查询用户昵称
     * @param unionId
     * @return
     */
    String selectnamebyunionid(String unionId);

}
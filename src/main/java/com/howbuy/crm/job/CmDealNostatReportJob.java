/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.cmztorerinfo.service.CmZtOrderInfoService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (PRO_DEAL_NOSTAT_REPORT_TASK 更新是否进入统计)
 * <AUTHOR>
 * @date 2023/6/5 11:30
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmDealNostatReportJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_DEALNOSTATORDER}")
    private String queue;

    @Autowired
    private CmZtOrderInfoService cmZtOrderInfoService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("CmDealNostatReportJob process start");
        try{
            // 业务处理逻辑
            cmZtOrderInfoService.dealNostatReportTask();
        } catch (Exception e){
            log.error("error in CmDealNostatReportJob", e);
        }
        log.info("CmDealNostatReportJob process end");
    }
}
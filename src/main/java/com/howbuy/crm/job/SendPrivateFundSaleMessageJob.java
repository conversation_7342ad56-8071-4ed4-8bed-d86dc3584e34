/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.message.service.SaleMessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM私募赎回消息发送定时任务处理类
 * <AUTHOR>
 * @date 2023/8/29 11:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SendPrivateFundSaleMessageJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.SEND_PRIVATE_FUND_SALE_MESSAGE_JOB对应的队列名称
     */
    @Value("${sync.SEND_PRIVATE_FUND_SALE_MESSAGE_JOB}")
    private String queue;

    @Autowired
    private SaleMessageService saleMessageService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SendPrivateFundSaleMessageJob| process start");
        try{
            saleMessageService.dealSaleMessage(getContent(message));
        } catch (Exception e) {
            log.error("error in SendPrivateFundSaleMessageJob|", e);
        }
        log.info("SendPrivateFundSaleMessageJob| process end");
    }
}
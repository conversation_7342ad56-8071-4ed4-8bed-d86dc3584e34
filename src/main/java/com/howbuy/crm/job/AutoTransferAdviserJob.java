/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.google.common.base.Throwables;
import com.howbuy.crm.nt.consultant.buss.AdivserTransBuss;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 自动划转投顾(已删除客户)
 * <AUTHOR>
 * @date 2023/9/27 上午11:00
 * @since JDK 1.8
 */
@Component
@Slf4j
public class AutoTransferAdviserJob extends AbstractBatchMessageJob {
    @Value("${sync.TOPIC_CRM_NT_TRANS_ADVISER}")
    private String queue;

    @Autowired
    private AdivserTransBuss adivserTransBuss;

    @Override
    protected int getExpireSecond() {
        return super.getExpireSecond();
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("c");
        try {
            adivserTransBuss.transAdviser();
        } catch (Exception e) {
            log.error("自动划转已删除客户投顾job异常:{}", Throwables.getStackTraceAsString(e));
        }
        log.info("自动划转已删除客户投顾job结束");

    }
}
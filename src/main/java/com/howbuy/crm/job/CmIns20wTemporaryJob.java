/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.consultant.service.CmCustConstantService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (零售市值达到20万客户转分配到CS的20万临时库)
 * <AUTHOR>
 * @date 2023/8/8 20:06
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmIns20wTemporaryJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_PROINS20WTEMPORARY}")
    private String queue;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Autowired
    private CmCustConstantService cmCustConstantService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("CmIns20wTemporaryJob process start");
        try{
            // 业务处理逻辑
            cmCustConstantService.mergeTo20WTemporaryLibrary();
        } catch (Exception e){
            log.error("error in CmIns20wTemporaryJob", e);
        }
        log.info("CmIns20wTemporaryJob process end");
    }
}
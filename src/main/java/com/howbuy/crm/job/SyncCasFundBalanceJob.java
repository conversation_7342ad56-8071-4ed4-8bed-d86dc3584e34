/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.casfund.service.CasFundBalanceService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM同步基础持仓
 * <AUTHOR>
 * @date 2023/8/28 16:08
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncCasFundBalanceJob extends AbstractBatchMessageJob {

    @Value("${sync.SYNC_CAS_FUND_BALANCE_JOB}")
    private String queue;

    @Autowired
    private CasFundBalanceService casFundBalanceService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncCasFundBalanceJob process start");
        try{
            casFundBalanceService.autoSyncCustFund(getContent(message));
        } catch (Exception e) {
            log.error("error in SyncCasFundBalanceJob", e);
        }
        log.info("SyncCasFundBalanceJob process end");
    }
}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.nt.lcjzsigndata.dto.CmLcjzsigndata;
import com.howbuy.crm.nt.lcjzsigndata.servcie.CmLcjzSignDataService;
import com.howbuy.crm.util.CrmNtConstant;
import com.howbuy.message.SimpleMessage;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: (接入大数据的rocketmq的 理财九章的报表数据的消息)
 * @date 2023/7/31 09:58
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmLcjzSignDataJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_LCJZSIGNDATA}")
    private String queue;

    @Autowired
    private CmLcjzSignDataService cmLcjzSignDataService;


    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        try {
            log.info("start to receive lcjzsigndata message. message: {}", message);
            Map<String, Object> jsonObject = JSON.parseObject(message.getContent().toString());
            String template = jsonObject.get("template").toString();
            String batchNo = jsonObject.get("batchNo").toString();
            Integer orderSize = (Integer) jsonObject.get("orderSize");
            Integer dataSize = (Integer) jsonObject.get("dataSize");

            log.info("SYNC FROM BDP. template: {}. batchNo: {}. totalCount: {}. current msg count: {}",
                    template, batchNo, orderSize, dataSize);

            List<Map<String, Object>> list = (List<Map<String, Object>>) jsonObject.get("data");

            if (template.equals(CrmNtConstant.LCJZSIGNDATA_TEMPLATE)) {
                List<CmLcjzsigndata> cmLcjzsigndataList = Lists.newArrayList();
                list.forEach(it -> cmLcjzsigndataList.add(transferCmLcjzsigndataByMap(it)));
                cmLcjzsigndataList.forEach(it -> cmLcjzSignDataService.mergeCmLcjzSignData(it));
                log.info("同步理财九章签到数据成功,同步的数据量{}", cmLcjzsigndataList.size());
            }
            log.info("end to receive lcjzsigndata message. message: {}", message);
        } catch (Exception e) {
            log.error("error in receive lianxindata message. message: {}", message, e);
        }
    }

    /**
     * @param it
     * @return com.howbuy.crm.nt.lcjzsigndata.dto.CmLcjzsigndata
     * @description:(kafka 数据格式转换)
     * @author: xufanchao
     * @date: 2023/7/30 18:57
     * @since JDK 1.8
     */
    private CmLcjzsigndata transferCmLcjzsigndataByMap(Map<String, Object> it) {
        CmLcjzsigndata cmLcjzsigndata = new CmLcjzsigndata();
        cmLcjzsigndata.setCourseid(StringUtil.isNotNullStr(it.get("course_id")) ? it.get("course_id").toString() : "");
        cmLcjzsigndata.setIfcheckin(StringUtil.isNotNullStr(it.get("if_checkin")) ? it.get("if_checkin").toString() : "");
        cmLcjzsigndata.setConferencetype(StringUtil.isNotNullStr(it.get("conference_type")) ? it.get("conference_type").toString() : "");
        cmLcjzsigndata.setU2name(StringUtil.isNotNullStr(it.get("u2_name")) ? it.get("u2_name").toString() : "");
        cmLcjzsigndata.setLecturer(StringUtil.isNotNullStr(it.get("lecturer")) ? it.get("lecturer").toString() : "");
        cmLcjzsigndata.setSecondlevelname(StringUtil.isNotNullStr(it.get("second_level_name")) ? it.get("second_level_name").toString() : "");
        cmLcjzsigndata.setU3name(StringUtil.isNotNullStr(it.get("u3_name")) ? it.get("u3_name").toString() : "");
        cmLcjzsigndata.setConscustno(StringUtil.isNotNullStr(it.get("conscustno")) ? it.get("conscustno").toString() : "");
        cmLcjzsigndata.setAppointtime(StringUtil.isNotNullStr(it.get("appoint_time")) ? DateUtil.formatToDate(it.get("appoint_time").toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setFirstlevelname(StringUtil.isNotNullStr(it.get("first_level_name")) ? it.get("first_level_name").toString() : "");
        cmLcjzsigndata.setGdcap(it.get("gd_cap") != null ? new BigDecimal(it.get("gd_cap").toString()) : new BigDecimal(""));
        cmLcjzsigndata.setHoldorg(StringUtil.isNotNullStr(it.get("hold_org")) ? it.get("hold_org").toString() : "");
        cmLcjzsigndata.setIfcallsuccess30(StringUtil.isNotNullStr(it.get("if_call_success_30")) ? it.get("if_call_success_30").toString() : "");
        cmLcjzsigndata.setConferenceid(StringUtil.isNotNullStr(it.get("conferenceid")) ? it.get("conferenceid").toString() : "");
        cmLcjzsigndata.setIfcallsametime(StringUtil.isNotNullStr(it.get("if_call_same_time")) ? it.get("if_call_same_time").toString() : "");
        cmLcjzsigndata.setGdfstackdt(StringUtil.isNotNullStr(it.get("gd_fst_ack_dt")) ? DateUtil.formatToDate(it.get("gd_fst_ack_dt").toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setCoursename(StringUtil.isNotNullStr(it.get("course_name")) ? it.get("course_name").toString() : "");
        cmLcjzsigndata.setFourthlevelname(StringUtil.isNotNullStr(it.get("fourth_level_name")) ? it.get("fourth_level_name").toString() : "");
        cmLcjzsigndata.setConscode(StringUtil.isNotNullStr(it.get("conscode")) ? it.get("conscode").toString() : "");
        cmLcjzsigndata.setIfcall30(StringUtil.isNotNullStr(it.get("if_call_30")) ? it.get("if_call_30").toString() : "");
        cmLcjzsigndata.setThirdlevelname(StringUtil.isNotNullStr(it.get("third_level_name")) ? it.get("third_level_name").toString() : "");
        cmLcjzsigndata.setU1name(StringUtil.isNotNullStr(it.get("u1_name")) ? it.get("u1_name").toString() : "");
        cmLcjzsigndata.setJoincoursename(StringUtil.isNotNullStr(it.get("join_course_name")) ? it.get("join_course_name").toString() : "");
        cmLcjzsigndata.setOnlineflag(StringUtil.isNotNullStr(it.get("online_flag")) ? it.get("online_flag").toString() : "");
        cmLcjzsigndata.setHboneno(StringUtil.isNotNullStr(it.get("hbone_no")) ? it.get("hbone_no").toString() : "");
        cmLcjzsigndata.setIfprdreserv(StringUtil.isNotNullStr(it.get("if_prd_reserv")) ? it.get("if_prd_reserv").toString() : "");
        cmLcjzsigndata.setRealname(StringUtil.isNotNullStr(it.get("real_name")) ? it.get("real_name").toString() : "");
        cmLcjzsigndata.setIfcallleads(StringUtil.isNotNullStr(it.get("if_call_leads")) ? it.get("if_call_leads").toString() : "");
        cmLcjzsigndata.setCustclassifyassign(StringUtil.isNotNullStr(it.get("cust_classify_assign")) ? it.get("cust_classify_assign").toString() : "");
        cmLcjzsigndata.setCityname(StringUtil.isNotNullStr(it.get("city_name")) ? it.get("city_name").toString() : "");
        cmLcjzsigndata.setIfcallsuccess(StringUtil.isNotNullStr(it.get("if_call_success")) ? it.get("if_call_success").toString() : "");
        cmLcjzsigndata.setIfcons(StringUtil.isNotNullStr(it.get("if_cons")) ? it.get("if_cons").toString() : "");
        cmLcjzsigndata.setGdholdflag(StringUtil.isNotNullStr(it.get("gd_hold_flag")) ? it.get("gd_hold_flag").toString() : "");
        cmLcjzsigndata.setActivityarea(StringUtil.isNotNullStr(it.get("activity_place")) ? it.get("activity_place").toString() : "");
        cmLcjzsigndata.setIfwithcons(StringUtil.isNotNullStr(it.get("if_with_cons")) ? it.get("if_with_cons").toString() : "");
        cmLcjzsigndata.setLscap(StringUtil.isNotNullStr(it.get("ls_cap")) ? new BigDecimal(it.get("ls_cap").toString()) : new BigDecimal(""));
        cmLcjzsigndata.setConsnamenow(StringUtil.isNotNullStr(it.get("consname_now")) ? it.get("consname_now").toString() : "");
        cmLcjzsigndata.setConferencename(StringUtil.isNotNullStr(it.get("conferencename")) ? it.get("conferencename").toString() : "");
        cmLcjzsigndata.setIfcall(StringUtil.isNotNullStr(it.get("if_call")) ? it.get("if_call").toString() : "");
        cmLcjzsigndata.setLsholdflag(StringUtil.isNotNullStr(it.get("ls_hold_flag")) ? it.get("ls_hold_flag").toString() : "");
        cmLcjzsigndata.setCallhandledate(StringUtil.isNotNullStr(it.get("call_handle_date")) ? DateUtil.formatToDate(it.get("call_handle_date").toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setAppointstatus(StringUtil.isNotNullStr(it.get("appoint_status")) ? it.get("appoint_status").toString() : "");
        cmLcjzsigndata.setCustclassifynow(StringUtil.isNotNullStr(it.get("cust_classify_now")) ? it.get("cust_classify_now").toString() : "");
        cmLcjzsigndata.setActivitytime(StringUtil.isNotNullStr(it.get("activity_time")) ? it.get("activity_time").toString() : "");
        cmLcjzsigndata.setConsname(StringUtil.isNotNullStr(it.get("consname")) ? it.get("consname").toString() : "");
        cmLcjzsigndata.setSponsor(StringUtil.isNotNullStr(it.get("sponsor")) ? it.get("sponsor").toString() : "");
        cmLcjzsigndata.setIfcallleads30(StringUtil.isNotNullStr(it.get("if_call_leads_30")) ? it.get("if_call_leads_30").toString() : "");
        cmLcjzsigndata.setAssignsource(StringUtil.isNotNullStr(it.get("assign_source")) ? it.get("assign_source").toString() : "");
        cmLcjzsigndata.setStartdate(StringUtil.isNotNullStr(it.get(START_DATE)) ? DateUtil.formatToDate(it.get(START_DATE).toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setConferencetime(StringUtil.isNotNullStr(it.get(START_DATE)) ? DateUtil.formatToDate(it.get(START_DATE).toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setCheckintime(StringUtil.isNotNullStr(it.get("checkin_time"))?DateUtil.formatToDate(it.get("checkin_time").toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setCallhandledate30(StringUtil.isNotNullStr(it.get("call_handle_date_30")) ? DateUtil.formatToDate(it.get("call_handle_date_30").toString(), DateUtil.STANDARDDATE) : new java.sql.Timestamp(0));
        cmLcjzsigndata.setCustproperty(StringUtil.isNotNullStr(it.get("cust_property")) ? it.get("cust_property").toString() : "");
        cmLcjzsigndata.setHwflag(StringUtil.isNotNullStr(it.get("hw_flag")) ? it.get("hw_flag").toString() : "");
        cmLcjzsigndata.setCredt(DateUtil.formatToDate(DateUtil.formatToString(new Date(), DateUtil.STANDARDDATE), DateUtil.STANDARDDATE));
        return cmLcjzsigndata;
    }

    /**
     * 开始日期
     */
    private static final String START_DATE = "start_date";
}
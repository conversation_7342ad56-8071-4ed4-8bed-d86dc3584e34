/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.google.common.base.Throwables;
import com.howbuy.crm.nt.conscust.buss.CmConsSurveyRecBuss;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: (更新所有入会情况job)
 * @date 2023/9/26 上午9:40
 * @since JDK 1.8
 */
@Component
@Slf4j
public class CmJoinClubJob extends AbstractBatchMessageJob {

    @Value("${sync.TOPIC_CRM_NT_JOIN_CLUB}")
    private String queue;

    @Autowired
    private CmConsSurveyRecBuss cmConsSurveyRecBuss;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected int getExpireSecond() {
        //30mins
        return 30*60;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        try {
            cmConsSurveyRecBuss.updateJoinClubInfo();
        } catch (Exception e) {
            log.error("更新入会error:{}", Throwables.getStackTraceAsString(e));
        }
    }
}
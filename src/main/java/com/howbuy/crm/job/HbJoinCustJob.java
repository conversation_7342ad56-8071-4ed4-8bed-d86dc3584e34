/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.joincust.buss.CmHbjoinCustBuss;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: (一账通对账调用EC调度)
 * @date 2023/4/17 07:39
 * @since JDK 1.8
 */
@Slf4j
@Component
public class HbJoinCustJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称，此处为了程序正常运行默认复制了CSDC_CANCEL_ORDER_QUEUE
     */
    @Value("${sync.TOPIC_CRM_NT_HBJOINCUST}")
    private String queue;

    @Autowired
    private CmHbjoinCustBuss cmHbjoinCustBuss;


    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("HbJoinCustJob process start");
        try{
            // 业务处理逻辑
            cmHbjoinCustBuss.hbJoinCostBuss();
        } catch (Exception e){
            log.error("error in HbJoinCustJob:", e);
        }
        log.info("HbJoinCustJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.custsource.service.SynchroCustTagsTaskService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: CRM_同步客户标签任务接口定时任务
 * <AUTHOR>
 * @date 2023/8/29 09:08
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SynchroCustTagsTaskJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.SYNCHRO_CUST_TAGS_TASK_JOB对应的队列名称
     */
    @Value("${sync.SYNCHRO_CUST_TAGS_TASK_JOB}")
    private String queue;

    @Autowired
    private SynchroCustTagsTaskService synchroCustTagsTaskService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SynchroCustTagsTaskJob process start");
        try{
            String msg = getContent(message);
            log.info("SynchroCustTagsTaskJob process, msg:{}", msg);
            synchroCustTagsTaskService.autoSynchroCustTags(msg);
        } catch (Exception e) {
            log.error("error in SynchroCustTagsTaskJob", e);
        }
        log.info("SynchroCustTagsTaskJob process end");
    }
}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindMsgCollectService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: CRM提醒消息收集新定时任务
 * <AUTHOR>
 * @date 2023/8/29 09:57
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindMsgCollectJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.REMIND_MSG_COLLECT_JOB对应的队列名称
     */
    @Value("${sync.REMIND_MSG_COLLECT_JOB}")
    private String queue;

    @Autowired
    private RemindMsgCollectService remindMsgCollectService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindMsgCollectJob process start");
        try{
            String msg = getContent(message);
            log.info("RemindMsgCollectJob process, msg:{}", msg);
            remindMsgCollectService.syncPushMsgData(msg);
        } catch (Exception e) {
            log.error("error in RemindMsgCollectJob", e);
        }
        log.info("RemindMsgCollectJob process end");
    }
}
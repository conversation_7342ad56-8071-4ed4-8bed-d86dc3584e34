/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dao.CmConferenceLogMapper;
import com.howbuy.crm.nt.conference.dto.CmConferenceLog;
import com.howbuy.crm.nt.conference.service.OrderConferenceService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description: (当日交易短信日志归档-- 存储过程改造   job/存储过程：PRO_BATCH_DEAL_MESSAGE)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Slf4j
@Component
public class UpdateOrderConferenceJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_ORDERCONFERENCE}")
    private String queue;

    @Autowired
    private CmConferenceLogMapper cmConferenceLogMapper;

    @Autowired
    private OrderConferenceService orderConferenceService;


    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("UpdateOrderConferenceJob process start");
        try{
            List<CmConferenceLog> cmConferenceLogs = cmConferenceLogMapper.listCmConferenceLog();
            if(CollectionUtils.isEmpty(cmConferenceLogs)){
                return;
            }
            cmConferenceLogs.forEach(it -> {
                try {
                    NtReturnMessageDto<String> result=orderConferenceService.reDealOrderConference(it);
                    log.info("single CmConferenceLog deal result:{}", JSON.toJSONString(result));
                } catch (Exception e) {
                    log.error("single CmConferenceLog deal error, condition:{}", JSON.toJSONString(it), e);
                }
            });
        } catch (Exception e){
            log.error("error in UpdateOrderConferenceJob", e);
        }
        log.info("UpdateConferenceJob process end");
    }
}
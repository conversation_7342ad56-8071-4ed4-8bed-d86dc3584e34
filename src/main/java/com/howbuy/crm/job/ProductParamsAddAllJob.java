/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM产品系数_异步新增全量表
 * <AUTHOR>
 * @date 2023/8/28 17:28
 * @since JDK 1.8
 */
@Slf4j
@Component
public class ProductParamsAddAllJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.PRODUCT_PARAMS_ADD_ALL_JOB对应的队列名称
     */
    @Value("${sync.PRODUCT_PARAMS_ADD_ALL_JOB}")
    private String queue;


    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("ProductParamsAddAllJob process start");
        log.info("ProductParamsAddAllJob process end");
    }
}
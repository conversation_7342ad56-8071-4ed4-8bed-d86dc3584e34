/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.message.buss.CmMessageLogBuss;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (当日交易短信日志归档-- 存储过程改造   job/存储过程：PRO_BATCH_DEAL_MESSAGE)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmMessageLogJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_BATCHDEALMESSAGE}")
    private String queue;

    @Autowired
    private CmMessageLogBuss cmMessageLogBuss;


    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("CmMessageLogJob process start");
        try{
            // 业务处理逻辑
            cmMessageLogBuss.batchInsertCmMessageLog();
        } catch (Exception e){
            log.error("error in CmMessageLogJob", e);
        }
        log.info("CmMessageLogJob process end");
    }
}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.consultant.service.DigestConsultantMobileService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: 投顾手机号刷摘要刷新定时任务
 * <AUTHOR>
 * @date 2023/8/28 17:08
 * @since JDK 1.8
 */
@Slf4j
@Component
public class DigestConsultantMobileJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.DIGEST_CONSULTANT_MOBILE_JOB对应的队列名称
     */
    @Value("${sync.DIGEST_CONSULTANT_MOBILE_JOB}")
    private String queue;

    @Autowired
    private DigestConsultantMobileService digestConsultantMobileService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("DigestConsultantMobileJob process start");
        try{
            digestConsultantMobileService.digestMobileData(getContent(message));
        } catch (Exception e) {
            log.error("error in DigestConsultantMobileJob", e);
        }
        log.info("DigestConsultantMobileJob process end");
    }
}
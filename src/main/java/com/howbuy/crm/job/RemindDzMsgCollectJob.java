/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindDzMsgCollectService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: CRM到账提醒-消息收集新
 * <AUTHOR>
 * @date 2023/8/28 17:40
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindDzMsgCollectJob  extends AbstractBatchMessageJob{

    @Value("${sync.REMIND_DZ_MSG_COLLECT_JOB}")
    private String queue;

    @Autowired
    private RemindDzMsgCollectService remindDzMsgCollectService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindDzMsgCollectJob process start");
        try{
            String msg = getContent(message);
            log.info("RemindDzMsgCollectJob process msg:{}", msg);
            remindDzMsgCollectService.syncCustDzMsgData(msg);
        } catch (Exception e) {
            log.error("error in RemindDzMsgCollectJob", e);
        }
        log.info("RemindDzMsgCollectJob process end");
    }
}
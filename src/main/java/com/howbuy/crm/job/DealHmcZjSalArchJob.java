/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.hmc.service.DealHmcZjSalArchService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM花名册职级薪资存档定时任务处理类
 * <AUTHOR>
 * @date 2023/8/29 08:57
 * @since JDK 1.8
 */
@Slf4j
@Component
public class DealHmcZjSalArchJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.DEAL_HMC_ZJ_SAL_ARCH_JOB对应的队列名称
     */
    @Value("${sync.DEAL_HMC_ZJ_SAL_ARCH_JOB}")
    private String queue;

    @Autowired
    private DealHmcZjSalArchService dealHmcZjSalArchService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("DealHmcZjSalArchJob process start");
        try{
            dealHmcZjSalArchService.execData(getContent(message));
        } catch (Exception e) {
            log.error("error in DealHmcZjSalArchJob", e);
        }
        log.info("DealHmcZjSalArchJob process end");
    }
}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.trade.service.UpdateLatestTradeDtService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @description: 存储过程迁移：【PRO_UPDATE_LATESTTRADEDT】 更新所有客户最近一次高净值交易日期
 * <AUTHOR>
 * @date 2023/11/21 11:25
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmProUpdateLatestTradeDtJob extends AbstractBatchMessageJob {
    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CM_PRO_UPDATE_LATESTTRADEDT_JOB}")
    private String queue;

    @Autowired
    private UpdateLatestTradeDtService updateLatestTradeDtService;

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("CmProUpdateLatestTradeDtJob process start");
        try {
            // 业务处理逻辑
            updateLatestTradeDtService.syncProdData(getContent(message));
        } catch (Exception e) {
            log.error("error in CmProUpdateLatestTradeDtJob", e);
        }
        log.info("CmProUpdateLatestTradeDtJob process end");
    }

}
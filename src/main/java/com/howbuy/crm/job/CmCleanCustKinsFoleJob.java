/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.conference.buss.CmCleanCustKinsFoleBuss;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (参会人员状态批处理(时间节点)--- job/存储过程: PRO_CLEAN_CUSTKINSFOLK)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmCleanCustKinsFoleJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置test.queue.job对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_CLEANCUST}")
    private String queue;

    @Autowired
    private CmCleanCustKinsFoleBuss cmCleanCustKinsFoleBuss;

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 调度业务逻辑处理
        log.info("CmCleanCustKinsFoleJob process start");
        try{
            // 业务处理逻辑
            //cmCleanCustKinsFoleBuss.batchUpdatePrebookResType();
        } catch (Exception e){
            log.error("error in CmCleanCustKinsFoleJob", e);
        }
        log.info("CmCleanCustKinsFoleJob process end");
    }
}
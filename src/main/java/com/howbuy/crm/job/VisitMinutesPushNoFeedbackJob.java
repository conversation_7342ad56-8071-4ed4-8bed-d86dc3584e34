package com.howbuy.crm.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.nt.commvisit.CmVisitMinutesService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2025/4/29 19:55
 * @since JDK 1.8
 */
@Slf4j
@Component
public class VisitMinutesPushNoFeedbackJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CRM_NT_PUSH_NO_FEEDBACK}")
    private String queue;
    @Autowired
    private CmVisitMinutesService cmVisitMinutesService;

    /**
     * @return java.lang.String 调度消息队列名称
     * @description: 获取调度消息队列名
     * @author: hongdong.xie
     * @date: 2023/3/8 17:09
     * @since JDK 1.8
     */
    @Override
    protected String getQuartMessageChannel() {
        return this.queue;
    }

    /**
     * @param message 消息
     * @description:具体执行的回调方法
     * @author: hongdong.xie
     * @date: 2023/3/8 17:08
     * @since JDK 1.8
     */
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("VisitMinutesPushNoFeedbackJob process start");
        try{
            String msg = getContent(message);
            String curPreDayParam = null;
            if(msg != null){
               JSONObject jsonObject = JSON.parseObject(msg);
               curPreDayParam = (String) jsonObject.get("curPreDayParam");
            }
            // 业务处理逻辑
            cmVisitMinutesService.execPushNoFeedback(curPreDayParam);
        } catch (Exception e){
            log.error("error in VisitMinutesPushNoFeedbackJob:", e);
        }
        log.info("VisitMinutesPushNoFeedbackJob process end");
    }
}
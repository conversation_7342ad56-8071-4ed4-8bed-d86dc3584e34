/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.synchbone.service.SyncHboneService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM_同步一账通扩展数据
 * <AUTHOR>
 * @date 2023/8/28 17:36
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncHboneExtendDataJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.SYNC_HBONE_EXTEND_DATA_JOB对应的队列名称
     */
    @Value("${sync.SYNC_HBONE_EXTEND_DATA_JOB}")
    private String queue;

    @Autowired
    private SyncHboneService syncHboneService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncHboneExtendDataJob process start");
        try{
            syncHboneService.syncHboneData(getContent(message));
        } catch (Exception e) {
            log.error("error in SyncHboneExtendDataJob", e);
        }
        log.info("SyncHboneExtendDataJob process end");
    }
}
package com.howbuy.crm.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.nt.conscust.domain.CmWaitTransferCustMsgDTO;
import com.howbuy.crm.nt.message.service.TransferCustMessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description 划转客户批复消息提醒
 * <AUTHOR>
 * @Date 2024/3/13 14:20
 */
@Slf4j
@Component
public class SendTransferCustomMsgJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.SEND_TRANSFER_CUSTOM_MSG_JOB对应的队列名称
     */
    @Value("${sync.SEND_TRANSFER_CUSTOM_MSG_JOB}")
    private String queue;

    @Autowired
    private TransferCustMessageService transferCustMessageService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SendTransferCustomMsgJob process start");
        try{
            String msg = getContent(message);
            if (StringUtil.isBlank(msg)) {
                log.warn("SendTransferCustomMsgJob msg is null");
                return;
            }
            log.info("SendTransferCustomMsgJob msg:{}", msg);
            CmWaitTransferCustMsgDTO custMsgDTO = JSON.parseObject(msg, CmWaitTransferCustMsgDTO.class);
            if (null == custMsgDTO) {
                return;
            }

            if ("1".equals(custMsgDTO.getType())) {
                // 划转批复-消息提醒
                transferCustMessageService.dealTransferReplyMessage(msg);
            } else if ("2".equals(custMsgDTO.getType())) {
                // 二次批复-消息提醒
                transferCustMessageService.dealTransferTwiceReplyMessage(msg);
            }
        } catch (Exception e) {
            log.error("SendTransferCustomMsgJob error={}", e.getMessage(), e);
        }
        log.info("SendTransferCustomMsgJob process end");
    }
}

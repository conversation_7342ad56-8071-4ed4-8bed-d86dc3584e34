package com.howbuy.crm.job;

import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.nt.message.service.PreBookMessageService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description 产品预约发送消息通知投顾上传售前留痕材料处理器
 * <AUTHOR>
 * @Date 2024/4/2 17:20
 */
@Slf4j
@Component
public class SendPreBookLeaglDocPushMsgJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.SEND_PREBOOK_LEGALDOC_MSG_JOB对应的队列名称
     */
    @Value("${sync.SEND_PREBOOK_LEGALDOC_MSG_JOB}")
    private String queue;

    @Autowired
    private PreBookMessageService preBookMessageService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SendPreBookLeaglDocPushMsgJob process start");
        try{
            String msg = getContent(message);
            if (StringUtil.isBlank(msg)) {
                log.warn("SendPreBookLeaglDocPushMsgJob msg is null");
                return;
            }
            // 处理消息推送逻辑
            preBookMessageService.dealLegalDocMessage(msg);
        } catch (Exception e) {
            log.error("SendPreBookLeaglDocPushMsgJob error={}", e.getMessage(), e);
        }
        log.info("SendPreBookLeaglDocPushMsgJob process end");
    }
}

/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.conference.buss.CmConferenceBusinessBuss;
import com.howbuy.crm.nt.conference.dao.CmConferenceScanMapper;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (针对投顾客户号为空的数据，通过手机号判断是否能匹配上惟一客户，若能匹配上，则将投顾客户号及对应投顾信息落下来)
 * <AUTHOR>
 * @date 2023/11/6 16:08
 * @since JDK 1.8
 */
@Slf4j
@Component
public class UpdateConferenceScanCustJob extends AbstractBatchMessageJob{


    /**
     * 调度消息队列，nacos中需要配置 sync.UPDATE_CONFERENCE_SCAN_CUST_JOB 对应的队列名称
     */
    @Value("${sync.UPDATE_CONFERENCE_SCAN_CUST_JOB}")
    private String queue;

    @Autowired
    private CmConferenceBusinessBuss conferenceBusinessBuss;


    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        //针对投顾客户号为空的数据，通过手机号判断是否能匹配上惟一客户，若能匹配上，则将投顾客户号及对应投顾信息落下来
        log.info("UpdateConferenceScanCustJob process start");
        try{
             conferenceBusinessBuss.updateScanCustState();
        } catch (Exception e) {
            log.error("error in UpdateConferenceScanCustJob", e);
        }
        log.info("UpdateConferenceScanCustJob process end");
    }

}
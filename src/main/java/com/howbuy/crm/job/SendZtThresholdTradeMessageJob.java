/**
 * Copyright (c) 2023, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.message.service.SendZtThresholdTradeService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 【触发频率】中台每个交易日一次，等交易申请日终做完后将满足以下条件的客户（预计在6、7点左右）MQ消息发给CRM，CRM根据发送的消息，发给对应的投顾
 *
 * corn: 0 0 19-21 * * 1-5
 * 【发送方式】crm提醒和企业微信消息（发crm应用）
 * 【发送对象】仅投顾自己可以收到消息
 * 【门槛】单客户交易日累计购买申请30W及以上，赎回申请份额*净值预估30W或以上
 * 【支付状态】需要卡付款状态，未付款的不展示
 * 【储蓄罐交易】不需要提醒
 * 【提醒类型】购买/定投/赎回申请成功的订单（不含自主撤单、强制撤单以及交易确认）。
 * 【分销渠道】仅好买官方渠道
 * @date 2024/6/17 11:00
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SendZtThresholdTradeMessageJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.SEND_ZT_THRESHOLD_TRADE_MESSAGE_JOB对应的队列名称
     */
    @Value("${sync.SEND_ZT_THRESHOLD_TRADE_MESSAGE_JOB}")
    private String queue;

    @Autowired
    private SendZtThresholdTradeService thresholdTradeService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SendZtThresholdTradeMessageJob process start");
        try {
            thresholdTradeService.deal(getContent(message));
        } catch (Exception e) {
            log.error("error in SendZtThresholdTradeMessageJob", e);
        }
        log.info("SendZtThresholdTradeMessageJob process end");
    }
}
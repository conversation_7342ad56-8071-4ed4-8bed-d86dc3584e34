/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindXgTaskProduceService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM新规任务生成提醒通知
 * <AUTHOR>
 * @date 2023/8/28 16:31
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindNewRegulationTaskJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列，nacos中需要配置sync.REMIND_NEW_REGULATION_TASK_JOB对应的队列名称
     */
    @Value("${sync.REMIND_NEW_REGULATION_TASK_JOB}")
    private String queue;

    @Autowired
    private RemindXgTaskProduceService remindXgTaskProduceService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindNewRegulationTaskJob process start");
        try{
            remindXgTaskProduceService.remindXgTaskProduce(getContent(message));
        } catch (Exception e) {
            log.error("error in RemindNewRegulationTaskJob", e);
        }
        log.info("RemindNewRegulationTaskJob process end");
    }
}
/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindPushMsgService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM消息推送NT
 * <AUTHOR>
 * @date 2023/8/28 17:47
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindPushMsgJob extends AbstractBatchMessageJob{

    @Value("${sync.REMIND_PUSH_MSG_JOB}")
    private String queue;

    @Autowired
    private RemindPushMsgService remindPushMsgService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindPushMsgJob process start");
        try{
            remindPushMsgService.pushMsgData(getContent(message));
        } catch (Exception e) {
            log.error("error in RemindPushMsgJob", e);
        }
        log.info("RemindPushMsgJob process end");
    }
}
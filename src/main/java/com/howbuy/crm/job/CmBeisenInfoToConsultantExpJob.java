/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.beiseninfo.service.CmBeisenService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: (调用北森同步花名册定时任务)
 * <AUTHOR>
 * @date 2024/10/14 16:34
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CmBeisenInfoToConsultantExpJob extends AbstractBatchMessageJob{
    /**
     * 调度消息队列，nacos中需要配置sync.TOPIC_CRM_NT_BEISENINFO_CONSULTANTEXP_TASK_JOB对应的队列名称
     */
    @Value("${sync.TOPIC_CRM_NT_BEISENINFO_CONSULTANTEXP_TASK_JOB}")
    private String queue;

    @Autowired
    private CmBeisenService cmBeisenService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("CmBeisenInfoToConsultantExpJob process start");
        try{
            cmBeisenService.execSyncBeisenToCrmByJobFacade();
        }catch (Exception e){
            log.error("error in CmBeisenInfoToConsultantExpJob",e);
        }
        log.info("CmBeisenInfoToConsultantExpJob process end");
    }
}
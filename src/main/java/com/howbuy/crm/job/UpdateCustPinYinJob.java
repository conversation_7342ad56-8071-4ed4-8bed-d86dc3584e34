package com.howbuy.crm.job;

import com.howbuy.crm.nt.conscust.service.UpdateCustPinYinService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 请在此添加描述
 * @date 2023/7/12 11:25
 * @since JDK 1.8
 */
@Slf4j
@Component
public class UpdateCustPinYinJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CRM_NT_UPDATE_PINYIN}")
    private String queue;

    @Autowired
    private UpdateCustPinYinService updateCustPinYinService;

    /**
     * @return java.lang.String 调度消息队列名称
     * @description: 获取调度消息队列名
     * @author: hongdong.xie
     * @date: 2023/3/8 17:09
     * @since JDK 1.8
     */
    @Override
    protected String getQuartMessageChannel() {
        return this.queue;
    }

    /**
     * @param message 消息
     * @description:具体执行的回调方法
     * @author: hongdong.xie
     * @date: 2023/3/8 17:08
     * @since JDK 1.8
     */
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("UpdateCustPinYinJob process start");
        try{
            // 业务处理逻辑
            updateCustPinYinService.execute(null);
        } catch (Exception e){
            log.error("error in UpdateCustPinYinJob:", e);
        }
        log.info("UpdateCustPinYinJob process end");
    }
}

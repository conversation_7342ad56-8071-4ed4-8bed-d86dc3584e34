package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindZlgxMsgCollectService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 同步产品资料更新通知17:30新
 * @date 2023/7/14 13:11
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindZlgxMsgCollectJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_CRM_NT_REMIND_ZLGX}")
    private String queue;

    @Autowired
    private RemindZlgxMsgCollectService remindZlgxMsgCollectService;

    @Override
    protected String getQuartMessageChannel() {
        return this.queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindZlgxMsgCollectJob process start");
        try{
            // 业务处理逻辑
            remindZlgxMsgCollectService.syncCustZlgxMsgData(message.getContent().toString());
        } catch (Exception e){
            log.error("error in RemindZlgxMsgCollectJob:", e);
        }
        log.info("RemindZlgxMsgCollectJob process end");
    }
}

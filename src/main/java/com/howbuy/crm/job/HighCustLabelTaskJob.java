/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.highcustlabel.service.HighCustLabelService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description: CRM标签数据同步任务
 * <AUTHOR>
 * @date 2023/10/9 19:47
 * @since JDK 1.8
 */
@Slf4j
@Component
public class HighCustLabelTaskJob extends AbstractBatchMessageJob{

    /**
     * 调度消息队列
     */
    @Value("${sync.TOPIC_HIGH_CUST_LABEL_TASK}")
    private String queue;

    @Autowired
    private HighCustLabelService highCustLabelService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("HighCustLabelTaskJob process start");
        try {
            highCustLabelService.syncHighCustLabel(getContent(message));
        } catch (Exception e){
            log.error(e.getMessage(), e);
        }
        log.info("HighCustLabelTaskJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }
}
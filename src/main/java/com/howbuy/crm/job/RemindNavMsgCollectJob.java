/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.job;

import com.howbuy.crm.nt.remind.service.RemindJzMsgCollectService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: CRM净值提醒9点执行-消息收集新
 * <AUTHOR>
 * @date 2023/8/28 16:46
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RemindNavMsgCollectJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.REMIND_NAV_MSG_COLLECT_JOB对应的队列名称
     */
    @Value("${sync.REMIND_NAV_MSG_COLLECT_JOB}")
    private String queue;

    @Autowired
    private RemindJzMsgCollectService remindJzMsgCollectService;

    @Override
    protected String getQuartMessageChannel() {
        return queue;
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("RemindNavMsgCollectJob process start");
        try{
            String msg = getContent(message);
            log.info("RemindNavMsgCollectJob process msg:{}", msg);
            remindJzMsgCollectService.syncJztzMsgData(msg);
        } catch (Exception e) {
            log.error("error in RemindNavMsgCollectJob", e);
        }
        log.info("RemindNavMsgCollectJob process end");
    }
}
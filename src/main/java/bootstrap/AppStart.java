package bootstrap;

import com.howbuy.crm.nt.consultant.buss.AdivserTransBuss;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.container.Main;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import java.io.IOException;
import java.util.Properties;

@Slf4j
public class AppStart {

	public static void main(String[] args) {
		try {
			Properties properties = new Properties();
			properties.load(AppStart.class.getClassLoader().getResourceAsStream("bootstrap.properties"));
			System.setProperty("nacosServerAddr", properties.getProperty("spring.cloud.nacos.config.server-addr"));
			System.setProperty("namespace", properties.getProperty("spring.cloud.nacos.config.namespace"));
			System.setProperty("username", properties.getProperty("nacos.config.username"));
			System.setProperty("password", properties.getProperty("nacos.config.password"));
			System.setProperty("groupId", properties.getProperty("spring.cloud.nacos.config.group"));
			System.setProperty("dataId", properties.getProperty("spring.application.name"));
			System.setProperty("dubbo.log4j.level","info");
			ClassPathXmlApplicationContext context = new ClassPathXmlApplicationContext("classpath:applicationContext.xml");
			context.start();
			Main.main(args);
		} catch (IOException e) {
			log.error("crm-nt启动异常！",e);
		}
	}
}

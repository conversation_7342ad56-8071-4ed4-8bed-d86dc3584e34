<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:util="http://www.springframework.org/schema/util" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
			http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!-- 提供方应用信息，用于计算依赖关系 -->
	<dubbo:application name="${dubbo.application.name}"
					   logger="slf4j"/>

	<dubbo:registry id="crmNtRegistry" protocol="${dubbo.registry.protocol}"
					address="${dubbo.registries.crm-nt-server.address}"/><!-- file="${dubbo.registry.file}" -->

	<dubbo:protocol name="dubbo" port="${dubbo.protocol.port}" threads="${dubbo.protocol.threads}"
					threadpool="${dubbo.protocol.threadpool}" dispatcher="${dubbo.protocol.dispatcher}"
					queues="${dubbo.protocol.queues}"/>

	<dubbo:provider timeout="${dubbo.provider.timeout}" />

	<!-- 查询会议列表服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.QueryNtCmConferenceInfoListService"
			ref="queryNtCmConferenceInfoListService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.QueryCmHzAppletConferenceListService"
			ref="queryCmHzAppletConferenceListService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.CmConferenceBusinessService"
			ref="cmConferenceBusinessService" registry="crmNtRegistry"
			retries="0" timeout="12000" />
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.CmConferenceScanService"
			ref="cmConferenceScanService" registry="crmNtRegistry"
			retries="0" timeout="12000" />
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.CmConferenceCustService"
			ref="cmConferenceCustService" registry="crmNtRegistry"
			retries="0" timeout="12000" />


	<!-- H5扫码签到服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.CreateCmConferenceScanService"
			ref="createCmConferenceScanService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 客户签到服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.UpdateNtCmConferenceCustInfoService"
			ref="updateNtCmConferenceCustInfoService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 客户签到服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.basedetail.service.QueryCustBaseDetailService"
			ref="queryCustBaseDetailService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 高端客户标签同步 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.highcustlabel.service.HighCustLabelService"
			ref="highCustLabelService" registry="crmNtRegistry" group="highcustlabel"
			retries="0" timeout="60000" />

	<!-- 同步资产中台持仓 -->
	<dubbo:service interface="com.howbuy.crm.nt.casfund.service.CasFundBalanceService"
				   ref="casFundBalanceServiceImpl" registry="crmNtRegistry"  group="zcfund"
				   retries="0" timeout="60000" />

	<!-- 打款短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.PayMessageService"
				   ref="payMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 成交短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.TradeMessageService"
				   ref="tradeMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 赎回短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.SaleMessageService"
				   ref="saleMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 直销固收产品到期短信-->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.ZxProductDueMessageService"
				   ref="zxProductDueMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<dubbo:service
			interface="com.howbuy.crm.nt.cmztorerinfo.service.CmZtOrderInfoService"
			ref="cmZtOrderInfoService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 代销固收产品到期提前通知短信-->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.DxProductDueMessageService"
				   ref="dxProductDueMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 直销固收产品分红短信-->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.ZxGsDividendMessageService"
				   ref="zxGsDividendMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 直销非固收产品分红短信-->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.ZxNotgsDividendMessageService"
				   ref="zxNotgsDividendMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 代销固收产品分红提前通知短信-->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.DxGsDividendMessageService"
				   ref="dxGsDividendMessageServiceImpl" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 资料更新通知短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.PushZlgxMsgCollectService"
				   ref="pushZlgxMsgCollectService" registry="crmNtRegistry"  group="message"
				   retries="0" timeout="60000" />

	<!-- 收集到账通知消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.PushDzMsgCollectService"
				   ref="pushDzMsgCollectService" registry="crmNtRegistry"  group="crmMsg"
				   retries="0" timeout="600000" />
				   
	<!-- 收集净值通知消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.message.service.PushJzMsgCollectService"
				   ref="pushJzMsgCollectService" registry="crmNtRegistry"  group="crmMsg"
				   retries="0" timeout="600000" />

	<!-- 查询投顾信息服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.consultant.service.QueryConsultantInfoService"
			ref="queryConsultantInfoService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 查询投顾客户信息列表服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.QueryConscustListService"
			ref="queryConscustListService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 查询投顾客户信息列表服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.QueryConscustListByConsService"
			ref="queryConscustListByConsService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 查询投顾客户信息列表服务 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscard.service.CreateConscardForNetService"
			ref="createConscardForNetService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 查询、修改投顾信息-->
	<dubbo:service interface="com.howbuy.crm.nt.consinfo.service.ConsInfoService"
		   ref="consInfoService" registry="crmNtRegistry"
		   retries="0" timeout="60000"/>

	<!-- 查询来源 -->
	<dubbo:service interface="com.howbuy.crm.nt.custsource.service.CustomerSourceLevelService"
		   ref="customerSourceLevelService" registry="crmNtRegistry"
		   retries="0" timeout="60000"/>
		   
	<!-- 客户入会服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.joinclub.service.JoinClubByHboneService"
		   ref="joinClubByHboneService" registry="crmNtRegistry"
		   retries="0" timeout="60000"/>
	<dubbo:service interface="com.howbuy.crm.nt.joinclub.service.CmJoinClubService"
				   ref="cmJoinClubService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>
				   
	<!-- 查询家庭账户其他成员服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.conscust.service.QueryFamilyCustInfoService"
				   ref="queryFamilyCustInfoService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>
				   
	<!-- 查询投顾客户基本信息接口服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.conscust.service.QueryCustInfoListForCCService"
				   ref="queryCustInfoListForCCService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>
				   
	<!-- 查询投顾信息接口服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.consultant.service.QueryConsInfoService"
				   ref="queryConsInfoService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>
				   
	<!-- 查询投顾信息接口服务(新) -->
	<dubbo:service interface="com.howbuy.crm.nt.consultant.service.QueryCmConsInfoService"
				   ref="queryCmConsInfoService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>

	<!-- 查询花名册投顾领导信息 接口服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.consultant.service.QueryConsultantExpService"
				   ref="queryConsultantExpService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>

	<!-- 查询客户资产证明状态 -->
	<dubbo:service interface="com.howbuy.crm.nt.conscust.service.QueryCustAssetCertificateService"
				   ref="queryCustAssetCertificateService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>
				   
	<!-- 验证投顾小秘用户和验证码是否过期 -->
	<dubbo:service interface="com.howbuy.crm.nt.secretkey.service.SecretKeyService"
				   ref="secretKeyService" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>

	<!-- 资料更新通知短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindZlgxMsgCollectService"
				   ref="remindZlgxMsgCollectService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="60000" />

	<!-- 收集提醒消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindMsgCollectService"
				   ref="remindMsgCollectService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="1200000" />

	<!-- 收集到账通知消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindDzMsgCollectService"
				   ref="remindDzMsgCollectService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="600000" />

	<!-- 收集净值通知消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindJzMsgCollectService"
				   ref="remindJzMsgCollectService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="600000" />

	<!-- 收集资料退回知消息数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindWithdrawMessageService"
				   ref="remindWithdrawMessageService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="600000" />

	<!-- 更新客户姓名拼音调度 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.UpdateCustPinYinService"
			ref="updateCustPinYinService" registry="crmNtRegistry" group="pinYin"
			retries="0" timeout="60000" />

	<!-- 投顾小秘查询客户接口 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.QueryCustForSecretService"
			ref="queryCustForSecretService" registry="crmNtRegistry"
			retries="0" timeout="600" />
			
			
	<!-- 同步客户标签任务接口 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.custsource.service.SynchroCustTagsTaskService"
			ref="synchroCustTagsTaskService" registry="crmNtRegistry" group="custTags"
			retries="0" timeout="600" />

	<!-- 同步一账通数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.synchbone.service.SyncHboneService"
				   ref="syncHboneService" registry="crmNtRegistry"  group="synchbone"
				   retries="0" timeout="60000" />

	<!-- 批量更新初始交易次数记录状态为失效 -->
	<dubbo:service interface="com.howbuy.crm.nt.reward.service.CmPrpInitialTradeNumService"
				   ref="CmPrpInitialTradeNumService" registry="crmNtRegistry"  group="reward"
				   retries="0" timeout="60000" />

    <!-- 消息推送接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.pushmsg.service.CmPushMsgService"
				   ref="CmPushMsgService" registry="crmNtRegistry"
				   retries="0" timeout="12000000" />

	<!-- 消息推送接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.pushmsg.service.CmPushMsgByConsCodeService"
				   ref="CmPushMsgByConsCodeService" registry="crmNtRegistry"
				   retries="0" timeout="12000000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatExtemalService"
				   ref="CmWechatExtemalService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWecharVoiceremindService"
				   ref="CmWecharVoiceremindService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信获取群用户 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatGroupUserService"
				   ref="CmWechatGroupUserService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatUserService"
				   ref="CmWechatUserService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatChooseuserGroupService"
				   ref="CmWechatChooseuserGroupService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatExtemalUserService"
				   ref="CmWechatExtemalUserService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 企业微信保留接口 -->
	<dubbo:service interface="com.howbuy.crm.nt.qywechat.service.CmWechatCustInfoService"
				   ref="CmWechatCustInfoServive" registry="crmNtRegistry"
				   retries="0" timeout="60000"/>


	<!-- 新增 查询 实际投顾数据接口 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.realconsultant.service.CmRealConsultantService"
			ref="cmRealConsultantService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 给cms提供的接口	-->
	<dubbo:service
			interface="com.howbuy.crm.nt.conference.service.OrderConferenceService"
			ref="orderConferenceService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

    <dubbo:service interface="com.howbuy.crm.nt.consultant.service.DigestConsultantMobileService"
				   ref="DigestConsultantMobileService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
				   

    <dubbo:service interface="com.howbuy.crm.nt.consultant.service.DigestConsultantMobileService"
				   ref="DigestConsultantMobileService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
				   
	<!-- 刷数据接口 -->

	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyConscustIcService"
				   ref="EncyConscustIcService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.CompareSensitiveIcService"
				   ref="CompareSensitiveIcService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyBxService"
				   ref="EncyBxService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCmBookingcustService"
				   ref="encyCmBookingcustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsCallinService"
				   ref="encyCsCallinService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsCalllossCustService"
				   ref="encyCsCalllossCustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsCalloutWaitdistributeService"
				   ref="encyCsCalloutWaitdistributeService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsLeavemsgCustService"
				   ref="encyCsLeavemsgCustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	  
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsAssignedCustConfigLogService"
				   ref="encyCsAssignedCustConfigLogService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsAssignedCustConfigMsgService"
				   ref="encyCsAssignedCustConfigMsgService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsAutoDistributeLogService"
				   ref="encyCsAutoDistributeLogService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncySyncAcCustService"
				   ref="encySyncAcCustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncySyncAcDisCustService"
				   ref="encySyncAcDisCustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncySyncCpCustBankAcctInfoService"
				   ref="encySyncCpCustBankAcctInfoService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyHboneCustinfoService"
				   ref="encyHboneCustinfoService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />			   
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCmPotentialcustService"
				   ref="encyCmPotentialcustService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />				   		   		   			   
				   
				   			   			   
<!-- 资料更新通知短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindXgTaskProduceService"
				   ref="remindXgTaskProduceService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="60000" />

	<!-- 处理协会邮件 -->
	<dubbo:service interface="com.howbuy.crm.nt.associationmail.service.AssociationMailService"
				   ref="associationMailService" registry="crmNtRegistry"  group="associationMail"
				   retries="0" timeout="300000" />
	<!-- 处理协会历史邮件 -->
	<dubbo:service interface="com.howbuy.crm.nt.associationmail.service.AssociationMailZipService"
				   ref="associationMailZipService" registry="crmNtRegistry"  group="associationMailZip"
				   retries="0" timeout="60000" />
	<!-- 协会邮件短信 -->
	<dubbo:service interface="com.howbuy.crm.nt.associationmail.service.AssociationMailMessageService"
				   ref="associationMailMessageService" registry="crmNtRegistry"  group="associationMailMessage"
				   retries="0" timeout="60000" />
	
	<!-- 重复客户刷数据 -->			   
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyOperationService"
				   ref="EncyOperationService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	
	
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyVerifyIdnoService"
				   ref="EncyVerifyIdnoService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />			
				   
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyPreBankService"
				   ref="EncyPreBankService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	   
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyConscustHisService"
				   ref="EncyConscustHisService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	
				   
    <dubbo:service interface="com.howbuy.crm.nt.sensitive.service.UpdateSurveyDataService"
				   ref="UpdateSurveyDataService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />	 
				   
	<!-- 刷外呼记录流水脱敏数据 -->			   
	<dubbo:service interface="com.howbuy.crm.nt.sensitive.service.EncyCsCalloutRecService"
				   ref="encyCsCalloutRecService" registry="crmNtRegistry" group="message"
				   retries="0" timeout="60000" />

	<!-- 路演管理-service -->
	<dubbo:service interface="com.howbuy.crm.nt.conference.service.CmConferenceInfoService"
				   ref="cmConferenceInfoService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />
				   
	<!-- 更新客户姓名拼音调度 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.UpdateCustRgSourceLogService"
			ref="updateCustRgSourceLogService" registry="crmNtRegistry" group="SourceLog"
			retries="0" timeout="60000" />

	<!-- 用户管理-在途账户关闭调度 -->
	<dubbo:service interface="com.howbuy.crm.nt.onwayaccount.service.OnwayAccountCloseScheduleService"
				   ref="onwayAccountCloseScheduleService" registry="crmNtRegistry" group="onwayAccountCloseSchedule"
				   retries="0" timeout="60000" />

	<!-- 用户管理-在途账户关闭提示 -->
	<dubbo:service interface="com.howbuy.crm.nt.onwayaccount.service.OnwayAccountCloseService"
				   ref="onwayAccountCloseService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 协会邮件-批量导入eml数据 -->
	<dubbo:service interface="com.howbuy.crm.nt.associationmail.service.AssociationMailZipImportService"
				   ref="associationMailZipImportService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!-- 消息推送 -->
	<dubbo:service interface="com.howbuy.crm.nt.remind.service.RemindPushMsgService"
				   ref="remindPushMsgService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="60000" />
				   
	<!-- 职级存档-->
	<dubbo:service interface="com.howbuy.crm.nt.hmc.service.DealHmcZjSalArchService"
				   ref="dealHmcZjSalArchService" registry="crmNtRegistry"  group="remind"
				   retries="0" timeout="60000" />

	<!-- 资产配置需要服务 -->
	<dubbo:service interface="com.howbuy.crm.nt.asset.service.CmAssetProductService"
                       ref="cmAssetProductService" registry="crmNtRegistry"
                       retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.asset.service.CmAssetCustService"
				   ref="cmAssetCustService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />


	<!--关联账户 -->
	<dubbo:service interface="com.howbuy.crm.nt.accountrelation.service.CmRelationAccountService"
				   ref="cmRelationAccountService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />
	<dubbo:service interface="com.howbuy.crm.nt.accountrelation.service.CmRelationAccountSubService"
				   ref="cmRelationAccountSubService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />


	<!--客户 直播访问数据 统计 service-->
	<dubbo:service interface="com.howbuy.crm.nt.yxslive.service.CustYxsLiveStaticService"
				   ref="custYxsLiveStaticService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />

	<!--各模块上传类型格式获取 service-->
	<dubbo:service interface="com.howbuy.crm.nt.uploadmodule.service.UploadModuleService"
				   ref="uploadModuleService" registry="crmNtRegistry"
				   retries="0" timeout="60000" />
	
	<!-- 新增沟通记录 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.CsCommunicateVisitService"
			ref="csCommunicateVisitService" registry="crmNtRegistry"
			retries="0" timeout="60000"/>

	<!-- 新增预约沟通记录 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.conscust.service.CmConsbookingService"
			ref="cmConsbookingService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 渠道管理 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.param.service.ChannelManagerService"
			ref="channelManagerService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 处理客户产品的预约是否需要进入黑名单 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.cmztorerinfo.service.CmZtOrderInfoService"
			ref="cmZtOrderInfoService" registry="crmNtRegistry"
			retries="0" timeout="60000" />

	<!-- 处理待划转客户消息提醒 -->
	<dubbo:service
			interface="com.howbuy.crm.nt.message.service.TransferCustMessageService"
			ref="transferCustMessageServiceImpl" registry="crmNtRegistry"
			retries="0" timeout="60000" />
</beans>
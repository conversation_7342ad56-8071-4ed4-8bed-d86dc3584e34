<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="performanceAdvice" class="com.howbuy.crm.util.AccessLogAdvice"/>
    <bean id="exceptionCatchAdvice" class="com.howbuy.crm.util.ExceptionCatchAdvice"/>

    <aop:config>
        <aop:advisor pointcut="execution(* com.howbuy.crm.nt.*.service..*.*(..))" advice-ref="exceptionCatchAdvice"
                     order="20"/>

        <aop:aspect id="performanceAspect" ref="performanceAdvice" order="10">
            <aop:pointcut id="performancePointcut" expression="execution(* com.howbuy.crm.nt.*.service..*.*(..))"/>
            <aop:around method="performance" pointcut-ref="performancePointcut"/>
        </aop:aspect>
    </aop:config>

</beans>
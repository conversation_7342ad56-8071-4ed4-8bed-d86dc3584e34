<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" packages="com">
    <properties>
        <property name="logPath">/data/logs/crm-nt-server</property>
        <property name="mainLogName">crm-nt-server-main</property>
    </properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <CustomPatternLayout>
                <pattern>
                    {"time":"%d{yyyy-MM-dd
                    HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n
                </pattern>
                <replaces>
                    <replace regex='(\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****"/>
                    <replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"mobileBank\\":\\"|mobileNo=)(\d{7})\d{4}'
                             replacement="$1$2****"/>
                    <replace
                            regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankAcctFull\\":\\"|bankAcct=)(\d{1,})(\d{6})'
                            replacement="$1******$3"/>
                    <replace
                            regex='(\\"custName\\":\\"|\\"userName\\":\\"|\\"bankAcctName\\":\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})'
                            replacement="$1$2*$3"/>
                    <replace regex='(\\"address\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                    <replace regex='(\\"addr\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                </replaces>
            </CustomPatternLayout>
        </Console>

        <RollingFile name="errorLog" fileName="${logPath}/error.log" filePattern="${logPath}/%d{yyyyMMdd}/error-%i.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <CustomPatternLayout>
                <pattern>
                    {"time":"%d{yyyy-MM-dd
                    HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n
                </pattern>
                <replaces>
                    <replace regex='(\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****"/>
                    <replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"mobileBank\\":\\"|mobileNo=)(\d{7})\d{4}'
                             replacement="$1$2****"/>
                    <replace
                            regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankAcctFull\\":\\"|bankAcct=)(\d{1,})(\d{6})'
                            replacement="$1******$3"/>
                    <replace
                            regex='(\\"custName\\":\\"|\\"userName\\":\\"|\\"bankAcctName\\":\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})'
                            replacement="$1$2*$3"/>
                    <replace regex='(\\"address\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                    <replace regex='(\\"addr\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                </replaces>
            </CustomPatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>

        <RollingFile name="DefaultLog" fileName="${logPath}/crm-nt-server.log"
                     filePattern="${logPath}/%d{yyyyMMdd}/crm-nt-server-%i.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="DENY" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <CustomPatternLayout>
                <pattern>
                    {"time":"%d{yyyy-MM-dd
                    HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n
                </pattern>
                <replaces>
                    <replace regex='(\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****"/>
                    <replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"mobileBank\\":\\"|mobileNo=)(\d{7})\d{4}'
                             replacement="$1$2****"/>
                    <replace
                            regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankAcctFull\\":\\"|bankAcct=)(\d{1,})(\d{6})'
                            replacement="$1******$3"/>
                    <replace
                            regex='(\\"custName\\":\\"|\\"userName\\":\\"|\\"bankAcctName\\":\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})'
                            replacement="$1$2*$3"/>
                    <replace regex='(\\"address\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                    <replace regex='(\\"addr\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                </replaces>
            </CustomPatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>

        <RollingFile name="AccessLog" fileName="${logPath}/access.log"
                     filePattern="${logPath}/%d{yyyyMMdd}/access-%i.log">
            <CustomPatternLayout>
                <pattern>
                    {"time":"%d{yyyy-MM-dd
                    HH:mm:ss.SSS}","custNo":"%X{custNo}","tid":"%X{uuid}","status":"%X{status}","labelName":"%markerSimpleName","thread":"%t","level":"%-5p","class_line":"%c:%L","msg":"%enc{%msg}{JSON}"}%n
                </pattern>
                <replaces>
                    <replace regex='(\\"idNo\\":\\"|idNo=)(\w{14})\w{4}' replacement="$1$2****"/>
                    <replace regex='(\\"mobile\\":\\"|\\"mobileNo\\":\\"|\\"mobileBank\\":\\"|mobileNo=)(\d{7})\d{4}'
                             replacement="$1$2****"/>
                    <replace
                            regex='(\\"bankAcct\\":\\"|\\"bankNo\\":\\"|\\"bankAcctFull\\":\\"|bankAcct=)(\d{1,})(\d{6})'
                            replacement="$1******$3"/>
                    <replace
                            regex='(\\"custName\\":\\"|\\"userName\\":\\"|\\"bankAcctName\\":\\")([\u4E00-\u9FA5]{1})[·\u4E00-\u9FA5]{0,}([·\u4E00-\u9FA5]{1})'
                            replacement="$1$2*$3"/>
                    <replace regex='(\\"address\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                    <replace regex='(\\"addr\\":\\"[^"]*?)[^"]{1,10}"' replacement='$1******\\"'/>
                </replaces>
            </CustomPatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>

        <RollingFile name="MainLogger"
                     fileName="${logPath}/${mainLogName}.log"
                     filePattern="${logPath}/%d{yyyyMMdd}/${mainLogName}.log">
            <PatternLayout pattern="%msg%n" />
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <logger name="howbuy-dubbo-access-log" level="error"
                additivity="false">
            <appender-ref ref="AccessLog"/>
        </logger>
        <logger name="cc-access-log" level="info"
                additivity="false">
            <appender-ref ref="AccessLog"/>
        </logger>
        <logger name="com.howbuy" level="INFO" additivity="false">
            <appender-ref ref="DefaultLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="Console"/>
        </logger>
        <logger name="mainlog" level="info" additivity="false">
            <appender-ref ref="MainLogger"/>
        </logger>

        <Root level="info">
            <AppenderRef ref="DefaultLog"/>
            <AppenderRef ref="errorLog"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>
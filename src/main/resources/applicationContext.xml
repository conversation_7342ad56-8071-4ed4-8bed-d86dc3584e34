<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:task="http://www.springframework.org/schema/task"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:nacos="http://nacos.io/schema/nacos"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd http://nacos.io/schema/nacos http://nacos.io/schema/nacos.xsd
		http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

	<context:annotation-config/>
	<context:component-scan base-package="com.howbuy.crm.**"/>
	<import resource="spring-dubbo-prov.xml" />
	<import resource="spring-dubbo-server.xml" />
	<import resource="performance.xml" />
	<import resource="spring-mybatis.xml" />

	<!--开启注解-->
	<nacos:annotation-driven></nacos:annotation-driven>
	<!--指定nacos配置地址-->
	<nacos:global-properties
			server-addr="${nacosServerAddr}"
			namespace="${namespace}"
			username="${username}"
			password="${password}"/>
	<!--指定dataId,group-id, 是否是自动刷新-->
	<nacos:property-source data-id="${dataId}" group-id="${groupId}" auto-refreshed="true"/>

</beans>
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xmlns:aop="http://www.springframework.org/schema/aop"
     xmlns:tx="http://www.springframework.org/schema/tx"
     xmlns:context="http://www.springframework.org/schema/context"
     xmlns:jdbc="http://www.springframework.org/schema/jdbc"
     xmlns:p="http://www.springframework.org/schema/p"
     xmlns:c="http://www.springframework.org/schema/c"
     xmlns:jaxws="http://cxf.apache.org/jaxws"
     xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
     xsi:schemaLocation="
     http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.2.xsd
     http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
     http://www.springframework.org/schema/jdbc http://www.springframework.org/schema/jdbc/spring-jdbc-3.2.xsd
     http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
     http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
     http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
     http://code.alibabatech.com/schema/dubbo  
     http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

	<!--引用dubbo请求日志过滤器-->
	<dubbo:consumer filter="requestLogDubboFilter"/>

	<!-- center-client -->
	<dubbo:registry id="center-client" protocol="zookeeper" address="${dubbo.registries.center-member-service.address}" check="false" />
	<dubbo:reference id="queryUserTagsListBatchService" registry="center-client" interface="com.howbuy.cc.center.member.tag.service.QueryUserTagsListBatchService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="queryUserTagsListService" registry="center-client" check="false" interface="com.howbuy.cc.center.member.tag.service.QueryUserTagsListService" />
	<dubbo:reference id="queryTagsHboneListByPageService" registry="center-client" check="false" interface="com.howbuy.cc.center.member.tag.service.QueryTagsHboneListByPageService" />
	<dubbo:reference id="QueryCasFundBalanceService" registry="center-client" interface="com.howbuy.cc.center.assetservice.centralization.service.QueryCasFundBalanceService" check="false" retries="0" timeout="1200000" />

	<!-- acc-center-server -->
	<dubbo:registry id="acc-center-server" protocol="zookeeper" address="${dubbo.registries.acc-center-server.address}" check="false" />
	<dubbo:reference id="QueryLastAnswerService" registry="acc-center-server" interface="com.howbuy.cc.center.feature.answer.service.QueryLastAnswerService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryAccHboneInfoFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade" check="false" retries="0" timeout="120000"/>
	<dubbo:reference id="queryCustInfoFacade" registry="acc-center-server"  interface="com.howbuy.acccenter.facade.query.querycustinfo.QueryCustInfoFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryDisCustSensitiveInfoFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.sensitive.discustinfo.QueryDisCustSensitiveInfoFacade" check="false" retries="0" timeout="120000"/>
	<dubbo:reference id="queryAssetManagementCertificateStatusFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.queryassetmanagementcertificatestatus.QueryAssetManagementCertificateStatusFacade" check="false" retries="0" timeout="12000" />
	<dubbo:reference id="queryCustAssetCertificateStatusFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.querycustassetcertificatestatus.QueryCustAssetCertificateStatusFacade" check="false" retries="0" timeout="12000" />
	<dubbo:reference id="queryCurrentAssetCertificateStatusService" registry="acc-center-server" interface="com.howbuy.cc.center.feature.asset.service.QueryCurrentAssetCertificateStatusService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="kycInfoFacade" registry="acc-center-server"  interface="com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="queryLastAnswerService" registry="acc-center-server"  interface="com.howbuy.cc.center.feature.answer.service.QueryLastAnswerService"  check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="queryAllCustInfoAndDisCustInfoFacade" registry="acc-center-server"  interface="com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoFacade"  check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryWechatAcctBindFacade" registry="acc-center-server"  interface="com.howbuy.acccenter.facade.query.querywechatbindinfo.QueryWechatAcctBindFacade"  check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="QueryOuterAcctLoginBindFacade" registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.queryouteracctloginbind.QueryOuterAcctLoginBindFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="createRelatedAccountFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.trade.relatedaccount.CreateRelatedAccountFacade" check="false" retries="0" timeout="60000"/>
	<dubbo:reference id="addSubRelatedAccountFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.trade.relatedaccount.AddSubRelatedAccountFacade" check="false" retries="0" timeout="60000"/>
	<dubbo:reference id="closeSubRelatedAccountFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.trade.relatedaccount.CloseSubRelatedAccountFacade" check="false" retries="0" timeout="60000"/>
	<dubbo:reference id="closeRelatedAccountFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.trade.relatedaccount.CloseRelatedAccountFacade" check="false" retries="0" timeout="60000"/>
	<dubbo:reference id="queryRelatedAccountFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.queryrelatedaccount.QueryRelatedAccountFacade" check="false" retries="0" timeout="60000"/>
	<dubbo:reference id="queryTxAcctByHboneFacade"  registry="acc-center-server" interface="com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade" check="false" retries="0" timeout="60000"/>

	<!-- message-remote -->
	<dubbo:registry id="message-remote" protocol="zookeeper" address="${dubbo.registries.message-remote.address}" check="false" />
	<dubbo:reference id="autoSendMessageService" registry="message-remote" interface="com.howbuy.cc.message.send.auto.service.AutoSendMessageService" timeout="60000" retries="0" check="false" cluster="failover"/>
	<dubbo:reference id="CompanySendService" registry="message-remote" interface="com.howbuy.cc.message.send.company.CompanySendService" check="false" retries="0" timeout="1200000" />

	<!-- crm-core-server -->
	<dubbo:registry id="crm-core-server" protocol="zookeeper" address="${dubbo.registries.crm-core-server.address}" check="false" />
	<dubbo:reference id="createConscardInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscard.service.CreateConscardInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="updateConscustInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.UpdateConscustInfoService" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="queryConscustInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.QueryConscustInfoService" check="false" timeout="12000" />
	<dubbo:reference id="updateCustconstantInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.UpdateCustconstantInfoService" check="false" timeout="12000" />
	<dubbo:reference id="core.jjxxInfoService" registry="crm-core-server" interface="com.howbuy.crm.jjxx.service.JjxxInfoService" check="false" timeout="12000" />
	<dubbo:reference id="core.hbConstantService" registry="crm-core-server" interface="com.howbuy.crm.hbconstant.service.HbConstantService" check="false" timeout="12000" />
	<dubbo:reference id="core.conscustInfoSerive" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.ConscustInfoSerive" check="false" timeout="12000" />
	<dubbo:reference id="core.cmHkConscustService" registry="crm-core-server" interface="com.howbuy.crm.hkcust.service.CmHkConscustService" check="false" timeout="12000" />
	<dubbo:reference id="consultantInfoService" registry="crm-core-server" interface="com.howbuy.crm.consultant.service.ConsultantInfoService" check="false" timeout="12000" />
	<dubbo:reference id="hbOrganizationService" registry="crm-core-server" interface="com.howbuy.crm.organization.service.HbOrganizationService" check="false" timeout="12000" />
	<dubbo:reference id="conscustAcctInfoService" registry="crm-core-server" interface="com.howbuy.crm.conscust.service.ConscustAcctInfoService" check="false" timeout="12000" />
	<dubbo:reference id="organizationCatchService" registry="crm-core-server" interface="com.howbuy.crm.organization.service.OrganizationCatchService" check="false" retries="0" timeout="1200000" />

	<!-- howbuy-simu-server -->
	<dubbo:registry id="howbuy-simu-server" protocol="zookeeper" address="${dubbo.registries.howbuy-simu-server.address}" check="false" />
	<dubbo:reference id="smjzAndHbService" registry="howbuy-simu-server" interface="com.howbuy.simu.service.business.product.SmjzAndHbService"  check="false" retries="0" timeout="1200000" />

	<!-- howbuy-auth-service -->
	<dubbo:registry id="howbuy-auth-service" protocol="zookeeper" address="${dubbo.registries.howbuy-auth-service.address}" check="false" />
	<dubbo:reference id="DecryptBatchFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.decrypt.DecryptBatchFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="EncryptBatchFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.encrypt.EncryptBatchFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="DecryptSingleFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.decrypt.DecryptSingleFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="EncryptSingleFacade" registry="howbuy-auth-service" interface="com.howbuy.auth.facade.encrypt.EncryptSingleFacade" check="false" retries="0" timeout="1200000" />

	<dubbo:registry id="robot-order-center-remote" protocol="zookeeper" address="${dubbo.registries.robot-order-center-remote.address}" check="false" />
	<dubbo:reference id="queryCustTradeAmtFacade" registry="robot-order-center-remote" interface="com.howbuy.tms.robot.orders.facade.query.querycusttradevol.QueryCustTradeAmtFacade" check="false" retries="0" timeout="1200000" />



	<!--crm-wechat-->
	<dubbo:registry id="crm-wechat" protocol="zookeeper" address="${dubbo.registries.crm-wechat-remote.address}" check="false" />
	<dubbo:reference id="queryWeChatMemberInfoService" registry="crm-wechat" interface="com.howbuy.crm.wechat.client.producer.wechatmembermanagement.QueryWeChatMemberInfoService" check="false" timeout="1200000" />

	<!-- export-proxy-gateway -->
	<dubbo:registry id="export-proxy-gateway" protocol="zookeeper" address="${dubbo.registries.export-proxy-gateway.address}" check="false" />
	<dubbo:reference id="cmBeisenTokenFacade" registry="export-proxy-gateway" interface="com.howbuy.gateway.captcha.facade.beisen.CmBeisenTokenFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="cmBeisenOrgFacade" registry="export-proxy-gateway" interface="com.howbuy.gateway.captcha.facade.beisen.CmBeisenOrgFacade" check="false" retries="0" timeout="1200000" />
	<dubbo:reference id="cmBeisenUserInfoFacade" registry="export-proxy-gateway" interface="com.howbuy.gateway.captcha.facade.beisen.CmBeisenUserInfoFacade" check="false" retries="0" timeout="1200000" />

	<!-- crm-account-remote -->
	<dubbo:registry id="crm-account-remote" protocol="zookeeper" address="${dubbo.registries.crm-account-remote.address}" check="false" />
	<dubbo:reference id="CmSyncBeisenFacade"  registry="crm-account-remote"  interface="com.howbuy.crm.account.client.facade.beisen.CmSyncBeisenFacade" check="false"  retries="0"  timeout="1200000"/>
	<dubbo:reference id="CmVisitMinutesFacade"  registry="crm-account-remote"  interface="com.howbuy.crm.account.client.facade.commvisit.CmVisitMinutesFacade" check="false"  retries="0"  timeout="1200000"/>
</beans>

<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context" xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/tx 
       http://www.springframework.org/schema/tx/spring-tx.xsd">

    <tx:annotation-driven/>

    <context:component-scan base-package="com.howbuy.crm.**"/>

    <!-- crmDataSource -->
    <bean id="dataSource" class="com.howbuy.common.db.HowbuyBasicDataSource3"  destroy-method="close">
        <property name="hpsUrl" value="${PS_URL}"/>
        <property name="url" value="${master.ds.jdbcUrl}" />
        <property name="username" value="${master.ds.user}" />
        <property name="password" value="${master.ds.password}" />
        <property name="initialSize" value="${master.ds.initialSize}" />
        <property name="minIdle" value="${master.ds.minIdle}" />
        <property name="maxIdle" value="${master.ds.maxIdle}" />
        <property name="maxActive" value="${master.ds.maxActive}" />
        <property name="maxWait" value="${master.ds.maxWait}" />
        <property name="timeBetweenEvictionRunsMillis" value="${master.ds.timeBetweenEvictionRunsMillis}" />
        <property name="minEvictableIdleTimeMillis" value="${master.ds.minEvictableIdleTimeMillis}" />
        <property name="validationQuery" value="${master.ds.validationQuery}" />
        <property name="testWhileIdle" value="${master.ds.testWhileIdle}" />
        <property name="testOnBorrow" value="${master.ds.testOnBorrow}" />
        <property name="testOnReturn" value="${master.ds.testOnReturn}" />
    </bean>

    <bean name="paginationInterceptor" class="com.howbuy.crm.dubbo.framework.dbutil.plugins.PaginationInterceptorPlugin"/>
    <bean name="cacheInterceptor" class="com.howbuy.crm.dubbo.framework.dbutil.plugins.CacheInterceptorPlugin"/>

    <!-- crmDataSource SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="typeAliasesPackage" value="com.howbuy.crm.**.domain"/>
        <property name="mapperLocations" value="classpath*:com/howbuy/crm/**/mapper/*Mapper.xml">
        </property>
        <property name="plugins">
            <array>
                <ref bean="paginationInterceptor"/>
                <ref bean="cacheInterceptor"/>
                <bean class="com.github.pagehelper.PageInterceptor">
                    <property name="properties">
                        <value>
                            helperDialect=oracle
                            supportMethodsArguments=true
                            params=count=countSql
                        </value>
                    </property>
                </bean>
            </array>
        </property>
        <!-- mybatis配置 -->
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
    </bean>

    <!-- scan for mappers and let them be autowired -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.howbuy.crm.**.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!-- (事务管理)transaction manager, use JtaTransactionManager for global tx -->
    <bean id="transactionManager"  class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

</beans>
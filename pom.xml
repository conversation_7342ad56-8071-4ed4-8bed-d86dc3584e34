<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.howbuy.crm</groupId>
    <artifactId>crm-nt-server</artifactId>
    <version>bugfix-20250717-RELEASE</version>

    <name>crm-nt-server</name>
    <!-- FIXME change it to the project's website -->
    <url>http://www.example.com</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <com.howbuy.dubbo-commons.version>1.2.4-RELEASE</com.howbuy.dubbo-commons.version>
        <com.howbuy.crm-nt-client.version>bugfix-20250717-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.crm-core-client.version>1.9.2.4-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.acc-center-facade.version>3.6.3-RELEASE</com.howbuy.acc-center-facade.version>

        <com.howbuy.center-client.version>4.6.53-RELEASE</com.howbuy.center-client.version>
        <com.howbuy.message-public-client.version>5.1.13-RELEASE</com.howbuy.message-public-client.version>
        <nacos.version>1.1.0</nacos.version>
        <com.howbuy.howbuy-content-client.version>release-20250331-khhx-2.0.0-2-RELEASE
        </com.howbuy.howbuy-content-client.version>
        <com.howbuy.howbuy-simu-client.version>release-20250423-fof2.0-RELEASE</com.howbuy.howbuy-simu-client.version>
        <powermock.version>2.0.7</powermock.version>
        <spring-boot.version>2.1.6.RELEASE</spring-boot.version>
        <log4j2.version>2.15.0</log4j2.version>
        <spring-support-alibaba.version>1.0.11</spring-support-alibaba.version><!--1.0.11-->
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-rocket.version>2.2.1-RELEASE</com.howbuy.howbuy-message-rocket.version>
        <com.howbuy.howbuy-message-amq.version>2.2.1-RELEASE</com.howbuy.howbuy-message-amq.version>
        <com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
        <com.howbuy.howbuy-cachemanagement.version>3.7.0-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.howbuy-message-service-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-service-2.version>
        <com.howbuy.howbuy-message-rocket-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-rocket-2.version>
        <com.howbuy.howbuy-message-amq-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-amq-2.version>
        <com.howbuy.robot-order-center.version>4.5.63-RELEASE</com.howbuy.robot-order-center.version>
        <com.howbuy.robot-order-center-client.version>4.7.99-RELEASE</com.howbuy.robot-order-center-client.version>
        <com.howbuy.crm-wechat.version>1.9.7.5-RELEASE</com.howbuy.crm-wechat.version>
        <com.howbuy.gateway-captcha-client.version>4.7.99-RELEASE</com.howbuy.gateway-captcha-client.version>
        <com.howbuy.crm-account.version>1.9.5.4-RELEASE</com.howbuy.crm-account.version>
        <com.howbuy.crm-wechat-client.version>1.9.6.7-RELEASE</com.howbuy.crm-wechat-client.version>
        <com.howbuy.crm-account-client.version>bugfix20250603-RELEASE</com.howbuy.crm-account-client.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>HowbuyServiceBus</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>dubbo-commons</artifactId>
            <version>${com.howbuy.dubbo-commons.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-wechat-client</artifactId>
            <version>${com.howbuy.crm-wechat-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>robot-order-center-client</artifactId>
            <version>${com.howbuy.robot-order-center-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-nt-client</artifactId>
            <version>${com.howbuy.crm-nt-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-service-2</artifactId>
            <version>${com.howbuy.howbuy-message-service-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.pa.framework</groupId>
            <artifactId>howbuy-framework-rpc</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>spring-test</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-tx</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>zookeeper</artifactId>
                    <groupId>org.apache.zookeeper</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hrb</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>aspectjrt</artifactId>
                    <groupId>org.aspectj</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-jdbc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-amq-2</artifactId>
            <version>${com.howbuy.howbuy-message-amq-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>activemq-all</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-rocket-2</artifactId>
            <version>${com.howbuy.howbuy-message-rocket-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-core-client</artifactId>
            <version>${com.howbuy.crm-core-client.version}</version>
        </dependency>

        <!-- log4j2 依赖 -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-bom</artifactId>
            <version>${log4j2.version}</version>
            <scope>import</scope>
            <type>pom</type>
        </dependency>

        <!-- Spring Boot 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>${spring-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--账户中心-->
        <dependency>
            <groupId>com.howbuy.acccenter</groupId>
            <artifactId>acc-center-facade</artifactId>
            <version>${com.howbuy.acc-center-facade.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>howbuy-cache-client</artifactId>
                    <groupId>com.howbuy.pa.cache</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-jdbc</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-jms</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-aop</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-beans</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-test</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-core</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-slf4j-impl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy.cc</groupId>
            <artifactId>center-client</artifactId>
            <version>${com.howbuy.center-client.version}</version>
        </dependency>
        <!-- 消息中心 -->
        <dependency>
            <groupId>com.howbuy.cc.message</groupId>
            <artifactId>message-public-client</artifactId>
            <version>${com.howbuy.message-public-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-service</artifactId>
            <version>${com.howbuy.howbuy-message-service.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-amq</artifactId>
            <version>${com.howbuy.howbuy-message-amq.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>activemq-all</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-rocket</artifactId>
            <version>${com.howbuy.howbuy-message-rocket.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>5.2.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.12.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cachemanagement</artifactId>
            <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>activemq-all</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-ccms-watcher</artifactId>
            <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
        </dependency>
        <!--jsr 303 -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>1.1.0.Final</version>
        </dependency>
        <!-- hibernate validator -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.2.0.Final</version>
        </dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>4.3.5.RELEASE</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>

        <!--资料更新通知-->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-content-client</artifactId>
            <version>${com.howbuy.howbuy-content-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>emoji-java</artifactId>
                    <groupId>com.vdurmont</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-annotation</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-simu-client</artifactId>
            <version>${com.howbuy.howbuy-simu-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_2.10</artifactId>
            <version>0.8.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sun.jdmk</groupId>
                    <artifactId>jmxtools</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sun.jmx</groupId>
                    <artifactId>jmxri</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 日志脱敏 -->
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>tms-common-log-pattern</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
            <version>${nacos.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.annotation-api</artifactId>
                    <groupId>javax.annotation</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang3</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--单元测试 start-->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-testng</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4-rule-agent</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>

        <!--单元测试 end-->

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.0.1</version>
        </dependency>

        <!--网关-->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>gateway-captcha-client</artifactId>
            <version>${com.howbuy.gateway-captcha-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-account-client</artifactId>
            <version>${com.howbuy.crm-account-client.version}</version>
        </dependency>
    </dependencies>

    <build>
        <!-- 解决maven打包mybatis映射文件打包不了的问题 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>

        <plugins>

            <!--<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/spring.handlers</resource>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>bootstrap.AppStart</mainClass>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>META-INF/spring.schemas</resource>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>mofang-config-client.properties</exclude>
                                        <exclude>config_register.properties</exclude>
                                        <exclude>server_url.properties</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>-->

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>mofang-config-client.properties</exclude>
                    </excludes>
                    <archive>
                        <manifestEntries>
                            <Package-Stamp>${parelease}</Package-Stamp>
                        </manifestEntries>
                        <manifest>
                            <useUniqueVersions>false</useUniqueVersions>
                            <addClasspath>true</addClasspath>
                            <mainClass>bootstrap.AppStart</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>install</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/crm-nt-server/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.5.201505241946</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
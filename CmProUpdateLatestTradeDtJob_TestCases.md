# CmProUpdateLatestTradeDtJob 测试场景分析和测试案例

## 1. 测试策略概述

### 1.1 测试目标
- 验证定时任务的正确执行
- 确保数据更新的准确性和完整性
- 验证异常处理和容错机制
- 测试并发场景下的分布式锁机制
- 验证性能和稳定性

### 1.2 测试范围
- 单元测试：核心业务逻辑
- 集成测试：消息队列、数据库交互
- 性能测试：大数据量处理能力
- 异常测试：各种异常场景处理
- 并发测试：分布式锁和并发安全

## 2. 测试环境准备

### 2.1 测试数据准备
```sql
-- 准备测试用的高净值交易数据
INSERT INTO CM_CUST_PRIVATE_FUND_TRADE (CUST_NO, TRADE_DATE, TRADE_AMOUNT, TRADE_TYPE) VALUES
('CUST001', '2024-01-15', 1000000, 'BUY'),
('CUST001', '2024-02-20', 500000, 'SELL'),
('CUST002', '2024-01-10', 2000000, 'BUY'),
('CUST003', '2024-03-01', 1500000, 'BUY');

-- 准备客户基础数据
INSERT INTO CM_CONSCUST (CONSCUST_NO, LATESTTRADEDT, CREATE_TIME) VALUES
('CUST001', '2023-12-01', NOW()),
('CUST002', '2023-11-15', NOW()),
('CUST003', NULL, NOW());
```

### 2.2 Mock配置
```java
@MockBean
private UpdateLatestTradeDtService updateLatestTradeDtService;

@MockBean
private LockService lockService;

@Value("${sync.TOPIC_CM_PRO_UPDATE_LATESTTRADEDT_JOB}")
private String testQueue = "test.queue.update.latest.trade.dt";
```

## 3. 单元测试案例

### 3.1 正常流程测试

#### TC001: 正常消息处理测试
```java
@Test
@DisplayName("正常消息处理流程测试")
public void testNormalMessageProcessing() {
    // Given
    SimpleMessage message = createTestMessage();
    when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
    doNothing().when(updateLatestTradeDtService).syncProdData(anyString());
    
    // When
    cmProUpdateLatestTradeDtJob.processMessage(message);
    
    // Then
    verify(updateLatestTradeDtService, times(1)).syncProdData(anyString());
    verify(lockService, times(1)).releaseLock(anyString());
}
```

#### TC002: 队列名称配置测试
```java
@Test
@DisplayName("队列名称配置正确性测试")
public void testQueueConfiguration() {
    // When
    String actualQueue = cmProUpdateLatestTradeDtJob.getQuartMessageChannel();
    
    // Then
    assertEquals(testQueue, actualQueue);
    assertNotNull(actualQueue);
    assertFalse(actualQueue.isEmpty());
}
```

### 3.2 业务逻辑测试

#### TC003: 数据更新逻辑测试
```java
@Test
@DisplayName("客户最新交易日期更新逻辑测试")
public void testUpdateLatestTradeDateLogic() {
    // Given
    List<ConscustInfo> testData = createTestConscustData();
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(testData);
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then
    verify(conscustMapper, times(1))
        .batchUpdateLatestTradeDtByConscustNo(anyList());
}
```

#### TC004: 空数据处理测试
```java
@Test
@DisplayName("空数据集处理测试")
public void testEmptyDataHandling() {
    // Given
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(Collections.emptyList());
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then
    verify(conscustMapper, never())
        .batchUpdateLatestTradeDtByConscustNo(anyList());
}
```

#### TC005: 批量处理逻辑测试
```java
@Test
@DisplayName("批量处理逻辑测试")
public void testBatchProcessing() {
    // Given - 创建250条测试数据（超过批次大小200）
    List<ConscustInfo> largeDataSet = createLargeTestData(250);
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(largeDataSet);
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then - 应该调用2次批量更新（200 + 50）
    verify(conscustMapper, times(2))
        .batchUpdateLatestTradeDtByConscustNo(anyList());
}
```

## 4. 集成测试案例

### 4.1 消息队列集成测试

#### TC006: 消息接收和处理集成测试
```java
@Test
@DisplayName("消息队列集成测试")
@Transactional
@Rollback
public void testMessageQueueIntegration() {
    // Given
    SimpleMessage message = createRealMessage();
    
    // When
    MessageService.getInstance()
        .sendMessage(testQueue, message);
    
    // Then - 等待异步处理完成
    await().atMost(10, SECONDS).untilAsserted(() -> {
        // 验证数据库中的数据已更新
        List<ConscustInfo> updatedData = conscustMapper
            .selectByConscustNos(Arrays.asList("CUST001", "CUST002"));
        assertThat(updatedData).isNotEmpty();
    });
}
```

### 4.2 数据库集成测试

#### TC007: 数据库事务测试
```java
@Test
@DisplayName("数据库事务完整性测试")
@Transactional
public void testDatabaseTransactionIntegrity() {
    // Given
    prepareTestData();
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then
    List<ConscustInfo> result = conscustMapper
        .selectByConscustNos(Arrays.asList("CUST001", "CUST002", "CUST003"));
    
    assertThat(result).hasSize(3);
    assertThat(result.get(0).getLatestTradeDt()).isEqualTo("2024-02-20");
    assertThat(result.get(1).getLatestTradeDt()).isEqualTo("2024-01-10");
    assertThat(result.get(2).getLatestTradeDt()).isEqualTo("2024-03-01");
}
```

## 5. 异常测试案例

### 5.1 分布式锁异常测试

#### TC008: 锁获取失败测试
```java
@Test
@DisplayName("分布式锁获取失败测试")
public void testLockAcquisitionFailure() {
    // Given
    SimpleMessage message = createTestMessage();
    when(lockService.getLock(anyString(), anyInt())).thenReturn(false);
    
    // When
    cmProUpdateLatestTradeDtJob.processMessage(message);
    
    // Then
    verify(updateLatestTradeDtService, never()).syncProdData(anyString());
    verify(lockService, never()).releaseLock(anyString());
}
```

#### TC009: 锁释放异常测试
```java
@Test
@DisplayName("锁释放异常处理测试")
public void testLockReleaseException() {
    // Given
    SimpleMessage message = createTestMessage();
    when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
    doThrow(new RuntimeException("Lock release failed"))
        .when(lockService).releaseLock(anyString());
    
    // When & Then
    assertDoesNotThrow(() -> {
        cmProUpdateLatestTradeDtJob.processMessage(message);
    });
}
```

### 5.2 数据库异常测试

#### TC010: 数据库连接异常测试
```java
@Test
@DisplayName("数据库连接异常测试")
public void testDatabaseConnectionException() {
    // Given
    SimpleMessage message = createTestMessage();
    when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
    doThrow(new DataAccessException("Database connection failed") {})
        .when(updateLatestTradeDtService).syncProdData(anyString());
    
    // When
    cmProUpdateLatestTradeDtJob.processMessage(message);
    
    // Then
    verify(lockService, times(1)).releaseLock(anyString());
    // 验证异常被正确处理，任务状态为失败
}
```

#### TC011: SQL执行异常测试
```java
@Test
@DisplayName("SQL执行异常测试")
public void testSQLExecutionException() {
    // Given
    List<ConscustInfo> testData = createTestConscustData();
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(testData);
    doThrow(new BadSqlGrammarException("SQL syntax error", "UPDATE", null))
        .when(conscustMapper).batchUpdateLatestTradeDtByConscustNo(anyList());
    
    // When & Then
    assertThrows(BadSqlGrammarException.class, () -> {
        updateLatestTradeDtService.syncProdData("test");
    });
}
```

### 5.3 业务异常测试

#### TC012: 消息格式异常测试
```java
@Test
@DisplayName("消息格式异常测试")
public void testInvalidMessageFormat() {
    // Given
    SimpleMessage invalidMessage = new SimpleMessage();
    invalidMessage.setContent("invalid json format");
    when(lockService.getLock(anyString(), anyInt())).thenReturn(true);
    
    // When & Then
    assertDoesNotThrow(() -> {
        cmProUpdateLatestTradeDtJob.processMessage(invalidMessage);
    });
    
    verify(lockService, times(1)).releaseLock(anyString());
}
```

## 6. 性能测试案例

### 6.1 大数据量处理测试

#### TC013: 大数据量处理性能测试
```java
@Test
@DisplayName("大数据量处理性能测试")
@Timeout(value = 30, unit = TimeUnit.SECONDS)
public void testLargeDataProcessingPerformance() {
    // Given - 创建10000条测试数据
    List<ConscustInfo> largeDataSet = createLargeTestData(10000);
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(largeDataSet);
    
    // When
    long startTime = System.currentTimeMillis();
    updateLatestTradeDtService.syncProdData("test");
    long endTime = System.currentTimeMillis();
    
    // Then
    long executionTime = endTime - startTime;
    assertThat(executionTime).isLessThan(30000); // 30秒内完成
    
    // 验证批次调用次数 (10000/200 = 50次)
    verify(conscustMapper, times(50))
        .batchUpdateLatestTradeDtByConscustNo(anyList());
}
```

### 6.2 内存使用测试

#### TC014: 内存使用优化测试
```java
@Test
@DisplayName("内存使用优化测试")
public void testMemoryUsageOptimization() {
    // Given
    Runtime runtime = Runtime.getRuntime();
    long initialMemory = runtime.totalMemory() - runtime.freeMemory();
    
    List<ConscustInfo> largeDataSet = createLargeTestData(5000);
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(largeDataSet);
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then
    System.gc(); // 建议垃圾回收
    long finalMemory = runtime.totalMemory() - runtime.freeMemory();
    long memoryIncrease = finalMemory - initialMemory;
    
    // 内存增长应该在合理范围内（例如小于100MB）
    assertThat(memoryIncrease).isLessThan(100 * 1024 * 1024);
}
```

## 7. 并发测试案例

### 7.1 并发安全测试

#### TC015: 并发执行安全测试
```java
@Test
@DisplayName("并发执行安全测试")
public void testConcurrentExecutionSafety() throws InterruptedException {
    // Given
    int threadCount = 5;
    CountDownLatch latch = new CountDownLatch(threadCount);
    AtomicInteger lockSuccessCount = new AtomicInteger(0);
    
    when(lockService.getLock(anyString(), anyInt()))
        .thenAnswer(invocation -> {
            // 模拟只有一个线程能获取到锁
            return lockSuccessCount.incrementAndGet() == 1;
        });
    
    // When
    ExecutorService executor = Executors.newFixedThreadPool(threadCount);
    for (int i = 0; i < threadCount; i++) {
        executor.submit(() -> {
            try {
                SimpleMessage message = createTestMessage();
                cmProUpdateLatestTradeDtJob.processMessage(message);
            } finally {
                latch.countDown();
            }
        });
    }
    
    latch.await(10, TimeUnit.SECONDS);
    executor.shutdown();
    
    // Then - 只有一个线程应该执行业务逻辑
    verify(updateLatestTradeDtService, times(1)).syncProdData(anyString());
}
```

## 8. 边界测试案例

### 8.1 边界值测试

#### TC016: 批处理边界值测试
```java
@Test
@DisplayName("批处理边界值测试")
public void testBatchProcessingBoundaryValues() {
    // Test Case 1: 正好200条数据
    testBatchSize(200, 1);
    
    // Test Case 2: 199条数据
    testBatchSize(199, 1);
    
    // Test Case 3: 201条数据
    testBatchSize(201, 2);
    
    // Test Case 4: 1条数据
    testBatchSize(1, 1);
}

private void testBatchSize(int dataSize, int expectedBatches) {
    // Given
    List<ConscustInfo> testData = createLargeTestData(dataSize);
    when(cmCustPrivateFundTradeMapper.listCustNoAndLatestTradeDt())
        .thenReturn(testData);
    
    // When
    updateLatestTradeDtService.syncProdData("test");
    
    // Then
    verify(conscustMapper, times(expectedBatches))
        .batchUpdateLatestTradeDtByConscustNo(anyList());
    
    reset(conscustMapper); // 重置mock以便下次测试
}
```

## 9. 测试数据工厂

### 9.1 测试数据创建工具
```java
public class TestDataFactory {
    
    public static SimpleMessage createTestMessage() {
        SimpleMessage message = new SimpleMessage();
        Map<String, Object> content = new HashMap<>();
        content.put("qrtz_log_id", "TEST_LOG_001");
        content.put("trigger_time", System.currentTimeMillis());
        message.setContent(content);
        return message;
    }
    
    public static List<ConscustInfo> createTestConscustData() {
        List<ConscustInfo> list = new ArrayList<>();
        
        ConscustInfo info1 = new ConscustInfo();
        info1.setConscustNo("CUST001");
        info1.setLatestTradeDt("2024-02-20");
        list.add(info1);
        
        ConscustInfo info2 = new ConscustInfo();
        info2.setConscustNo("CUST002");
        info2.setLatestTradeDt("2024-01-10");
        list.add(info2);
        
        return list;
    }
    
    public static List<ConscustInfo> createLargeTestData(int size) {
        List<ConscustInfo> list = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            ConscustInfo info = new ConscustInfo();
            info.setConscustNo("CUST" + String.format("%06d", i));
            info.setLatestTradeDt("2024-01-" + String.format("%02d", (i % 28) + 1));
            list.add(info);
        }
        return list;
    }
}
```

## 10. 测试执行计划

### 10.1 测试阶段划分
```
Phase 1: 单元测试 (TC001-TC005)
├── 开发阶段执行
├── 代码覆盖率要求: >90%
└── 执行频率: 每次代码提交

Phase 2: 集成测试 (TC006-TC007)
├── 集成环境执行
├── 数据库和消息队列依赖
└── 执行频率: 每日构建

Phase 3: 异常测试 (TC008-TC012)
├── 测试环境执行
├── 模拟各种异常场景
└── 执行频率: 版本发布前

Phase 4: 性能测试 (TC013-TC014)
├── 性能测试环境执行
├── 大数据量和长时间运行
└── 执行频率: 版本发布前

Phase 5: 并发测试 (TC015)
├── 压力测试环境执行
├── 多线程并发场景
└── 执行频率: 版本发布前

Phase 6: 边界测试 (TC016)
├── 测试环境执行
├── 各种边界条件验证
└── 执行频率: 版本发布前
```

### 10.2 测试通过标准
- 所有单元测试通过率100%
- 集成测试通过率100%
- 异常测试能正确处理各种异常情况
- 性能测试满足预期指标
- 并发测试确保数据一致性
- 边界测试覆盖所有边界条件

### 10.3 测试报告要求
- 测试用例执行结果统计
- 代码覆盖率报告
- 性能测试指标报告
- 异常处理验证报告
- 问题和风险识别报告
# CmProUpdateLatestTradeDtJob 业务分析文档

## 1. 概述

`CmProUpdateLatestTradeDtJob` 是一个定时调度任务，用于更新所有客户最近一次高净值交易日期。该任务通过消息队列机制触发，替代了原有的存储过程 `PRO_UPDATE_LATESTTRADEDT`。

## 2. 核心功能

### 2.1 主要职责
- 定期更新客户表（CM_CONSCUST）中的最近交易日期字段（LATESTTRADEDT）
- 从高净值交易数据中统计客户的最新交易时间
- 批量处理数据以提高性能

### 2.2 技术特点
- 基于消息队列的异步处理机制
- 分布式锁确保任务唯一性执行
- 批量数据处理（每批200条记录）
- 完整的日志记录和异常处理

## 3. 数据推演分析

### 3.1 数据来源
```
CM_CUST_PRIVATE_FUND_TRADE (高净值客户交易表)
├── 客户号 (CUST_NO)
├── 交易日期 (TRADE_DATE)
└── 其他交易相关字段
```

### 3.2 数据处理流程
```
1. 数据读取阶段
   ├── 从 CM_CUST_PRIVATE_FUND_TRADE 表查询
   ├── 按客户号分组，获取最新交易日期
   └── 返回 List<ConscustInfo> 结果集

2. 数据验证阶段
   ├── 检查结果集是否为空
   └── 记录待处理数据总量

3. 数据更新阶段
   ├── 将数据按200条分批处理
   ├── 批量更新 CM_CONSCUST 表
   └── 更新 LATESTTRADEDT 字段
```

### 3.3 数据表关系
```
CM_CUST_PRIVATE_FUND_TRADE (源表)
    ↓ (聚合统计)
CM_HIGH_CUSTINFO (统计表)
    ↓ (同步更新)
CM_CONSCUST (目标表)
```

### 3.4 数据量预估
- 预期处理客户数量：根据高净值客户基数确定
- 批处理大小：200条/批
- 处理频率：根据调度配置确定

## 4. 业务流程图

```mermaid
flowchart TD
    A[调度中心] -->|发送消息| B[消息队列]
    B --> C[CmProUpdateLatestTradeDtJob]
    C --> D{获取分布式锁}
    D -->|失败| E[任务结束]
    D -->|成功| F[执行业务逻辑]
    F --> G[UpdateLatestTradeDtService]
    G --> H[查询高净值交易数据]
    H --> I{数据是否为空}
    I -->|是| J[记录日志并结束]
    I -->|否| K[数据分批处理]
    K --> L[批量更新客户表]
    L --> M[释放分布式锁]
    M --> N[返回执行结果]
    N --> O[调度中心接收反馈]
    
    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style H fill:#fff3e0
    style L fill:#e8f5e8
```

## 5. 时序图

```mermaid
sequenceDiagram
    participant SC as 调度中心
    participant MQ as 消息队列
    participant Job as CmProUpdateLatestTradeDtJob
    participant Lock as 分布式锁服务
    participant Service as UpdateLatestTradeDtService
    participant TradeDB as 交易数据库
    participant CustDB as 客户数据库
    participant Cache as 缓存服务

    SC->>MQ: 发送调度消息
    MQ->>Job: 接收消息并触发任务
    Job->>Lock: 尝试获取分布式锁
    
    alt 获取锁成功
        Lock-->>Job: 锁获取成功
        Job->>Service: 调用syncProdData()
        Service->>TradeDB: 查询客户最新交易日期
        TradeDB-->>Service: 返回客户交易数据
        
        alt 有数据需要更新
            Service->>Service: 数据分批(200条/批)
            loop 批量处理
                Service->>CustDB: 批量更新客户表
                CustDB-->>Service: 更新完成确认
            end
        else 无数据更新
            Service->>Service: 记录日志并结束
        end
        
        Service-->>Job: 业务处理完成
        Job->>Lock: 释放分布式锁
        Job->>Cache: 写入执行结果
        Job-->>SC: 返回执行状态
        
    else 获取锁失败
        Lock-->>Job: 锁获取失败
        Job->>Job: 记录日志并结束
    end
```

## 6. 关键技术点

### 6.1 分布式锁机制
- **锁键**: `LOCK_KEY_PREFIX + 队列名称`
- **过期时间**: 600秒（默认）
- **作用**: 确保同一时间只有一个实例执行任务

### 6.2 批量处理策略
- **批次大小**: 200条记录/批
- **优势**: 减少数据库连接开销，提高处理效率
- **实现**: 使用Google Guava的Lists.partition()方法

### 6.3 异常处理机制
- **异常捕获**: 在doProcessMessage层面捕获所有异常
- **日志记录**: 详细记录异常信息和执行状态
- **结果反馈**: 向调度中心反馈执行结果（成功/失败）

### 6.4 性能监控
- **执行时间**: 记录各阶段耗时
- **数据量统计**: 记录处理的数据条数
- **UUID追踪**: 使用LoggerUtils设置请求追踪ID

## 7. 配置说明

### 7.1 必要配置项
```properties
# 消息队列配置
sync.TOPIC_CM_PRO_UPDATE_LATESTTRADEDT_JOB=队列名称

# 数据库连接配置
# MyBatis配置
# 缓存服务配置
```

### 7.2 依赖服务
- 消息队列服务
- 分布式锁服务（Redis）
- 数据库服务（MySQL）
- 缓存服务

## 8. 监控建议

### 8.1 关键指标
- 任务执行频率和耗时
- 处理的数据量统计
- 异常发生率
- 锁获取成功率

### 8.2 告警设置
- 任务执行失败告警
- 执行时间超时告警
- 数据处理异常告警
- 锁获取失败告警

## 9. 优化建议

### 9.1 性能优化
- 根据数据量调整批处理大小
- 考虑使用数据库连接池优化
- 增加数据库索引优化查询性能

### 9.2 可靠性优化
- 增加重试机制
- 优化异常处理逻辑
- 考虑增加数据一致性校验

### 9.3 可维护性优化
- 增加更详细的业务日志
- 考虑增加执行进度反馈
- 优化配置管理方式